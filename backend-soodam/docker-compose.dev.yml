version: "3.7"
services:
  fastapi_dev:
    image: python:latest
    container_name: dev_backend_soodam
    build:
      context: "./backend_soodam"
      target: "dev"
    working_dir: /src
      #restart: always
    volumes:
      - ./backend_soodam:/src
      - ./logs:/src/logs
#      - ./data/media:/src/media
#      - ./data/static:/src/static
      - ./backend_soodam/media:/src/media
      - ./backend_soodam/static:/src/static
    ports:
      - "4000:4000"
      - "4001:4001"
    environment:
      DB_NAME: soodam_dev
      DB_USER: soodam_dev
      DB_PASSWORD: soodam_dev_pass_123456
      DB_HOST: postgres_dev
      DB_PORT: 5432
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 1
      REDIS_URL: redis://redis:6379/1
#      - ELASTICSEARCH_URL=http://elasticsearch:9200
#      - REDIS_URL=redis://redis:6379/0
      ENVIRONMENT: development
      ENV_STATE: development
      STATIC_ROOT: /src/static
      MEDIA_ROOT: /src/media
    env_file:
      - ./backend_soodam/fastapi.env
        #  - ./app/backend_soodam/fastapi.env.dev
    depends_on:
      - postgres_dev
      - redis
#      - elasticsearch
#    links:
#      - postgres_dev
    command: ./scripts/dev/runlocalserver.sh
#    networks:
#      - app-web-net

  postgres_dev:
    container_name: dev_backend_soodam_postgres
    image: 'postgis/postgis:latest'
      #restart: unless-stopped
    volumes:
      - ./data/postgres_dev:/var/lib/postgresql/data
      #- ./data/postgres:/var/lib/postgresql/data
      #- ./postgres-config/pg_hba.conf:/etc/postgresql/pg_hba.conf
      #- ./postgres-config/postgresql.conf:/etc/postgresql/postgresql.conf
#      - postgres_data_dev:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: soodam_dev
      POSTGRES_PASSWORD: soodam_dev_pass_123456
      POSTGRES_DB: soodam_dev
    ports:
      - 54432:5432
#    networks:
#      - app-web-net

  redis:
    container_name: soodam_dev_redis
    image: 'redis:alpine'
      #restart: unless-stopped
    volumes:
      - ./data/redis:/data
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass ""
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
#  elasticsearch:
#    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
#    container_name: soodam_dev_elasticsearch
#    restart: unless-stopped
#    environment:
#      - discovery.type=single-node
#      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
#    volumes:
#      - ./data/elasticsearch:/usr/share/elasticsearch/data
#    ports:
#      - "9201:9200"  # Internal port 9200, external 9201
#  pgadmin:
#    container_name: PgAdmin_container
#    image: dpage/pgadmin4
#    restart: always
#    depends_on:
#      - postgres_dev
#    environment:
#      PGADMIN_DEFAULT_EMAIL: <EMAIL>
#      PGADMIN_DEFAULT_PASSWORD: pgadmin4
#    ports:
#      - "5050:80"
#    networks:
#      - app-web-net
#volumes:
#  postgres_data_dev:
#  elasticsearch_data:
#networks:
#  app-web-net:
#    driver: bridge

