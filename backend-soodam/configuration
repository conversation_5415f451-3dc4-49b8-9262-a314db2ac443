
  GNU nano 6.2                                                                                                      /etc/nginx/sites-available/soodam
                                                                                                                                                                                                                                                  1,1           All
server {
    listen 80;
    server_name dev.soodam.com;

    location / {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Host $http_host;
      proxy_set_header X-NginX-Proxy true;
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
      proxy_set_header Access-Control-Allow-Origin *;
#add_header Access-Control-Allow-Origin *;
      proxy_pass http://localhost:4000/;
      proxy_redirect off;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
    }
 }

