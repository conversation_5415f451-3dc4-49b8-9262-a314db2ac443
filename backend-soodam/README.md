# Soodam Backend

This is the backend API for the Soodam application, built with FastAPI and Django ORM.

## Overview

Soodam Backend is a modern API service that combines the speed and simplicity of FastAPI with the powerful ORM and admin interface of Django.

## Features

- User Management
- Advertisement Management
- Blog Management
- Admin Dashboard
- Geolocation Services
- API Versioning
- Caching
- Rate Limiting
- Monitoring and Logging
- Elasticsearch Integration

## Tech Stack

- Python 3.10+
- Django 4.x
- FastAPI
- PostgreSQL with PostGIS
- Redis
- Elasticsearch
- Celery
- Docker
- GitHub Actions

## Prerequisites

- Docker and Docker Compose
- Python 3.10+
- Poetry (recommended) or pip

## Development Setup

### Option 1: Using Docker Compose (Recommended)

1. Clone the repository:
   ```bash
   <NAME_EMAIL>:soodamApp/backend-soodam.git
   cd backend-soodam
   ```
2. Create required directories
   ```bash
   mkdir -p data/redis logs backend_soodam/media backend_soodam/static
   ```
3. Create environment file:
   ```bash
   cp backend_soodam/fastapi.env.tmpl backend_soodam/fastapi.env
   ```
   - Ensure REDIS_URL=redis://redis:6379/0 in backend_soodam/fastapi.env.

4. Start the development environment:
   ```bash
   # For development environment
   docker compose -f docker-compose.dev.yml up -d

   # For production environment
   docker compose -f docker-compose.prod.yml up -d
   
   ```
   - Requires Docker Compose V2:
   ```bash
   sudo apt install docker-compose-plugin
   ```
   - if port 6379 (Redis) is in use, use 6380:6379 in docker-compose.dev.yml or free the port:
   ```bash
   sudo netstat -tulnp | grep 6379
   sudo kill -9 <pid>
   ```
5. Run migrations:

   ```bash
   # For development
   docker compose -f docker-compose.dev.yml run --rm fastapi_dev python manage.py migrate

   # For production
   docker compose -f docker-compose.prod.yml run --rm fastapi_prod python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   # For development
   docker compose -f docker-compose.dev.yml run --rm fastapi_dev python manage.py createsuperuser

   # For production
   docker compose -f docker-compose.prod.yml run --rm fastapi_prod python manage.py createsuperuser
   ```

7. Access the API:
   - Development: http://localhost:4000/api/docs
   - Production: http://localhost:8000/api/docs

#### Notes:

- Use docker compose (not docker-compose) for Docker Compose V2, which is included with Docker Desktop or installed via sudo apt install docker-compose-plugin.
- If port 6379 (Redis) is in use, edit docker-compose.dev.yml to use 6380:6379 or free the port with:
   ```bash
   sudo netstat -tulnp | grep 6379
   sudo kill -9 <pid>
   ```
- If you encounter a ContainerConfig error, ensure redis:7-alpine is used in docker-compose.dev.yml.

### Option 2: Local Development with Poetry

1. Clone the repository:
   ```bash
   <NAME_EMAIL>:soodamApp/backend-soodam.git
   cd backend-soodam
   ```

2. Create a virtual environment and install dependencies:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install --upgrade pip
   poetry install
   ```

3. Set up pre-commit hooks (for developers):
   ```bash
   pip install pre-commit
   pre-commit install
   ```

4. Create environment file:
   ```bash
   cp backend_soodam/fastapi.env.tmpl backend_soodam/fastapi.env
   ```

5. Run migrations:
   ```bash
   cd backend_soodam
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

8. Access the API at http://localhost:8000/api/docs

### Option 3: Local Development with pip

1. Clone the repository:
   ```bash
   <NAME_EMAIL>:soodamApp/backend-soodam.git
   cd backend-soodam
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r backend_soodam/requirements.txt
   ```

4. Create environment file:
   ```bash
   cp backend_soodam/fastapi.env.tmpl backend_soodam/fastapi.env
   ```

5. Run migrations:
   ```bash
   cd backend_soodam
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

8. Access the API at http://localhost:8000/api/docs

## API Documentation

The API documentation is available at:

- Swagger UI: http://localhost:8000/api/docs
- ReDoc: http://localhost:8000/api/redoc
- OpenAPI JSON: http://localhost:8000/api/openapi.json

## API Versioning

API versioning is supported through the `Accept` header:

```
Accept: application/json; version=1.0
```

Available versions:
- 1.0 (default)
- 2.0

## Authentication

Most endpoints require authentication using JWT tokens. To authenticate:

1. Use the `/api/auth/login` endpoint to obtain a token
2. Include the token in the `Authorization` header as `Bearer {token}`

## Testing

Run tests with pytest:

```bash
pytest
```

Run tests with coverage:

```bash
pytest --cov=app tests/
```

## Deployment

The application is automatically deployed using GitHub Actions:

- Push to `develop` branch: Deploys to staging environment
- Push to `master` branch: Deploys to production environment

## Project Structure
The Soodam backend combines Django and FastAPI. The main application is in backend_soodam/app/, with a secondary FastAPI module in backend_soodam/soodam_app/. Key directories include:
```
backend_soodam/
├── app/                    # Django/FastAPI application
│   ├── api/              # API endpoints (e.g., auth.py)
│   ├── core/             # Utilities (e.g., security.py)
│   ├── dependencies/     # Dependency injection (e.g., auth.py)
│   ├── models/           # Database models (e.g., user.py)
│   ├── routers/          # FastAPI routers (e.g., auth.py)
│   ├── schemas/          # Pydantic models (e.g., auth.py)
│   ├── utils/            # Utilities (e.g., email sending)
│   └── templates/        # Email and HTML templates
├── soodam_app/            # Secondary FastAPI module
│   ├── api/v1/          # FastAPI endpoints (e.g., auth.py)
│   ├── deps/            # Dependencies (e.g., auth.py)
│   ├── models/          # Additional models
│   ├── schemas/         # Additional Pydantic models
│   └── main.py          # FastAPI entry point
├── config/                # Django configuration
│   ├── settings/        # Django settings
│   ├── jwt.py           # JWT configuration
│   └── urls.py          # Django URL routing
├── scripts/               # Scripts
│   └── dev/
│       └── runlocalserver.sh  # Development server script
├── media/                 # Uploaded files
├── static/                # Static assets
├── logs/                  # Log files
├── fastapi.env            # Environment variables
├── manage.py              # Django management
├── Dockerfile             # Docker configuration
├── requirements.txt       # Dependencies
└── pyproject.toml         # Poetry configuration
```

## Useful Commands

### Docker Compose Commands

```bash
# Build the containers
docker-compose build

# Start the development environment
docker-compose -f docker-compose.dev.yml up -d

# Start the production environment
docker-compose -f docker-compose.prod.yml up -d

# Run migrations in development
docker-compose -f docker-compose.dev.yml run --rm fastapi_dev python manage.py migrate

# Run migrations in production
docker-compose -f docker-compose.prod.yml run --rm fastapi_prod python manage.py migrate

# Collect static files (production)
docker-compose -f docker-compose.prod.yml run --rm fastapi_prod python manage.py collectstatic --noinput
```

### Aliases for Frequently Used Commands

```bash
source alias.sh
```

## Contributing
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Open a Pull Request

[//]: # (1. Create a new branch from `develop`)

[//]: # (2. Make your changes)

[//]: # (3. Write tests for your changes)

[//]: # (4. Run the test suite)

[//]: # (5. Submit a pull request to `develop`)

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
