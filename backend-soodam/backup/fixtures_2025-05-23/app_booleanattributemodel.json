[{"model": "app.booleanattributemodel", "pk": "12fb1c68-0762-49b0-9cd3-620e859e35cb", "fields": {"name": "سیستم گرم کننده", "star": false, "key": "bool_heating_system", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "486a411d-0893-41c7-9ad0-fb7f905ea0a3", "fields": {"name": "آسانسور", "star": false, "key": "bool_elevator", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "547464b7-2294-493a-b160-7f7fd0f036e7", "fields": {"name": "بال<PERSON>ن", "star": false, "key": "bool_balcony", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "54b600c8-87e5-4670-9aed-a46f01d110d0", "fields": {"name": "روف گاردن(باغچه پشت بام)", "star": false, "key": "bool_roof_garden", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "609dd23a-e51f-46e4-8811-ff2b3fddc107", "fields": {"name": "کلوزت(اتاق لباس)", "star": false, "key": "bool_closet", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "632463e5-eef1-43a8-aa06-2ab0e17a6ecb", "fields": {"name": "سرویس بهداشتی فرنگی", "star": false, "key": "bool_toilet", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "762ade73-095d-41c8-9b7c-b11be090ac20", "fields": {"name": "استخر", "star": false, "key": "bool_swimming_pool", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "7ef34fab-9dc5-4929-a314-0e6ba101c208", "fields": {"name": "لاندری(اتاق لباسشویی)", "star": false, "key": "bool_laundry_room", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "94d76376-1b42-489f-a291-0cf887ab6a39", "fields": {"name": "سونا", "star": false, "key": "bool_sauna", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "9d03fd2b-bee5-4a7f-bfc1-2b1f98f76fff", "fields": {"name": "آبگرمکن", "star": false, "key": "bool_water_heater", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "abc69474-4914-4163-a661-0dae4fb71f71", "fields": {"name": "سیستم خنک کننده", "star": false, "key": "bool_cooling_system", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "ae55ce78-2b5a-4877-be36-1a23709705ef", "fields": {"name": "انبار", "star": false, "key": "bool_warehouse", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "b76432be-53d8-4894-b449-02e5b21107e3", "fields": {"name": "بافت فرسوده", "star": false, "key": "bool_worn_texture", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "c7df7126-c0d1-4559-9275-75f6792c826f", "fields": {"name": "قابل تبدیل", "star": false, "key": "bool_convertible", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "de6b8e47-d27f-4cad-839f-ed91ef4f5612", "fields": {"name": "سند", "star": false, "key": "bool_document", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "ded97a97-e476-4f9a-b218-d42ee20c045b", "fields": {"name": "وان و جکوزی", "star": false, "key": "bool_bathtub_j<PERSON><PERSON>zi", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "f462b6f0-ff8d-439d-b48f-53cf4a3249a6", "fields": {"name": "پارکینگ", "star": false, "key": "bool_parking", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "f9247abf-b5f6-437e-ba31-7f319951e43a", "fields": {"name": "مستر(حمام والدین)", "star": false, "key": "bool_master", "placeholder": null, "icon": "documents/features/user.jpg"}}, {"model": "app.booleanattributemodel", "pk": "f9e32fa7-fe70-4a2c-a32c-8c8304864c86", "fields": {"name": "بازسازی شده است", "star": false, "key": "bool_renovated", "placeholder": null, "icon": "documents/features/user.jpg"}}]