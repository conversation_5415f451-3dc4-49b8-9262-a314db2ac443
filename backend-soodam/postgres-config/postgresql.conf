# -----------------------------
# PostgreSQL configuration file
# -----------------------------

# CONNECTIONS AND AUTHENTICATION
listen_addresses = '*'
max_connections = 100

# RESOURCE USAGE
shared_buffers = 128MB
dynamic_shared_memory_type = posix

# WRITE-AHEAD LOG
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB

# REPORTING AND LOGGING
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_truncate_on_rotation = off
log_rotation_age = 1d
log_rotation_size = 10MB
log_line_prefix = '%m [%p] %q%u@%d '
log_timezone = 'UTC'

# CLIENT CONNECTION DEFAULTS
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# INCLUDE CUSTOM CONFIGURATION
include_dir = 'conf.d'
hba_file = '/etc/postgresql/pg_hba.conf'
