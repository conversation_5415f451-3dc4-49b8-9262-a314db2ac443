# PostgreSQL Client Authentication Configuration File
# ===================================================
#
# This file controls: which hosts are allowed to connect, how clients
# are authenticated, which PostgreSQL user names they can use, which
# databases they can access.
#
# TYPE  DATABASE        USER            ADDRESS                 METHOD
# "local" is for Unix domain socket connections only
local   all             all                                     trust
# IPv4 local connections:
host    all             all             127.0.0.1/32            trust
# IPv6 local connections:
host    all             all             ::1/128                 trust
# Allow connections from Docker containers
host    all             all             **********/12           md5
# Allow connections from anywhere (for development only)
host    all             all             0.0.0.0/0               md5
