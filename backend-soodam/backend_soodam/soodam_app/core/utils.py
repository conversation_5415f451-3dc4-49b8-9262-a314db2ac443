import jwt
from datetime import datetime, timedelta
from typing import Dict

SECRET_KEY = "your_secret_key"  # باید به صورت امن ذخیره بشه
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60  # مدت زمان انقضا (1 ساعت)
ACCESS_TOKEN_EXPIRE_DAYS = 60  # مدت زمان انقضا (1 ساعت)


def jwt_decode_handler(token: str) -> dict:
    return jwt.decode(token,SECRET_KEY, algorithms=[JWT_ALGORITHM])


def create_access_token_response(
        data: dict, expires_delta: timedelta | None = None, verify_code: str = ''
) -> dict[str, str]:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=ACCESS_TOKEN_EXPIRE_DAYS)
    data.update({"exp": expire})
    return {
        "token": jwt.encode(
            data, SECRET_KEY, algorithm=JWT_ALGORITHM
        ),
        "token_type": "bearer",
    }
