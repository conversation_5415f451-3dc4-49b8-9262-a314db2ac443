from fastapi import HTTPException, status

class RateLimitException(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Please wait before requesting another OTP."
        )

class InvalidOTPException(HTTPException):
    def __init__(self, detail: str = "Invalid OTP"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )
