from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import get_db
from app.schemas.auth import SendOTPRequest, VerifyOTPRequest
from app.services import otp_service
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from core.dependencies import get_db
from user.services import OTPService
from user.schemas import OTPVerifySchema
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from core.dependencies import get_db
from user.services import OTPService
from user.schemas import OTPRequestSchema
router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/request-otp")
async def request_otp(
    request: Request,
    schema: OTPRequestSchema,
    db: AsyncSession = Depends(get_db)
):
    ip = request.client.host
    user_agent = request.headers.get("user-agent", "")
    await OTPService.create_otp(schema.phone_number, ip, user_agent, db)
    return {"detail": "OTP sent successfully"}

@router.post("/verify-code")
async def verify_otp(
    payload: VerifyOTPRequest,
    db: AsyncSession = Depends(get_db)
):
    token = await otp_service.verify_otp(payload.phone_number, payload.code, db)
    return token
    # await OTPService.verify_otp(schema.phone_number, schema.code, db)
    # return {"detail": "OTP verified"}
