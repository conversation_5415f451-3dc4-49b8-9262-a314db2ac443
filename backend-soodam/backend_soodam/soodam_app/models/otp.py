# models/otp.py

from datetime import datetime
from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from app.models.base import Base

class OTPCode(Base):
    __tablename__ = "otp_codes"

    phone: Mapped[str] = mapped_column(String(15), index=True, nullable=False)
    code: Mapped[str] = mapped_column(String(6), nullable=False)
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    expires_at: Mapped[datetime]
    is_active: Mapped[bool] = mapped_column(default=True)
    attempt_count: Mapped[int] = mapped_column(default=0)
    ip_address: Mapped[str] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[str] = mapped_column(String(256), nullable=True)

    def is_expired(self) -> bool:
        return datetime.utcnow() > self.expires_at
