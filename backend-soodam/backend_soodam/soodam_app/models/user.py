import enum
import uuid
from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from core.database import Base
from models.mixins import TimestampUUIDMixin


class UserTypeEnum(str, enum.Enum):
    ADMIN = "admin"
    CUSTOMER = "customer"
    ADVERTISER = "advertiser"


class GenderEnum(str, enum.Enum):
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"


class User(Base, TimestampUUIDMixin):
    __tablename__ = "users"

    phone_number: Mapped[str] = mapped_column(String(20), unique=True, index=True)
    email: Mapped[str | None] = mapped_column(String(255), nullable=True, unique=True)
    username: Mapped[str | None] = mapped_column(String(150), nullable=True, unique=True)
    first_name: Mapped[str] = mapped_column(String(100))
    last_name: Mapped[str] = mapped_column(String(100))
    father_name: Mapped[str | None] = mapped_column(String(100), nullable=True)
    national_code: Mapped[str | None] = mapped_column(String(10), nullable=True, unique=True)
    gender: Mapped[GenderEnum | None] = mapped_column(Enum(GenderEnum), nullable=True)
    user_type: Mapped[UserTypeEnum] = mapped_column(Enum(UserTypeEnum), default=UserTypeEnum.CUSTOMER)

    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False)

    address_id: Mapped[uuid.UUID | None] = mapped_column(ForeignKey("addresses.id"), nullable=True)
    city_id: Mapped[uuid.UUID | None] = mapped_column(ForeignKey("cities.id"), nullable=True)
    province_id: Mapped[uuid.UUID | None] = mapped_column(ForeignKey("provinces.id"), nullable=True)

    address: Mapped["Address"] = relationship("Address", foreign_keys=[address_id], lazy="joined")
    city: Mapped["City"] = relationship("City", foreign_keys=[city_id], lazy="joined")
    province: Mapped["Province"] = relationship("Province", foreign_keys=[province_id], lazy="joined")
