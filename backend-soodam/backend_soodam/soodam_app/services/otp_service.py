from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from core.utils import generate_otp_code, send_sms
from soodam_app.models.otp import OTPCode

from soodam_app.core.utils import create_access_token


class OTPService:
    MAX_ATTEMPTS = 5
    MAX_REQUESTS = 5
    EXPIRY_MINUTES = 2
    REQUEST_WINDOW = timedelta(minutes=10)  # بازه زمانی برای محدودیت

    @staticmethod
    async def create_otp(phone_number: str, ip: str, user_agent: str, db: AsyncSession):
        now = datetime.utcnow()

        # بررسی تعداد درخواست‌ها در بازه زمانی
        stmt = select(OTPCode).where(
            OTPCode.phone_number == phone_number,
            OTPCode.created_at >= now - OTPService.REQUEST_WINDOW
        )
        result = await db.execute(stmt)
        recent_requests = result.scalars().all()

        if len(recent_requests) >= OTPService.MAX_REQUESTS:
            raise Exception("Too many requests. Please try again later.")

        # بررسی موجود بودن کد فعال قبلی
        stmt = select(OTPCode).where(
            OTPCode.phone_number == phone_number,
            OTPCode.is_active == True,
            OTPCode.expires_at > now
        )
        result = await db.execute(stmt)
        existing_otp = result.scalar_one_or_none()

        code = generate_otp_code()

        if existing_otp:
            if existing_otp.attempt_count >= OTPService.MAX_ATTEMPTS:
                raise Exception("Too many attempts. Please try later.")

            existing_otp.attempt_count += 1
            existing_otp.code = code
            existing_otp.created_at = now
            existing_otp.expires_at = now + timedelta(minutes=OTPService.EXPIRY_MINUTES)
            existing_otp.ip_address = ip
            existing_otp.user_agent = user_agent
        else:
            new_otp = OTPCode(
                phone_number=phone_number,
                code=code,
                created_at=now,
                expires_at=now + timedelta(minutes=OTPService.EXPIRY_MINUTES),
                ip_address=ip,
                user_agent=user_agent
            )
            db.add(new_otp)

        await db.commit()
        send_sms(phone_number, code)

    @staticmethod
    async def verify_otp(phone_number: str, code: str, db: AsyncSession):
        now = datetime.utcnow()
        stmt = select(OTPCode).where(
            OTPCode.phone_number == phone_number,
            OTPCode.code == code,
            OTPCode.is_active == True,
            OTPCode.expires_at > now
        )
        result = await db.execute(stmt)
        otp = result.scalar_one_or_none()

        if not otp:
            raise Exception("Invalid or expired code")

        otp.is_active = False
        await db.commit()

        token = create_access_token(data={"sub": phone_number})
        return token
