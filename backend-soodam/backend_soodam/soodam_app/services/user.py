# services/user_service.py

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import NoResultFound
from models.user import User
from soodam_app.schemas.user import CreateUserSchema
from utils.hashing import hash_password
from uuid import uuid4


class UserService:
    @staticmethod
    async def create_user(db: AsyncSession, user_data: CreateUserSchema) -> User:
        new_user = User(
            id=uuid4(),
            email=user_data.email,
            phone_number=user_data.phone_number,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            father_name=user_data.father_name,
            gender=user_data.gender,
            user_type=user_data.user_type,
            password=hash_password(user_data.password),
            province_id=user_data.province_id,
            city_id=user_data.city_id,
            address_id=user_data.address_id,
        )
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        return new_user

    @staticmethod
    async def get_user_by_email(db: AsyncSession, email: str) -> User:
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()
        return user

    @staticmethod
    async def get_user_by_phone(db: AsyncSession, phone_number: str) -> User:
        result = await db.execute(select(User).where(User.phone_number == phone_number))
        return result.scalar_one_or_none()

    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: str) -> User:
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()


    @staticmethod
    async def get_user_info(current_user: User) -> User:
        return current_user

    @staticmethod
    async def edit_user_info(data: EditUserSchema, current_user: User, db: AsyncSession):
        for key, value in data.dict(exclude_unset=True).items():
            setattr(current_user, key, value)
        await db.commit()
        await db.refresh(current_user)
        return current_user

    @staticmethod
    async def update_user_info(db: AsyncSession, user: User, update_data: dict) -> User:
        for key, value in update_data.items():
            setattr(user, key, value)
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user
