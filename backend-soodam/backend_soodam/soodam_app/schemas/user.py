from typing import Optional
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field

from schemas.address import CityOutSchema, ProvinceOutSchema, AddressOutSchema
from enums.user_type import UserType  # فرض بر اینکه Enum جداگانه‌ای برای user_type دارید


# اسکیمای خروجی اطلاعات پایه کاربر
class UserBaseSchema(BaseModel):
    id: int
    uuid: UUID
    email: EmailStr
    phone_number: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    father_name: Optional[str]
    gender: Optional[str]
    user_type: Optional[UserType]

    province: Optional[ProvinceOutSchema]
    city: Optional[CityOutSchema]
    address: Optional[AddressOutSchema]

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# اسکیمای کامل برای گرفتن اطلاعات یوزر
class UserOutSchema(UserBaseSchema):
    pass


# اسکیمای ورودی برای ثبت یا آپدیت اطلاعات کاربر
class UserEditSchema(BaseModel):
    phone_number: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    father_name: Optional[str]
    gender: Optional[str]
    user_type: Optional[UserType]

    address_id: Optional[int]
    province_id: Optional[int]
    city_id: Optional[int]

    class Config:
        from_attributes = True

