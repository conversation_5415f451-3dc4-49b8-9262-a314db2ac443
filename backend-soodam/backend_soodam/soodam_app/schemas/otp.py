from pydantic import BaseModel, Field

class SendOTPRequest(BaseModel):
    phone_number: str = Field(..., example="09123456789")

class VerifyOTPRequest(BaseModel):
    phone_number: str = Field(..., example="09123456789")
    code: str = Field(..., example="123456")
# class OTPVerifySchema(BaseModel):
#     phone_number: str
#     code: str  # کد تایید
class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"


