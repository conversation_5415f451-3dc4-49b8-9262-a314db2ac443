from fastapi import Depends, HTTPException, status
from app.models import CustomUserModel
from config.exceptions import InvalidTokenException
from config.jwt import jwt_decode_handler
from jose import JWTError

from fastapi import Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")



async def get_current_user(token: str = Depends(oauth2_scheme)) -> CustomUserModel:
    try:
        payload = jwt_decode_handler(token)
    except JWTError:
        raise InvalidTokenException()

    user = await CustomUserModel.objects.filter(uuid=payload.get("sub", "")).afirst()
    if not user:
        raise InvalidTokenException()
    return user


async def get_current_admin_user(
        current_user: CustomUserModel = Depends(get_current_user),
        ) -> CustomUserModel:
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    return current_user


