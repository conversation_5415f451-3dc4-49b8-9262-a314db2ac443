# Core dependencies
Django>=4.2.0
fastapi>=0.78.0
pydantic>=1.9.1
pydantic[email]>=1.9.1
uvicorn>=0.34.0
whitenoise>=6.2.0
#psycopg2>=2.9.3
psycopg2-binary>=2.9.3
gunicorn>=20.1.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.5
pytz>=2022.1
pytest>=8.0.0
pytest-django>=4.0.0
python-dateutil>=2.8.2
jinja2>=3.1.0
django-notifications-hq>=1.8.3
django-model-utils>=5.0.0
celery>=5.4.0
redis>=5.2.1
django_celery_beat>=2.7.0
django-celery-results>=2.5.1
python-decouple>=3.8
pandas>=2.2.3
openpyxl>=3.1.5
kavenegar>=1.1.2
django-jalali>=7.3.0
pillow>=11.1.0
pillow-heif>=0.14.0  # For iOS HEIC/HEIF image support
django-nested-admin>=4.1.1
django-resized>=1.0.3

# Development dependencies
black>=22.10.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.2.0
pre-commit>=2.19.0

# Additional dependencies for consolidated modules
django-mptt>=0.14.0  # For hierarchical categories
django-taggit>=4.0.0  # For tagging advertisements
django-money>=3.0.0  # For handling price fields
django-storages>=1.13.2  # For media storage
boto3>=1.26.115  # For AWS S3 storage
django-admin-interface>=0.24.2  # For enhanced admin interface
django-import-export>=3.2.0  # For import/export functionality
django-admin-rangefilter>=0.10.0  # For date range filtering
pytest-asyncio>=0.21.0  # For testing async code
httpx>=0.24.0  # For HTTP client
aiohttp>=3.8.4  # For async HTTP client
sentry-sdk>=1.19.1  # For error tracking
prometheus-client>=0.16.0  # For metrics
pyotp>=2.9.0
graphene-django>=3.2.3
django-filter>=24.3
elasticsearch>=8.12.0
elasticsearch-dsl>=8.12.0
scikit-learn>=1.6.1
django-redis>=6.0.0
# GIS dependencies (from Dockerfile)
#gdal>=3.0.0  # For GIS functionality
pydantic[email]