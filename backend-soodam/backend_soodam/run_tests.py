#!/usr/bin/env python3
"""
Comprehensive test runner for the <PERSON><PERSON> backend project.

This script provides various options for running tests with different configurations.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description or command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=False)
        print(f"✅ {description or 'Command'} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed with exit code {e.returncode}")
        return False


def setup_test_environment():
    """Set up the test environment."""
    print("Setting up test environment...")
    
    # Set environment variables for testing
    os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.local'
    os.environ['TESTING'] = 'true'
    
    # Create necessary directories
    test_dirs = [
        'tests/reports',
        'tests/coverage',
        'media/test_uploads'
    ]
    
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ Test environment setup completed")


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests."""
    cmd_parts = ["python", "-m", "pytest"]
    
    # Add test paths
    cmd_parts.extend([
        "tests/api/",
        "tests/models/",
        "tests/crud/"
    ])
    
    # Add markers
    cmd_parts.extend(["-m", "unit or api or models"])
    
    if verbose:
        cmd_parts.append("-v")
    
    if coverage:
        cmd_parts.extend([
            "--cov=app",
            "--cov-report=html:tests/coverage/unit",
            "--cov-report=term-missing"
        ])
    
    command = " ".join(cmd_parts)
    return run_command(command, "Unit Tests")


def run_integration_tests(verbose=False, coverage=False):
    """Run integration tests."""
    cmd_parts = ["python", "-m", "pytest"]
    
    # Add test paths
    cmd_parts.extend([
        "tests/integration/"
    ])
    
    # Add markers
    cmd_parts.extend(["-m", "integration"])
    
    if verbose:
        cmd_parts.append("-v")
    
    if coverage:
        cmd_parts.extend([
            "--cov=app",
            "--cov-report=html:tests/coverage/integration",
            "--cov-report=term-missing"
        ])
    
    command = " ".join(cmd_parts)
    return run_command(command, "Integration Tests")


def run_performance_tests(verbose=False):
    """Run performance tests."""
    cmd_parts = ["python", "-m", "pytest"]
    
    # Add test paths
    cmd_parts.extend([
        "tests/performance/"
    ])
    
    if verbose:
        cmd_parts.append("-v")
    
    command = " ".join(cmd_parts)
    return run_command(command, "Performance Tests")


def run_specific_module_tests(module, verbose=False, coverage=False):
    """Run tests for a specific module."""
    module_test_map = {
        'auth': 'tests/api/test_auth_comprehensive.py',
        'advertisement': 'tests/api/test_advertisement_comprehensive.py',
        'payment': 'tests/api/test_payment_comprehensive.py',
        'user': 'tests/api/test_user_comprehensive.py',
        'admin': 'tests/api/test_admin_comprehensive.py',
        'models': 'tests/models/test_models_comprehensive.py'
    }
    
    if module not in module_test_map:
        print(f"❌ Unknown module: {module}")
        print(f"Available modules: {', '.join(module_test_map.keys())}")
        return False
    
    cmd_parts = ["python", "-m", "pytest", module_test_map[module]]
    
    if verbose:
        cmd_parts.append("-v")
    
    if coverage:
        cmd_parts.extend([
            "--cov=app",
            f"--cov-report=html:tests/coverage/{module}",
            "--cov-report=term-missing"
        ])
    
    command = " ".join(cmd_parts)
    return run_command(command, f"{module.title()} Module Tests")


def run_all_tests(verbose=False, coverage=False, skip_slow=False):
    """Run all tests."""
    cmd_parts = ["python", "-m", "pytest"]
    
    if skip_slow:
        cmd_parts.extend(["-m", "not slow"])
    
    if verbose:
        cmd_parts.append("-v")
    
    if coverage:
        cmd_parts.extend([
            "--cov=app",
            "--cov-report=html:tests/coverage/all",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    command = " ".join(cmd_parts)
    return run_command(command, "All Tests")


def run_linting():
    """Run code linting."""
    commands = [
        ("flake8 app/ --max-line-length=120 --exclude=migrations", "Flake8 Linting"),
        ("black --check app/", "Black Code Formatting Check"),
        ("isort --check-only app/", "Import Sorting Check"),
        ("mypy app/ --ignore-missing-imports", "Type Checking")
    ]
    
    all_passed = True
    for command, description in commands:
        if not run_command(command, description):
            all_passed = False
    
    return all_passed


def generate_test_report():
    """Generate a comprehensive test report."""
    print("\n" + "="*60)
    print("GENERATING COMPREHENSIVE TEST REPORT")
    print("="*60)
    
    # Run tests with coverage
    cmd = [
        "python", "-m", "pytest",
        "--cov=app",
        "--cov-report=html:tests/coverage/report",
        "--cov-report=xml:tests/coverage/coverage.xml",
        "--cov-report=term-missing",
        "--junitxml=tests/reports/junit.xml",
        "--html=tests/reports/report.html",
        "--self-contained-html"
    ]
    
    command = " ".join(cmd)
    success = run_command(command, "Comprehensive Test Report Generation")
    
    if success:
        print("\n📊 Test reports generated:")
        print("  - HTML Coverage Report: tests/coverage/report/index.html")
        print("  - XML Coverage Report: tests/coverage/coverage.xml")
        print("  - JUnit XML Report: tests/reports/junit.xml")
        print("  - HTML Test Report: tests/reports/report.html")
    
    return success


def main():
    """Main function to handle command line arguments and run tests."""
    parser = argparse.ArgumentParser(description="Soodam Backend Test Runner")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "all", "module", "lint", "report"],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--module",
        choices=["auth", "advertisement", "payment", "user", "admin", "models"],
        help="Specific module to test (only with 'module' test_type)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage reports"
    )
    
    parser.add_argument(
        "--skip-slow",
        action="store_true",
        help="Skip slow tests"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="Only setup test environment"
    )
    
    args = parser.parse_args()
    
    # Setup test environment
    setup_test_environment()
    
    if args.setup_only:
        print("✅ Test environment setup completed. Exiting.")
        return
    
    success = True
    
    if args.test_type == "unit":
        success = run_unit_tests(args.verbose, args.coverage)
    elif args.test_type == "integration":
        success = run_integration_tests(args.verbose, args.coverage)
    elif args.test_type == "performance":
        success = run_performance_tests(args.verbose)
    elif args.test_type == "all":
        success = run_all_tests(args.verbose, args.coverage, args.skip_slow)
    elif args.test_type == "module":
        if not args.module:
            print("❌ --module is required when test_type is 'module'")
            sys.exit(1)
        success = run_specific_module_tests(args.module, args.verbose, args.coverage)
    elif args.test_type == "lint":
        success = run_linting()
    elif args.test_type == "report":
        success = generate_test_report()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
