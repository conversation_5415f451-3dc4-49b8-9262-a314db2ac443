# Soodam Backend

This is the backend API for the Soodam application.

## Features

- User Management
- Advertisement Management
- Blog Management
- Admin Dashboard
- Geolocation Services
- API Versioning
- Caching
- Rate Limiting
- Monitoring and Logging

## Tech Stack

- Python 3.10
- Django 4.x
- FastAPI
- PostgreSQL
- Redis
- Celery
- Docker
- GitHub Actions

## Development Setup

### Prerequisites

- Docker and Docker Compose
- Python 3.10+
- Poetry (optional)

### Local Development with Docker

1. Clone the repository:
   ```bash
   <NAME_EMAIL>:soodamApp/backend-soodam.git
   cd backend-soodam
   ```

2. Create a `.env` file from the example:
   ```bash
   cp fastapi.env.dev.example fastapi.env.dev
   ```

3. Start the development environment:
   ```bash
   docker-compose up -d
   ```

4. Access the API at http://localhost:8000/api/docs

### Local Development without Docker

1. Clone the repository:
   ```bash
   <NAME_EMAIL>:soodamApp/backend-soodam.git
   cd backend-soodam
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file from the example:
   ```bash
   cp fastapi.env.dev.example fastapi.env.dev
   ```

5. Run migrations:
   ```bash
   python manage.py migrate
   ```

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

8. Access the API at http://localhost:8000/api/docs

## API Documentation

The API documentation is available at:

- Swagger UI: http://localhost:8000/api/docs
- ReDoc: http://localhost:8000/api/redoc
- OpenAPI JSON: http://localhost:8000/api/openapi.json

## API Versioning

API versioning is supported through the `Accept` header:

```
Accept: application/json; version=1.0
```

Available versions:
- 1.0 (default)
- 2.0

## Authentication

Most endpoints require authentication using JWT tokens. To authenticate:

1. Use the `/api/auth/login` endpoint to obtain a token
2. Include the token in the `Authorization` header as `Bearer {token}`

## Testing

Run tests with pytest:

```bash
pytest
```

Run tests with coverage:

```bash
pytest --cov=app tests/
```

## Deployment

The application is automatically deployed using GitHub Actions:

- Push to `develop` branch: Deploys to staging environment
- Push to `master` branch: Deploys to production environment

## Project Structure

```
backend_soodam/
├── app/                    # Main application code
│   ├── api/                # API implementation
│   ├── core/               # Core functionality
│   ├── enums/              # Enumerations
│   ├── middleware/         # Middleware components
│   ├── models/             # Database models
│   ├── routers/            # API routers
│   ├── schemas/            # Pydantic schemas
│   └── utils/              # Utility functions
├── config/                 # Project configuration
├── tests/                  # Test suite
├── .github/                # GitHub Actions workflows
├── docker-compose.yml      # Docker Compose configuration
├── Dockerfile              # Docker configuration
└── requirements.txt        # Python dependencies
```

## Contributing

1. Create a new branch from `develop`
2. Make your changes
3. Write tests for your changes
4. Run the test suite
5. Submit a pull request to `develop`

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
