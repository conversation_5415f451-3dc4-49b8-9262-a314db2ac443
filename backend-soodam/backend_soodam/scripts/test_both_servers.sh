#!/bin/bash
set -e

# This script tests if static files are being served correctly on both FastAPI and Django servers

echo "Testing static files on both servers..."

# 1. Test FastAPI static files
echo "Testing FastAPI static files (port 8000)..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/static/css/main.4d6ce6e6.css)
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ FastAPI static file access successful!"
else
    echo "❌ FastAPI static file access failed with HTTP code: $HTTP_CODE"
fi

# 2. Test Django static files
echo "Testing Django static files (port 8001)..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/static/css/main.4d6ce6e6.css)
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ Django static file access successful!"
else
    echo "❌ Django static file access failed with HTTP code: $HTTP_CODE"
fi

# 3. Test FastAPI media files
echo "Testing FastAPI media files (port 8000)..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/media/logo.png)
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ FastAPI media file access successful!"
else
    echo "❌ FastAPI media file access failed with HTTP code: $HTTP_CODE"
fi

# 4. Test Django media files
echo "Testing Django media files (port 8001)..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/media/logo.png)
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ Django media file access successful!"
else
    echo "❌ Django media file access failed with HTTP code: $HTTP_CODE"
fi

echo "Static files test complete!"