#!/bin/bash
set -e

# Configuration for PROD environment
BACKUP_DIR="/opt/soodam/prod/backup"

# Function to display usage
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  --date DATE    Restore PROD database from backup on DATE (YYYY-MM-DD)"
  echo "  --list         List available backups"
  echo "  --help         Display this help message"
  exit 1
}

# Function to list available backups
list_backups() {
  echo "Available PROD backups:"
  find "${BACKUP_DIR}" -name "soodam_prod_*.backup" | sort | xargs -n1 basename
}

# Function to restore PROD database
restore_prod() {
  DATE=$1
  BACKUP_FILE="${BACKUP_DIR}/soodam_prod_${DATE}.backup"
  
  if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file not found: $BACKUP_FILE"
    exit 1
  fi
  
  echo "Restoring PROD database from backup: $BACKUP_FILE"
  docker cp "${BA<PERSON><PERSON>UP_FILE}" prod_backend_soodam_postgres:/tmp/restore.backup
  
  # Drop and recreate the database
  docker exec prod_backend_soodam_postgres psql -U soodam_prod -c "DROP DATABASE IF EXISTS soodam_prod;"
  docker exec prod_backend_soodam_postgres psql -U soodam_prod -c "CREATE DATABASE soodam_prod;"
  
  # Restore the backup
  docker exec prod_backend_soodam_postgres pg_restore -U soodam_prod -d soodam_prod /tmp/restore.backup
  
  echo "PROD database restored successfully!"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
  usage
fi

while [ $# -gt 0 ]; do
  case "$1" in
    --date)
      restore_prod "$2"
      shift 2
      ;;
    --list)
      list_backups
      shift
      ;;
    --help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done