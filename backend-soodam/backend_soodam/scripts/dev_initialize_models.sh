#!/bin/bash
set -e

# Function to display usage
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  --source SOURCE    Source of initialization data (fixtures or detail)"
  echo "  --fixtures-dir DIR Path to fixtures directory (default: latest in backup)"
  echo "  --detail-dir DIR   Path to detail directory (default: app/detail)"
  echo "  --skip-existing    Skip models that already have data"
  echo "  --help             Display this help message"
  exit 1
}

# Default values
SOURCE="fixtures"
FIXTURES_DIR=""
DETAIL_DIR="app/detail"
SKIP_EXISTING=""

# Parse command line arguments
while [ $# -gt 0 ]; do
  case "$1" in
    --source)
      SOURCE="$2"
      shift 2
      ;;
    --fixtures-dir)
      FIXTURES_DIR="$2"
      shift 2
      ;;
    --detail-dir)
      DETAIL_DIR="$2"
      shift 2
      ;;
    --skip-existing)
      SKIP_EXISTING="--skip-existing"
      shift
      ;;
    --help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# If fixtures directory not specified, use the latest in backup
if [ -z "$FIXTURES_DIR" ] && [ "$SOURCE" = "fixtures" ]; then
  FIXTURES_DIR=$(find /opt/soodam/dev/backup -name "fixtures_*" -type d | sort -r | head -n 1)
  if [ -z "$FIXTURES_DIR" ]; then
    echo "Error: No fixtures directory found in backup"
    exit 1
  fi
  echo "Using latest fixtures directory: $FIXTURES_DIR"
fi

# Copy fixtures to container if needed
if [ "$SOURCE" = "fixtures" ]; then
  echo "Copying fixtures to container..."
  docker exec dev_backend_soodam mkdir -p /tmp/fixtures
  docker cp "${FIXTURES_DIR}/." dev_backend_soodam:/tmp/fixtures/
  CONTAINER_FIXTURES_DIR="/tmp/fixtures"
else
  CONTAINER_FIXTURES_DIR=""
fi

# Run the initialize command in the container
echo "Initializing models in DEV environment..."
COMMAND="python manage.py initialize_models --source=${SOURCE}"

if [ -n "$CONTAINER_FIXTURES_DIR" ]; then
  COMMAND="${COMMAND} --fixtures-dir=${CONTAINER_FIXTURES_DIR}"
fi

if [ -n "$DETAIL_DIR" ]; then
  COMMAND="${COMMAND} --detail-dir=${DETAIL_DIR}"
fi

if [ -n "$SKIP_EXISTING" ]; then
  COMMAND="${COMMAND} ${SKIP_EXISTING}"
fi

echo "Running command: $COMMAND"
docker exec dev_backend_soodam $COMMAND

echo "Models initialized successfully in DEV environment"