#!/bin/bash
set -e

# Configuration
EXPORT_DIR="/home/<USER>/public_html/soodam/dev/backup/fixtures_$(date +%Y-%m-%d)"
mkdir -p "${EXPORT_DIR}"

echo "Exporting models from DEV environment to ${EXPORT_DIR}..."

# Run the export command in the container
docker exec dev_backend_soodam python manage.py export_models_json --output-dir=/tmp/fixtures

# Copy the exported files from the container
docker cp dev_backend_soodam:/tmp/fixtures/. "${EXPORT_DIR}/"

echo "Models exported successfully to ${EXPORT_DIR}"