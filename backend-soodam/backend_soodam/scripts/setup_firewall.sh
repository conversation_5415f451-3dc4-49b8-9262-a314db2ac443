#!/bin/bash

# Setup firewall rules to protect against common attacks

# Enable UFW
ufw enable

# Allow SSH, HTTP, HTTPS
ufw allow ssh
ufw allow http
ufw allow https

# Allow specific application ports
ufw allow 4000/tcp  # FastAPI
ufw allow 4001/tcp  # Additional port if needed

# Block common attack vectors
# Block null packets
ufw deny proto tcp from any to any with tcp-flags ALL NONE

# Block XMAS packets
ufw deny proto tcp from any to any with tcp-flags ALL FIN,PSH,URG

# Block SYN-FIN packets
ufw deny proto tcp from any to any with tcp-flags SYN,FIN SYN,FIN

# Block SYN-RST packets
ufw deny proto tcp from any to any with tcp-flags SYN,RST SYN,RST

# Install fail2ban for brute force protection
apt-get update
apt-get install -y fail2ban

# Configure fail2ban
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 86400  # 24 hours
findtime = 600   # 10 minutes
maxretry = 3     # 3 retries

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-badbots]
enabled = true
filter = nginx-badbots
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2

[nginx-req-limit]
enabled = true
filter = nginx-req-limit
port = http,https
logpath = /var/log/nginx/error.log
EOF

# Create custom filter for FastAPI
cat > /etc/fail2ban/filter.d/fastapi.conf << EOF
[Definition]
failregex = ^.*\[error\].*client: <HOST>,.*"(GET|POST|HEAD).*HTTP.*" 400
            ^.*\[error\].*client: <HOST>,.*"(GET|POST|HEAD).*HTTP.*" 401
            ^.*\[error\].*client: <HOST>,.*"(GET|POST|HEAD).*HTTP.*" 403
            ^.*\[error\].*client: <HOST>,.*"(GET|POST|HEAD).*HTTP.*" 404
ignoreregex =
EOF

# Add FastAPI jail
cat >> /etc/fail2ban/jail.local << EOF
[fastapi]
enabled = true
filter = fastapi
port = 4000,4001
logpath = /opt/soodam/logs/fastapi.log
maxretry = 3
bantime = 86400
EOF

# Restart fail2ban
systemctl restart fail2ban

echo "Firewall and fail2ban setup complete"