#!/bin/bash
set -e

# Configuration for DEV environment
BACKUP_DIR="/opt/soodam/dev/backup"

# Function to display usage
usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  --date DATE    Restore DEV database from backup on DATE (YYYY-MM-DD)"
  echo "  --list         List available backups"
  echo "  --help         Display this help message"
  exit 1
}

# Function to list available backups
list_backups() {
  echo "Available DEV backups:"
  find "${BACKUP_DIR}" -name "soodam_dev_*.backup" | sort | xargs -n1 basename
}

# Function to restore DEV database
restore_dev() {
  DATE=$1
  BACKUP_FILE="${BACKUP_DIR}/soodam_dev_${DATE}.backup"
  
  if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file not found: $BACKUP_FILE"
    exit 1
  fi
  
  echo "Restoring DEV database from backup: $BACKUP_FILE"
  docker cp "${BAC<PERSON>UP_FILE}" dev_backend_soodam_postgres:/tmp/restore.backup
  
  # Drop and recreate the database
  docker exec dev_backend_soodam_postgres psql -U soodam_dev -c "DROP DATABASE IF EXISTS soodam_dev;"
  docker exec dev_backend_soodam_postgres psql -U soodam_dev -c "CREATE DATABASE soodam_dev;"
  
  # Restore the backup
  docker exec dev_backend_soodam_postgres pg_restore -U soodam_dev -d soodam_dev /tmp/restore.backup
  
  echo "DEV database restored successfully!"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
  usage
fi

while [ $# -gt 0 ]; do
  case "$1" in
    --date)
      restore_dev "$2"
      shift 2
      ;;
    --list)
      list_backups
      shift
      ;;
    --help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done