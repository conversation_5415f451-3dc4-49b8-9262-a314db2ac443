#!/usr/bin/env python
"""
Benchmarking script for the Soodam backend API.

This script benchmarks the performance of the API endpoints.
"""

import asyncio
import argparse
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional

import httpx
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.benchmark import benchmark_endpoint

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("benchmark.log")
    ]
)

logger = logging.getLogger("benchmark_script")

# Define the endpoints to benchmark
ENDPOINTS = [
    {"url": "/api/health", "method": "GET", "name": "Health Check"},
    {"url": "/api/auth/login", "method": "POST", "name": "Login", "data": {"username": "<EMAIL>", "password": "adminpassword"}},
    {"url": "/api/user/profile", "method": "GET", "name": "Get User Profile", "auth_required": True},
    {"url": "/api/adv", "method": "GET", "name": "Get Advertisements"},
    {"url": "/api/blogs", "method": "GET", "name": "Get Blogs"},
    {"url": "/api/admin/dashboard/stats", "method": "GET", "name": "Admin Dashboard Stats", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/advertisements", "method": "GET", "name": "Admin Get Advertisements", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/users", "method": "GET", "name": "Admin Get Users", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/blogs", "method": "GET", "name": "Admin Get Blogs", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/special-admins", "method": "GET", "name": "Admin Get Special Admins", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/transactions", "method": "GET", "name": "Admin Get Transactions", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/activities", "method": "GET", "name": "Admin Get Activities", "auth_required": True, "admin_required": True},
    {"url": "/api/admin/types", "method": "GET", "name": "Admin Get Types", "auth_required": True, "admin_required": True},
]

async def run_benchmarks(
    base_url: str,
    endpoints: List[Dict],
    iterations: int = 10,
    admin_token: Optional[str] = None,
    user_token: Optional[str] = None
) -> List[Dict]:
    """
    Run benchmarks for the specified endpoints.
    
    Args:
        base_url: The base URL of the API
        endpoints: The endpoints to benchmark
        iterations: The number of iterations to run
        admin_token: The admin token to use for authenticated endpoints
        user_token: The user token to use for authenticated endpoints
        
    Returns:
        List[Dict]: The benchmark results
    """
    results = []
    
    async with httpx.AsyncClient(base_url=base_url, timeout=30.0) as client:
        # Get tokens if not provided
        if not admin_token:
            try:
                response = await client.post(
                    "/api/auth/login",
                    json={"username": "<EMAIL>", "password": "adminpassword"}
                )
                admin_token = response.json()["access_token"]
                logger.info("Admin token obtained successfully")
            except Exception as e:
                logger.error(f"Failed to obtain admin token: {str(e)}")
        
        if not user_token:
            try:
                response = await client.post(
                    "/api/auth/login",
                    json={"username": "<EMAIL>", "password": "userpassword"}
                )
                user_token = response.json()["access_token"]
                logger.info("User token obtained successfully")
            except Exception as e:
                logger.error(f"Failed to obtain user token: {str(e)}")
        
        # Run benchmarks for each endpoint
        for endpoint in endpoints:
            url = endpoint["url"]
            method = endpoint["method"]
            name = endpoint.get("name", f"{method} {url}")
            data = endpoint.get("data")
            auth_required = endpoint.get("auth_required", False)
            admin_required = endpoint.get("admin_required", False)
            
            headers = {}
            if auth_required:
                if admin_required and admin_token:
                    headers["Authorization"] = f"Bearer {admin_token}"
                elif user_token:
                    headers["Authorization"] = f"Bearer {user_token}"
                else:
                    logger.warning(f"Skipping {name} because no token is available")
                    continue
            
            logger.info(f"Benchmarking {name}...")
            result = await benchmark_endpoint(
                client=client,
                url=url,
                method=method,
                headers=headers,
                data=data,
                iterations=iterations,
                name=name
            )
            
            results.append(result.to_dict())
            logger.info(f"Benchmark completed for {name}")
    
    return results

def save_results(results: List[Dict], output_dir: str) -> None:
    """
    Save benchmark results to files.
    
    Args:
        results: The benchmark results
        output_dir: The directory to save the results to
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Save results as JSON
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_path = os.path.join(output_dir, f"benchmark_results_{timestamp}.json")
    with open(json_path, "w") as f:
        json.dump(results, f, indent=2)
    
    # Save results as CSV
    csv_path = os.path.join(output_dir, f"benchmark_results_{timestamp}.csv")
    df = pd.DataFrame(results)
    df.to_csv(csv_path, index=False)
    
    # Create plots
    create_plots(results, output_dir, timestamp)
    
    logger.info(f"Results saved to {output_dir}")

def create_plots(results: List[Dict], output_dir: str, timestamp: str) -> None:
    """
    Create plots from benchmark results.
    
    Args:
        results: The benchmark results
        output_dir: The directory to save the plots to
        timestamp: The timestamp to use in the filenames
    """
    df = pd.DataFrame(results)
    
    # Bar chart of average times
    plt.figure(figsize=(12, 8))
    plt.bar(df["name"], df["average_time"] * 1000)  # Convert to milliseconds
    plt.xlabel("Endpoint")
    plt.ylabel("Average Time (ms)")
    plt.title("Average Response Time by Endpoint")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"benchmark_avg_times_{timestamp}.png"))
    
    # Box plot of execution times
    execution_times = []
    names = []
    for result in results:
        execution_times.append([t * 1000 for t in result.get("execution_times", [])])  # Convert to milliseconds
        names.append(result["name"])
    
    plt.figure(figsize=(12, 8))
    plt.boxplot(execution_times, labels=names)
    plt.xlabel("Endpoint")
    plt.ylabel("Response Time (ms)")
    plt.title("Response Time Distribution by Endpoint")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"benchmark_boxplot_{timestamp}.png"))

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Benchmark the Soodam backend API")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL of the API")
    parser.add_argument("--iterations", type=int, default=10, help="Number of iterations to run")
    parser.add_argument("--output-dir", default="benchmark_results", help="Directory to save results to")
    parser.add_argument("--admin-token", help="Admin token to use for authenticated endpoints")
    parser.add_argument("--user-token", help="User token to use for authenticated endpoints")
    return parser.parse_args()

async def main():
    """Main function."""
    args = parse_args()
    
    logger.info(f"Starting benchmarks with {args.iterations} iterations")
    results = await run_benchmarks(
        base_url=args.base_url,
        endpoints=ENDPOINTS,
        iterations=args.iterations,
        admin_token=args.admin_token,
        user_token=args.user_token
    )
    
    save_results(results, args.output_dir)
    logger.info("Benchmarks completed")

if __name__ == "__main__":
    asyncio.run(main())
