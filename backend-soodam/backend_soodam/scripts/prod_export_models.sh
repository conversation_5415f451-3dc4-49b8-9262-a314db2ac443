#!/bin/bash
set -e

# Configuration
EXPORT_DIR="/opt/soodam/prod/backup/fixtures_$(date +%Y-%m-%d)"
mkdir -p "${EXPORT_DIR}"

echo "Exporting models from PROD environment to ${EXPORT_DIR}..."

# Run the export command in the container
docker exec prod_backend_soodam python manage.py export_models_json --output-dir=/tmp/fixtures

# Copy the exported files from the container
docker cp prod_backend_soodam:/tmp/fixtures/. "${EXPORT_DIR}/"

echo "Models exported successfully to ${EXPORT_DIR}"