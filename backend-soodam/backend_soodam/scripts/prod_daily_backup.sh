#!/bin/bash
set -e

# Configuration for PROD environment
BACKUP_DIR="/opt/soodam/prod/backup"
DATE=$(date +%Y-%m-%d)
RETENTION_DAYS=7

# Create backup directory
mkdir -p "${BACKUP_DIR}"

echo "Starting PROD daily backup process..."

# 1. Backup PROD database
echo "Backing up PROD database..."
PROD_BACKUP_FILE="${BACKUP_DIR}/soodam_prod_${DATE}.backup"
docker exec prod_backend_soodam_postgres pg_dump -U soodam_prod -d soodam_prod -F c -f /tmp/prod_dump.backup
docker cp prod_backend_soodam_postgres:/tmp/prod_dump.backup "${PROD_BACKUP_FILE}"
echo "PROD database backup completed: ${PROD_BACKUP_FILE}"

# 2. Export fixtures from PROD (with relationships)
echo "Exporting model fixtures from PROD..."
FIXTURES_DIR="${BACKUP_DIR}/fixtures_${DATE}"
mkdir -p "${FIXTURES_DIR}"

# Export each model with relationships
MODELS=(
  "app.MainCategoryModel"
  "app.SubCategoryModel"
  "app.SubCategoryLevelTwoModel"
  "app.ChoiceAttributeModel"
  "app.ChoiceOptionModel"
  "app.BooleanAttributeModel"
  "app.TextAttributeModel"
  "app.PropertyModel"
  "app.HighlightAttributeModel"
  "app.ProvinceModel"
  "app.CityModel"
)

for MODEL in "${MODELS[@]}"; do
  docker exec prod_backend_soodam python manage.py dumpdata ${MODEL} --indent=2 --natural-foreign --natural-primary > "${FIXTURES_DIR}/$(echo ${MODEL} | tr '.' '_').json"
done

echo "Model fixtures exported to: ${FIXTURES_DIR}"

# 3. Clean up old backups
echo "Cleaning up backups older than ${RETENTION_DAYS} days..."
find "${BACKUP_DIR}" -name "soodam_prod_*.backup" -type f -mtime +${RETENTION_DAYS} -delete
find "${BACKUP_DIR}" -name "fixtures_*" -type d -mtime +${RETENTION_DAYS} -exec rm -rf {} \;

echo "PROD daily backup process completed successfully!"