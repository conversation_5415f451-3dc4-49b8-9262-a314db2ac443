#!/bin/bash
set -e

# Print current directory
echo "Current directory: $(pwd)"

# Check if static directory exists
echo "Checking static directory..."
if [ -d "static" ]; then
  echo "Static directory exists"
  echo "Contents of static directory:"
  ls -la static/
else
  echo "Static directory does not exist!"
  echo "Creating static directory..."
  mkdir -p static
fi

# Check if media directory exists
echo "Checking media directory..."
if [ -d "media" ]; then
  echo "Media directory exists"
  echo "Contents of media directory:"
  ls -la media/
else
  echo "Media directory does not exist!"
  echo "Creating media directory..."
  mkdir -p media
fi

# Check permissions
echo "Setting proper permissions..."
chmod -R 755 static/
chmod -R 755 media/

# Run collectstatic
echo "Running collectstatic..."
python manage.py collectstatic --noinput --clear

# Check if files were collected
echo "Contents of static directory after collectstatic:"
ls -la static/

echo "Debug complete"