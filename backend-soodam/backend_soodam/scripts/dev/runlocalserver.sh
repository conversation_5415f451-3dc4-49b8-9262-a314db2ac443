#!/bin/bash
set -e

# Set environment variables
export ENV_STATE=local
export PYTHONPATH=/src

# Start Celery workers (uncomment if needed)
# celery -A config.celery beat --loglevel=info &
# celery -A config.celery worker --loglevel=info &

# Apply database migrations
echo "Applying database migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Start the FastAPI and Django applications
echo "Starting FastAPI application on port 4000..."
uvicorn config.asgi:fastapi_app --reload --host 0.0.0.0 --port 4000 &

echo "Starting Django application on port 4001..."
uvicorn config.asgi:django_app --reload --host 0.0.0.0 --port 4001

## Use system Python instead of Poetry
#uvicorn config.asgi:fastapi_app --reload --host 0.0.0.0 --port 4000 &
#uvicorn config.asgi:django_app --reload --host 0.0.0.0 --port 4001


