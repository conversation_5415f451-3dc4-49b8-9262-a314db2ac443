#!/bin/bash
set -e

# Configuration for DEV environment
BACKUP_DIR="/opt/soodam/dev/backup"
DATE=$(date +%Y-%m-%d)
RETENTION_DAYS=7

# Create backup directory
mkdir -p "${BACKUP_DIR}"

echo "Starting DEV daily backup process..."

# 1. Backup DEV database
echo "Backing up DEV database..."
DEV_BACKUP_FILE="${BACKUP_DIR}/soodam_dev_${DATE}.backup"
docker exec dev_backend_soodam_postgres pg_dump -U soodam_dev -d soodam_dev -F c -f /tmp/dev_dump.backup
docker cp dev_backend_soodam_postgres:/tmp/dev_dump.backup "${DEV_BACKUP_FILE}"
echo "DEV database backup completed: ${DEV_BACKUP_FILE}"

# 2. Export fixtures from DEV (with relationships)
echo "Exporting model fixtures from DEV..."
FIXTURES_DIR="${BACKUP_DIR}/fixtures_${DATE}"
mkdir -p "${FIXTURES_DIR}"

# Export each model with relationships
MODELS=(
  "app.MainCategoryModel"
  "app.SubCategoryModel"
  "app.SubCategoryLevelTwoModel"
  "app.ChoiceAttributeModel"
  "app.ChoiceOptionModel"
  "app.BooleanAttributeModel"
  "app.TextAttributeModel"
  "app.PropertyModel"
  "app.HighlightAttributeModel"
  "app.ProvinceModel"
  "app.CityModel"
)

for MODEL in "${MODELS[@]}"; do
  docker exec dev_backend_soodam python manage.py dumpdata ${MODEL} --indent=2 --natural-foreign --natural-primary > "${FIXTURES_DIR}/$(echo ${MODEL} | tr '.' '_').json"
done

echo "Model fixtures exported to: ${FIXTURES_DIR}"

# 3. Clean up old backups
echo "Cleaning up backups older than ${RETENTION_DAYS} days..."
find "${BACKUP_DIR}" -name "soodam_dev_*.backup" -type f -mtime +${RETENTION_DAYS} -delete
find "${BACKUP_DIR}" -name "fixtures_*" -type d -mtime +${RETENTION_DAYS} -exec rm -rf {} \;

echo "DEV daily backup process completed successfully!"