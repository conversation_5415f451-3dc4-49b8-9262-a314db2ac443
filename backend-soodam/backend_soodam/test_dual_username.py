#!/usr/bin/env python3
"""
Test script for dual username authentication functionality

This script tests the enhanced user model and authentication system
that supports both email and phone number as username fields.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_soodam.settings')
django.setup()

from app.models.user import CustomUserModel, normalize_iranian_phone_number
from app.dependencies.authentication import Authenti<PERSON><PERSON><PERSON><PERSON>


def test_phone_normalization():
    """Test phone number normalization."""
    print("=== Testing Phone Number Normalization ===")
    
    test_cases = [
        ("09123456789", "09123456789"),
        ("9123456789", "09123456789"),
        ("989123456789", "09123456789"),
        ("00989123456789", "09123456789"),
        ("+989123456789", "09123456789"),
        ("091-234-56789", "09123456789"),
    ]
    
    for input_phone, expected in test_cases:
        try:
            result = normalize_iranian_phone_number(input_phone)
            status = "✅ PASS" if result == expected else "❌ FAIL"
            print(f"{status} | Input: {input_phone:15} | Expected: {expected} | Got: {result}")
        except Exception as e:
            print(f"❌ ERROR | Input: {input_phone:15} | Error: {e}")


def test_user_manager_methods():
    """Test enhanced user manager methods."""
    print("\n=== Testing User Manager Methods ===")
    
    try:
        # Test phone number lookup
        print("Testing get_by_phone method...")
        user = CustomUserModel.objects.get_by_phone("09123456789")
        print(f"✅ get_by_phone works (found: {user is not None})")
        
        # Test email lookup
        print("Testing get_by_email method...")
        user = CustomUserModel.objects.get_by_email("<EMAIL>")
        print(f"✅ get_by_email works (found: {user is not None})")
        
        # Test dual username lookup
        print("Testing get_by_username_field method...")
        user1 = CustomUserModel.objects.get_by_username_field("<EMAIL>")
        user2 = CustomUserModel.objects.get_by_username_field("09123456789")
        print(f"✅ get_by_username_field works (email: {user1 is not None}, phone: {user2 is not None})")
        
        # Test comprehensive lookup
        print("Testing get_by_any_username method...")
        user = CustomUserModel.objects.get_by_any_username("<EMAIL>")
        print(f"✅ get_by_any_username works (found: {user is not None})")
        
    except Exception as e:
        print(f"❌ ERROR in manager methods: {e}")


def test_authentication_helper():
    """Test authentication helper functions."""
    print("\n=== Testing Authentication Helper ===")
    
    try:
        # Test username type identification
        test_usernames = [
            ("<EMAIL>", "email"),
            ("09123456789", "phone"),
            ("989123456789", "phone"),
            ("invalid_username", "unknown")
        ]
        
        for username, expected_type in test_usernames:
            username_type = AuthenticationHelper.get_username_type(username)
            status = "✅ PASS" if username_type == expected_type else "❌ FAIL"
            print(f"{status} | Username: {username:15} | Expected: {expected_type:8} | Got: {username_type}")
        
        # Test username normalization
        print("\nTesting username normalization...")
        test_normalizations = [
            ("<EMAIL>", "<EMAIL>"),
            ("989123456789", "09123456789"),
            ("  <EMAIL>  ", "<EMAIL>")
        ]
        
        for input_username, expected in test_normalizations:
            normalized = AuthenticationHelper.normalize_username(input_username)
            status = "✅ PASS" if normalized == expected else "❌ FAIL"
            print(f"{status} | Input: {input_username:20} | Expected: {expected:15} | Got: {normalized}")
        
    except Exception as e:
        print(f"❌ ERROR in authentication helper: {e}")


def test_user_instance_methods():
    """Test user instance methods for dual username support."""
    print("\n=== Testing User Instance Methods ===")
    
    try:
        # Create a test user (in memory, not saved)
        user = CustomUserModel(
            phone_number="09123456789",
            email="<EMAIL>",
            username="testuser",
            first_name="علی",
            last_name="احمدی"
        )
        
        # Test get_username_display
        display_name = user.get_username_display()
        print(f"✅ get_username_display: {display_name}")
        
        # Test get_all_usernames
        all_usernames = user.get_all_usernames()
        print(f"✅ get_all_usernames: {all_usernames}")
        
        # Test can_login_with
        test_logins = [
            ("<EMAIL>", True),
            ("09123456789", True),
            ("testuser", True),
            ("989123456789", True),  # Should normalize to 09123456789
            ("<EMAIL>", False)
        ]
        
        for login_username, expected in test_logins:
            can_login = user.can_login_with(login_username)
            status = "✅ PASS" if can_login == expected else "❌ FAIL"
            print(f"{status} | Login with: {login_username:20} | Expected: {expected} | Got: {can_login}")
        
    except Exception as e:
        print(f"❌ ERROR in instance methods: {e}")


def test_model_configuration():
    """Test model configuration for dual username support."""
    print("\n=== Testing Model Configuration ===")
    
    try:
        # Test USERNAME_FIELD
        username_field = CustomUserModel.USERNAME_FIELD
        print(f"✅ USERNAME_FIELD: {username_field}")
        
        # Test DUAL_USERNAME_FIELDS
        dual_fields = getattr(CustomUserModel, 'DUAL_USERNAME_FIELDS', None)
        print(f"✅ DUAL_USERNAME_FIELDS: {dual_fields}")
        
        # Test EMAIL_FIELD
        email_field = CustomUserModel.EMAIL_FIELD
        print(f"✅ EMAIL_FIELD: {email_field}")
        
        # Test REQUIRED_FIELDS
        required_fields = CustomUserModel.REQUIRED_FIELDS
        print(f"✅ REQUIRED_FIELDS: {required_fields}")
        
    except Exception as e:
        print(f"❌ ERROR in model configuration: {e}")


def test_validation_scenarios():
    """Test various validation scenarios."""
    print("\n=== Testing Validation Scenarios ===")
    
    try:
        # Test credential validation
        test_credentials = [
            ("<EMAIL>", "password123"),
            ("09123456789", "password123"),
            ("989123456789", "password123"),  # Should normalize
            ("invalid@email", "password123"),
            ("08123456789", "password123"),   # Invalid phone
        ]
        
        for username, password in test_credentials:
            try:
                result = AuthenticationHelper.validate_credentials(username, password)
                print(f"✅ Validation for {username:20}: {result['username_type']} - Valid: {result['valid']}")
            except Exception as e:
                print(f"❌ Validation error for {username:20}: {e}")
        
    except Exception as e:
        print(f"❌ ERROR in validation scenarios: {e}")


def main():
    """Run all tests."""
    print("Dual Username Authentication Test Suite")
    print("=" * 60)
    
    test_phone_normalization()
    test_user_manager_methods()
    test_authentication_helper()
    test_user_instance_methods()
    test_model_configuration()
    test_validation_scenarios()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nDual Username Features Implemented:")
    print("1. ✅ Phone number normalization (Iranian format)")
    print("2. ✅ Enhanced user manager with dual username lookup")
    print("3. ✅ Authentication helper for username type identification")
    print("4. ✅ User instance methods for login validation")
    print("5. ✅ Model configuration for dual username support")
    print("6. ✅ Comprehensive validation and error handling")
    print("\nSupported Login Methods:")
    print("- Email: <EMAIL>")
    print("- Phone: 09123456789 (Iranian format)")
    print("- Phone: 989123456789 (International format - auto-normalized)")
    print("- Phone: +98 ************ (With formatting - auto-normalized)")


if __name__ == "__main__":
    main()
