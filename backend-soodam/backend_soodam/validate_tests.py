#!/usr/bin/env python3
"""
Test validation script to check if the test setup is working correctly.
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path


def check_file_exists(file_path, description):
    """Check if a file exists."""
    if Path(file_path).exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (NOT FOUND)")
        return False


def check_python_syntax(file_path):
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, 'r') as f:
            compile(f.read(), file_path, 'exec')
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in {file_path}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking {file_path}: {e}")
        return False


def check_imports(file_path):
    """Check if imports in a Python file are valid."""
    try:
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        if spec is None:
            return False
        module = importlib.util.module_from_spec(spec)
        # We don't actually execute the module, just check if it can be loaded
        return True
    except Exception as e:
        print(f"❌ Import error in {file_path}: {e}")
        return False


def validate_test_structure():
    """Validate the test directory structure."""
    print("🔍 Validating test directory structure...")
    
    required_files = [
        ("tests/conftest.py", "Test configuration"),
        ("tests/README.md", "Test documentation"),
        ("tests/api/test_auth_comprehensive.py", "Authentication tests"),
        ("tests/api/test_advertisement_comprehensive.py", "Advertisement tests"),
        ("tests/api/test_payment_comprehensive.py", "Payment tests"),
        ("tests/api/test_user_comprehensive.py", "User tests"),
        ("tests/api/test_admin_comprehensive.py", "Admin tests"),
        ("tests/models/test_models_comprehensive.py", "Model tests"),
        ("tests/integration/test_full_workflow.py", "Integration tests"),
        ("pytest.ini", "Pytest configuration"),
        ("run_tests.py", "Test runner script")
    ]
    
    all_exist = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def validate_python_syntax():
    """Validate Python syntax in test files."""
    print("\n🔍 Validating Python syntax...")
    
    test_files = [
        "tests/conftest.py",
        "tests/api/test_auth_comprehensive.py",
        "tests/api/test_advertisement_comprehensive.py",
        "tests/api/test_payment_comprehensive.py",
        "tests/api/test_user_comprehensive.py",
        "tests/api/test_admin_comprehensive.py",
        "tests/models/test_models_comprehensive.py",
        "tests/integration/test_full_workflow.py",
        "run_tests.py",
        "validate_tests.py"
    ]
    
    all_valid = True
    for file_path in test_files:
        if Path(file_path).exists():
            if not check_python_syntax(file_path):
                all_valid = False
            else:
                print(f"✅ Syntax OK: {file_path}")
        else:
            print(f"⚠️  File not found: {file_path}")
            all_valid = False
    
    return all_valid


def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n🔍 Checking test dependencies...")
    
    required_packages = [
        "pytest",
        "pytest-django",
        "pytest-asyncio",
        "pytest-cov",
        "fastapi",
        "httpx"
    ]
    
    all_installed = True
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is NOT installed")
            all_installed = False
    
    return all_installed


def check_pytest_config():
    """Check pytest configuration."""
    print("\n🔍 Checking pytest configuration...")
    
    if not Path("pytest.ini").exists():
        print("❌ pytest.ini not found")
        return False
    
    try:
        with open("pytest.ini", 'r') as f:
            content = f.read()
            
        required_settings = [
            "DJANGO_SETTINGS_MODULE",
            "python_files",
            "python_classes",
            "python_functions",
            "testpaths"
        ]
        
        all_present = True
        for setting in required_settings:
            if setting in content:
                print(f"✅ {setting} configured")
            else:
                print(f"❌ {setting} not configured")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error reading pytest.ini: {e}")
        return False


def run_basic_test():
    """Run a basic test to check if pytest works."""
    print("\n🔍 Running basic pytest validation...")
    
    try:
        # Run pytest with dry-run to check configuration
        result = subprocess.run(
            ["python", "-m", "pytest", "--collect-only", "-q"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Pytest configuration is valid")
            print(f"📊 Collected tests output:\n{result.stdout}")
            return True
        else:
            print(f"❌ Pytest configuration error:\n{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Pytest validation timed out")
        return False
    except Exception as e:
        print(f"❌ Error running pytest: {e}")
        return False


def check_test_runner():
    """Check if the test runner script works."""
    print("\n🔍 Checking test runner script...")
    
    if not Path("run_tests.py").exists():
        print("❌ run_tests.py not found")
        return False
    
    try:
        # Test the help command
        result = subprocess.run(
            ["python", "run_tests.py", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Test runner script is working")
            return True
        else:
            print(f"❌ Test runner script error:\n{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Test runner validation timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking test runner: {e}")
        return False


def main():
    """Main validation function."""
    print("🧪 Soodam Backend Test Setup Validation")
    print("=" * 50)
    
    # Change to the backend_soodam directory
    if Path("backend_soodam").exists():
        os.chdir("backend_soodam")
        print("📁 Changed to backend_soodam directory")
    
    validation_steps = [
        ("Test Structure", validate_test_structure),
        ("Python Syntax", validate_python_syntax),
        ("Dependencies", check_dependencies),
        ("Pytest Config", check_pytest_config),
        ("Test Runner", check_test_runner),
        ("Basic Test", run_basic_test)
    ]
    
    results = {}
    for step_name, step_function in validation_steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        results[step_name] = step_function()
    
    # Summary
    print("\n" + "="*50)
    print("📋 VALIDATION SUMMARY")
    print("="*50)
    
    all_passed = True
    for step_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{step_name:20} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All validations passed! Your test setup is ready.")
        print("\nNext steps:")
        print("1. Run tests: python run_tests.py all --verbose")
        print("2. Generate coverage: python run_tests.py all --coverage")
        print("3. Run specific module: python run_tests.py module --module auth")
        sys.exit(0)
    else:
        print("💥 Some validations failed. Please fix the issues above.")
        print("\nCommon fixes:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check file paths and syntax errors")
        print("3. Ensure Django settings are properly configured")
        sys.exit(1)


if __name__ == "__main__":
    main()
