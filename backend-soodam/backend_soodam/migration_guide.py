"""
Migration Guide for Enhanced User Model

This file contains migration helpers and default values for the enhanced user model.
"""

import jdatetime
from django.utils import timezone
from django.db import migrations, models
import uuid


# =============================================================================
# DEFAULT VALUES FOR MIGRATIONS
# =============================================================================

def get_default_jalali_date():
    """Get current Jalali date for migration defaults."""
    return jdatetime.date.today()


def get_default_jalali_datetime():
    """Get current Jalali datetime for migration defaults."""
    return jdatetime.datetime.now()


def get_default_uuid():
    """Get default UUID for migration."""
    return uuid.uuid4()


# =============================================================================
# MIGRATION DATA FUNCTIONS
# =============================================================================

def create_user_wallets(apps, schema_editor):
    """Create wallet for existing users."""
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    UserWallet = apps.get_model('app', 'UserWallet')

    for user in CustomUserModel.objects.all():
        if not hasattr(user, 'wallet') or not user.wallet:
            UserWallet.objects.create(
                user=user,
                amount=0,
                is_active=True
            )


def create_user_ratings(apps, schema_editor):
    """Create rating for existing users."""
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    UserRating = apps.get_model('app', 'UserRating')

    for user in CustomUserModel.objects.all():
        if not hasattr(user, 'rating') or not user.rating:
            UserRating.objects.create(
                user=user,
                positive_rating=0.0,
                negative_rating=0.0,
                total_reviews=0,
                average_rating=0.0
            )


def normalize_phone_numbers(apps, schema_editor):
    """Normalize existing phone numbers to Iranian format."""
    import re

    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    iranian_phone_regex = r'^09[0-9]{9}$'

    for user in CustomUserModel.objects.all():
        if user.phone_number:
            # Normalize phone number
            phone = user.phone_number
            # Remove non-digits
            cleaned = ''.join(filter(str.isdigit, phone))

            # Handle different formats
            if cleaned.startswith('0098') and len(cleaned) == 14:
                # Convert 00989123456789 to 09123456789
                cleaned = '0' + cleaned[4:]
            elif cleaned.startswith('98') and len(cleaned) == 12:
                # Convert 989123456789 to 09123456789
                cleaned = '0' + cleaned[2:]
            elif not cleaned.startswith('0') and len(cleaned) == 10:
                # Convert 9123456789 to 09123456789
                cleaned = '0' + cleaned

            # Update if changed and valid
            if cleaned != user.phone_number and len(cleaned) == 11 and re.match(iranian_phone_regex, cleaned):
                user.phone_number = cleaned
                user.save()


def reverse_create_user_wallets(apps, schema_editor):
    """Reverse function for wallet creation."""
    UserWallet = apps.get_model('app', 'UserWallet')
    UserWallet.objects.all().delete()


def reverse_create_user_ratings(apps, schema_editor):
    """Reverse function for rating creation."""
    UserRating = apps.get_model('app', 'UserRating')
    UserRating.objects.all().delete()


# =============================================================================
# SAMPLE MIGRATION FILE CONTENT
# =============================================================================

SAMPLE_MIGRATION_CONTENT = '''
# Generated migration file example
# File: app/migrations/XXXX_enhance_user_model.py

from django.db import migrations, models
import django.db.models.deletion
import django_jalali.db.models
import jdatetime
import uuid
from django.core.validators import RegexValidator


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),  # Replace with your last migration
    ]

    operations = [
        # Add new fields to CustomUserModel
        migrations.AddField(
            model_name='customusermodel',
            name='verification_uuid',
            field=models.UUIDField(
                default=uuid.uuid4,
                help_text='شناسه منحصر به فرد برای تایید کاربر',
                unique=True,
                verbose_name='شناسه تایید'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='country_code',
            field=models.CharField(
                blank=True,
                default='IR',
                help_text='کد کشور (پیش‌فرض: ایران)',
                max_length=5,
                verbose_name='کد کشور'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='user_group',
            field=models.IntegerField(
                choices=[(1, 'سوپر ادمین'), (2, 'ادمین'), (3, 'کاربر اشتراکی'), (4, 'کاربر عادی')],
                default=4,
                help_text='سطح دسترسی کاربر',
                verbose_name='گروه کاربری'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='is_admin',
            field=models.BooleanField(
                default=False,
                help_text='آیا این کاربر ادمین است؟',
                verbose_name='ادمین'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='created_at_jalali',
            field=django_jalali.db.models.jDateTimeField(
                auto_now_add=True,
                default=jdatetime.datetime.now,
                help_text='تاریخ ایجاد به تقویم شمسی',
                verbose_name='تاریخ ایجاد (شمسی)'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='updated_at_jalali',
            field=django_jalali.db.models.jDateTimeField(
                auto_now=True,
                help_text='تاریخ آخرین به‌روزرسانی به تقویم شمسی',
                verbose_name='تاریخ آپدیت (شمسی)'
            ),
        ),

        # Modify existing fields
        migrations.AlterField(
            model_name='customusermodel',
            name='phone_number',
            field=models.CharField(
                db_index=True,
                help_text='شماره تلفن همراه با فرمت 09xxxxxxxxx',
                max_length=11,
                unique=True,
                validators=[RegexValidator(
                    code='invalid_phone',
                    message='شماره تلفن باید با 09 شروع شده و 11 رقم باشد',
                    regex='^09[0-9]{9}$'
                )],
                verbose_name='شماره تلفن'
            ),
        ),

        # Add indexes
        migrations.AddIndex(
            model_name='customusermodel',
            index=models.Index(fields=['phone_number'], name='user_phone_idx'),
        ),
        migrations.AddIndex(
            model_name='customusermodel',
            index=models.Index(fields=['email'], name='user_email_idx'),
        ),
        migrations.AddIndex(
            model_name='customusermodel',
            index=models.Index(fields=['is_active', 'is_verified'], name='user_status_idx'),
        ),

        # Create UserWallet model
        migrations.CreateModel(
            name='UserWallet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=0, default=0, help_text='موجودی کیف پول به ریال', max_digits=15, verbose_name='موجودی')),
                ('is_active', models.BooleanField(default=True, help_text='آیا کیف پول فعال است؟', verbose_name='فعال')),
                ('last_transaction_at', models.DateTimeField(blank=True, help_text='زمان آخرین تراکنش', null=True, verbose_name='آخرین تراکنش')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('modified_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wallet', to='app.customusermodel', verbose_name='کاربر')),
            ],
            options={
                'verbose_name': 'کیف پول کاربر',
                'verbose_name_plural': 'کیف پول‌های کاربران',
                'db_table': 'custom_user_wallet',
            },
        ),

        # Create UserRating model
        migrations.CreateModel(
            name='UserRating',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('positive_rating', models.FloatField(default=0.0, help_text='مجموع امتیازات مثبت', verbose_name='امتیاز مثبت')),
                ('negative_rating', models.FloatField(default=0.0, help_text='مجموع امتیازات منفی', verbose_name='امتیاز منفی')),
                ('total_reviews', models.PositiveIntegerField(default=0, help_text='تعداد کل نظرات ثبت شده', verbose_name='تعداد نظرات')),
                ('average_rating', models.FloatField(default=0.0, help_text='میانگین امتیاز کاربر', verbose_name='میانگین امتیاز')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='app.customusermodel', verbose_name='کاربر')),
            ],
            options={
                'verbose_name': 'امتیاز کاربر',
                'verbose_name_plural': 'امتیازات کاربران',
                'db_table': 'custom_user_rating',
            },
        ),

        # Run data migration to create wallets and ratings for existing users
        migrations.RunPython(
            code=create_user_wallets,
            reverse_code=reverse_create_user_wallets,
        ),
        migrations.RunPython(
            code=create_user_ratings,
            reverse_code=reverse_create_user_ratings,
        ),
        migrations.RunPython(
            code=normalize_phone_numbers,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
'''

# =============================================================================
# MIGRATION COMMANDS
# =============================================================================

MIGRATION_COMMANDS = '''
# Step-by-step migration process:

1. Create migration files:
   python manage.py makemigrations

2. When prompted for default values, choose:
   - For jDateTimeField: Select option 2 and provide: jdatetime.datetime.now()
   - For UUIDField: Select option 2 and provide: uuid.uuid4()
   - For other fields: Use the defaults shown

3. Review the generated migration file

4. Apply migrations:
   python manage.py migrate

5. If you encounter issues, you can create a custom migration:
   python manage.py makemigrations --empty app
   # Then edit the migration file manually

# Common default values for migration prompts:

For jDateTimeField:
>>> jdatetime.datetime.now()

For jDateField:
>>> jdatetime.date.today()

For UUIDField:
>>> uuid.uuid4()

For DecimalField (amount):
>>> 0

For BooleanField:
>>> False (or True depending on the field)

For CharField:
>>> '' (empty string)
'''
