import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.core.config import settings
from app.enums.status import AdvertisementStatus, AdminStatus, ActionType, TransactionType
from app.models import CustomUserModel, AdvertisementModel, SpecialAdmin, AdminType


@pytest.fixture
def admin_token_headers(client: TestClient) -> dict[str, str]:
    """Get admin token headers for authentication"""
    login_data = {
        "username": "<EMAIL>",
        "password": "adminpassword",
    }
    with patch("app.dependencies.auth.get_current_admin_user", return_value=MagicMock(is_superuser=True)):
        r = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        tokens = r.json()
        return {"Authorization": f"Bearer {tokens['access_token']}"}


def test_get_admin_dashboard_stats(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting admin dashboard statistics"""
    with patch("app.api.admin_enhanced.AdminAPI.get_admin_dashboard_stats") as mock_get_stats:
        mock_get_stats.return_value = {
            "total_users": 100,
            "active_users": 80,
            "total_advertisements": 200,
            "pending_advertisements": 50,
            "approved_advertisements": 120,
            "rejected_advertisements": 30,
            "advertisements_by_category": [
                {"category": "Real Estate", "count": 50},
                {"category": "Vehicles", "count": 40},
                {"category": "Electronics", "count": 30},
            ]
        }

        response = client.get(
            "/api/admin/dashboard/stats",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["total_users"] == 100
        assert content["active_users"] == 80
        assert content["total_advertisements"] == 200
        assert content["pending_advertisements"] == 50
        assert content["approved_advertisements"] == 120
        assert content["rejected_advertisements"] == 30
        assert len(content["advertisements_by_category"]) == 3


def test_get_all_advertisements(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting all advertisements with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_all_advertisements") as mock_get_advs:
        mock_get_advs.return_value = [
            {
                "id": 1,
                "title": "Test Advertisement 1",
                "description": "Test Description 1",
                "status": AdvertisementStatus.APPROVED,
                "created_at": "2023-01-01T00:00:00",
                "user_id": 1
            },
            {
                "id": 2,
                "title": "Test Advertisement 2",
                "description": "Test Description 2",
                "status": AdvertisementStatus.PENDING,
                "created_at": "2023-01-02T00:00:00",
                "user_id": 2
            }
        ]

        response = client.get(
            "/api/admin/advertisements?status=1&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert len(content) == 2
        assert content[0]["id"] == 1
        assert content[0]["title"] == "Test Advertisement 1"
        assert content[0]["status"] == AdvertisementStatus.APPROVED
        assert content[1]["id"] == 2
        assert content[1]["title"] == "Test Advertisement 2"
        assert content[1]["status"] == AdvertisementStatus.PENDING


def test_approve_advertisement(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test approving an advertisement"""
    with patch("app.api.admin_enhanced.AdminAPI.approve_advertisement") as mock_approve:
        mock_approve.return_value = {
            "id": 1,
            "status": AdvertisementStatus.APPROVED,
            "message": "Advertisement approved successfully"
        }

        response = client.post(
            "/api/admin/advertisement/1/approve",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == AdvertisementStatus.APPROVED
        assert content["message"] == "Advertisement approved successfully"


def test_reject_advertisement(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test rejecting an advertisement"""
    with patch("app.api.admin_enhanced.AdminAPI.approve_advertisement") as mock_reject:
        mock_reject.return_value = {
            "id": 1,
            "status": AdvertisementStatus.REJECTED,
            "message": "Advertisement rejected successfully"
        }

        response = client.post(
            "/api/admin/advertisement/1/reject",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == AdvertisementStatus.REJECTED
        assert content["message"] == "Advertisement rejected successfully"


def test_delete_advertisement(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test deleting an advertisement"""
    with patch("app.api.admin_enhanced.AdminAPI.delete_advertisement") as mock_delete:
        mock_delete.return_value = {
            "id": 1,
            "status": "deleted",
            "message": "Advertisement deleted successfully"
        }

        response = client.delete(
            "/api/admin/advertisement/1",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "deleted"
        assert content["message"] == "Advertisement deleted successfully"


def test_get_all_users(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting all users with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_all_users") as mock_get_users:
        mock_get_users.return_value = [
            {
                "id": 1,
                "email": "<EMAIL>",
                "is_active": True,
                "first_name": "User",
                "last_name": "One",
                "phone_number": "1234567890",
                "date_joined": "2023-01-01T00:00:00"
            },
            {
                "id": 2,
                "email": "<EMAIL>",
                "is_active": True,
                "first_name": "User",
                "last_name": "Two",
                "phone_number": "0987654321",
                "date_joined": "2023-01-02T00:00:00"
            }
        ]

        response = client.get(
            "/api/admin/users?is_active=true&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert len(content) == 2
        assert content[0]["id"] == 1
        assert content[0]["email"] == "<EMAIL>"
        assert content[0]["is_active"] is True
        assert content[1]["id"] == 2
        assert content[1]["email"] == "<EMAIL>"
        assert content[1]["is_active"] is True


def test_ban_user(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test banning a user"""
    with patch("app.api.admin_enhanced.AdminAPI.ban_user") as mock_ban:
        mock_ban.return_value = {
            "id": 1,
            "status": "banned",
            "message": "User banned successfully"
        }

        response = client.post(
            "/api/admin/user/1/ban?reason=Violation of terms",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "banned"
        assert content["message"] == "User banned successfully"


def test_unban_user(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test unbanning a user"""
    with patch("app.api.admin_enhanced.AdminAPI.ban_user") as mock_unban:
        mock_unban.return_value = {
            "id": 1,
            "status": "active",
            "message": "User unbanned successfully"
        }

        response = client.post(
            "/api/admin/user/1/unban",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "active"
        assert content["message"] == "User unbanned successfully"


def test_get_all_blogs(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting all blogs with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_all_blogs") as mock_get_blogs:
        mock_get_blogs.return_value = [
            {
                "id": 1,
                "title": "Test Blog 1",
                "content": "Test Content 1",
                "post_status": "published",
                "created_at": "2023-01-01T00:00:00",
                "author_id": 1
            },
            {
                "id": 2,
                "title": "Test Blog 2",
                "content": "Test Content 2",
                "post_status": "draft",
                "created_at": "2023-01-02T00:00:00",
                "author_id": 2
            }
        ]

        response = client.get(
            "/api/admin/blogs?status=published&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert len(content) == 2
        assert content[0]["id"] == 1
        assert content[0]["title"] == "Test Blog 1"
        assert content[0]["post_status"] == "published"
        assert content[1]["id"] == 2
        assert content[1]["title"] == "Test Blog 2"
        assert content[1]["post_status"] == "draft"


def test_approve_blog(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test approving a blog"""
    with patch("app.api.admin_enhanced.AdminAPI.approve_blog") as mock_approve:
        mock_approve.return_value = {
            "id": 1,
            "status": "published",
            "message": "Blog approved successfully"
        }

        response = client.post(
            "/api/admin/blog/1/approve",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "published"
        assert content["message"] == "Blog approved successfully"


def test_reject_blog(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test rejecting a blog"""
    with patch("app.api.admin_enhanced.AdminAPI.approve_blog") as mock_reject:
        mock_reject.return_value = {
            "id": 1,
            "status": "rejected",
            "message": "Blog rejected successfully"
        }

        response = client.post(
            "/api/admin/blog/1/reject",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "rejected"
        assert content["message"] == "Blog rejected successfully"


def test_delete_blog(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test deleting a blog"""
    with patch("app.api.admin_enhanced.AdminAPI.delete_blog") as mock_delete:
        mock_delete.return_value = {
            "id": 1,
            "status": "deleted",
            "message": "Blog deleted successfully"
        }

        response = client.delete(
            "/api/admin/blog/1",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "deleted"
        assert content["message"] == "Blog deleted successfully"


def test_get_all_special_admins(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting all special admins with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_all_special_admins") as mock_get_admins:
        mock_get_admins.return_value = {
            "items": [
                {
                    "id": 1,
                    "user": {
                        "id": 1,
                        "email": "<EMAIL>",
                        "first_name": "Admin",
                        "last_name": "One"
                    },
                    "admin_type": {
                        "id": 1,
                        "name": "Content Manager"
                    },
                    "status": AdminStatus.ACTIVE,
                    "can_manage_users": True,
                    "can_manage_advertisements": True,
                    "can_manage_blogs": True,
                    "can_manage_agents": False,
                    "created_at": "2023-01-01T00:00:00"
                },
                {
                    "id": 2,
                    "user": {
                        "id": 2,
                        "email": "<EMAIL>",
                        "first_name": "Admin",
                        "last_name": "Two"
                    },
                    "admin_type": {
                        "id": 2,
                        "name": "User Manager"
                    },
                    "status": AdminStatus.ACTIVE,
                    "can_manage_users": True,
                    "can_manage_advertisements": False,
                    "can_manage_blogs": False,
                    "can_manage_agents": False,
                    "created_at": "2023-01-02T00:00:00"
                }
            ],
            "total": 2,
            "page": 1,
            "size": 10,
            "pages": 1
        }

        response = client.get(
            "/api/admin/special-admins?status=1&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["total"] == 2
        assert content["page"] == 1
        assert content["size"] == 10
        assert content["pages"] == 1
        assert len(content["items"]) == 2
        assert content["items"][0]["id"] == 1
        assert content["items"][0]["user"]["email"] == "<EMAIL>"
        assert content["items"][0]["admin_type"]["name"] == "Content Manager"
        assert content["items"][0]["status"] == AdminStatus.ACTIVE
        assert content["items"][1]["id"] == 2
        assert content["items"][1]["user"]["email"] == "<EMAIL>"
        assert content["items"][1]["admin_type"]["name"] == "User Manager"
        assert content["items"][1]["status"] == AdminStatus.ACTIVE


def test_get_special_admin_detail(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting special admin details"""
    with patch("app.api.admin_enhanced.AdminAPI.get_special_admin_detail") as mock_get_admin:
        mock_get_admin.return_value = {
            "id": 1,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "first_name": "Admin",
                "last_name": "One"
            },
            "admin_type": {
                "id": 1,
                "name": "Content Manager"
            },
            "status": AdminStatus.ACTIVE,
            "can_manage_users": True,
            "can_manage_advertisements": True,
            "can_manage_blogs": True,
            "can_manage_agents": False,
            "created_at": "2023-01-01T00:00:00",
            "cities": [
                {"id": 1, "name": "New York"},
                {"id": 2, "name": "Los Angeles"}
            ],
            "provinces": [
                {"id": 1, "name": "California"}
            ]
        }

        response = client.get(
            "/api/admin/special-admins/1",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["user"]["email"] == "<EMAIL>"
        assert content["admin_type"]["name"] == "Content Manager"
        assert content["status"] == AdminStatus.ACTIVE
        assert content["can_manage_users"] is True
        assert content["can_manage_advertisements"] is True
        assert content["can_manage_blogs"] is True
        assert content["can_manage_agents"] is False
        assert len(content["cities"]) == 2
        assert len(content["provinces"]) == 1


def test_create_special_admin(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test creating a special admin"""
    with patch("app.api.admin_enhanced.AdminAPI.create_special_admin") as mock_create:
        mock_create.return_value = {
            "id": 3,
            "user": {
                "id": 3,
                "email": "<EMAIL>",
                "first_name": "Admin",
                "last_name": "Three"
            },
            "admin_type": {
                "id": 1,
                "name": "Content Manager"
            },
            "status": AdminStatus.ACTIVE,
            "can_manage_users": True,
            "can_manage_advertisements": True,
            "can_manage_blogs": True,
            "can_manage_agents": False,
            "created_at": "2023-01-03T00:00:00"
        }

        data = {
            "user_id": 3,
            "admin_type_id": 1,
            "can_manage_users": True,
            "can_manage_advertisements": True,
            "can_manage_blogs": True,
            "can_manage_agents": False,
            "city_ids": [1, 2],
            "province_ids": [1]
        }

        response = client.post(
            "/api/admin/special-admins",
            headers=admin_token_headers,
            json=data
        )

        assert response.status_code == 201
        content = response.json()
        assert content["id"] == 3
        assert content["user"]["email"] == "<EMAIL>"
        assert content["admin_type"]["name"] == "Content Manager"
        assert content["status"] == AdminStatus.ACTIVE
        assert content["can_manage_users"] is True
        assert content["can_manage_advertisements"] is True
        assert content["can_manage_blogs"] is True
        assert content["can_manage_agents"] is False


def test_update_special_admin(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test updating a special admin"""
    with patch("app.api.admin_enhanced.AdminAPI.update_special_admin") as mock_update:
        mock_update.return_value = {
            "id": 1,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "first_name": "Admin",
                "last_name": "One"
            },
            "admin_type": {
                "id": 2,
                "name": "User Manager"
            },
            "status": AdminStatus.ACTIVE,
            "can_manage_users": True,
            "can_manage_advertisements": False,
            "can_manage_blogs": False,
            "can_manage_agents": False,
            "created_at": "2023-01-01T00:00:00"
        }

        data = {
            "admin_type_id": 2,
            "can_manage_users": True,
            "can_manage_advertisements": False,
            "can_manage_blogs": False,
            "can_manage_agents": False,
            "status": AdminStatus.ACTIVE,
            "city_ids": [1],
            "province_ids": [1]
        }

        response = client.put(
            "/api/admin/special-admins/1",
            headers=admin_token_headers,
            json=data
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["user"]["email"] == "<EMAIL>"
        assert content["admin_type"]["name"] == "User Manager"
        assert content["status"] == AdminStatus.ACTIVE
        assert content["can_manage_users"] is True
        assert content["can_manage_advertisements"] is False
        assert content["can_manage_blogs"] is False
        assert content["can_manage_agents"] is False


def test_delete_special_admin(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test deleting a special admin"""
    with patch("app.api.admin_enhanced.AdminAPI.delete_special_admin") as mock_delete:
        mock_delete.return_value = {
            "id": 1,
            "status": "deleted",
            "message": "Special admin deleted successfully"
        }

        response = client.delete(
            "/api/admin/special-admins/1",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["id"] == 1
        assert content["status"] == "deleted"
        assert content["message"] == "Special admin deleted successfully"


def test_get_admin_transactions(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting admin transactions with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_admin_transactions") as mock_get_transactions:
        mock_get_transactions.return_value = {
            "items": [
                {
                    "id": 1,
                    "admin_id": 1,
                    "amount": 1000.0,
                    "transaction_type": TransactionType.SALARY,
                    "is_positive": True,
                    "description": "Monthly salary",
                    "transaction_date": "2023-01-01T00:00:00"
                },
                {
                    "id": 2,
                    "admin_id": 1,
                    "amount": 200.0,
                    "transaction_type": TransactionType.BONUS,
                    "is_positive": True,
                    "description": "Performance bonus",
                    "transaction_date": "2023-01-15T00:00:00"
                }
            ],
            "total": 2,
            "page": 1,
            "size": 10,
            "pages": 1
        }

        response = client.get(
            "/api/admin/transactions?admin_id=1&is_positive=true&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["total"] == 2
        assert content["page"] == 1
        assert content["size"] == 10
        assert content["pages"] == 1
        assert len(content["items"]) == 2
        assert content["items"][0]["id"] == 1
        assert content["items"][0]["admin_id"] == 1
        assert content["items"][0]["amount"] == 1000.0
        assert content["items"][0]["transaction_type"] == TransactionType.SALARY
        assert content["items"][0]["is_positive"] is True
        assert content["items"][1]["id"] == 2
        assert content["items"][1]["admin_id"] == 1
        assert content["items"][1]["amount"] == 200.0
        assert content["items"][1]["transaction_type"] == TransactionType.BONUS
        assert content["items"][1]["is_positive"] is True


def test_add_admin_transaction(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test adding an admin transaction"""
    with patch("app.api.admin_enhanced.AdminAPI.add_admin_transaction") as mock_add:
        mock_add.return_value = {
            "id": 3,
            "admin_id": 1,
            "amount": 500.0,
            "transaction_type": TransactionType.COMMISSION,
            "is_positive": True,
            "description": "Commission for sales",
            "transaction_date": "2023-01-20T00:00:00"
        }

        data = {
            "admin_id": 1,
            "amount": 500.0,
            "transaction_type": TransactionType.COMMISSION,
            "is_positive": True,
            "description": "Commission for sales"
        }

        response = client.post(
            "/api/admin/transactions",
            headers=admin_token_headers,
            json=data
        )

        assert response.status_code == 201
        content = response.json()
        assert content["id"] == 3
        assert content["admin_id"] == 1
        assert content["amount"] == 500.0
        assert content["transaction_type"] == TransactionType.COMMISSION
        assert content["is_positive"] is True
        assert content["description"] == "Commission for sales"


def test_get_admin_activities(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting admin activities with filtering and pagination"""
    with patch("app.api.admin_enhanced.AdminAPI.get_admin_activities") as mock_get_activities:
        mock_get_activities.return_value = {
            "items": [
                {
                    "id": 1,
                    "admin_id": 1,
                    "admin_name": "Admin One",
                    "action_type": ActionType.CREATE,
                    "entity_type": "advertisement",
                    "entity_id": 1,
                    "description": "Created advertisement",
                    "created_at": "2023-01-01T00:00:00"
                },
                {
                    "id": 2,
                    "admin_id": 1,
                    "admin_name": "Admin One",
                    "action_type": ActionType.UPDATE,
                    "entity_type": "advertisement",
                    "entity_id": 1,
                    "description": "Updated advertisement",
                    "created_at": "2023-01-02T00:00:00"
                }
            ],
            "total": 2,
            "page": 1,
            "size": 10,
            "pages": 1
        }

        response = client.get(
            "/api/admin/activities?admin_id=1&action_type=1&page=1&limit=10",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert content["total"] == 2
        assert content["page"] == 1
        assert content["size"] == 10
        assert content["pages"] == 1
        assert len(content["items"]) == 2
        assert content["items"][0]["id"] == 1
        assert content["items"][0]["admin_id"] == 1
        assert content["items"][0]["admin_name"] == "Admin One"
        assert content["items"][0]["action_type"] == ActionType.CREATE
        assert content["items"][0]["entity_type"] == "advertisement"
        assert content["items"][1]["id"] == 2
        assert content["items"][1]["admin_id"] == 1
        assert content["items"][1]["admin_name"] == "Admin One"
        assert content["items"][1]["action_type"] == ActionType.UPDATE
        assert content["items"][1]["entity_type"] == "advertisement"


def test_get_admin_types(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test getting all admin types"""
    with patch("app.api.admin_enhanced.AdminAPI.get_admin_types") as mock_get_types:
        mock_get_types.return_value = [
            {
                "id": 1,
                "name": "Content Manager",
                "description": "Manages content like advertisements and blogs"
            },
            {
                "id": 2,
                "name": "User Manager",
                "description": "Manages user accounts and permissions"
            }
        ]

        response = client.get(
            "/api/admin/types",
            headers=admin_token_headers,
        )

        assert response.status_code == 200
        content = response.json()
        assert len(content) == 2
        assert content[0]["id"] == 1
        assert content[0]["name"] == "Content Manager"
        assert content[0]["description"] == "Manages content like advertisements and blogs"
        assert content[1]["id"] == 2
        assert content[1]["name"] == "User Manager"
        assert content[1]["description"] == "Manages user accounts and permissions"


def test_create_admin_type(client: TestClient, admin_token_headers: dict[str, str]) -> None:
    """Test creating an admin type"""
    with patch("app.api.admin_enhanced.AdminAPI.create_admin_type") as mock_create:
        mock_create.return_value = {
            "id": 3,
            "name": "System Manager",
            "description": "Manages system settings and configurations"
        }

        data = {
            "name": "System Manager",
            "description": "Manages system settings and configurations"
        }

        response = client.post(
            "/api/admin/types",
            headers=admin_token_headers,
            json=data
        )

        assert response.status_code == 201
        content = response.json()
        assert content["id"] == 3
        assert content["name"] == "System Manager"
        assert content["description"] == "Manages system settings and configurations"
