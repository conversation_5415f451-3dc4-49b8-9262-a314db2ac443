"""
Comprehensive tests for authentication API endpoints.
"""

import pytest
import re
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status

from app.models import CustomUserModel, LogAccess


class TestAuthAPI:
    """Test cases for authentication API."""

    @pytest.mark.asyncio
    async def test_get_verification_code_success(self, client: TestClient, mock_sms_service):
        """Test successful verification code generation."""
        phone_number = "09123456789"
        
        # Mock the LogAccess creation
        with patch('app.models.LogAccess.objects.acreate') as mock_create:
            mock_create.return_value = AsyncMock()
            
            response = client.post(
                "/api/auth/get_ver_code",
                json={"phone_number": phone_number}
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "success"
            assert "verify_code" in data

    @pytest.mark.asyncio
    async def test_get_verification_code_invalid_phone(self, client: TestClient):
        """Test verification code generation with invalid phone number."""
        invalid_phone = "123456789"  # Invalid format
        
        response = client.post(
            "/api/auth/get_ver_code",
            json={"phone_number": invalid_phone}
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert data["status"] == "failed"

    @pytest.mark.asyncio
    async def test_verify_code_success(self, client: TestClient, test_user):
        """Test successful code verification."""
        phone_number = "09123456789"
        verify_code = "123456"
        
        # Create a LogAccess entry for testing
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.try_to_verify = 1
            mock_log_access.user_token = "test-token"
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/verify",
                json={
                    "phone_number": phone_number,
                    "verify_code": verify_code
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == 200
            assert data["token"] == "test-token"
            assert data["token_type"] == "bearer"

    @pytest.mark.asyncio
    async def test_verify_code_invalid_code(self, client: TestClient):
        """Test verification with invalid code."""
        phone_number = "09123456789"
        verify_code = "000000"  # Invalid code
        
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.try_to_verify = 1
            mock_filter.return_value.afirst.return_value = None  # No matching record
            
            response = client.post(
                "/api/auth/verify",
                json={
                    "phone_number": phone_number,
                    "verify_code": verify_code
                }
            )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_verify_code_max_attempts_exceeded(self, client: TestClient):
        """Test verification when max attempts exceeded."""
        phone_number = "09123456789"
        verify_code = "123456"
        
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.try_to_verify = 5  # Exceeded max attempts
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/verify",
                json={
                    "phone_number": phone_number,
                    "verify_code": verify_code
                }
            )
            
            assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS

    @pytest.mark.asyncio
    async def test_see_verification_code(self, client: TestClient):
        """Test viewing verification code (for testing purposes)."""
        phone_number = "09123456789"
        
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.verify_code = "123456"
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/see_ver_code",
                json={"phone_number": phone_number}
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["code"] == "123456"

    @pytest.mark.asyncio
    async def test_call_send_verify_code(self, client: TestClient, mock_sms_service):
        """Test sending verification code via call."""
        phone_number = "09123456789"
        
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.verify_code = "123456"
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/call_send_verify_code",
                json={"phone_number": phone_number}
            )
            
            assert response.status_code == status.HTTP_200_OK
            mock_sms_service.send_sms.assert_called_once()

    def test_phone_number_validation(self):
        """Test phone number validation regex."""
        valid_phones = [
            "09123456789",
            "09121234567",
            "09351234567"
        ]
        
        invalid_phones = [
            "123456789",
            "9123456789",
            "091234567890",
            "08123456789"
        ]
        
        phone_regex = r"09([0-9][0-9])-?[0-9]{3}-?[0-9]{4}"
        
        for phone in valid_phones:
            assert re.search(phone_regex, phone) is not None, f"Valid phone {phone} failed validation"
        
        for phone in invalid_phones:
            assert re.search(phone_regex, phone) is None, f"Invalid phone {phone} passed validation"

    @pytest.mark.asyncio
    async def test_login_with_oauth2_form(self, client: TestClient, test_user):
        """Test login with OAuth2 password form."""
        with patch('app.api.auth.AuthAPI.login') as mock_login:
            mock_login.return_value = {
                "access_token": "test-token",
                "token_type": "bearer"
            }
            
            response = client.post(
                "/api/auth/login",
                data={
                    "username": "09123456789",
                    "password": "test-password"
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "access_token" in data
            assert data["token_type"] == "bearer"

    @pytest.mark.asyncio
    async def test_concurrent_verification_requests(self, client: TestClient, mock_sms_service):
        """Test handling of concurrent verification requests."""
        phone_number = "09123456789"
        
        # Simulate multiple concurrent requests
        responses = []
        for _ in range(3):
            response = client.post(
                "/api/auth/get_ver_code",
                json={"phone_number": phone_number}
            )
            responses.append(response)
        
        # At least one should succeed
        success_count = sum(1 for r in responses if r.status_code == status.HTTP_200_OK)
        assert success_count >= 1

    @pytest.mark.asyncio
    async def test_rate_limiting_verification_requests(self, client: TestClient):
        """Test rate limiting for verification requests."""
        phone_number = "09123456789"
        
        # Send multiple requests rapidly
        for i in range(10):
            response = client.post(
                "/api/auth/get_ver_code",
                json={"phone_number": phone_number}
            )
            
            # After certain number of requests, should be rate limited
            if i > 5:
                assert response.status_code in [
                    status.HTTP_200_OK, 
                    status.HTTP_429_TOO_MANY_REQUESTS
                ]

    @pytest.mark.asyncio
    async def test_verification_code_expiry(self, client: TestClient):
        """Test verification code expiry handling."""
        phone_number = "09123456789"
        verify_code = "123456"
        
        # Mock expired verification code
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.try_to_verify = 1
            mock_log_access.created_at = "2023-01-01 00:00:00"  # Old timestamp
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/verify",
                json={
                    "phone_number": phone_number,
                    "verify_code": verify_code
                }
            )
            
            # Should handle expired codes appropriately
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_410_GONE
            ]
