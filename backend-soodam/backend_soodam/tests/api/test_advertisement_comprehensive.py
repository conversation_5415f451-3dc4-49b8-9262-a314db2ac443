"""
Comprehensive tests for advertisement API endpoints.
"""

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from fastapi import status, UploadFile
from io import BytesIO

from app.models.advertisement import (
    AdvertisementModel, MainCategoryModel, SubCategoryModel,
    AdvertisementViewModel, AdvertisementFavoriteModel
)
from app.enums.status import AdvertisementStatus


class TestAdvertisementAPI:
    """Test cases for advertisement API."""

    @pytest.mark.asyncio
    async def test_get_meta_data(self, client: TestClient):
        """Test getting advertisement metadata."""
        with patch('app.api.advertisement.AdvertisementAPI.get_meta_data') as mock_meta:
            mock_meta.return_value = {
                "categories": [
                    {"id": 1, "name": "املاک", "slug": "real-estate"}
                ],
                "provinces": [
                    {"id": 1, "name": "تهران", "slug": "tehran"}
                ]
            }
            
            response = client.get("/api/advertisements/meta")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "categories" in data
            assert "provinces" in data

    @pytest.mark.asyncio
    async def test_get_features_by_category(self, client: TestClient, test_category):
        """Test getting features by category."""
        category_data = {
            "main_category_id": test_category.id,
            "sub_category_id": None
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.get_features_by_category') as mock_features:
            mock_features.return_value = {
                "boolean_attributes": [],
                "choice_attributes": [],
                "text_attributes": []
            }
            
            response = client.post(
                "/api/advertisements/features",
                json=category_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "boolean_attributes" in data
            assert "choice_attributes" in data
            assert "text_attributes" in data

    @pytest.mark.asyncio
    async def test_create_advertisement_success(self, client: TestClient, normal_user_token_headers, 
                                              test_category, test_subcategory, test_province, test_city):
        """Test successful advertisement creation."""
        advertisement_data = {
            "title": "Test Advertisement",
            "description": "Test Description",
            "phone_number_owner_building": "09123456789",
            "main_category_id": test_category.id,
            "sub_category_id": test_subcategory.id,
            "province_id": test_province.id,
            "city_id": test_city.id,
            "price": 1000000,
            "is_negotiable": True
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.create_advertisement') as mock_create:
            mock_create.return_value = {
                "id": 1,
                "title": "Test Advertisement",
                "status": "pending"
            }
            
            response = client.post(
                "/api/advertisements/",
                json=advertisement_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["title"] == "Test Advertisement"

    @pytest.mark.asyncio
    async def test_create_advertisement_validation_error(self, client: TestClient, normal_user_token_headers):
        """Test advertisement creation with validation errors."""
        invalid_data = {
            "title": "",  # Empty title
            "description": "Test Description",
            "phone_number_owner_building": "invalid_phone",  # Invalid phone
        }
        
        response = client.post(
            "/api/advertisements/",
            json=invalid_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_get_advertisement_list(self, client: TestClient):
        """Test getting advertisement list with pagination."""
        with patch('app.api.advertisement.AdvertisementAPI.get_advertisements') as mock_list:
            mock_list.return_value = {
                "items": [
                    {
                        "id": 1,
                        "title": "Test Ad 1",
                        "price": 1000000
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10
            }
            
            response = client.get("/api/advertisements/?page=1&per_page=10")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "items" in data
            assert "total" in data
            assert data["total"] == 1

    @pytest.mark.asyncio
    async def test_get_advertisement_detail(self, client: TestClient, test_advertisement):
        """Test getting advertisement detail."""
        with patch('app.api.advertisement.AdvertisementAPI.get_advertisement_detail') as mock_detail:
            mock_detail.return_value = {
                "id": test_advertisement.id,
                "title": test_advertisement.title,
                "description": test_advertisement.description,
                "views": 0,
                "favorites": 0
            }
            
            response = client.get(f"/api/advertisements/{test_advertisement.id}")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == test_advertisement.id
            assert data["title"] == test_advertisement.title

    @pytest.mark.asyncio
    async def test_update_advertisement(self, client: TestClient, normal_user_token_headers, test_advertisement):
        """Test updating advertisement."""
        update_data = {
            "title": "Updated Title",
            "description": "Updated Description"
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.update_advertisement') as mock_update:
            mock_update.return_value = {
                "id": test_advertisement.id,
                "title": "Updated Title",
                "description": "Updated Description"
            }
            
            response = client.put(
                f"/api/advertisements/{test_advertisement.id}",
                json=update_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["title"] == "Updated Title"

    @pytest.mark.asyncio
    async def test_delete_advertisement(self, client: TestClient, normal_user_token_headers, test_advertisement):
        """Test deleting advertisement."""
        with patch('app.api.advertisement.AdvertisementAPI.delete_advertisement') as mock_delete:
            mock_delete.return_value = {"message": "Advertisement deleted successfully"}
            
            response = client.delete(
                f"/api/advertisements/{test_advertisement.id}",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_upload_advertisement_images(self, client: TestClient, normal_user_token_headers, test_advertisement):
        """Test uploading advertisement images."""
        # Create a mock image file
        image_content = b"fake image content"
        files = {
            "files": ("test.jpg", BytesIO(image_content), "image/jpeg")
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.upload_images') as mock_upload:
            mock_upload.return_value = {
                "uploaded_images": [
                    {"id": 1, "url": "/media/ads/test.jpg"}
                ]
            }
            
            response = client.post(
                f"/api/advertisements/{test_advertisement.id}/images",
                files=files,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "uploaded_images" in data

    @pytest.mark.asyncio
    async def test_search_advertisements(self, client: TestClient):
        """Test searching advertisements."""
        search_params = {
            "q": "test",
            "category_id": 1,
            "province_id": 1,
            "min_price": 100000,
            "max_price": 2000000
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.search_advertisements') as mock_search:
            mock_search.return_value = {
                "items": [
                    {"id": 1, "title": "Test Advertisement"}
                ],
                "total": 1
            }
            
            response = client.get("/api/advertisements/search", params=search_params)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "items" in data

    @pytest.mark.asyncio
    async def test_favorite_advertisement(self, client: TestClient, normal_user_token_headers, test_advertisement):
        """Test adding advertisement to favorites."""
        with patch('app.api.advertisement.AdvertisementAPI.toggle_favorite') as mock_favorite:
            mock_favorite.return_value = {
                "is_favorite": True,
                "message": "Advertisement added to favorites"
            }
            
            response = client.post(
                f"/api/advertisements/{test_advertisement.id}/favorite",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["is_favorite"] is True

    @pytest.mark.asyncio
    async def test_flag_advertisement(self, client: TestClient, normal_user_token_headers, test_advertisement):
        """Test flagging inappropriate advertisement."""
        flag_data = {
            "reason": "inappropriate_content",
            "description": "This content is inappropriate"
        }
        
        with patch('app.api.advertisement.AdvertisementAPI.flag_advertisement') as mock_flag:
            mock_flag.return_value = {
                "message": "Advertisement flagged successfully"
            }
            
            response = client.post(
                f"/api/advertisements/{test_advertisement.id}/flag",
                json=flag_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_get_user_advertisements(self, client: TestClient, normal_user_token_headers):
        """Test getting user's own advertisements."""
        with patch('app.api.advertisement.AdvertisementAPI.get_user_advertisements') as mock_user_ads:
            mock_user_ads.return_value = {
                "items": [
                    {"id": 1, "title": "My Advertisement"}
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/advertisements/my-ads",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "items" in data

    @pytest.mark.asyncio
    async def test_advertisement_statistics(self, client: TestClient, test_advertisement):
        """Test getting advertisement statistics."""
        with patch('app.api.advertisement.AdvertisementAPI.get_advertisement_statistics') as mock_stats:
            mock_stats.return_value = {
                "views": 100,
                "favorites": 5,
                "contacts": 10,
                "daily_views": [
                    {"date": "2023-01-01", "views": 10}
                ]
            }
            
            response = client.get(f"/api/advertisements/{test_advertisement.id}/statistics")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "views" in data
            assert "favorites" in data

    @pytest.mark.asyncio
    async def test_advertisement_view_tracking(self, client: TestClient, test_advertisement):
        """Test advertisement view tracking."""
        with patch('app.api.advertisement.AdvertisementAPI.track_view') as mock_track:
            mock_track.return_value = {"view_recorded": True}
            
            response = client.post(f"/api/advertisements/{test_advertisement.id}/view")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["view_recorded"] is True

    @pytest.mark.asyncio
    async def test_similar_advertisements(self, client: TestClient, test_advertisement):
        """Test getting similar advertisements."""
        with patch('app.api.advertisement.AdvertisementAPI.get_similar_advertisements') as mock_similar:
            mock_similar.return_value = {
                "items": [
                    {"id": 2, "title": "Similar Advertisement"}
                ]
            }
            
            response = client.get(f"/api/advertisements/{test_advertisement.id}/similar")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "items" in data
