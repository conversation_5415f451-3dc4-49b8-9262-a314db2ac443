"""
Comprehensive tests for user API endpoints.
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import <PERSON><PERSON><PERSON>
from fastapi import status
from io import BytesIO

from app.models import CustomUserModel, UserWallet, SubScriptionModel, AddressesModel


class TestUserAPI:
    """Test cases for user API."""

    @pytest.mark.asyncio
    async def test_get_user_info_success(self, client: TestClient, normal_user_token_headers, test_user):
        """Test successful user info retrieval."""
        with patch('app.api.user.RegisterUserAPI.get_user_info') as mock_get_info:
            mock_get_info.return_value = {
                "id": test_user.id,
                "first_name": test_user.first_name,
                "last_name": test_user.last_name,
                "phone_number": test_user.phone_number,
                "email": test_user.email,
                "is_verified": test_user.is_verified,
                "wallet_balance": 0,
                "subscription_status": "inactive"
            }
            
            response = client.get(
                "/api/user/profile",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == test_user.id
            assert data["phone_number"] == test_user.phone_number

    @pytest.mark.asyncio
    async def test_update_user_info_success(self, client: TestClient, normal_user_token_headers, test_user):
        """Test successful user info update."""
        update_data = {
            "id": test_user.id,
            "first_name": "Updated Name",
            "last_name": "Updated Last",
            "father_name": "Updated Father",
            "security_number": "1234567890",
            "email": "<EMAIL>",
            "birthday": "1990-01-01",
            "gender": "male",
            "avatar": "new_avatar.jpg"
        }
        
        with patch('app.api.user.RegisterUserAPI.edit_user_info') as mock_update:
            mock_update.return_value = {
                "message": "User info updated successfully",
                "user": update_data
            }
            
            response = client.put(
                "/api/user/profile",
                json=update_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["user"]["first_name"] == "Updated Name"

    @pytest.mark.asyncio
    async def test_update_user_info_validation_error(self, client: TestClient, normal_user_token_headers, test_user):
        """Test user info update with validation errors."""
        invalid_data = {
            "id": test_user.id,
            "first_name": "",  # Empty name
            "security_number": "123",  # Invalid security number
            "email": "invalid-email",  # Invalid email format
            "birthday": "invalid-date"  # Invalid date format
        }
        
        response = client.put(
            "/api/user/profile",
            json=invalid_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_upload_user_avatar(self, client: TestClient, normal_user_token_headers, test_user):
        """Test user avatar upload."""
        # Create a mock image file
        image_content = b"fake image content"
        files = {
            "avatar": ("avatar.jpg", BytesIO(image_content), "image/jpeg")
        }
        
        with patch('app.api.user.RegisterUserAPI.upload_avatar') as mock_upload:
            mock_upload.return_value = {
                "message": "Avatar uploaded successfully",
                "avatar_url": "/media/avatars/avatar.jpg"
            }
            
            response = client.post(
                "/api/user/avatar",
                files=files,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "avatar_url" in data

    @pytest.mark.asyncio
    async def test_upload_invalid_avatar_format(self, client: TestClient, normal_user_token_headers):
        """Test uploading invalid avatar format."""
        # Create a mock text file (invalid format)
        text_content = b"not an image"
        files = {
            "avatar": ("document.txt", BytesIO(text_content), "text/plain")
        }
        
        response = client.post(
            "/api/user/avatar",
            files=files,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_create_user_address(self, client: TestClient, normal_user_token_headers, test_province, test_city):
        """Test creating user address."""
        address_data = {
            "province_id": test_province.id,
            "city_id": test_city.id,
            "address_line": "Test Address Line",
            "postal_code": "1234567890",
            "is_primary": True
        }
        
        with patch('app.api.user.RegisterUserAPI.create_address') as mock_create:
            mock_create.return_value = {
                "id": 1,
                "address_line": "Test Address Line",
                "postal_code": "1234567890",
                "is_primary": True
            }
            
            response = client.post(
                "/api/user/address",
                json=address_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["address_line"] == "Test Address Line"

    @pytest.mark.asyncio
    async def test_get_user_addresses(self, client: TestClient, normal_user_token_headers):
        """Test getting user addresses."""
        with patch('app.api.user.RegisterUserAPI.get_user_addresses') as mock_get_addresses:
            mock_get_addresses.return_value = {
                "addresses": [
                    {
                        "id": 1,
                        "address_line": "Test Address",
                        "is_primary": True
                    }
                ]
            }
            
            response = client.get(
                "/api/user/addresses",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "addresses" in data
            assert len(data["addresses"]) == 1

    @pytest.mark.asyncio
    async def test_update_user_address(self, client: TestClient, normal_user_token_headers):
        """Test updating user address."""
        address_id = 1
        update_data = {
            "address_line": "Updated Address Line",
            "postal_code": "0987654321"
        }
        
        with patch('app.api.user.RegisterUserAPI.update_address') as mock_update:
            mock_update.return_value = {
                "id": address_id,
                "address_line": "Updated Address Line",
                "postal_code": "0987654321"
            }
            
            response = client.put(
                f"/api/user/address/{address_id}",
                json=update_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["address_line"] == "Updated Address Line"

    @pytest.mark.asyncio
    async def test_delete_user_address(self, client: TestClient, normal_user_token_headers):
        """Test deleting user address."""
        address_id = 1
        
        with patch('app.api.user.RegisterUserAPI.delete_address') as mock_delete:
            mock_delete.return_value = {
                "message": "Address deleted successfully"
            }
            
            response = client.delete(
                f"/api/user/address/{address_id}",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_get_user_wallet(self, client: TestClient, normal_user_token_headers):
        """Test getting user wallet information."""
        with patch('app.api.user.RegisterUserAPI.get_user_wallet') as mock_wallet:
            mock_wallet.return_value = {
                "balance": 50000,
                "currency": "IRR",
                "transactions": [
                    {
                        "id": 1,
                        "amount": 10000,
                        "type": "deposit",
                        "date": "2023-01-01T00:00:00Z"
                    }
                ]
            }
            
            response = client.get(
                "/api/user/wallet",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["balance"] == 50000
            assert "transactions" in data

    @pytest.mark.asyncio
    async def test_get_user_subscription(self, client: TestClient, normal_user_token_headers):
        """Test getting user subscription information."""
        with patch('app.api.user.RegisterUserAPI.get_user_subscription') as mock_subscription:
            mock_subscription.return_value = {
                "plan": "premium",
                "status": "active",
                "expires_at": "2023-12-31T23:59:59Z",
                "features": ["unlimited_ads", "priority_support"]
            }
            
            response = client.get(
                "/api/user/subscription",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["plan"] == "premium"
            assert data["status"] == "active"

    @pytest.mark.asyncio
    async def test_change_user_password(self, client: TestClient, normal_user_token_headers):
        """Test changing user password."""
        password_data = {
            "current_password": "old_password",
            "new_password": "new_password123",
            "confirm_password": "new_password123"
        }
        
        with patch('app.api.user.RegisterUserAPI.change_password') as mock_change:
            mock_change.return_value = {
                "message": "Password changed successfully"
            }
            
            response = client.post(
                "/api/user/change-password",
                json=password_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "message" in data

    @pytest.mark.asyncio
    async def test_change_password_mismatch(self, client: TestClient, normal_user_token_headers):
        """Test changing password with mismatched confirmation."""
        password_data = {
            "current_password": "old_password",
            "new_password": "new_password123",
            "confirm_password": "different_password"
        }
        
        response = client.post(
            "/api/user/change-password",
            json=password_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_deactivate_user_account(self, client: TestClient, normal_user_token_headers):
        """Test deactivating user account."""
        deactivation_data = {
            "reason": "No longer needed",
            "password": "user_password"
        }
        
        with patch('app.api.user.RegisterUserAPI.deactivate_account') as mock_deactivate:
            mock_deactivate.return_value = {
                "message": "Account deactivated successfully"
            }
            
            response = client.post(
                "/api/user/deactivate",
                json=deactivation_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_get_user_activity_log(self, client: TestClient, normal_user_token_headers):
        """Test getting user activity log."""
        with patch('app.api.user.RegisterUserAPI.get_activity_log') as mock_activity:
            mock_activity.return_value = {
                "activities": [
                    {
                        "id": 1,
                        "action": "login",
                        "timestamp": "2023-01-01T00:00:00Z",
                        "ip_address": "***********"
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/user/activity",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "activities" in data
            assert len(data["activities"]) == 1

    @pytest.mark.asyncio
    async def test_user_privacy_settings(self, client: TestClient, normal_user_token_headers):
        """Test updating user privacy settings."""
        privacy_data = {
            "show_phone": False,
            "show_email": True,
            "allow_notifications": True,
            "public_profile": False
        }
        
        with patch('app.api.user.RegisterUserAPI.update_privacy_settings') as mock_privacy:
            mock_privacy.return_value = {
                "message": "Privacy settings updated",
                "settings": privacy_data
            }
            
            response = client.put(
                "/api/user/privacy",
                json=privacy_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["settings"]["show_phone"] is False
