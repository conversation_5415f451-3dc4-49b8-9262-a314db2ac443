"""
Comprehensive tests for admin API endpoints.
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status
from datetime import datetime, timedelta

from app.models import CustomUserModel, AdvertisementModel, BlogModel
from app.enums.status import Advertisement<PERSON>tatus, BlogStatus, AdminStatus


class TestAdminAPI:
    """Test cases for admin API."""

    @pytest.mark.asyncio
    async def test_admin_dashboard_stats(self, client: TestClient, superuser_token_headers):
        """Test getting admin dashboard statistics."""
        with patch('app.api.admin.AdminAPI.get_dashboard_stats') as mock_stats:
            mock_stats.return_value = {
                "total_users": 1000,
                "total_advertisements": 500,
                "total_blogs": 50,
                "pending_advertisements": 25,
                "active_users_today": 100,
                "revenue_today": 1000000,
                "revenue_this_month": 30000000
            }
            
            response = client.get(
                "/api/admin/dashboard/stats",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total_users"] == 1000
            assert data["total_advertisements"] == 500

    @pytest.mark.asyncio
    async def test_get_all_users(self, client: TestClient, superuser_token_headers):
        """Test getting all users with pagination."""
        with patch('app.api.admin.AdminAPI.get_all_users') as mock_users:
            mock_users.return_value = {
                "users": [
                    {
                        "id": 1,
                        "first_name": "John",
                        "last_name": "Doe",
                        "email": "<EMAIL>",
                        "phone_number": "09123456789",
                        "is_active": True,
                        "created_at": "2023-01-01T00:00:00Z"
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10
            }
            
            response = client.get(
                "/api/admin/users?page=1&per_page=10",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "users" in data
            assert data["total"] == 1

    @pytest.mark.asyncio
    async def test_get_user_detail(self, client: TestClient, superuser_token_headers, test_user):
        """Test getting detailed user information."""
        with patch('app.api.admin.AdminAPI.get_user_detail') as mock_detail:
            mock_detail.return_value = {
                "id": test_user.id,
                "first_name": test_user.first_name,
                "last_name": test_user.last_name,
                "email": test_user.email,
                "phone_number": test_user.phone_number,
                "is_active": test_user.is_active,
                "advertisements_count": 5,
                "wallet_balance": 50000,
                "last_login": "2023-01-01T00:00:00Z"
            }
            
            response = client.get(
                f"/api/admin/users/{test_user.id}",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == test_user.id

    @pytest.mark.asyncio
    async def test_update_user_status(self, client: TestClient, superuser_token_headers, test_user):
        """Test updating user status (activate/deactivate)."""
        update_data = {
            "is_active": False,
            "reason": "Violation of terms"
        }
        
        with patch('app.api.admin.AdminAPI.update_user_status') as mock_update:
            mock_update.return_value = {
                "message": "User status updated successfully",
                "user_id": test_user.id,
                "is_active": False
            }
            
            response = client.put(
                f"/api/admin/users/{test_user.id}/status",
                json=update_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["is_active"] is False

    @pytest.mark.asyncio
    async def test_get_pending_advertisements(self, client: TestClient, superuser_token_headers):
        """Test getting pending advertisements for review."""
        with patch('app.api.admin.AdminAPI.get_pending_advertisements') as mock_pending:
            mock_pending.return_value = {
                "advertisements": [
                    {
                        "id": 1,
                        "title": "Pending Ad",
                        "status": "pending",
                        "created_at": "2023-01-01T00:00:00Z",
                        "user": {
                            "id": 1,
                            "name": "John Doe"
                        }
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/admin/advertisements/pending",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "advertisements" in data
            assert data["advertisements"][0]["status"] == "pending"

    @pytest.mark.asyncio
    async def test_approve_advertisement(self, client: TestClient, superuser_token_headers, test_advertisement):
        """Test approving an advertisement."""
        approval_data = {
            "status": "approved",
            "admin_notes": "Advertisement approved"
        }
        
        with patch('app.api.admin.AdminAPI.update_advertisement_status') as mock_approve:
            mock_approve.return_value = {
                "message": "Advertisement approved successfully",
                "advertisement_id": test_advertisement.id,
                "status": "approved"
            }
            
            response = client.put(
                f"/api/admin/advertisements/{test_advertisement.id}/status",
                json=approval_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "approved"

    @pytest.mark.asyncio
    async def test_reject_advertisement(self, client: TestClient, superuser_token_headers, test_advertisement):
        """Test rejecting an advertisement."""
        rejection_data = {
            "status": "rejected",
            "admin_notes": "Inappropriate content",
            "rejection_reason": "content_violation"
        }
        
        with patch('app.api.admin.AdminAPI.update_advertisement_status') as mock_reject:
            mock_reject.return_value = {
                "message": "Advertisement rejected",
                "advertisement_id": test_advertisement.id,
                "status": "rejected"
            }
            
            response = client.put(
                f"/api/admin/advertisements/{test_advertisement.id}/status",
                json=rejection_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "rejected"

    @pytest.mark.asyncio
    async def test_get_flagged_content(self, client: TestClient, superuser_token_headers):
        """Test getting flagged content for review."""
        with patch('app.api.admin.AdminAPI.get_flagged_content') as mock_flagged:
            mock_flagged.return_value = {
                "flagged_items": [
                    {
                        "id": 1,
                        "content_type": "advertisement",
                        "content_id": 1,
                        "reason": "inappropriate_content",
                        "reporter": {
                            "id": 2,
                            "name": "Reporter User"
                        },
                        "created_at": "2023-01-01T00:00:00Z"
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/admin/flagged-content",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "flagged_items" in data

    @pytest.mark.asyncio
    async def test_resolve_flagged_content(self, client: TestClient, superuser_token_headers):
        """Test resolving flagged content."""
        flag_id = 1
        resolution_data = {
            "action": "remove_content",
            "admin_notes": "Content removed due to violation"
        }
        
        with patch('app.api.admin.AdminAPI.resolve_flagged_content') as mock_resolve:
            mock_resolve.return_value = {
                "message": "Flagged content resolved",
                "flag_id": flag_id,
                "action": "remove_content"
            }
            
            response = client.put(
                f"/api/admin/flagged-content/{flag_id}/resolve",
                json=resolution_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_get_system_analytics(self, client: TestClient, superuser_token_headers):
        """Test getting system analytics."""
        with patch('app.api.admin.AdminAPI.get_system_analytics') as mock_analytics:
            mock_analytics.return_value = {
                "user_registrations": {
                    "today": 10,
                    "this_week": 50,
                    "this_month": 200
                },
                "advertisement_stats": {
                    "created_today": 25,
                    "approved_today": 20,
                    "rejected_today": 3
                },
                "revenue_stats": {
                    "today": 1000000,
                    "this_month": 30000000
                }
            }
            
            response = client.get(
                "/api/admin/analytics",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "user_registrations" in data
            assert "advertisement_stats" in data

    @pytest.mark.asyncio
    async def test_export_users_data(self, client: TestClient, superuser_token_headers):
        """Test exporting users data."""
        export_params = {
            "format": "csv",
            "date_from": "2023-01-01",
            "date_to": "2023-12-31"
        }
        
        with patch('app.api.admin.AdminAPI.export_users_data') as mock_export:
            mock_export.return_value = {
                "download_url": "/api/admin/downloads/users_export_123.csv",
                "file_size": "2.5MB",
                "record_count": 1000
            }
            
            response = client.post(
                "/api/admin/export/users",
                json=export_params,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "download_url" in data

    @pytest.mark.asyncio
    async def test_create_admin_user(self, client: TestClient, superuser_token_headers):
        """Test creating a new admin user."""
        admin_data = {
            "first_name": "Admin",
            "last_name": "User",
            "email": "<EMAIL>",
            "phone_number": "09123456789",
            "admin_type": "moderator",
            "permissions": ["manage_advertisements", "manage_users"]
        }
        
        with patch('app.api.admin.AdminAPI.create_admin_user') as mock_create:
            mock_create.return_value = {
                "id": 2,
                "first_name": "Admin",
                "last_name": "User",
                "email": "<EMAIL>",
                "admin_type": "moderator"
            }
            
            response = client.post(
                "/api/admin/admins",
                json=admin_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["admin_type"] == "moderator"

    @pytest.mark.asyncio
    async def test_get_admin_activity_log(self, client: TestClient, superuser_token_headers):
        """Test getting admin activity log."""
        with patch('app.api.admin.AdminAPI.get_admin_activity_log') as mock_log:
            mock_log.return_value = {
                "activities": [
                    {
                        "id": 1,
                        "admin_id": 1,
                        "action": "approve_advertisement",
                        "target_type": "advertisement",
                        "target_id": 1,
                        "timestamp": "2023-01-01T00:00:00Z"
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/admin/activity-log",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "activities" in data

    @pytest.mark.asyncio
    async def test_bulk_user_operations(self, client: TestClient, superuser_token_headers):
        """Test bulk operations on users."""
        bulk_data = {
            "user_ids": [1, 2, 3],
            "action": "deactivate",
            "reason": "Bulk deactivation for cleanup"
        }
        
        with patch('app.api.admin.AdminAPI.bulk_user_operations') as mock_bulk:
            mock_bulk.return_value = {
                "message": "Bulk operation completed",
                "affected_users": 3,
                "action": "deactivate"
            }
            
            response = client.post(
                "/api/admin/users/bulk",
                json=bulk_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["affected_users"] == 3

    @pytest.mark.asyncio
    async def test_system_settings_update(self, client: TestClient, superuser_token_headers):
        """Test updating system settings."""
        settings_data = {
            "maintenance_mode": False,
            "max_ads_per_user": 10,
            "auto_approve_ads": False,
            "email_notifications": True
        }
        
        with patch('app.api.admin.AdminAPI.update_system_settings') as mock_settings:
            mock_settings.return_value = {
                "message": "System settings updated",
                "settings": settings_data
            }
            
            response = client.put(
                "/api/admin/settings",
                json=settings_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_unauthorized_admin_access(self, client: TestClient, normal_user_token_headers):
        """Test unauthorized access to admin endpoints."""
        response = client.get(
            "/api/admin/dashboard/stats",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.asyncio
    async def test_admin_search_users(self, client: TestClient, superuser_token_headers):
        """Test searching users in admin panel."""
        search_params = {
            "q": "john",
            "status": "active",
            "date_from": "2023-01-01",
            "date_to": "2023-12-31"
        }
        
        with patch('app.api.admin.AdminAPI.search_users') as mock_search:
            mock_search.return_value = {
                "users": [
                    {
                        "id": 1,
                        "first_name": "John",
                        "last_name": "Doe",
                        "email": "<EMAIL>"
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/admin/users/search",
                params=search_params,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["users"]) == 1
