"""
Tests for the security API.
"""

import pytest
import pyotp
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from fastapi.testclient import TestClient

from app.core.security import (
    generate_api_key,
    generate_totp_secret,
    hash_api_key,
    verify_api_key,
    verify_totp,
)
from app.models.security import APIKeyModel, TwoFactorAuthModel, LoginAttemptModel


@pytest.fixture
def user_token_headers(client: TestClient) -> dict[str, str]:
    """Get user token headers for authentication"""
    login_data = {
        "username": "<EMAIL>",
        "password": "userpassword",
    }
    with patch("app.dependencies.auth.get_current_user", return_value=MagicMock(is_active=True)):
        r = client.post("/api/auth/login", data=login_data)
        tokens = r.json()
        return {"Authorization": f"Bearer {tokens['access_token']}"}


@pytest.fixture
def mock_api_key():
    """Mock API key"""
    return "sk_1234567890abcdef1234567890abcdef"


@pytest.fixture
def mock_totp_secret():
    """Mock TOTP secret"""
    return "JBSWY3DPEHPK3PXP"


@pytest.fixture
def mock_totp_token(mock_totp_secret):
    """Mock TOTP token"""
    totp = pyotp.TOTP(mock_totp_secret)
    return totp.now()


class TestAPIKeys:
    """Tests for API keys"""
    
    def test_create_api_key(self, client: TestClient, user_token_headers: dict[str, str], mock_api_key):
        """Test creating an API key"""
        with patch("app.core.security.generate_api_key", return_value=mock_api_key):
            response = client.post(
                "/api/security/api-keys",
                headers=user_token_headers,
                json={"name": "Test API Key"}
            )
            
            assert response.status_code == 201
            data = response.json()
            assert data["name"] == "Test API Key"
            assert data["key"] == mock_api_key
            assert data["is_active"] is True
    
    def test_get_api_keys(self, client: TestClient, user_token_headers: dict[str, str]):
        """Test getting API keys"""
        with patch("app.api.security.SecurityAPI.get_api_keys") as mock_get_api_keys:
            mock_get_api_keys.return_value = {
                "items": [
                    {
                        "id": 1,
                        "name": "Test API Key",
                        "is_active": True,
                        "created_at": datetime.now().isoformat(),
                        "last_used_at": None,
                        "expires_at": None
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/security/api-keys",
                headers=user_token_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["items"]) == 1
            assert data["items"][0]["name"] == "Test API Key"
            assert data["total"] == 1
    
    def test_revoke_api_key(self, client: TestClient, user_token_headers: dict[str, str]):
        """Test revoking an API key"""
        with patch("app.api.security.SecurityAPI.revoke_api_key") as mock_revoke_api_key:
            mock_revoke_api_key.return_value = None
            
            response = client.delete(
                "/api/security/api-keys/1",
                headers=user_token_headers
            )
            
            assert response.status_code == 204
            mock_revoke_api_key.assert_called_once()


class TestTwoFactorAuth:
    """Tests for two-factor authentication"""
    
    def test_setup_two_factor_auth(self, client: TestClient, user_token_headers: dict[str, str], mock_totp_secret):
        """Test setting up two-factor authentication"""
        with patch("app.core.security.generate_totp_secret", return_value=mock_totp_secret):
            response = client.post(
                "/api/security/2fa/setup",
                headers=user_token_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["secret"] == mock_totp_secret
            assert "uri" in data
    
    def test_enable_two_factor_auth(self, client: TestClient, user_token_headers: dict[str, str], mock_totp_token):
        """Test enabling two-factor authentication"""
        with patch("app.core.security.verify_totp", return_value=True):
            response = client.post(
                "/api/security/2fa/enable",
                headers=user_token_headers,
                json={"token": mock_totp_token}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_disable_two_factor_auth(self, client: TestClient, user_token_headers: dict[str, str]):
        """Test disabling two-factor authentication"""
        with patch("app.api.security.SecurityAPI.disable_two_factor_auth") as mock_disable:
            mock_disable.return_value = True
            
            response = client.post(
                "/api/security/2fa/disable",
                headers=user_token_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_get_two_factor_auth_status(self, client: TestClient, user_token_headers: dict[str, str]):
        """Test getting two-factor authentication status"""
        with patch("app.api.security.SecurityAPI.get_two_factor_auth_status") as mock_get_status:
            mock_get_status.return_value = {
                "is_enabled": True,
                "created_at": datetime.now().isoformat(),
                "last_verified_at": datetime.now().isoformat()
            }
            
            response = client.get(
                "/api/security/2fa/status",
                headers=user_token_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["is_enabled"] is True
            assert "created_at" in data
            assert "last_verified_at" in data
    
    def test_verify_two_factor_auth(self, client: TestClient, user_token_headers: dict[str, str], mock_totp_token):
        """Test verifying two-factor authentication"""
        with patch("app.core.security.verify_totp", return_value=True):
            response = client.post(
                "/api/security/2fa/verify",
                headers=user_token_headers,
                json={"token": mock_totp_token}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestLoginAttempts:
    """Tests for login attempts"""
    
    def test_get_login_attempts(self, client: TestClient, user_token_headers: dict[str, str]):
        """Test getting login attempts"""
        with patch("app.api.security.SecurityAPI.get_login_attempts") as mock_get_attempts:
            mock_get_attempts.return_value = {
                "items": [
                    {
                        "id": 1,
                        "ip_address": "127.0.0.1",
                        "user_agent": "Mozilla/5.0",
                        "success": True,
                        "created_at": datetime.now().isoformat()
                    },
                    {
                        "id": 2,
                        "ip_address": "127.0.0.1",
                        "user_agent": "Mozilla/5.0",
                        "success": False,
                        "created_at": datetime.now().isoformat()
                    }
                ],
                "total": 2
            }
            
            response = client.get(
                "/api/security/login-attempts",
                headers=user_token_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["items"]) == 2
            assert data["total"] == 2
