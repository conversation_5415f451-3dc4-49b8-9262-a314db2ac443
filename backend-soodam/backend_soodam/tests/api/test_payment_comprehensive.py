"""
Comprehensive tests for payment API endpoints and Iranian bank gateways.
"""

import pytest
import uuid
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from fastapi import status

from app.services.iranian_payment import (
    PaymentGatewayFactory, ZarinpalGateway, MellatGateway,
    SamanGateway, ParsianGateway
)
from app.models import UserPayment


class TestPaymentAPI:
    """Test cases for payment API."""

    @pytest.mark.asyncio
    async def test_initialize_payment_success(self, client: TestClient, normal_user_token_headers, mock_payment_gateway):
        """Test successful payment initialization."""
        payment_data = {
            "amount": 10000,
            "bank_name": "zarinpal",
            "description": "Test payment",
            "mobile_number": "***********"
        }
        
        response = client.post(
            "/api/payment/initialize",
            json=payment_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "redirect_url" in data
        assert "reference_number" in data
        assert "tracking_code" in data

    @pytest.mark.asyncio
    async def test_initialize_payment_invalid_bank(self, client: TestClient, normal_user_token_headers):
        """Test payment initialization with invalid bank."""
        payment_data = {
            "amount": 10000,
            "bank_name": "invalid_bank",
            "description": "Test payment"
        }
        
        response = client.post(
            "/api/payment/initialize",
            json=payment_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_initialize_payment_invalid_amount(self, client: TestClient, normal_user_token_headers):
        """Test payment initialization with invalid amount."""
        payment_data = {
            "amount": -1000,  # Negative amount
            "bank_name": "zarinpal",
            "description": "Test payment"
        }
        
        response = client.post(
            "/api/payment/initialize",
            json=payment_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_payment_callback_success(self, client: TestClient, mock_payment_gateway):
        """Test successful payment callback."""
        callback_data = {
            "tracking_code": "test-tracking",
            "status": 100,  # Success status
            "reference_id": "test-ref-id"
        }
        
        response = client.post(
            "/api/payment/callback",
            json=callback_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

    @pytest.mark.asyncio
    async def test_payment_callback_failure(self, client: TestClient):
        """Test failed payment callback."""
        callback_data = {
            "tracking_code": "test-tracking",
            "status": -1,  # Failure status
            "reference_id": None
        }
        
        with patch('app.services.iranian_payment.PaymentGatewayFactory.create_gateway') as mock_factory:
            gateway_mock = AsyncMock()
            gateway_mock.verify_payment.return_value = {
                "success": False,
                "error_code": -1,
                "error_message": "Payment failed"
            }
            mock_factory.return_value = gateway_mock
            
            response = client.post(
                "/api/payment/callback",
                json=callback_data
            )
            
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_get_payment_history(self, client: TestClient, normal_user_token_headers):
        """Test getting user payment history."""
        with patch('app.api.payment.PaymentAPI.get_payment_history') as mock_history:
            mock_history.return_value = {
                "payments": [
                    {
                        "id": 1,
                        "amount": 10000,
                        "status": "completed",
                        "created_at": "2023-01-01T00:00:00Z"
                    }
                ],
                "total": 1
            }
            
            response = client.get(
                "/api/payment/history",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "payments" in data
            assert len(data["payments"]) == 1

    @pytest.mark.asyncio
    async def test_get_payment_status(self, client: TestClient, normal_user_token_headers):
        """Test getting payment status by tracking code."""
        tracking_code = "test-tracking"
        
        with patch('app.api.payment.PaymentAPI.get_payment_status') as mock_status:
            mock_status.return_value = {
                "tracking_code": tracking_code,
                "status": "completed",
                "amount": 10000,
                "reference_id": "test-ref"
            }
            
            response = client.get(
                f"/api/payment/status/{tracking_code}",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["tracking_code"] == tracking_code
            assert data["status"] == "completed"


class TestZarinpalGateway:
    """Test cases for Zarinpal payment gateway."""

    @pytest.mark.asyncio
    async def test_zarinpal_request_payment_success(self):
        """Test successful payment request with Zarinpal."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "Status": 100,
                "Authority": "test-authority"
            }
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = ZarinpalGateway(
                merchant_id="test-merchant",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.request_payment(
                amount=10000,
                description="Test payment",
                mobile="***********"
            )
            
            assert result["success"] is True
            assert "redirect_url" in result
            assert "tracking_code" in result

    @pytest.mark.asyncio
    async def test_zarinpal_verify_payment_success(self):
        """Test successful payment verification with Zarinpal."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful verification response
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "Status": 100,
                "RefID": "test-ref-id"
            }
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = ZarinpalGateway(
                merchant_id="test-merchant",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.verify_payment(
                tracking_code="test-tracking",
                reference_id="10000"
            )
            
            assert result["success"] is True
            assert result["reference_id"] == "test-ref-id"

    @pytest.mark.asyncio
    async def test_zarinpal_request_payment_failure(self):
        """Test failed payment request with Zarinpal."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock failure response
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "Status": -1,
                "Authority": ""
            }
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = ZarinpalGateway(
                merchant_id="test-merchant",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.request_payment(
                amount=10000,
                description="Test payment"
            )
            
            assert result["success"] is False
            assert "error_code" in result


class TestMellatGateway:
    """Test cases for Mellat payment gateway."""

    @pytest.mark.asyncio
    async def test_mellat_request_payment_success(self):
        """Test successful payment request with Mellat."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.text = "0,test-ref-id"
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = MellatGateway(
                terminal_id="test-terminal",
                username="test-user",
                password="test-pass",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.request_payment(
                amount=10000,
                description="Test payment"
            )
            
            assert result["success"] is True
            assert "redirect_url" in result

    @pytest.mark.asyncio
    async def test_mellat_verify_payment_success(self):
        """Test successful payment verification with Mellat."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful verification response
            mock_response = AsyncMock()
            mock_response.text = "0"
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = MellatGateway(
                terminal_id="test-terminal",
                username="test-user",
                password="test-pass",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.verify_payment(
                tracking_code="test-tracking",
                reference_id="test-ref"
            )
            
            assert result["success"] is True


class TestPaymentGatewayFactory:
    """Test cases for payment gateway factory."""

    def test_create_zarinpal_gateway(self):
        """Test creating Zarinpal gateway."""
        with patch('app.services.iranian_payment.ZarinpalGateway') as mock_zarinpal:
            mock_zarinpal.return_value = MagicMock()
            
            gateway = PaymentGatewayFactory.create_gateway(
                bank_name="zarinpal",
                callback_url="https://example.com/callback"
            )
            
            assert mock_zarinpal.called

    def test_create_mellat_gateway(self):
        """Test creating Mellat gateway."""
        with patch('app.services.iranian_payment.MellatGateway') as mock_mellat:
            mock_mellat.return_value = MagicMock()
            
            gateway = PaymentGatewayFactory.create_gateway(
                bank_name="mellat",
                callback_url="https://example.com/callback"
            )
            
            assert mock_mellat.called

    def test_create_unsupported_gateway(self):
        """Test creating unsupported gateway."""
        with pytest.raises(ValueError):
            PaymentGatewayFactory.create_gateway(
                bank_name="unsupported",
                callback_url="https://example.com/callback"
            )

    def test_gateway_configuration_validation(self):
        """Test gateway configuration validation."""
        # Test missing required configuration
        with pytest.raises(ValueError):
            ZarinpalGateway(
                merchant_id="",  # Empty merchant ID
                callback_url="https://example.com/callback"
            )

    @pytest.mark.asyncio
    async def test_payment_amount_validation(self):
        """Test payment amount validation across gateways."""
        gateway = ZarinpalGateway(
            merchant_id="test-merchant",
            callback_url="https://example.com/callback"
        )
        
        # Test minimum amount validation
        with pytest.raises(ValueError):
            await gateway.request_payment(
                amount=0,  # Invalid amount
                description="Test payment"
            )

    @pytest.mark.asyncio
    async def test_payment_timeout_handling(self):
        """Test payment timeout handling."""
        with patch('httpx.AsyncClient') as mock_client:
            # Mock timeout exception
            mock_client_instance = AsyncMock()
            mock_client_instance.post.side_effect = Exception("Timeout")
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            gateway = ZarinpalGateway(
                merchant_id="test-merchant",
                callback_url="https://example.com/callback"
            )
            
            result = await gateway.request_payment(
                amount=10000,
                description="Test payment"
            )
            
            assert result["success"] is False
            assert "error" in result
