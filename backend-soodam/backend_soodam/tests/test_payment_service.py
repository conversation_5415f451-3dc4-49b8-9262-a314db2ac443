"""
Tests for the Iranian payment service.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.iranian_payment import (
    IranianBankGateway,
    ZarinpalGateway,
    MellatGateway,
    PaymentGatewayFactory
)


@pytest.mark.asyncio
async def test_zarinpal_request_payment_success():
    """Test successful payment request with Zarinpal"""
    # Mock the httpx client
    with patch('httpx.AsyncClient') as mock_client:
        # Set up the mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "Status": 100,
            "Authority": "test-authority"
        }
        
        # Set up the mock client
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create the gateway
        gateway = ZarinpalGateway(
            merchant_id="test-merchant-id",
            callback_url="https://example.com/callback"
        )
        
        # Request payment
        result = await gateway.request_payment(
            amount=10000,
            description="Test payment",
            mobile="09123456789"
        )
        
        # Check the result
        assert result["success"] is True
        assert "redirect_url" in result
        assert "tracking_code" in result
        assert "reference_number" in result


@pytest.mark.asyncio
async def test_zarinpal_request_payment_failure():
    """Test failed payment request with Zarinpal"""
    # Mock the httpx client
    with patch('httpx.AsyncClient') as mock_client:
        # Set up the mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "Status": -1,
            "Authority": ""
        }
        
        # Set up the mock client
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create the gateway
        gateway = ZarinpalGateway(
            merchant_id="test-merchant-id",
            callback_url="https://example.com/callback"
        )
        
        # Request payment
        result = await gateway.request_payment(
            amount=10000,
            description="Test payment",
            mobile="09123456789"
        )
        
        # Check the result
        assert result["success"] is False
        assert "error_code" in result
        assert "error_message" in result


@pytest.mark.asyncio
async def test_zarinpal_verify_payment_success():
    """Test successful payment verification with Zarinpal"""
    # Mock the httpx client
    with patch('httpx.AsyncClient') as mock_client:
        # Set up the mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "Status": 100,
            "RefID": "test-ref-id"
        }
        
        # Set up the mock client
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create the gateway
        gateway = ZarinpalGateway(
            merchant_id="test-merchant-id",
            callback_url="https://example.com/callback"
        )
        
        # Verify payment
        result = await gateway.verify_payment(
            tracking_code="test-tracking-code",
            reference_id="10000"
        )
        
        # Check the result
        assert result["success"] is True
        assert result["reference_id"] == "test-ref-id"
        assert "message" in result


@pytest.mark.asyncio
async def test_zarinpal_verify_payment_failure():
    """Test failed payment verification with Zarinpal"""
    # Mock the httpx client
    with patch('httpx.AsyncClient') as mock_client:
        # Set up the mock response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "Status": -1,
            "RefID": ""
        }
        
        # Set up the mock client
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create the gateway
        gateway = ZarinpalGateway(
            merchant_id="test-merchant-id",
            callback_url="https://example.com/callback"
        )
        
        # Verify payment
        result = await gateway.verify_payment(
            tracking_code="test-tracking-code",
            reference_id="10000"
        )
        
        # Check the result
        assert result["success"] is False
        assert "error_code" in result
        assert "error_message" in result


@pytest.mark.asyncio
async def test_payment_gateway_factory():
    """Test the payment gateway factory"""
    # Test Zarinpal gateway
    with patch('app.services.iranian_payment.ZarinpalGateway') as mock_zarinpal:
        mock_zarinpal.return_value = MagicMock()
        
        gateway = PaymentGatewayFactory.create_gateway(
            bank_name="zarinpal",
            callback_url="https://example.com/callback"
        )
        
        assert mock_zarinpal.called
        
    # Test Mellat gateway
    with patch('app.services.iranian_payment.MellatGateway') as mock_mellat:
        mock_mellat.return_value = MagicMock()
        
        gateway = PaymentGatewayFactory.create_gateway(
            bank_name="mellat",
            callback_url="https://example.com/callback"
        )
        
        assert mock_mellat.called
        
    # Test unsupported gateway
    with pytest.raises(ValueError):
        PaymentGatewayFactory.create_gateway(
            bank_name="unsupported",
            callback_url="https://example.com/callback"
        )
