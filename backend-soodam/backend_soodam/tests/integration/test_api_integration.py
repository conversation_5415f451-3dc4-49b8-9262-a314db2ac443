"""
Integration tests for the API endpoints.
"""

import os
import pytest
import json
from datetime import datetime, timedelta
from typing import Dict, Generator, List

import jwt
from fastapi.testclient import TestClient

from app.core.security import JWT_SECRET_KEY, JWT_ALGORITHM
from app.models import CustomUserModel
from app.models.advertisement import AdvertisementModel
from app.models.security import APIKeyModel, TwoFactorAuthModel
from app.enums.status import AdvertisementStatus


@pytest.fixture
def db_setup():
    """Set up the database for testing"""
    # This would normally create test data in the database
    # For now, we'll just use mocks
    yield


@pytest.fixture
def test_user() -> CustomUserModel:
    """Create a test user"""
    user = CustomUserModel(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        is_active=True
    )
    user.set_password("testpassword")
    user.save()
    yield user
    user.delete()


@pytest.fixture
def test_admin() -> CustomUserModel:
    """Create a test admin user"""
    admin = CustomUserModel(
        email="<EMAIL>",
        first_name="Admin",
        last_name="User",
        is_active=True,
        is_staff=True,
        is_superuser=True
    )
    admin.set_password("adminpassword")
    admin.save()
    yield admin
    admin.delete()


@pytest.fixture
def test_advertisement(test_user) -> AdvertisementModel:
    """Create a test advertisement"""
    adv = AdvertisementModel(
        title="Test Advertisement",
        description="This is a test advertisement",
        status=AdvertisementStatus.PENDING,
        user=test_user
    )
    adv.save()
    yield adv
    adv.delete()


@pytest.fixture
def test_api_key(test_user) -> APIKeyModel:
    """Create a test API key"""
    from app.core.security import generate_api_key, hash_api_key
    
    api_key_value = generate_api_key()
    api_key = APIKeyModel(
        name="Test API Key",
        key_hash=hash_api_key(api_key_value),
        user=test_user,
        is_active=True
    )
    api_key.save()
    
    # Return both the model and the raw key
    yield api_key, api_key_value
    
    api_key.delete()


@pytest.fixture
def test_2fa(test_user) -> TwoFactorAuthModel:
    """Create a test 2FA setup"""
    from app.core.security import generate_totp_secret
    
    secret = generate_totp_secret()
    two_factor = TwoFactorAuthModel(
        user=test_user,
        secret=secret,
        is_enabled=True
    )
    two_factor.save()
    
    yield two_factor
    
    two_factor.delete()


@pytest.fixture
def user_token(test_user) -> str:
    """Create a JWT token for the test user"""
    payload = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return token


@pytest.fixture
def admin_token(test_admin) -> str:
    """Create a JWT token for the test admin"""
    payload = {
        "sub": str(test_admin.id),
        "email": test_admin.email,
        "is_superuser": True,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return token


@pytest.fixture
def user_headers(user_token) -> Dict[str, str]:
    """Create headers with user token"""
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def admin_headers(admin_token) -> Dict[str, str]:
    """Create headers with admin token"""
    return {"Authorization": f"Bearer {admin_token}"}


class TestAuthIntegration:
    """Integration tests for authentication endpoints"""
    
    def test_login(self, client: TestClient, test_user):
        """Test login endpoint"""
        response = client.post(
            "/api/auth/login",
            data={"username": test_user.email, "password": "testpassword"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self, client: TestClient, test_user):
        """Test login with invalid credentials"""
        response = client.post(
            "/api/auth/login",
            data={"username": test_user.email, "password": "wrongpassword"}
        )
        
        assert response.status_code == 401
    
    def test_refresh_token(self, client: TestClient, user_token):
        """Test refresh token endpoint"""
        # This would normally use a refresh token, but we'll use the access token for simplicity
        response = client.post(
            "/api/auth/refresh",
            json={"refresh_token": user_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data


class TestUserIntegration:
    """Integration tests for user endpoints"""
    
    def test_get_current_user(self, client: TestClient, user_headers, test_user):
        """Test getting the current user"""
        response = client.get(
            "/api/user/me",
            headers=user_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["first_name"] == test_user.first_name
        assert data["last_name"] == test_user.last_name
    
    def test_update_user(self, client: TestClient, user_headers, test_user):
        """Test updating the user profile"""
        response = client.put(
            "/api/user/me",
            headers=user_headers,
            json={"first_name": "Updated", "last_name": "User"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["first_name"] == "Updated"
        assert data["last_name"] == "User"


class TestSecurityIntegration:
    """Integration tests for security endpoints"""
    
    def test_create_api_key(self, client: TestClient, user_headers):
        """Test creating an API key"""
        response = client.post(
            "/api/security/api-keys",
            headers=user_headers,
            json={"name": "Integration Test API Key"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Integration Test API Key"
        assert "key" in data
        assert data["is_active"] is True
    
    def test_get_api_keys(self, client: TestClient, user_headers, test_api_key):
        """Test getting API keys"""
        response = client.get(
            "/api/security/api-keys",
            headers=user_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert len(data["items"]) > 0
    
    def test_setup_2fa(self, client: TestClient, user_headers):
        """Test setting up 2FA"""
        response = client.post(
            "/api/security/2fa/setup",
            headers=user_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "secret" in data
        assert "uri" in data


class TestAdminIntegration:
    """Integration tests for admin endpoints"""
    
    def test_get_admin_dashboard_stats(self, client: TestClient, admin_headers):
        """Test getting admin dashboard stats"""
        response = client.get(
            "/api/admin/dashboard/stats",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "total_users" in data
        assert "total_advertisements" in data
    
    def test_get_all_advertisements(self, client: TestClient, admin_headers, test_advertisement):
        """Test getting all advertisements"""
        response = client.get(
            "/api/admin/advertisements",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) > 0
        assert data[0]["title"] == test_advertisement.title
    
    def test_approve_advertisement(self, client: TestClient, admin_headers, test_advertisement):
        """Test approving an advertisement"""
        response = client.post(
            f"/api/admin/advertisement/{test_advertisement.id}/approve",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_advertisement.id
        assert data["status"] == AdvertisementStatus.APPROVED
