"""
Integration tests for complete user workflows.
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status
import json


class TestUserRegistrationWorkflow:
    """Test complete user registration and verification workflow."""

    @pytest.mark.asyncio
    async def test_complete_user_registration_flow(self, client: TestClient, mock_sms_service):
        """Test complete user registration flow from phone verification to profile setup."""
        phone_number = "09123456789"
        
        # Step 1: Request verification code
        with patch('app.models.LogAccess.objects.acreate') as mock_create:
            mock_create.return_value = AsyncMock()
            
            response = client.post(
                "/api/auth/get_ver_code",
                json={"phone_number": phone_number}
            )
            
            assert response.status_code == status.HTTP_200_OK
            mock_sms_service.send_sms.assert_called_once()

        # Step 2: Verify code and get token
        with patch('app.models.LogAccess.objects.filter') as mock_filter:
            mock_log_access = AsyncMock()
            mock_log_access.try_to_verify = 1
            mock_log_access.user_token = "test-token"
            mock_filter.return_value.afirst.return_value = mock_log_access
            
            response = client.post(
                "/api/auth/verify",
                json={
                    "phone_number": phone_number,
                    "verify_code": "123456"
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            token = data["token"]
            headers = {"Authorization": f"Bearer {token}"}

        # Step 3: Complete user profile
        with patch('app.api.user.RegisterUserAPI.edit_user_info') as mock_update:
            mock_update.return_value = {
                "message": "Profile updated successfully"
            }
            
            profile_data = {
                "id": 1,
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "birthday": "1990-01-01",
                "gender": "male"
            }
            
            response = client.put(
                "/api/user/profile",
                json=profile_data,
                headers=headers
            )
            
            assert response.status_code == status.HTTP_200_OK

        # Step 4: Add user address
        with patch('app.api.user.RegisterUserAPI.create_address') as mock_address:
            mock_address.return_value = {
                "id": 1,
                "address_line": "Test Address",
                "is_primary": True
            }
            
            address_data = {
                "province_id": 1,
                "city_id": 1,
                "address_line": "Test Address",
                "postal_code": "1234567890",
                "is_primary": True
            }
            
            response = client.post(
                "/api/user/address",
                json=address_data,
                headers=headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED


class TestAdvertisementWorkflow:
    """Test complete advertisement creation and management workflow."""

    @pytest.mark.asyncio
    async def test_complete_advertisement_workflow(self, client: TestClient, normal_user_token_headers):
        """Test complete advertisement workflow from creation to publication."""
        
        # Step 1: Get metadata for advertisement creation
        with patch('app.api.advertisement.AdvertisementAPI.get_meta_data') as mock_meta:
            mock_meta.return_value = {
                "categories": [{"id": 1, "name": "املاک"}],
                "provinces": [{"id": 1, "name": "تهران"}]
            }
            
            response = client.get("/api/advertisements/meta")
            assert response.status_code == status.HTTP_200_OK

        # Step 2: Get features for selected category
        with patch('app.api.advertisement.AdvertisementAPI.get_features_by_category') as mock_features:
            mock_features.return_value = {
                "boolean_attributes": [{"id": 1, "name": "پارکینگ"}],
                "choice_attributes": [{"id": 1, "name": "نوع ملک"}]
            }
            
            response = client.post(
                "/api/advertisements/features",
                json={"main_category_id": 1}
            )
            assert response.status_code == status.HTTP_200_OK

        # Step 3: Create advertisement
        with patch('app.api.advertisement.AdvertisementAPI.create_advertisement') as mock_create:
            mock_create.return_value = {
                "id": 1,
                "title": "Test Advertisement",
                "status": "pending"
            }
            
            ad_data = {
                "title": "Test Advertisement",
                "description": "Test Description",
                "main_category_id": 1,
                "sub_category_id": 1,
                "province_id": 1,
                "city_id": 1,
                "price": 1000000
            }
            
            response = client.post(
                "/api/advertisements/",
                json=ad_data,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            ad_id = response.json()["id"]

        # Step 4: Upload images
        with patch('app.api.advertisement.AdvertisementAPI.upload_images') as mock_upload:
            mock_upload.return_value = {
                "uploaded_images": [{"id": 1, "url": "/media/ads/test.jpg"}]
            }
            
            files = {"files": ("test.jpg", b"fake image", "image/jpeg")}
            response = client.post(
                f"/api/advertisements/{ad_id}/images",
                files=files,
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

        # Step 5: Check advertisement status
        with patch('app.api.advertisement.AdvertisementAPI.get_advertisement_detail') as mock_detail:
            mock_detail.return_value = {
                "id": ad_id,
                "title": "Test Advertisement",
                "status": "pending"
            }
            
            response = client.get(f"/api/advertisements/{ad_id}")
            assert response.status_code == status.HTTP_200_OK


class TestPaymentWorkflow:
    """Test complete payment workflow."""

    @pytest.mark.asyncio
    async def test_complete_payment_workflow(self, client: TestClient, normal_user_token_headers, mock_payment_gateway):
        """Test complete payment workflow from initialization to verification."""
        
        # Step 1: Initialize payment
        payment_data = {
            "amount": 10000,
            "bank_name": "zarinpal",
            "description": "Test payment"
        }
        
        response = client.post(
            "/api/payment/initialize",
            json=payment_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        tracking_code = data["tracking_code"]
        redirect_url = data["redirect_url"]
        
        # Verify redirect URL is provided
        assert redirect_url.startswith("https://")

        # Step 2: Simulate user completing payment at bank gateway
        # (This would happen externally)

        # Step 3: Handle payment callback
        callback_data = {
            "tracking_code": tracking_code,
            "status": 100,  # Success status
            "reference_id": "test-ref-id"
        }
        
        response = client.post(
            "/api/payment/callback",
            json=callback_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

        # Step 4: Check payment status
        with patch('app.api.payment.PaymentAPI.get_payment_status') as mock_status:
            mock_status.return_value = {
                "tracking_code": tracking_code,
                "status": "completed",
                "amount": 10000
            }
            
            response = client.get(
                f"/api/payment/status/{tracking_code}",
                headers=normal_user_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "completed"


class TestAdminWorkflow:
    """Test admin workflow for content moderation."""

    @pytest.mark.asyncio
    async def test_admin_content_moderation_workflow(self, client: TestClient, superuser_token_headers):
        """Test admin workflow for moderating content."""
        
        # Step 1: Get pending advertisements
        with patch('app.api.admin.AdminAPI.get_pending_advertisements') as mock_pending:
            mock_pending.return_value = {
                "advertisements": [
                    {
                        "id": 1,
                        "title": "Pending Ad",
                        "status": "pending"
                    }
                ]
            }
            
            response = client.get(
                "/api/admin/advertisements/pending",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            ads = response.json()["advertisements"]
            ad_id = ads[0]["id"]

        # Step 2: Review advertisement details
        with patch('app.api.advertisement.AdvertisementAPI.get_advertisement_detail') as mock_detail:
            mock_detail.return_value = {
                "id": ad_id,
                "title": "Pending Ad",
                "description": "Test description",
                "status": "pending"
            }
            
            response = client.get(f"/api/advertisements/{ad_id}")
            assert response.status_code == status.HTTP_200_OK

        # Step 3: Approve advertisement
        with patch('app.api.admin.AdminAPI.update_advertisement_status') as mock_approve:
            mock_approve.return_value = {
                "message": "Advertisement approved",
                "status": "approved"
            }
            
            approval_data = {
                "status": "approved",
                "admin_notes": "Content approved"
            }
            
            response = client.put(
                f"/api/admin/advertisements/{ad_id}/status",
                json=approval_data,
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK

        # Step 4: Log admin activity
        with patch('app.api.admin.AdminAPI.get_admin_activity_log') as mock_log:
            mock_log.return_value = {
                "activities": [
                    {
                        "action": "approve_advertisement",
                        "target_id": ad_id,
                        "timestamp": "2023-01-01T00:00:00Z"
                    }
                ]
            }
            
            response = client.get(
                "/api/admin/activity-log",
                headers=superuser_token_headers
            )
            
            assert response.status_code == status.HTTP_200_OK


class TestSearchAndRecommendationWorkflow:
    """Test search and recommendation workflow."""

    @pytest.mark.asyncio
    async def test_search_and_recommendation_workflow(self, client: TestClient):
        """Test search and recommendation workflow."""
        
        # Step 1: Search advertisements
        with patch('app.api.advertisement.AdvertisementAPI.search_advertisements') as mock_search:
            mock_search.return_value = {
                "items": [
                    {"id": 1, "title": "Test Ad 1"},
                    {"id": 2, "title": "Test Ad 2"}
                ],
                "total": 2
            }
            
            search_params = {
                "q": "test",
                "category_id": 1,
                "min_price": 100000,
                "max_price": 2000000
            }
            
            response = client.get("/api/advertisements/search", params=search_params)
            assert response.status_code == status.HTTP_200_OK
            results = response.json()
            assert len(results["items"]) == 2

        # Step 2: View advertisement details (for tracking)
        ad_id = results["items"][0]["id"]
        
        with patch('app.api.advertisement.AdvertisementAPI.track_view') as mock_track:
            mock_track.return_value = {"view_recorded": True}
            
            response = client.post(f"/api/advertisements/{ad_id}/view")
            assert response.status_code == status.HTTP_200_OK

        # Step 3: Get similar advertisements
        with patch('app.api.advertisement.AdvertisementAPI.get_similar_advertisements') as mock_similar:
            mock_similar.return_value = {
                "items": [
                    {"id": 3, "title": "Similar Ad"}
                ]
            }
            
            response = client.get(f"/api/advertisements/{ad_id}/similar")
            assert response.status_code == status.HTTP_200_OK

        # Step 4: Get recommendations (if user is logged in)
        with patch('app.api.recommendations.RecommendationsAPI.get_personalized_recommendations') as mock_recommendations:
            mock_recommendations.return_value = {
                "items": [
                    {"id": 4, "title": "Recommended Ad"}
                ]
            }
            
            response = client.get("/api/recommendations/personalized")
            assert response.status_code == status.HTTP_200_OK


class TestErrorHandlingWorkflow:
    """Test error handling in various workflows."""

    @pytest.mark.asyncio
    async def test_authentication_error_handling(self, client: TestClient):
        """Test authentication error handling."""
        
        # Try to access protected endpoint without token
        response = client.get("/api/user/profile")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # Try with invalid token
        headers = {"Authorization": "Bearer invalid-token"}
        response = client.get("/api/user/profile", headers=headers)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_validation_error_handling(self, client: TestClient, normal_user_token_headers):
        """Test validation error handling."""
        
        # Try to create advertisement with invalid data
        invalid_data = {
            "title": "",  # Empty title
            "description": "Test",
            "price": -1000  # Negative price
        }
        
        response = client.post(
            "/api/advertisements/",
            json=invalid_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_not_found_error_handling(self, client: TestClient):
        """Test not found error handling."""
        
        # Try to access non-existent advertisement
        response = client.get("/api/advertisements/99999")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_permission_error_handling(self, client: TestClient, normal_user_token_headers):
        """Test permission error handling."""
        
        # Try to access admin endpoint with normal user token
        response = client.get(
            "/api/admin/dashboard/stats",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
