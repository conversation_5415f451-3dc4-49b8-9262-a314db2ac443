"""
Comprehensive tests for Django models.
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from datetime import datetime, timedelta

from app.models import (
    CustomUserModel, AdvertisementModel, MainCategoryModel,
    SubCategoryModel, ProvinceModel, CityModel, UserPayment,
    BlogModel, AddressesModel, UserWallet, SubScriptionModel
)
from app.models.advertisement import (
    BooleanAttributeModel, ChoiceAttributeModel, ChoiceOptionModel,
    TextAttributeModel, PropertyModel, AdvertisementImagesModel,
    AdvertisementFavoriteModel, AdvertisementViewModel
)


class TestCustomUserModel(TestCase):
    """Test cases for CustomUserModel."""

    def setUp(self):
        """Set up test data."""
        self.user_data = {
            'phone_number': '***********',
            'first_name': 'Test',
            'last_name': 'User',
            'email': '<EMAIL>'
        }

    def test_create_user_success(self):
        """Test successful user creation."""
        user = CustomUserModel.objects.create(**self.user_data)
        
        assert user.phone_number == '***********'
        assert user.first_name == 'Test'
        assert user.last_name == 'User'
        assert user.email == '<EMAIL>'
        assert user.is_verified is False  # Default value
        assert user.is_active is True  # Default value

    def test_user_phone_number_unique(self):
        """Test phone number uniqueness constraint."""
        CustomUserModel.objects.create(**self.user_data)
        
        # Try to create another user with same phone number
        with pytest.raises(IntegrityError):
            CustomUserModel.objects.create(**self.user_data)

    def test_user_email_unique(self):
        """Test email uniqueness constraint."""
        CustomUserModel.objects.create(**self.user_data)
        
        # Try to create another user with same email
        duplicate_data = self.user_data.copy()
        duplicate_data['phone_number'] = '09123456788'
        
        with pytest.raises(IntegrityError):
            CustomUserModel.objects.create(**duplicate_data)

    def test_user_str_representation(self):
        """Test user string representation."""
        user = CustomUserModel.objects.create(**self.user_data)
        expected_str = f"{user.first_name} {user.last_name}"
        
        assert str(user) == expected_str

    def test_user_full_name_property(self):
        """Test user full name property."""
        user = CustomUserModel.objects.create(**self.user_data)
        expected_full_name = f"{user.first_name} {user.last_name}"
        
        assert user.get_full_name() == expected_full_name


class TestAdvertisementModel(TestCase):
    """Test cases for AdvertisementModel."""

    def setUp(self):
        """Set up test data."""
        self.user = CustomUserModel.objects.create(
            phone_number='***********',
            first_name='Test',
            last_name='User',
            email='<EMAIL>'
        )
        
        self.province = ProvinceModel.objects.create(
            name='تهران',
            slug='tehran'
        )
        
        self.city = CityModel.objects.create(
            name='تهران',
            slug='tehran',
            province=self.province
        )
        
        self.main_category = MainCategoryModel.objects.create(
            name='املاک',
            slug='real-estate',
            is_active=True
        )
        
        self.sub_category = SubCategoryModel.objects.create(
            name='آپارتمان',
            slug='apartment',
            main_category=self.main_category,
            is_active=True
        )

    def test_create_advertisement_success(self):
        """Test successful advertisement creation."""
        ad_data = {
            'title': 'Test Advertisement',
            'description': 'Test Description',
            'phone_number_owner_building': '***********',
            'main_category': self.main_category,
            'sub_category': self.sub_category,
            'province': self.province,
            'city': self.city,
            'user': self.user
        }
        
        advertisement = AdvertisementModel.objects.create(**ad_data)
        
        assert advertisement.title == 'Test Advertisement'
        assert advertisement.description == 'Test Description'
        assert advertisement.user == self.user
        assert advertisement.is_active is True  # Default value

    def test_advertisement_slug_generation(self):
        """Test automatic slug generation for advertisements."""
        ad_data = {
            'title': 'Test Advertisement Title',
            'description': 'Test Description',
            'phone_number_owner_building': '***********',
            'main_category': self.main_category,
            'sub_category': self.sub_category,
            'province': self.province,
            'city': self.city,
            'user': self.user
        }
        
        advertisement = AdvertisementModel.objects.create(**ad_data)
        
        # Check if slug is generated (implementation depends on your slug logic)
        assert hasattr(advertisement, 'slug')

    def test_advertisement_str_representation(self):
        """Test advertisement string representation."""
        advertisement = AdvertisementModel.objects.create(
            title='Test Advertisement',
            description='Test Description',
            phone_number_owner_building='***********',
            main_category=self.main_category,
            sub_category=self.sub_category,
            province=self.province,
            city=self.city,
            user=self.user
        )
        
        assert str(advertisement) == 'Test Advertisement'

    def test_advertisement_category_relationship(self):
        """Test advertisement category relationships."""
        advertisement = AdvertisementModel.objects.create(
            title='Test Advertisement',
            description='Test Description',
            phone_number_owner_building='***********',
            main_category=self.main_category,
            sub_category=self.sub_category,
            province=self.province,
            city=self.city,
            user=self.user
        )
        
        assert advertisement.main_category == self.main_category
        assert advertisement.sub_category == self.sub_category
        assert advertisement.sub_category.main_category == self.main_category


class TestCategoryModels(TestCase):
    """Test cases for category models."""

    def test_main_category_creation(self):
        """Test main category creation."""
        category = MainCategoryModel.objects.create(
            name='املاک',
            slug='real-estate',
            is_active=True
        )
        
        assert category.name == 'املاک'
        assert category.slug == 'real-estate'
        assert category.is_active is True

    def test_sub_category_creation(self):
        """Test sub category creation."""
        main_category = MainCategoryModel.objects.create(
            name='املاک',
            slug='real-estate',
            is_active=True
        )
        
        sub_category = SubCategoryModel.objects.create(
            name='آپارتمان',
            slug='apartment',
            main_category=main_category,
            is_active=True
        )
        
        assert sub_category.name == 'آپارتمان'
        assert sub_category.main_category == main_category

    def test_category_hierarchy(self):
        """Test category hierarchy relationships."""
        main_category = MainCategoryModel.objects.create(
            name='املاک',
            slug='real-estate',
            is_active=True
        )
        
        sub_category = SubCategoryModel.objects.create(
            name='آپارتمان',
            slug='apartment',
            main_category=main_category,
            is_active=True
        )
        
        # Test reverse relationship
        assert sub_category in main_category.subcategorymodel_set.all()


class TestLocationModels(TestCase):
    """Test cases for location models."""

    def test_province_creation(self):
        """Test province creation."""
        province = ProvinceModel.objects.create(
            name='تهران',
            slug='tehran'
        )
        
        assert province.name == 'تهران'
        assert province.slug == 'tehran'

    def test_city_creation(self):
        """Test city creation."""
        province = ProvinceModel.objects.create(
            name='تهران',
            slug='tehran'
        )
        
        city = CityModel.objects.create(
            name='تهران',
            slug='tehran',
            province=province
        )
        
        assert city.name == 'تهران'
        assert city.province == province

    def test_province_city_relationship(self):
        """Test province-city relationship."""
        province = ProvinceModel.objects.create(
            name='تهران',
            slug='tehran'
        )
        
        city1 = CityModel.objects.create(
            name='تهران',
            slug='tehran',
            province=province
        )
        
        city2 = CityModel.objects.create(
            name='کرج',
            slug='karaj',
            province=province
        )
        
        # Test reverse relationship
        cities = province.citymodel_set.all()
        assert city1 in cities
        assert city2 in cities


class TestAttributeModels(TestCase):
    """Test cases for attribute models."""

    def test_boolean_attribute_creation(self):
        """Test boolean attribute creation."""
        attr = BooleanAttributeModel.objects.create(
            name='پارکینگ',
            slug='parking',
            is_active=True
        )
        
        assert attr.name == 'پارکینگ'
        assert attr.slug == 'parking'
        assert attr.is_active is True

    def test_choice_attribute_creation(self):
        """Test choice attribute creation."""
        attr = ChoiceAttributeModel.objects.create(
            name='نوع ملک',
            slug='property-type',
            is_active=True
        )
        
        assert attr.name == 'نوع ملک'
        assert attr.slug == 'property-type'

    def test_choice_option_creation(self):
        """Test choice option creation."""
        choice_attr = ChoiceAttributeModel.objects.create(
            name='نوع ملک',
            slug='property-type',
            is_active=True
        )
        
        option = ChoiceOptionModel.objects.create(
            choice_attribute=choice_attr,
            name='آپارتمان',
            slug='apartment',
            is_active=True
        )
        
        assert option.name == 'آپارتمان'
        assert option.choice_attribute == choice_attr

    def test_text_attribute_creation(self):
        """Test text attribute creation."""
        attr = TextAttributeModel.objects.create(
            name='توضیحات اضافی',
            slug='additional-description',
            is_active=True
        )
        
        assert attr.name == 'توضیحات اضافی'
        assert attr.slug == 'additional-description'


class TestUserPaymentModel(TestCase):
    """Test cases for UserPayment model."""

    def setUp(self):
        """Set up test data."""
        self.user = CustomUserModel.objects.create(
            phone_number='***********',
            first_name='Test',
            last_name='User',
            email='<EMAIL>'
        )

    def test_payment_creation(self):
        """Test payment creation."""
        payment = UserPayment.objects.create(
            user=self.user,
            amount=10000,
            bank_name='zarinpal',
            reference_number='test-ref-123',
            status_payment=False,
            payment_finished=False
        )
        
        assert payment.user == self.user
        assert payment.amount == 10000
        assert payment.bank_name == 'zarinpal'
        assert payment.status_payment is False

    def test_payment_status_update(self):
        """Test payment status update."""
        payment = UserPayment.objects.create(
            user=self.user,
            amount=10000,
            bank_name='zarinpal',
            reference_number='test-ref-123',
            status_payment=False,
            payment_finished=False
        )
        
        # Update payment status
        payment.status_payment = True
        payment.payment_finished = True
        payment.save()
        
        # Refresh from database
        payment.refresh_from_db()
        
        assert payment.status_payment is True
        assert payment.payment_finished is True


class TestAdvertisementRelatedModels(TestCase):
    """Test cases for advertisement related models."""

    def setUp(self):
        """Set up test data."""
        self.user = CustomUserModel.objects.create(
            phone_number='***********',
            first_name='Test',
            last_name='User',
            email='<EMAIL>'
        )
        
        self.province = ProvinceModel.objects.create(
            name='تهران',
            slug='tehran'
        )
        
        self.city = CityModel.objects.create(
            name='تهران',
            slug='tehran',
            province=self.province
        )
        
        self.main_category = MainCategoryModel.objects.create(
            name='املاک',
            slug='real-estate',
            is_active=True
        )
        
        self.sub_category = SubCategoryModel.objects.create(
            name='آپارتمان',
            slug='apartment',
            main_category=self.main_category,
            is_active=True
        )
        
        self.advertisement = AdvertisementModel.objects.create(
            title='Test Advertisement',
            description='Test Description',
            phone_number_owner_building='***********',
            main_category=self.main_category,
            sub_category=self.sub_category,
            province=self.province,
            city=self.city,
            user=self.user
        )

    def test_advertisement_favorite(self):
        """Test advertisement favorite functionality."""
        favorite = AdvertisementFavoriteModel.objects.create(
            user=self.user,
            advertisement=self.advertisement
        )
        
        assert favorite.user == self.user
        assert favorite.advertisement == self.advertisement

    def test_advertisement_view_tracking(self):
        """Test advertisement view tracking."""
        view = AdvertisementViewModel.objects.create(
            advertisement=self.advertisement,
            ip_address='***********',
            user_agent='Test Browser'
        )
        
        assert view.advertisement == self.advertisement
        assert view.ip_address == '***********'

    def test_advertisement_image_upload(self):
        """Test advertisement image model."""
        image = AdvertisementImagesModel.objects.create(
            advertisement=self.advertisement,
            image='test_image.jpg',
            is_primary=True
        )
        
        assert image.advertisement == self.advertisement
        assert image.is_primary is True
