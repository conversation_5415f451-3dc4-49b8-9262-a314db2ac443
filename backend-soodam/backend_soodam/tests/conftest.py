from collections.abc import Generator
import asyncio
import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlmodel import Session, delete
from django.test import TestCase
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

from app.core.config import settings
from app.core.db import engine, init_db
from app.main import app
from app.models import (
    CustomUserModel, AdvertisementModel, MainCategoryModel,
    SubCategoryModel, ProvinceModel, CityModel, UserPayment,
    BlogModel, AddressesModel
)
from app.tests.utils.user import authentication_token_from_email
from app.tests.utils.utils import get_superuser_token_headers


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session", autouse=True)
def db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        init_db(session)
        yield session
        # Clean up test data
        statement = delete(AdvertisementModel)
        session.execute(statement)
        statement = delete(CustomUserModel)
        session.execute(statement)
        session.commit()


@pytest.fixture(scope="module")
def client() -> Generator[TestClient, None, None]:
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def superuser_token_headers(client: TestClient) -> dict[str, str]:
    return get_superuser_token_headers(client)


@pytest.fixture(scope="module")
def normal_user_token_headers(client: TestClient, db: Session) -> dict[str, str]:
    return authentication_token_from_email(
        client=client, email=settings.EMAIL_TEST_USER, db=db
    )


@pytest.fixture
async def test_user():
    """Create a test user for testing."""
    user = await CustomUserModel.objects.acreate(
        phone_number="09123456789",
        first_name="Test",
        last_name="User",
        email="<EMAIL>",
        is_verified=True
    )
    yield user
    await user.adelete()


@pytest.fixture
async def test_province():
    """Create a test province."""
    province = await ProvinceModel.objects.acreate(
        name="تهران",
        slug="tehran"
    )
    yield province
    await province.adelete()


@pytest.fixture
async def test_city(test_province):
    """Create a test city."""
    city = await CityModel.objects.acreate(
        name="تهران",
        slug="tehran",
        province=test_province
    )
    yield city
    await city.adelete()


@pytest.fixture
async def test_category():
    """Create a test main category."""
    category = await MainCategoryModel.objects.acreate(
        name="املاک",
        slug="real-estate",
        is_active=True
    )
    yield category
    await category.adelete()


@pytest.fixture
async def test_subcategory(test_category):
    """Create a test subcategory."""
    subcategory = await SubCategoryModel.objects.acreate(
        name="آپارتمان",
        slug="apartment",
        main_category=test_category,
        is_active=True
    )
    yield subcategory
    await subcategory.adelete()


@pytest.fixture
async def test_advertisement(test_user, test_category, test_subcategory, test_province, test_city):
    """Create a test advertisement."""
    advertisement = await AdvertisementModel.objects.acreate(
        title="Test Advertisement",
        description="Test Description",
        phone_number_owner_building="09123456789",
        main_category=test_category,
        sub_category=test_subcategory,
        province=test_province,
        city=test_city,
        user=test_user,
        is_active=True
    )
    yield advertisement
    await advertisement.adelete()


@pytest.fixture
def mock_sms_service():
    """Mock SMS service for testing."""
    with patch('ManagementPanelSMSCall.views.singleton_object') as mock:
        mock.send_sms.return_value = True
        yield mock


@pytest.fixture
def mock_payment_gateway():
    """Mock payment gateway for testing."""
    with patch('app.services.iranian_payment.PaymentGatewayFactory.create_gateway') as mock:
        gateway_mock = AsyncMock()
        gateway_mock.request_payment.return_value = {
            "success": True,
            "redirect_url": "https://example.com/payment",
            "tracking_code": "test-tracking",
            "reference_number": "test-ref"
        }
        gateway_mock.verify_payment.return_value = {
            "success": True,
            "reference_id": "test-ref-id",
            "message": "Payment successful"
        }
        mock.return_value = gateway_mock
        yield gateway_mock
