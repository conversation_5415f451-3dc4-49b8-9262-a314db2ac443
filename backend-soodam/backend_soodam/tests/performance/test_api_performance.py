"""
Performance tests for the API endpoints.
"""

import asyncio
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import pytest
import httpx
from fastapi.testclient import TestClient

from app.core.benchmark import benchmark, benchmark_endpoint


@pytest.fixture
def performance_thresholds():
    """Performance thresholds for API endpoints"""
    return {
        "auth": {
            "login": 500,  # ms
            "refresh": 200  # ms
        },
        "user": {
            "get_me": 100,  # ms
            "update_me": 200  # ms
        },
        "admin": {
            "dashboard_stats": 500,  # ms
            "get_advertisements": 300,  # ms
            "get_users": 300  # ms
        },
        "security": {
            "create_api_key": 200,  # ms
            "get_api_keys": 200,  # ms
            "setup_2fa": 200  # ms
        }
    }


@pytest.fixture
def auth_token(client: TestClient):
    """Get an authentication token for performance tests"""
    # In a real test, we would use real credentials
    # For now, we'll use a mock token
    response = client.post(
        "/api/auth/login",
        data={"username": "<EMAIL>", "password": "testpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def auth_headers(auth_token):
    """Create headers with authentication token"""
    return {"Authorization": f"Bearer {auth_token}"}


@pytest.mark.asyncio
async def test_login_performance(client: TestClient, performance_thresholds):
    """Test login endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/auth/login",
        method="POST",
        data={"username": "<EMAIL>", "password": "testpassword"},
        iterations=10,
        name="Login"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["auth"]["login"], \
        f"Login performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['auth']['login']}ms"
    
    # Print performance results
    print(f"Login performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_get_current_user_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test get current user endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/user/me",
        method="GET",
        headers=auth_headers,
        iterations=10,
        name="Get Current User"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["user"]["get_me"], \
        f"Get Current User performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['user']['get_me']}ms"
    
    # Print performance results
    print(f"Get Current User performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_admin_dashboard_stats_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test admin dashboard stats endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/admin/dashboard/stats",
        method="GET",
        headers=auth_headers,
        iterations=5,
        name="Admin Dashboard Stats"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["admin"]["dashboard_stats"], \
        f"Admin Dashboard Stats performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['admin']['dashboard_stats']}ms"
    
    # Print performance results
    print(f"Admin Dashboard Stats performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_get_advertisements_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test get advertisements endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/admin/advertisements",
        method="GET",
        headers=auth_headers,
        iterations=5,
        name="Get Advertisements"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["admin"]["get_advertisements"], \
        f"Get Advertisements performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['admin']['get_advertisements']}ms"
    
    # Print performance results
    print(f"Get Advertisements performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_create_api_key_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test create API key endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/security/api-keys",
        method="POST",
        headers=auth_headers,
        data={"name": f"Performance Test API Key {datetime.now().isoformat()}"},
        iterations=5,
        name="Create API Key"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["security"]["create_api_key"], \
        f"Create API Key performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['security']['create_api_key']}ms"
    
    # Print performance results
    print(f"Create API Key performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_get_api_keys_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test get API keys endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/security/api-keys",
        method="GET",
        headers=auth_headers,
        iterations=5,
        name="Get API Keys"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["security"]["get_api_keys"], \
        f"Get API Keys performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['security']['get_api_keys']}ms"
    
    # Print performance results
    print(f"Get API Keys performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_setup_2fa_performance(client: TestClient, auth_headers, performance_thresholds):
    """Test setup 2FA endpoint performance"""
    result = await benchmark_endpoint(
        client=client,
        url="/api/security/2fa/setup",
        method="POST",
        headers=auth_headers,
        iterations=5,
        name="Setup 2FA"
    )
    
    # Assert that the average response time is below the threshold
    assert result.average_time * 1000 < performance_thresholds["security"]["setup_2fa"], \
        f"Setup 2FA performance is too slow: {result.average_time * 1000:.2f}ms > {performance_thresholds['security']['setup_2fa']}ms"
    
    # Print performance results
    print(f"Setup 2FA performance: {result.average_time * 1000:.2f}ms")


@pytest.mark.asyncio
async def test_concurrent_requests_performance():
    """Test performance under concurrent load"""
    async def make_request(url, session):
        start_time = time.time()
        async with session.get(url) as response:
            end_time = time.time()
            return {
                "url": url,
                "status": response.status_code,
                "time": end_time - start_time
            }
    
    # List of endpoints to test
    endpoints = [
        "/api/health",
        "/api/blogs",
        "/api/adv",
        "/api/geolocation/cities",
    ]
    
    # Number of concurrent requests per endpoint
    concurrency = 10
    
    # Create tasks for concurrent requests
    async with httpx.AsyncClient(base_url="http://localhost:8000") as session:
        tasks = []
        for endpoint in endpoints:
            for _ in range(concurrency):
                tasks.append(make_request(endpoint, session))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks)
    
    # Analyze results
    endpoint_times = {}
    for result in results:
        url = result["url"]
        if url not in endpoint_times:
            endpoint_times[url] = []
        endpoint_times[url].append(result["time"] * 1000)  # Convert to ms
    
    # Print results
    for endpoint, times in endpoint_times.items():
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        print(f"Endpoint {endpoint} under concurrent load:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Max: {max_time:.2f}ms")
        print(f"  Min: {min_time:.2f}ms")
        
        # Assert that the average response time is reasonable
        assert avg_time < 1000, f"Endpoint {endpoint} is too slow under concurrent load: {avg_time:.2f}ms"
