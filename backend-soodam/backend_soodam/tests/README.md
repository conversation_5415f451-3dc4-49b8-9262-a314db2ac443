# Soodam Backend Test Suite

This directory contains comprehensive tests for the Soodam backend application, covering all major functionality including authentication, advertisements, payments, user management, and admin operations.

## Test Structure

```
tests/
├── README.md                           # This file
├── conftest.py                         # Shared test fixtures and configuration
├── api/                               # API endpoint tests
│   ├── test_auth_comprehensive.py     # Authentication API tests
│   ├── test_advertisement_comprehensive.py  # Advertisement API tests
│   ├── test_payment_comprehensive.py  # Payment API tests
│   ├── test_user_comprehensive.py     # User API tests
│   └── test_admin_comprehensive.py    # Admin API tests
├── models/                            # Database model tests
│   └── test_models_comprehensive.py   # Model validation and relationship tests
├── integration/                       # Integration tests
│   └── test_full_workflow.py         # End-to-end workflow tests
├── performance/                       # Performance tests
│   └── test_api_performance.py       # API performance benchmarks
└── utils/                            # Test utilities
    ├── user.py                       # User-related test helpers
    └── utils.py                      # General test utilities
```

## Test Categories

### 1. API Tests (`tests/api/`)

#### Authentication Tests (`test_auth_comprehensive.py`)
- Phone number verification flow
- OTP generation and validation
- Login with OAuth2 form
- Rate limiting and security measures
- Error handling for invalid credentials

#### Advertisement Tests (`test_advertisement_comprehensive.py`)
- Advertisement CRUD operations
- Image and video upload
- Search and filtering
- Favorites and flagging
- Category and location management
- Statistics tracking

#### Payment Tests (`test_payment_comprehensive.py`)
- Iranian bank gateway integration
- Payment initialization and verification
- Multiple payment providers (Zarinpal, Mellat, Saman, Parsian)
- Payment history and status tracking
- Error handling and timeout scenarios

#### User Tests (`test_user_comprehensive.py`)
- User profile management
- Address management
- Avatar upload
- Wallet and subscription management
- Privacy settings
- Account deactivation

#### Admin Tests (`test_admin_comprehensive.py`)
- Dashboard statistics
- User management
- Content moderation
- Flagged content resolution
- System analytics
- Admin activity logging

### 2. Model Tests (`tests/models/`)

#### Model Validation Tests (`test_models_comprehensive.py`)
- Model creation and validation
- Relationship integrity
- Constraint enforcement
- String representations
- Custom model methods

### 3. Integration Tests (`tests/integration/`)

#### Full Workflow Tests (`test_full_workflow.py`)
- Complete user registration flow
- Advertisement creation and publication
- Payment processing workflow
- Admin content moderation
- Search and recommendation flow
- Error handling scenarios

### 4. Performance Tests (`tests/performance/`)

#### API Performance Tests (`test_api_performance.py`)
- Response time benchmarks
- Load testing scenarios
- Database query optimization
- Caching effectiveness

## Running Tests

### Prerequisites

1. Install test dependencies:
```bash
pip install -r requirements.txt
```

2. Set up test database:
```bash
python manage.py migrate --settings=config.settings.local
```

### Using the Test Runner

The project includes a comprehensive test runner script (`run_tests.py`) with various options:

#### Run All Tests
```bash
python run_tests.py all --verbose --coverage
```

#### Run Specific Test Categories
```bash
# Unit tests only
python run_tests.py unit --coverage

# Integration tests only
python run_tests.py integration --verbose

# Performance tests only
python run_tests.py performance
```

#### Run Tests for Specific Modules
```bash
# Authentication tests
python run_tests.py module --module auth --verbose

# Advertisement tests
python run_tests.py module --module advertisement --coverage

# Payment tests
python run_tests.py module --module payment --verbose

# User management tests
python run_tests.py module --module user --coverage

# Admin tests
python run_tests.py module --module admin --verbose

# Model tests
python run_tests.py module --module models --coverage
```

#### Generate Comprehensive Test Report
```bash
python run_tests.py report
```

#### Run Code Quality Checks
```bash
python run_tests.py lint
```

### Using pytest Directly

You can also run tests directly with pytest:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/api/test_auth_comprehensive.py -v

# Run tests with specific markers
pytest -m "auth" -v
pytest -m "not slow" -v

# Run tests matching pattern
pytest -k "test_payment" -v
```

## Test Markers

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.api` - API tests
- `@pytest.mark.models` - Model tests
- `@pytest.mark.auth` - Authentication tests
- `@pytest.mark.payment` - Payment tests
- `@pytest.mark.admin` - Admin tests
- `@pytest.mark.user` - User tests
- `@pytest.mark.advertisement` - Advertisement tests
- `@pytest.mark.slow` - Slow tests (can be skipped)

## Test Configuration

### Environment Variables

Tests use the following environment variables:

- `DJANGO_SETTINGS_MODULE=config.settings.local`
- `TESTING=true`

### Test Database

Tests use a separate test database configured in `config.settings.local`.

### Fixtures

Common test fixtures are defined in `conftest.py`:

- `test_user` - Creates a test user
- `test_province` - Creates a test province
- `test_city` - Creates a test city
- `test_category` - Creates a test category
- `test_subcategory` - Creates a test subcategory
- `test_advertisement` - Creates a test advertisement
- `mock_sms_service` - Mocks SMS service
- `mock_payment_gateway` - Mocks payment gateway

## Coverage Reports

Test coverage reports are generated in the following locations:

- HTML Report: `tests/coverage/report/index.html`
- XML Report: `tests/coverage/coverage.xml`
- Terminal output with missing lines

Target coverage: 80% minimum

## Continuous Integration

Tests are designed to run in CI/CD environments. The test runner script provides exit codes for CI integration:

- Exit code 0: All tests passed
- Exit code 1: Some tests failed

## Best Practices

### Writing New Tests

1. **Use descriptive test names**: Test names should clearly describe what is being tested
2. **Follow AAA pattern**: Arrange, Act, Assert
3. **Use appropriate fixtures**: Leverage existing fixtures for common test data
4. **Mock external dependencies**: Use mocks for SMS services, payment gateways, etc.
5. **Test both success and failure scenarios**: Include error handling tests
6. **Add appropriate markers**: Use pytest markers to categorize tests

### Test Data Management

1. **Use factories or fixtures**: Don't create test data manually in each test
2. **Clean up after tests**: Fixtures should clean up created data
3. **Avoid test interdependencies**: Each test should be independent
4. **Use realistic test data**: Test data should resemble production data

### Performance Considerations

1. **Use database transactions**: Tests should use database transactions for speed
2. **Mock expensive operations**: Mock external API calls and file operations
3. **Parallel test execution**: Tests are designed to run in parallel
4. **Selective test running**: Use markers to run only relevant tests during development

## Troubleshooting

### Common Issues

1. **Database connection errors**: Ensure test database is properly configured
2. **Import errors**: Check that all dependencies are installed
3. **Fixture errors**: Verify that fixture dependencies are correctly defined
4. **Mock errors**: Ensure mocks are properly configured for external services

### Debug Mode

Run tests with additional debugging:

```bash
pytest --pdb --pdbcls=IPython.terminal.debugger:Pdb
```

### Verbose Output

For detailed test output:

```bash
pytest -v -s
```

## Contributing

When adding new features:

1. Write tests for new functionality
2. Ensure existing tests still pass
3. Maintain or improve test coverage
4. Update this documentation if needed
5. Follow the established testing patterns
