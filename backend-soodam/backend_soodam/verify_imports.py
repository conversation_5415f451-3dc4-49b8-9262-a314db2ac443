"""
Sc<PERSON>t to verify that our consolidated imports work correctly.
"""

try:
    print("Verifying imports...")
    
    # Try to import from advertisement.py
    print("Importing from advertisement.py...")
    from app.api.v1.advertisement import AdvertisementAPI
    print("✅ Successfully imported AdvertisementAPI from app.api.advertisement")
    
    # Try to import from admin.py
    print("Importing from admin.py...")
    from app.api.admin import AdminAPI
    print("✅ Successfully imported AdminAPI from app.api.admin")
    
    # Try to import from advertisement schema
    print("Importing from advertisement schema...")
    from app.schemas.advertisement import (
        AdvertisementDetailSchemaV2,
        AdvertisementListSchemaV2,
        ReadFeatureByCategorySchema
    )
    print("✅ Successfully imported schemas from app.schemas.advertisement")
    
    print("All imports verified successfully!")
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
