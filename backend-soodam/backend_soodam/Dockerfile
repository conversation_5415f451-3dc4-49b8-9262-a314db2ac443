FROM python:latest AS base

FROM base AS dev

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/src
WORKDIR /src
# Copy both dependency files
COPY requirements.txt pyproject.toml poetry.lock* ./
RUN apt update \
    && apt install -y --no-install-recommends less
# Install dependencies using both pip and poetry
#RUN #pip install poetry \
#    && poetry config virtualenvs.in-project true \
#    && poetry install --no-dev \
RUN pip install -r requirements.txt

RUN apt-get update \
    && apt-get install -y binutils libproj-dev gdal-bin \
    && apt-get install -y software-properties-common \
    && apt-get install gnupg \
#    && add-apt-repository ppa:ubuntugis/ppa \
    && apt-get install -y libgeos++-dev \
    && apt-get install -y proj-bin \
    && apt-get install -y gdal-bin \
    && apt-get install -y libgdal-dev


FROM base AS prod

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/src
ENV ENV_STATE=production
WORKDIR /src
# Copy both dependency files
COPY requirements.txt pyproject.toml poetry.lock* ./
RUN apt update \
    && apt install -y --no-install-recommends less
# Install dependencies using pip
RUN pip install --no-cache-dir -r requirements.txt

RUN apt-get update \
    && apt-get install -y binutils libproj-dev gdal-bin \
    && apt-get install -y software-properties-common \
    && apt-get install ca-certificates gnupg \
#    && add-apt-repository ppa:ubuntugis/ppa \
    && apt-get install -y libgeos++-dev \
    && apt-get install -y proj-bin \
    && apt-get install -y gdal-bin \
    && apt-get install -y libgdal-dev