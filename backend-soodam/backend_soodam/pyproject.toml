[tool.poetry]
name = "soodam_toml"
version = "0.1.0"
description = ""
authors = ["dr_r00t3r <<EMAIL>>"]
package-mode = false
[tool.poetry.dependencies]
python = "^3.10"
Django = "^4.1.0"
fastapi = "^0.78.0"
pydantic = "^1.9.1"
uvicorn = "^0.34.0"
whitenoise = "^6.2.0"
#psycopg2 = "^2.9.3"
psycopg2-binary="^2"
gunicorn = "^20.1.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.5"
pytz = "^2022.1"
pytest="^8"
pytest-django="^4"
python-dateutil="^2"
jinja2="^3.1"
django-notifications-hq="^1.8.3"
django-model-utils="^5.0.0"
celery="^5.4.0"
redis="^5.2.1"
django_celery_beat="^2.7.0"
django-celery-results="^2.5.1"
python-decouple="^3.8"
pandas="^2.2.3"
openpyxl="^3.1.5"
kavenegar="^1.1.2"
django-jalali="^7.3.0"
pillow="^11.1.0"
django-nested-admin="^4.1.1"
django-resized="^1.0.3"
pyotp="^2.9.0"
graphene-django="^3.2.3"
django-filter="^24.3"
elasticsearch="^8.12.0"
elasticsearch-dsl="^8.12.0"
scikit-learn="^1.6.1"

#[tool.poetry.group.dev.dependencies]
black = {version = "^22.10.0", allow-prereleases = true}

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
