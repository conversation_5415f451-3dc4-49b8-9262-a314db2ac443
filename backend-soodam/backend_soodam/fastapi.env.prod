ENV_STATE=production
SECRET_KEY=secret

# Django settings
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,*

# Database settings
DB_NAME=soodam_prod
DB_USER=soodam_prod
DB_PASSWORD= soodam_prod_pass_123456
DB_HOST=postgres_prod
DB_PORT=5432

# Redis settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_URL=redis://redis:6379/0

# Email settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# AWS settings
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=your-region

# Superuser settings
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=admin

# Sentry settings
SENTRY_DSN=your-sentry-dsn

# Celery settings
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

#elasticsearch settings
ELASTICSEARCH_ENABLED=true
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=changeme


#bank getway

# Payment gateway settings
PAYMENT_SANDBOX_MODE=True
PAYMENT_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Zarinpal settings
ZARINPAL_MERCHANT_ID=your-zarinpal-merchant-id
ZARINPAL_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Mellat settings
MELLAT_MERCHANT_ID=your-mellat-merchant-id
MELLAT_TERMINAL_ID=your-mellat-terminal-id
MELLAT_USERNAME=your-mellat-username
MELLAT_PASSWORD=your-mellat-password
MELLAT_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Saman settings
SAMAN_MERCHANT_ID=your-saman-merchant-id
SAMAN_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Parsian settings
PARSIAN_PIN=your-parsian-pin
PARSIAN_CALLBACK_URL=https://api.soodam.com/api/payments/callback