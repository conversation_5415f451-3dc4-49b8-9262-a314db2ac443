# Admin API Documentation

This document provides comprehensive documentation for the Admin API endpoints.

## Authentication

All admin API endpoints require authentication using a JWT token. The token should be included in the `Authorization` header as a Bearer token.

```
Authorization: Bearer <token>
```

To obtain a token, use the `/api/auth/login` endpoint with admin credentials.

## Base URL

All endpoints are prefixed with `/api/admin`.

## Dashboard

### Get Dashboard Statistics

Retrieves statistics for the admin dashboard.

**Endpoint:** `GET /dashboard/stats`

**Authentication:** Admin user

**Response:**

```json
{
  "total_users": 100,
  "active_users": 80,
  "total_advertisements": 200,
  "pending_advertisements": 50,
  "approved_advertisements": 120,
  "rejected_advertisements": 30,
  "advertisements_by_category": [
    {
      "category": "Real Estate",
      "count": 50
    },
    {
      "category": "Vehicles",
      "count": 40
    }
  ]
}
```

## Advertisement Management

### Get All Advertisements

Retrieves all advertisements with filtering and pagination.

**Endpoint:** `GET /advertisements`

**Authentication:** Admin user

**Query Parameters:**

- `status` (optional): Filter by status (0=pending, 1=approved, 2=rejected)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search by title or description

**Response:**

```json
[
  {
    "id": 1,
    "title": "Advertisement 1",
    "description": "Description 1",
    "status": 1,
    "created_at": "2023-01-01T00:00:00",
    "user_id": 1
  },
  {
    "id": 2,
    "title": "Advertisement 2",
    "description": "Description 2",
    "status": 0,
    "created_at": "2023-01-02T00:00:00",
    "user_id": 2
  }
]
```

### Approve Advertisement

Approves an advertisement.

**Endpoint:** `POST /advertisement/{adv_id}/approve`

**Authentication:** Admin user

**Path Parameters:**

- `adv_id`: Advertisement ID

**Response:**

```json
{
  "id": 1,
  "status": 1,
  "message": "Advertisement approved successfully"
}
```

### Reject Advertisement

Rejects an advertisement.

**Endpoint:** `POST /advertisement/{adv_id}/reject`

**Authentication:** Admin user

**Path Parameters:**

- `adv_id`: Advertisement ID

**Response:**

```json
{
  "id": 1,
  "status": 2,
  "message": "Advertisement rejected successfully"
}
```

### Delete Advertisement

Deletes an advertisement.

**Endpoint:** `DELETE /advertisement/{adv_id}`

**Authentication:** Admin user

**Path Parameters:**

- `adv_id`: Advertisement ID

**Response:**

```json
{
  "id": 1,
  "status": "deleted",
  "message": "Advertisement deleted successfully"
}
```

## User Management

### Get All Users

Retrieves all users with filtering and pagination.

**Endpoint:** `GET /users`

**Authentication:** Admin user

**Query Parameters:**

- `is_active` (optional): Filter by active status (true/false)
- `search` (optional): Search by email, name, or phone number
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

**Response:**

```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "is_active": true,
    "first_name": "User",
    "last_name": "One",
    "phone_number": "1234567890",
    "date_joined": "2023-01-01T00:00:00"
  },
  {
    "id": 2,
    "email": "<EMAIL>",
    "is_active": true,
    "first_name": "User",
    "last_name": "Two",
    "phone_number": "0987654321",
    "date_joined": "2023-01-02T00:00:00"
  }
]
```

### Ban User

Bans a user.

**Endpoint:** `POST /user/{user_id}/ban`

**Authentication:** Admin user

**Path Parameters:**

- `user_id`: User ID

**Query Parameters:**

- `reason` (optional): Reason for banning the user

**Response:**

```json
{
  "id": 1,
  "status": "banned",
  "message": "User banned successfully"
}
```

### Unban User

Unbans a user.

**Endpoint:** `POST /user/{user_id}/unban`

**Authentication:** Admin user

**Path Parameters:**

- `user_id`: User ID

**Response:**

```json
{
  "id": 1,
  "status": "active",
  "message": "User unbanned successfully"
}
```

## Blog Management

### Get All Blogs

Retrieves all blogs with filtering and pagination.

**Endpoint:** `GET /blogs`

**Authentication:** Admin user

**Query Parameters:**

- `status` (optional): Filter by status (draft, published, rejected)
- `search` (optional): Search by title or content
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

**Response:**

```json
[
  {
    "id": 1,
    "title": "Blog 1",
    "content": "Content 1",
    "post_status": "published",
    "created_at": "2023-01-01T00:00:00",
    "author_id": 1
  },
  {
    "id": 2,
    "title": "Blog 2",
    "content": "Content 2",
    "post_status": "draft",
    "created_at": "2023-01-02T00:00:00",
    "author_id": 2
  }
]
```

### Approve Blog

Approves a blog post.

**Endpoint:** `POST /blog/{blog_id}/approve`

**Authentication:** Admin user

**Path Parameters:**

- `blog_id`: Blog ID

**Response:**

```json
{
  "id": 1,
  "status": "published",
  "message": "Blog approved successfully"
}
```

### Reject Blog

Rejects a blog post.

**Endpoint:** `POST /blog/{blog_id}/reject`

**Authentication:** Admin user

**Path Parameters:**

- `blog_id`: Blog ID

**Response:**

```json
{
  "id": 1,
  "status": "rejected",
  "message": "Blog rejected successfully"
}
```

### Delete Blog

Deletes a blog post.

**Endpoint:** `DELETE /blog/{blog_id}`

**Authentication:** Admin user

**Path Parameters:**

- `blog_id`: Blog ID

**Response:**

```json
{
  "id": 1,
  "status": "deleted",
  "message": "Blog deleted successfully"
}
```

## Special Admin Management

### Get All Special Admins

Retrieves all special admins with filtering and pagination.

**Endpoint:** `GET /special-admins`

**Authentication:** Admin user (Super admin for all admins, regular admin for self only)

**Query Parameters:**

- `admin_type_id` (optional): Filter by admin type ID
- `status` (optional): Filter by status (1=Active, 2=Inactive, 3=Suspended)
- `search` (optional): Search by name or email
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

**Response:**

```json
{
  "items": [
    {
      "id": 1,
      "user": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "One"
      },
      "admin_type": {
        "id": 1,
        "name": "Content Manager"
      },
      "status": 1,
      "can_manage_users": true,
      "can_manage_advertisements": true,
      "can_manage_blogs": true,
      "can_manage_agents": false,
      "created_at": "2023-01-01T00:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

### Get Special Admin Details

Retrieves detailed information about a special admin.

**Endpoint:** `GET /special-admins/{admin_id}`

**Authentication:** Admin user (Super admin for any admin, regular admin for self only)

**Path Parameters:**

- `admin_id`: Special admin ID

**Response:**

```json
{
  "id": 1,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "Admin",
    "last_name": "One"
  },
  "admin_type": {
    "id": 1,
    "name": "Content Manager"
  },
  "status": 1,
  "can_manage_users": true,
  "can_manage_advertisements": true,
  "can_manage_blogs": true,
  "can_manage_agents": false,
  "created_at": "2023-01-01T00:00:00",
  "cities": [
    {"id": 1, "name": "New York"},
    {"id": 2, "name": "Los Angeles"}
  ],
  "provinces": [
    {"id": 1, "name": "California"}
  ]
}
```

### Create Special Admin

Creates a new special admin.

**Endpoint:** `POST /special-admins`

**Authentication:** Super admin

**Request Body:**

```json
{
  "user_id": 3,
  "admin_type_id": 1,
  "can_manage_users": true,
  "can_manage_advertisements": true,
  "can_manage_blogs": true,
  "can_manage_agents": false,
  "city_ids": [1, 2],
  "province_ids": [1]
}
```

**Response:**

```json
{
  "id": 3,
  "user": {
    "id": 3,
    "email": "<EMAIL>",
    "first_name": "Admin",
    "last_name": "Three"
  },
  "admin_type": {
    "id": 1,
    "name": "Content Manager"
  },
  "status": 1,
  "can_manage_users": true,
  "can_manage_advertisements": true,
  "can_manage_blogs": true,
  "can_manage_agents": false,
  "created_at": "2023-01-03T00:00:00"
}
```

### Update Special Admin

Updates a special admin's details.

**Endpoint:** `PUT /special-admins/{admin_id}`

**Authentication:** Super admin

**Path Parameters:**

- `admin_id`: Special admin ID

**Request Body:**

```json
{
  "admin_type_id": 2,
  "can_manage_users": true,
  "can_manage_advertisements": false,
  "can_manage_blogs": false,
  "can_manage_agents": false,
  "status": 1,
  "city_ids": [1],
  "province_ids": [1]
}
```

**Response:**

```json
{
  "id": 1,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "Admin",
    "last_name": "One"
  },
  "admin_type": {
    "id": 2,
    "name": "User Manager"
  },
  "status": 1,
  "can_manage_users": true,
  "can_manage_advertisements": false,
  "can_manage_blogs": false,
  "can_manage_agents": false,
  "created_at": "2023-01-01T00:00:00"
}
```

### Delete Special Admin

Deletes a special admin.

**Endpoint:** `DELETE /special-admins/{admin_id}`

**Authentication:** Super admin

**Path Parameters:**

- `admin_id`: Special admin ID

**Response:**

```json
{
  "id": 1,
  "status": "deleted",
  "message": "Special admin deleted successfully"
}
```

## Admin Transactions

### Get Admin Transactions

Retrieves admin transactions with filtering and pagination.

**Endpoint:** `GET /transactions`

**Authentication:** Admin user (Super admin for all transactions, regular admin for self only)

**Query Parameters:**

- `admin_id` (optional): Filter by admin ID
- `transaction_type` (optional): Filter by transaction type (1=Salary, 2=Bonus, 3=Commission, 4=Refund, 5=Other)
- `is_positive` (optional): Filter by positive/negative transactions (true/false)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

**Response:**

```json
{
  "items": [
    {
      "id": 1,
      "admin_id": 1,
      "amount": 1000.0,
      "transaction_type": 1,
      "is_positive": true,
      "description": "Monthly salary",
      "transaction_date": "2023-01-01T00:00:00"
    },
    {
      "id": 2,
      "admin_id": 1,
      "amount": 200.0,
      "transaction_type": 2,
      "is_positive": true,
      "description": "Performance bonus",
      "transaction_date": "2023-01-15T00:00:00"
    }
  ],
  "total": 2,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

### Add Admin Transaction

Adds a financial transaction for an admin.

**Endpoint:** `POST /transactions`

**Authentication:** Super admin

**Request Body:**

```json
{
  "admin_id": 1,
  "amount": 500.0,
  "transaction_type": 3,
  "is_positive": true,
  "description": "Commission for sales"
}
```

**Response:**

```json
{
  "id": 3,
  "admin_id": 1,
  "amount": 500.0,
  "transaction_type": 3,
  "is_positive": true,
  "description": "Commission for sales",
  "transaction_date": "2023-01-20T00:00:00"
}
```

## Admin Activities

### Get Admin Activities

Retrieves admin activities with filtering and pagination.

**Endpoint:** `GET /activities`

**Authentication:** Admin user (Super admin for all activities, regular admin for self only)

**Query Parameters:**

- `admin_id` (optional): Filter by admin ID
- `action_type` (optional): Filter by action type (1=Create, 2=Update, 3=Delete, 4=View, 5=Other)
- `entity_type` (optional): Filter by entity type (e.g., "advertisement", "user", "blog")
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

**Response:**

```json
{
  "items": [
    {
      "id": 1,
      "admin_id": 1,
      "admin_name": "Admin One",
      "action_type": 1,
      "entity_type": "advertisement",
      "entity_id": 1,
      "description": "Created advertisement",
      "created_at": "2023-01-01T00:00:00"
    },
    {
      "id": 2,
      "admin_id": 1,
      "admin_name": "Admin One",
      "action_type": 2,
      "entity_type": "advertisement",
      "entity_id": 1,
      "description": "Updated advertisement",
      "created_at": "2023-01-02T00:00:00"
    }
  ],
  "total": 2,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

## Admin Types

### Get Admin Types

Retrieves all admin types.

**Endpoint:** `GET /types`

**Authentication:** Admin user

**Response:**

```json
[
  {
    "id": 1,
    "name": "Content Manager",
    "description": "Manages content like advertisements and blogs"
  },
  {
    "id": 2,
    "name": "User Manager",
    "description": "Manages user accounts and permissions"
  }
]
```

### Create Admin Type

Creates a new admin type.

**Endpoint:** `POST /types`

**Authentication:** Super admin

**Request Body:**

```json
{
  "name": "System Manager",
  "description": "Manages system settings and configurations"
}
```

**Response:**

```json
{
  "id": 3,
  "name": "System Manager",
  "description": "Manages system settings and configurations"
}
```

## Status Codes

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: The user does not have permission to access the resource
- `404 Not Found`: The resource was not found
- `500 Internal Server Error`: An error occurred on the server

## Error Responses

Error responses have the following format:

```json
{
  "detail": "Error message"
}
```
