# VPS Server Setup for Development and Production Environments

This guide provides instructions for setting up both development and production environments on a VPS server with GitHub integration.

## Server Requirements

- Ubuntu 20.04 LTS or newer
- At least 4GB RAM
- 2+ CPU cores
- 40GB+ SSD storage
- Root access

## Initial Server Setup

### 1. Update System

```bash
apt update && apt upgrade -y
```

### 2. Install Required Packages

```bash
apt install -y git docker.io docker-compose nginx certbot python3-certbot-nginx ufw
```

### 3. Configure Firewall

```bash
# Allow SSH, HTTP, and HTTPS
ufw allow ssh
ufw allow http
ufw allow https
ufw enable
```

### 4. Create Deployment User

```bash
# Create deployment user
adduser deployer
# Add to sudo and docker groups
usermod -aG sudo deployer
usermod -aG docker deployer
```

### 5. Set Up SSH Keys

On your local machine:
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-copy-id deployer@your_server_ip
```

## GitHub Repository Setup

### 1. Create Deploy Keys

On your server:
```bash
# Switch to deployer user
su - deployer

# Generate SSH key for GitHub
ssh-keygen -t ed25519 -C "deployer@your_server_name"
cat ~/.ssh/id_ed25519.pub
```

Add this key to your GitHub repository as a deploy key with read access.

### 2. Test GitHub Connection

```bash
ssh -T **************
```

## Directory Structure

Create the following directory structure:

```bash
mkdir -p public_html/soodam/{dev,prod}/{app,data,logs,nginx}
chown -R deployer:deployer /opt/soodam
```

## Environment Setup

### 1. Development Environment

```bash
# Switch to deployer user
su - deployer

# Clone repository (development branch)
cd /opt/soodam/dev/app
git clone -<NAME_EMAIL>:soodamApp/backend-soodam.git .
#this line
# Create environment file
cp backend_soodam/fastapi.env.tmpl backend_soodam/fastapi.env
```

Edit the environment file with development settings.

### 2. Production Environment

```bash
# Switch to deployer user
su - deployer

# Clone repository (master branch)
cd /opt/soodam/prod/app
git clone -<NAME_EMAIL>:soodamApp/backend-soodam.git .

# Create environment file
cp backend_soodam/fastapi.env.tmpl backend_soodam/fastapi.env
```

Edit the environment file with production settings.

## Docker Compose Configuration

### 1. Development Environment

Create `/opt/soodam/dev/docker-compose.yml`:

```yaml
version: "3.7"
services:
  fastapi_dev:
    container_name: soodam_dev_backend
    build:
      context: "./app/backend_soodam"
      target: "dev"
    working_dir: /src
    restart: unless-stopped
    volumes:
      - ./app/backend_soodam:/src
      - ./logs:/src/logs
    ports:
      - "8001:8000"  # Internal port 8000, external 8001
    environment:
      - DB_NAME=soodam_dev
      - DB_USER=soodam_dev
      - DB_PASSWORD=your_secure_password
      - DB_HOST=postgres_dev
      - DB_PORT=5432
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    depends_on:
      - postgres_dev
      - redis
      - elasticsearch

  postgres_dev:
    container_name: soodam_dev_postgres
    image: 'postgis/postgis:latest'
    restart: unless-stopped
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=soodam_dev
      - POSTGRES_PASSWORD=your_secure_password
      - POSTGRES_DB=soodam_dev
    ports:
      - "5433:5432"  # Internal port 5432, external 5433
    command: ./scripts/dev/runlocalserver.sh

  redis:
    container_name: soodam_dev_redis
    image: redis:alpine
    restart: unless-stopped
    volumes:
      - ./data/redis:/data

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    container_name: soodam_dev_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - ./data/elasticsearch:/usr/share/elasticsearch/data
    ports:
      - "9201:9200"  # Internal port 9200, external 9201

volumes:
  postgres_data_dev:
  elasticsearch_data:
```

### 2. Production Environment

Create `/opt/soodam/prod/docker-compose.yml`:

```yaml
version: "3.7"
services:
  fastapi_prod:
    container_name: soodam_prod_backend
    build:
      context: "./app/backend_soodam"
      target: "prod"
    working_dir: /src
    restart: always
    volumes:
      - ./app/backend_soodam:/src
      - ./logs:/src/logs
      - ./app/backend_soodam/media:/src/media
      - ./app/backend_soodam/static:/src/static
    ports:
      - "8000:8000"
    environment:
      - DB_NAME=soodam_prod
      - DB_USER=soodam_prod
      - DB_PASSWORD=your_very_secure_password
      - DB_HOST=postgres_prod
      - DB_PORT=5432
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    depends_on:
      - postgres_prod
      - redis
      - elasticsearch
    command: ./scripts/runlocalserver.sh

  postgres_prod:
    container_name: soodam_prod_postgres
    image: 'postgis/postgis:latest'
    restart: always
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=soodam_prod
      - POSTGRES_PASSWORD=your_very_secure_password
      - POSTGRES_DB=soodam_prod
    ports:
      - "5432:5432"

  redis:
    container_name: soodam_prod_redis
    image: redis:alpine
    restart: always
    volumes:
      - ./data/redis:/data

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    container_name: soodam_prod_elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - ./data/elasticsearch:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

volumes:
  postgres_data_prod:
  elasticsearch_data:
```

## Nginx Configuration

### 1. Development Environment

Create `/etc/nginx/sites-available/dev.soodam.com`:

```nginx
server {
    listen 80;
    server_name dev.soodam.com;

    location / {
        proxy_pass http://localhost:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/public_html/soodam/dev/app/backend_soodam/static/;
    }

    location /media/ {
        alias /home/<USER>/public_html/soodam/dev/app/backend_soodam/media/;
    }
}
```

### 2. Production Environment

Create `/etc/nginx/sites-available/soodam.com`:

```nginx
server {
    listen 80;
    server_name soodam.com www.soodam.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/public_html/soodam/prod/app/backend_soodam/static/;
    }

    location /media/ {
        alias /home/<USER>/public_html/soodam/prod/app/backend_soodam/media/;
    }
}
```

Enable the sites:

```bash
sudo ln -s /etc/nginx/sites-available/dev.soodam.com /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/soodam.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## SSL Configuration

```bash
certbot --nginx -d soodam.com -d www.soodam.com
certbot --nginx -d drr00t3r.ir -d www.drr00t3r.ir
certbot --nginx -d dev.drr00t3r.ir
certbot --nginx -d dev.soodam.com
```
