# GitHub Actions CI/CD Setup

This guide provides instructions for setting up GitHub Actions for continuous integration and deployment to your VPS server.

## GitHub Secrets Setup

Add the following secrets to your GitHub repository:

1. `DEV_SERVER_HOST` - Your server IP or hostname
2. `DEV_SERVER_USER` - The deployment user (e.g., deployer)
3. `DEV_SERVER_SSH_KEY` - The private SSH key for the deployment user
4. `PROD_SERVER_HOST` - Your server IP or hostname (same as DEV if using the same server)
5. `PROD_SERVER_USER` - The deployment user (e.g., deployer)
6. `PROD_SERVER_SSH_KEY` - The private SSH key for the deployment user

## GitHub Actions Workflow Files

### 1. Development Workflow

Create `.github/workflows/deploy-dev.yml`:

```yaml
name: Deploy to Development

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.DEV_SERVER_SSH_KEY }}

      - name: Add server to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan ${{ secrets.DEV_SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to development server
        run: |
          ssh ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_HOST }} "cd /opt/soodam/dev/app && git pull origin develop"
          ssh ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_HOST }} "cd /opt/soodam/dev && docker-compose down && docker-compose build && docker-compose up -d"
          ssh ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_HOST }} "cd /opt/soodam/dev && docker-compose exec -T fastapi_dev python manage.py migrate"
          ssh ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_HOST }} "cd /opt/soodam/dev && docker-compose exec -T fastapi_dev python manage.py collectstatic --noinput"
```

### 2. Production Workflow

Create `.github/workflows/deploy-prod.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches:
      - master

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r backend_soodam/requirements.txt
          pip install pytest pytest-cov

      - name: Run tests
        run: |
          cd backend_soodam
          pytest --cov=app tests/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.PROD_SERVER_SSH_KEY }}

      - name: Add server to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan ${{ secrets.PROD_SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to production server
        run: |
          ssh ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} "cd /opt/soodam/prod/app && git pull origin master"
          ssh ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} "cd /opt/soodam/prod && docker-compose down && docker-compose build && docker-compose up -d"
          ssh ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} "cd /opt/soodam/prod && docker-compose exec -T fastapi_prod python manage.py migrate"
          ssh ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} "cd /opt/soodam/prod && docker-compose exec -T fastapi_prod python manage.py collectstatic --noinput"
```

## Deployment Scripts

### 1. Development Deployment Script

Create `/opt/soodam/dev/deploy.sh`:

```bash
#!/bin/bash
set -e

# Pull latest changes
cd /opt/soodam/dev/app
git pull origin develop

# Rebuild and restart containers
cd /opt/soodam/dev
docker-compose down
docker-compose build
docker-compose up -d

# Run migrations and collect static files
docker-compose exec -T fastapi_dev python manage.py migrate
docker-compose exec -T fastapi_dev python manage.py collectstatic --noinput

echo "Development deployment completed successfully!"
```

### 2. Production Deployment Script

Create `/opt/soodam/prod/deploy.sh`:

```bash
#!/bin/bash
set -e

# Pull latest changes
cd /opt/soodam/prod/app
git pull origin master

# Rebuild and restart containers
cd /opt/soodam/prod
docker-compose down
docker-compose build
docker-compose up -d

# Run migrations and collect static files
docker-compose exec -T fastapi_prod python manage.py migrate
docker-compose exec -T fastapi_prod python manage.py collectstatic --noinput

echo "Production deployment completed successfully!"
```

Make the scripts executable:

```bash
chmod +x /opt/soodam/dev/deploy.sh
chmod +x /opt/soodam/prod/deploy.sh
```

## Backup Scripts

### 1. Database Backup Script

Create `/opt/soodam/backup.sh`:

```bash
#!/bin/bash
set -e

# Set variables
BACKUP_DIR="/opt/soodam/backups"
DATE=$(date +%Y-%m-%d_%H-%M-%S)
PROD_DB_CONTAINER="soodam_prod_postgres"
DEV_DB_CONTAINER="soodam_dev_postgres"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR/prod
mkdir -p $BACKUP_DIR/dev

# Backup production database
docker exec $PROD_DB_CONTAINER pg_dump -U soodam_prod -d soodam_prod | gzip > $BACKUP_DIR/prod/soodam_prod_$DATE.sql.gz

# Backup development database
docker exec $DEV_DB_CONTAINER pg_dump -U soodam_dev -d soodam_dev | gzip > $BACKUP_DIR/dev/soodam_dev_$DATE.sql.gz

# Remove backups older than 7 days
find $BACKUP_DIR/prod -type f -name "*.sql.gz" -mtime +7 -delete
find $BACKUP_DIR/dev -type f -name "*.sql.gz" -mtime +7 -delete

echo "Backup completed successfully!"
```

Make the script executable:

```bash
chmod +x /opt/soodam/backup.sh
```

Add to crontab:

```bash
# Run backup daily at 2 AM
echo "0 2 * * * /opt/soodam/backup.sh >> /var/log/soodam_backup.log 2>&1" | sudo tee -a /etc/crontab
```
