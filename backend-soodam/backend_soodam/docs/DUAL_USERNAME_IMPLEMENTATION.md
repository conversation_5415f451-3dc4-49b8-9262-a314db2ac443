# Dual Username Implementation Guide

## Overview

I have successfully implemented a comprehensive dual username authentication system for the Soodam platform that allows users to login with either their **email address** or **phone number**. This implementation provides flexibility and better user experience while maintaining security and data integrity.

## 🚀 **Key Features Implemented**

### **1. Dual Username Support**
- ✅ **Email Login**: `<EMAIL>`
- ✅ **Phone Login**: `09123456789` (Iranian format)
- ✅ **Auto-Normalization**: Supports various phone formats
- ✅ **Backward Compatibility**: Existing authentication continues to work

### **2. Enhanced User Model**
- ✅ **Dual Username Fields**: Both email and phone_number can be used for login
- ✅ **Phone Normalization**: Automatic Iranian phone number formatting
- ✅ **Enhanced Manager**: New methods for dual username lookup
- ✅ **Instance Methods**: User-level validation and utility methods

### **3. Authentication System**
- ✅ **Custom Authentication Backend**: Supports both username types
- ✅ **Authentication Helper**: Utility functions for validation
- ✅ **Enhanced API Endpoints**: Multiple login options
- ✅ **Comprehensive Validation**: Input validation and error handling

## 📋 **Implementation Details**

### **1. Enhanced User Model (`app/models/user.py`)**

#### **Model Configuration:**
```python
class CustomUserModel(AbstractBaseUser, PermissionsMixin, BaseModelMixin):
    # Support both email and phone_number as username fields
    USERNAME_FIELD = "phone_number"  # Primary username field for Django admin
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = ["email"]  # Required when creating superuser
    
    # Custom fields for dual username support
    DUAL_USERNAME_FIELDS = ["email", "phone_number"]  # Both can be used for login
```

#### **Enhanced Manager Methods:**
```python
class EnhancedUserManager(BaseUserManager):
    def get_by_email(self, email: str) -> Optional["CustomUserModel"]
    def get_by_phone(self, phone_number: str) -> Optional["CustomUserModel"]
    def get_by_username_field(self, username: str) -> Optional["CustomUserModel"]
    def get_by_any_username(self, username: str) -> Optional["CustomUserModel"]
    def authenticate_user(self, username: str, password: str) -> Optional["CustomUserModel"]
```

#### **Enhanced Instance Methods:**
```python
class CustomUserModel:
    def get_username_display(self) -> str
    def get_all_usernames(self) -> List[str]
    def can_login_with(self, username: str) -> bool
```

### **2. Authentication Backend (`app/authentication.py`)**

#### **Dual Username Authentication Backend:**
```python
class DualUsernameAuthenticationBackend(BaseBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        # Supports both email and phone number authentication
        return CustomUserModel.objects.authenticate_user(username, password)
```

#### **Authentication Helper:**
```python
class AuthenticationHelper:
    @staticmethod
    def authenticate(username: str, password: str) -> Optional[CustomUserModel]
    @staticmethod
    def get_username_type(username: str) -> str  # 'email', 'phone', 'unknown'
    @staticmethod
    def normalize_username(username: str) -> str
    @staticmethod
    def validate_credentials(username: str, password: str) -> dict
```

### **3. Enhanced Schemas (`app/schemas/auth.py`)**

#### **Dual Username Login Schema:**
```python
class DualUsernameLoginSchema(BaseModel):
    username: str  # Email or phone number
    password: str
    remember_me: bool = False
    
    @validator('username')
    def validate_username(cls, v):
        # Validates both email and Iranian phone number formats
```

#### **Specialized Login Schemas:**
```python
class EmailLoginSchema(BaseModel):
    email: EmailStr
    password: str
    remember_me: bool = False

class PhoneLoginSchema(BaseModel):
    phone_number: str  # Iranian format with validation
    password: str
    remember_me: bool = False
```

### **4. Enhanced API Endpoints (`app/routers/auth.py`)**

#### **Multiple Login Endpoints:**
```python
# Dual username login (recommended)
POST /api/auth/login/dual
{
    "username": "<EMAIL>",  # or "09123456789"
    "password": "secure_password123",
    "remember_me": false
}

# Email-specific login
POST /api/auth/login/email
{
    "email": "<EMAIL>",
    "password": "secure_password123"
}

# Phone-specific login
POST /api/auth/login/phone
{
    "phone_number": "09123456789",
    "password": "secure_password123"
}

# Username type identification
POST /api/auth/username/identify
{
    "username": "<EMAIL>"
}
```

## 🔧 **Phone Number Normalization**

### **Supported Formats:**
```python
# All these formats are automatically normalized to: 09123456789

"09123456789"      # Standard Iranian format
"9123456789"       # Without leading zero
"989123456789"     # International without +
"00989123456789"   # International with country code
"+989123456789"    # With + sign
"091-234-56789"    # With dashes
"091 234 56789"    # With spaces
"(091) 234-56789"  # With parentheses
```

### **Normalization Function:**
```python
from app.models.user import normalize_iranian_phone_number

# Normalize any phone format
normalized = normalize_iranian_phone_number("+98 ************")
print(normalized)  # Output: 09123456789
```

## 📊 **Usage Examples**

### **1. User Creation with Dual Username:**
```python
# Create user with both email and phone
user = CustomUserModel.objects.create_user(
    phone_number="09123456789",
    email="<EMAIL>",
    password="secure_password123",
    first_name="علی",
    last_name="احمدی"
)

# User can now login with either:
# - Email: <EMAIL>
# - Phone: 09123456789
```

### **2. Authentication Examples:**

```python
from app.dependencies.authentication import AuthenticationHelper

# Authenticate with email
user = AuthenticationHelper.authenticate("<EMAIL>", "password123")

# Authenticate with phone number
user = AuthenticationHelper.authenticate("09123456789", "password123")

# Authenticate with international phone format (auto-normalized)
user = AuthenticationHelper.authenticate("989123456789", "password123")
```

### **3. Username Type Identification:**

```python
from app.dependencies.authentication import AuthenticationHelper

# Identify username type
username_type = AuthenticationHelper.get_username_type("<EMAIL>")
print(username_type)  # Output: "email"

username_type = AuthenticationHelper.get_username_type("09123456789")
print(username_type)  # Output: "phone"
```

### **4. User Instance Methods:**
```python
user = CustomUserModel.objects.get(id=1)

# Get all possible usernames for this user
usernames = user.get_all_usernames()
print(usernames)  # ['<EMAIL>', '09123456789', 'username']

# Check if user can login with specific username
can_login = user.can_login_with("<EMAIL>")
print(can_login)  # True

# Get primary username for display
display_username = user.get_username_display()
print(display_username)  # <EMAIL> (or phone if no email)
```

## 🔒 **Security Features**

### **1. Input Validation:**
- ✅ **Email Format Validation**: RFC-compliant email validation
- ✅ **Phone Format Validation**: Iranian phone number format (09xxxxxxxxx)
- ✅ **Automatic Normalization**: Prevents format inconsistencies
- ✅ **SQL Injection Protection**: Parameterized queries

### **2. Authentication Security:**
- ✅ **Password Hashing**: Django's built-in password hashing
- ✅ **Active User Check**: Only active users can authenticate
- ✅ **Rate Limiting**: Can be implemented at endpoint level
- ✅ **Comprehensive Logging**: Authentication attempts are logged

### **3. Data Integrity:**
- ✅ **Unique Constraints**: Email and phone number are unique
- ✅ **Database Indexes**: Optimized for lookup performance
- ✅ **Validation at Model Level**: Ensures data consistency

## 📈 **Performance Optimizations**

### **1. Database Optimizations:**
```python
# Enhanced indexes for dual username lookup
indexes = [
    models.Index(fields=['phone_number']),
    models.Index(fields=['email']),
    models.Index(fields=['is_active', 'is_verified']),
]
```

### **2. Efficient Lookup Strategy:**
```python
def get_by_username_field(self, username: str):
    # Smart lookup based on username format
    if '@' in username:
        return self.get_by_email(username)  # Direct email lookup
    else:
        return self.get_by_phone(username)  # Phone lookup with normalization
```

## 🔄 **Migration Guide**

### **1. Database Migration:**
```bash
# Create migration for enhanced user model
python manage.py makemigrations

# Apply migrations
python manage.py migrate
```

### **2. Update Authentication Settings:**
```python
# In settings.py, add custom authentication backend
AUTHENTICATION_BACKENDS = [
    'app.authentication.DualUsernameAuthenticationBackend',
    'django.contrib.auth.backends.ModelBackend',  # Fallback
]
```

### **3. Frontend Integration:**
```javascript
// Frontend can now use either email or phone for login
const loginData = {
    username: "<EMAIL>",  // or "09123456789"
    password: "secure_password123",
    remember_me: false
};

// POST to /api/auth/login/dual
```

## ✅ **Benefits**

### **For Users:**
- ✅ **Flexibility**: Login with email or phone number
- ✅ **Convenience**: No need to remember which field was used for registration
- ✅ **International Support**: Various phone number formats accepted
- ✅ **Familiar Experience**: Standard login patterns

### **For Developers:**
- ✅ **Clean API**: Well-organized authentication endpoints
- ✅ **Type Safety**: Comprehensive validation and error handling
- ✅ **Extensible**: Easy to add new authentication methods
- ✅ **Backward Compatible**: Existing code continues to work

### **For System:**
- ✅ **Performance**: Optimized database queries and indexes
- ✅ **Security**: Comprehensive validation and logging
- ✅ **Maintainability**: Clean, well-documented code
- ✅ **Scalability**: Efficient lookup algorithms

## 🧪 **Testing**

### **Run Tests:**
```bash
# Run the comprehensive test suite
python test_dual_username.py
```

### **Test Coverage:**
- ✅ **Phone Number Normalization**: All format variations
- ✅ **User Manager Methods**: All lookup methods
- ✅ **Authentication Helper**: Username identification and validation
- ✅ **User Instance Methods**: Login validation and utility methods
- ✅ **Model Configuration**: Dual username field configuration
- ✅ **API Endpoints**: All authentication endpoints

## 📝 **API Documentation**

The enhanced authentication system provides comprehensive API documentation through FastAPI's automatic documentation:

- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`

### **Key Endpoints:**
- `POST /api/auth/login/dual` - Dual username login (recommended)
- `POST /api/auth/login/email` - Email-only login
- `POST /api/auth/login/phone` - Phone-only login
- `POST /api/auth/username/identify` - Username type identification
- `POST /api/auth/login` - Legacy login (backward compatibility)

This comprehensive dual username implementation provides a modern, flexible, and secure authentication system that enhances user experience while maintaining backward compatibility and system performance.
