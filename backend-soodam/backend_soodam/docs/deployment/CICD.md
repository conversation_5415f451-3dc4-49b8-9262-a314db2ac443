# CI/CD Pipeline Guide

## 🚀 Overview

This guide covers comprehensive CI/CD pipeline setup for the Soodam backend using GitHub Actions, GitLab CI, and Jenkins. It includes automated testing, building, security scanning, and deployment strategies.

## 🏗️ Pipeline Architecture

### CI/CD Flow
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ • Feature Branch│───▶│ • Auto Deploy  │───▶│ • Manual Deploy │
│ • Unit Tests    │    │ • Integration   │    │ • Blue/Green    │
│ • Code Quality  │    │ • E2E Tests     │    │ • Health Checks │
│ • Security Scan │    │ • Performance   │    │ • Rollback      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 GitHub Actions Workflow

### Main CI/CD Pipeline
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:15-3.3
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_soodam
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Set up environment
      run: |
        cp .env.example .env.test
        echo "DB_HOST=localhost" >> .env.test
        echo "DB_NAME=test_soodam" >> .env.test
        echo "DB_USER=postgres" >> .env.test
        echo "DB_PASSWORD=postgres" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test

    - name: Run migrations
      run: |
        python manage.py migrate --settings=config.settings.test

    - name: Run unit tests
      run: |
        pytest tests/unit/ --cov=app --cov-report=xml --cov-report=html
      env:
        DJANGO_SETTINGS_MODULE: config.settings.test

    - name: Run integration tests
      run: |
        pytest tests/integration/ --cov-append --cov=app --cov-report=xml
      env:
        DJANGO_SETTINGS_MODULE: config.settings.test

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy bandit

    - name: Run Black
      run: black --check --diff .

    - name: Run isort
      run: isort --check-only --diff .

    - name: Run flake8
      run: flake8 .

    - name: Run mypy
      run: mypy app/

    - name: Run Bandit security check
      run: bandit -r app/ -f json -o bandit-report.json

    - name: Upload Bandit report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Safety check
      run: |
        pip install safety
        safety check --json --output safety-report.json
      continue-on-error: true

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, lint, security]
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test commands here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here
```

### Feature Branch Workflow
```yaml
# .github/workflows/feature-branch.yml
name: Feature Branch CI

on:
  pull_request:
    branches: [develop]

jobs:
  validate:
    name: Validate Feature Branch
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt

    - name: Check commit messages
      run: |
        # Validate commit message format
        git log --oneline origin/develop..HEAD | while read line; do
          if ! echo "$line" | grep -qE "^[a-f0-9]+ (feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+"; then
            echo "Invalid commit message format: $line"
            exit 1
          fi
        done

    - name: Check for breaking changes
      run: |
        # Check for database migrations
        if git diff --name-only origin/develop..HEAD | grep -q "migrations/"; then
          echo "::warning::Database migrations detected. Review carefully."
        fi

    - name: Run tests with coverage
      run: |
        pytest --cov=app --cov-report=term-missing --cov-fail-under=80

    - name: Check code complexity
      run: |
        pip install radon
        radon cc app/ --min B
        radon mi app/ --min B
```

## 🦊 GitLab CI Configuration

### GitLab CI Pipeline
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - security
  - deploy-staging
  - deploy-production

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  POSTGRES_DB: test_soodam
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  REDIS_URL: redis://redis:6379/0

# Test stage
test:unit:
  stage: test
  image: python:3.11
  services:
    - name: postgis/postgis:15-3.3
      alias: postgres
    - name: redis:7-alpine
      alias: redis
  before_script:
    - pip install -r requirements.txt
    - pip install -r requirements-dev.txt
    - cp .env.example .env.test
  script:
    - python manage.py migrate --settings=config.settings.test
    - pytest tests/unit/ --cov=app --cov-report=xml
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    expire_in: 1 week

test:integration:
  stage: test
  image: python:3.11
  services:
    - name: postgis/postgis:15-3.3
      alias: postgres
    - name: redis:7-alpine
      alias: redis
  before_script:
    - pip install -r requirements.txt
    - pip install -r requirements-dev.txt
  script:
    - python manage.py migrate --settings=config.settings.test
    - pytest tests/integration/

lint:
  stage: test
  image: python:3.11
  before_script:
    - pip install black flake8 isort mypy
  script:
    - black --check .
    - flake8 .
    - isort --check-only .
    - mypy app/

# Build stage
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:latest
  only:
    - main
    - develop

# Security stage
security:sast:
  stage: security
  image: python:3.11
  before_script:
    - pip install bandit safety
  script:
    - bandit -r app/ -f json -o bandit-report.json
    - safety check --json --output safety-report.json
  artifacts:
    reports:
      sast: bandit-report.json
    paths:
      - safety-report.json
    expire_in: 1 week
  allow_failure: true

security:container:
  stage: security
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock 
      -v $PWD:/tmp/.cache/ aquasec/trivy image 
      --exit-code 0 --no-progress --format table 
      $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  only:
    - main
    - develop

# Deploy stages
deploy:staging:
  stage: deploy-staging
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying to staging..."
    - curl -X POST "$STAGING_WEBHOOK_URL" -H "Authorization: Bearer $STAGING_TOKEN"
  environment:
    name: staging
    url: https://staging-api.soodam.com
  only:
    - develop

deploy:production:
  stage: deploy-production
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying to production..."
    - curl -X POST "$PRODUCTION_WEBHOOK_URL" -H "Authorization: Bearer $PRODUCTION_TOKEN"
  environment:
    name: production
    url: https://api.soodam.com
  when: manual
  only:
    - main
```

## 🔨 Jenkins Pipeline

### Jenkinsfile
```groovy
// Jenkinsfile
pipeline {
    agent any
    
    environment {
        DOCKER_REGISTRY = 'your-registry.com'
        IMAGE_NAME = 'soodam-backend'
        DOCKER_CREDENTIALS = credentials('docker-registry-credentials')
        POSTGRES_CREDENTIALS = credentials('postgres-credentials')
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                }
            }
        }
        
        stage('Setup Environment') {
            steps {
                sh '''
                    python -m venv venv
                    . venv/bin/activate
                    pip install --upgrade pip
                    pip install -r requirements.txt
                    pip install -r requirements-dev.txt
                '''
            }
        }
        
        stage('Lint and Format') {
            parallel {
                stage('Black') {
                    steps {
                        sh '. venv/bin/activate && black --check .'
                    }
                }
                stage('Flake8') {
                    steps {
                        sh '. venv/bin/activate && flake8 .'
                    }
                }
                stage('isort') {
                    steps {
                        sh '. venv/bin/activate && isort --check-only .'
                    }
                }
            }
        }
        
        stage('Security Scan') {
            parallel {
                stage('Bandit') {
                    steps {
                        sh '''
                            . venv/bin/activate
                            bandit -r app/ -f json -o bandit-report.json
                        '''
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: '.',
                            reportFiles: 'bandit-report.json',
                            reportName: 'Bandit Security Report'
                        ])
                    }
                }
                stage('Safety') {
                    steps {
                        sh '''
                            . venv/bin/activate
                            safety check --json --output safety-report.json
                        '''
                    }
                }
            }
        }
        
        stage('Test') {
            steps {
                script {
                    docker.image('postgis/postgis:15-3.3').withRun('-e POSTGRES_DB=test_soodam -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres') { postgres ->
                        docker.image('redis:7-alpine').withRun() { redis ->
                            sh '''
                                . venv/bin/activate
                                export DB_HOST=localhost
                                export DB_NAME=test_soodam
                                export DB_USER=postgres
                                export DB_PASSWORD=postgres
                                export REDIS_URL=redis://localhost:6379/0
                                
                                python manage.py migrate --settings=config.settings.test
                                pytest tests/ --cov=app --cov-report=xml --cov-report=html --junitxml=test-results.xml
                            '''
                        }
                    }
                }
            }
            post {
                always {
                    publishTestResults testResultsPattern: 'test-results.xml'
                    publishCoverage adapters: [
                        coberturaAdapter('coverage.xml')
                    ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                }
            }
        }
        
        stage('Build Docker Image') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
            }
            steps {
                script {
                    def image = docker.build("${DOCKER_REGISTRY}/${IMAGE_NAME}:${env.GIT_COMMIT_SHORT}")
                    docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            steps {
                script {
                    sh '''
                        echo "Deploying to staging environment..."
                        # Add staging deployment commands
                    '''
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                input message: 'Deploy to production?', ok: 'Deploy'
                script {
                    sh '''
                        echo "Deploying to production environment..."
                        # Add production deployment commands
                    '''
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            slackSend(
                channel: '#deployments',
                color: 'good',
                message: "✅ Pipeline succeeded for ${env.JOB_NAME} - ${env.BUILD_NUMBER}"
            )
        }
        failure {
            slackSend(
                channel: '#deployments',
                color: 'danger',
                message: "❌ Pipeline failed for ${env.JOB_NAME} - ${env.BUILD_NUMBER}"
            )
        }
    }
}
```

## 🔄 Deployment Strategies

### Blue-Green Deployment
```bash
#!/bin/bash
# scripts/blue-green-deploy.sh

set -e

ENVIRONMENT=${1:-production}
NEW_VERSION=${2:-latest}
HEALTH_CHECK_URL="https://api.soodam.com/health"

echo "🔄 Starting Blue-Green deployment..."

# Determine current and new environments
if docker-compose -f docker-compose.blue.yml ps | grep -q "Up"; then
    CURRENT="blue"
    NEW="green"
else
    CURRENT="green"
    NEW="blue"
fi

echo "Current environment: $CURRENT"
echo "Deploying to: $NEW"

# Deploy to new environment
echo "🚀 Deploying to $NEW environment..."
docker-compose -f docker-compose.$NEW.yml up -d

# Wait for health check
echo "🏥 Waiting for health check..."
for i in {1..30}; do
    if curl -f $HEALTH_CHECK_URL; then
        echo "✅ Health check passed"
        break
    fi
    echo "Waiting for service to be ready... ($i/30)"
    sleep 10
done

# Switch traffic
echo "🔀 Switching traffic to $NEW environment..."
# Update load balancer configuration
# This would typically involve updating nginx config or cloud load balancer

# Stop old environment
echo "🛑 Stopping $CURRENT environment..."
docker-compose -f docker-compose.$CURRENT.yml down

echo "✅ Blue-Green deployment completed successfully!"
```

### Rolling Deployment
```bash
#!/bin/bash
# scripts/rolling-deploy.sh

set -e

NEW_VERSION=${1:-latest}
REPLICAS=3

echo "🔄 Starting rolling deployment..."

for i in $(seq 1 $REPLICAS); do
    echo "Updating replica $i/$REPLICAS..."
    
    # Update one replica
    docker service update --image soodam-backend:$NEW_VERSION soodam_backend_$i
    
    # Wait for health check
    sleep 30
    
    # Verify health
    if ! curl -f http://localhost:8000/health; then
        echo "❌ Health check failed for replica $i"
        exit 1
    fi
    
    echo "✅ Replica $i updated successfully"
done

echo "✅ Rolling deployment completed successfully!"
```

## 📊 Pipeline Monitoring

### Monitoring Dashboard
```yaml
# monitoring/pipeline-dashboard.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: pipeline-dashboard
data:
  dashboard.json: |
    {
      "dashboard": {
        "title": "CI/CD Pipeline Metrics",
        "panels": [
          {
            "title": "Build Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(jenkins_builds_success_total[1h]) / rate(jenkins_builds_total[1h]) * 100"
              }
            ]
          },
          {
            "title": "Deployment Frequency",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(deployments_total[1d])"
              }
            ]
          },
          {
            "title": "Lead Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, deployment_lead_time_seconds_bucket)"
              }
            ]
          }
        ]
      }
    }
```

### Notification Configuration
```yaml
# .github/workflows/notifications.yml
name: Notifications

on:
  workflow_run:
    workflows: ["CI/CD Pipeline"]
    types: [completed]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()

    - name: Notify Teams
      uses: skitionek/notify-microsoft-teams@master
      if: failure()
      with:
        webhook_url: ${{ secrets.TEAMS_WEBHOOK }}
        message: "❌ Pipeline failed for ${{ github.repository }}"
```

---

This CI/CD pipeline guide provides comprehensive automation for testing, building, security scanning, and deployment of the Soodam backend across multiple platforms and environments.
