# Production Deployment Guide

## 🚀 Deployment Overview

This guide covers deploying the Soodam backend to production environments. The application supports various deployment strategies including Docker containers, traditional server deployment, and cloud platforms.

## 🏗️ Deployment Architecture

### Production Stack
```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer (Nginx)                    │
├─────────────────────────────────────────────────────────────┤
│  Application Servers                                        │
│  ├── Gunicorn + Uvicorn Workers                            │
│  ├── Django Application                                     │
│  └── FastAPI Application                                    │
├─────────────────────────────────────────────────────────────┤
│  Background Services                                         │
│  ├── Celery Workers                                        │
│  ├── Celery Beat (Scheduler)                               │
│  └── Redis (Message Broker)                                │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── PostgreSQL + PostGIS                                  │
│  ├── Redis (Cache)                                         │
│  └── Elasticsearch                                         │
├─────────────────────────────────────────────────────────────┤
│  Monitoring & Logging                                       │
│  ├── Prometheus + Grafana                                  │
│  ├── Sentry (Error Tracking)                               │
│  └── Centralized Logging                                   │
└─────────────────────────────────────────────────────────────┘
```

## 🐳 Docker Deployment (Recommended)

### 1. Production Docker Compose

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.prod
    environment:
      - ENV_STATE=production
    volumes:
      - ./media:/app/media
      - ./static:/app/static
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./media:/app/media
      - ./static:/app/static
    depends_on:
      - backend
    restart: unless-stopped

  postgres:
    image: postgis/postgis:14-3.2
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  celery:
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: celery -A config worker -l info
    environment:
      - ENV_STATE=production
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: celery -A config beat -l info
    environment:
      - ENV_STATE=production
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 2. Production Dockerfile

Create `Dockerfile.prod`:
```dockerfile
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV ENV_STATE=production

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        gdal-bin \
        libgdal-dev \
        build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "config.asgi:application"]
```

### 3. Nginx Configuration

Create `nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # Static files
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /app/media/;
            expires 1y;
            add_header Cache-Control "public";
        }

        # API endpoints
        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Health check
        location /health {
            access_log off;
            proxy_pass http://backend;
        }
    }
}
```

### 4. Environment Configuration

Create `.env.prod`:
```bash
# Database
DB_NAME=soodam_prod
DB_USER=soodam_user
DB_PASSWORD=your_secure_production_password
DB_HOST=postgres
DB_PORT=5432

# Redis
REDIS_URL=redis://redis:6379/0

# Django
SECRET_KEY=your-super-secure-production-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Email
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# File Storage (AWS S3 example)
USE_S3=True
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket
AWS_S3_REGION_NAME=us-east-1

# Monitoring
SENTRY_DSN=your-sentry-dsn
```

### 5. Deploy to Production

```bash
# Clone repository on production server
git clone https://github.com/soodamApp/backend-soodam.git
cd backend-soodam/backend_soodam

# Set up environment
cp .env.prod.example .env.prod
nano .env.prod

# Create required directories
mkdir -p media static logs backups ssl

# Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# Create superuser
docker-compose -f docker-compose.prod.yml exec backend python manage.py createsuperuser

# Load initial data
docker-compose -f docker-compose.prod.yml exec backend python manage.py loaddata fixtures/initial_data.json
```

## 🖥️ Traditional Server Deployment

### 1. Server Setup (Ubuntu 20.04+)

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.10 python3.10-venv python3-pip
sudo apt install -y postgresql postgresql-contrib postgis
sudo apt install -y redis-server
sudo apt install -y nginx
sudo apt install -y supervisor
sudo apt install -y git curl wget

# Install Node.js (for some build tools)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

### 2. Application Setup

```bash
# Create application user
sudo adduser soodam
sudo usermod -aG sudo soodam

# Switch to application user
sudo su - soodam

# Clone repository
git clone https://github.com/soodamApp/backend-soodam.git
cd backend-soodam/backend_soodam

# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn uvicorn
```

### 3. Database Setup

```bash
# Create database and user
sudo -u postgres createdb soodam_prod
sudo -u postgres createuser soodam_user
sudo -u postgres psql -c "ALTER USER soodam_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE soodam_prod TO soodam_user;"
sudo -u postgres psql soodam_prod -c "CREATE EXTENSION postgis;"
```

### 4. Gunicorn Configuration

Create `/home/<USER>/backend-soodam/backend_soodam/gunicorn.conf.py`:
```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

### 5. Supervisor Configuration

Create `/etc/supervisor/conf.d/soodam.conf`:
```ini
[program:soodam]
command=/home/<USER>/backend-soodam/backend_soodam/venv/bin/gunicorn -c gunicorn.conf.py config.asgi:application
directory=/home/<USER>/backend-soodam/backend_soodam
user=soodam
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/soodam/gunicorn.log

[program:soodam-celery]
command=/home/<USER>/backend-soodam/backend_soodam/venv/bin/celery -A config worker -l info
directory=/home/<USER>/backend-soodam/backend_soodam
user=soodam
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/soodam/celery.log

[program:soodam-celery-beat]
command=/home/<USER>/backend-soodam/backend_soodam/venv/bin/celery -A config beat -l info
directory=/home/<USER>/backend-soodam/backend_soodam
user=soodam
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/soodam/celery-beat.log
```

## 🔒 Security Configuration

### 1. Firewall Setup

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Security Headers

Add to Nginx configuration:
```nginx
# Security headers
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Referrer-Policy "strict-origin-when-cross-origin";
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';";
```

## 📊 Monitoring Setup

### 1. Health Checks

```bash
# Create health check script
cat > /home/<USER>/health_check.sh << 'EOF'
#!/bin/bash
curl -f http://localhost:8000/api/health || exit 1
EOF

chmod +x /home/<USER>/health_check.sh

# Add to crontab
crontab -e
# Add: */5 * * * * /home/<USER>/health_check.sh
```

### 2. Log Rotation

Create `/etc/logrotate.d/soodam`:
```
/var/log/soodam/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 soodam soodam
    postrotate
        supervisorctl restart soodam
    endscript
}
```

## 🔄 Deployment Automation

### 1. Deployment Script

Create `deploy.sh`:
```bash
#!/bin/bash
set -e

echo "Starting deployment..."

# Pull latest code
git pull origin main

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic --noinput

# Restart services
sudo supervisorctl restart soodam
sudo supervisorctl restart soodam-celery
sudo supervisorctl restart soodam-celery-beat

# Reload Nginx
sudo nginx -t && sudo systemctl reload nginx

echo "Deployment completed successfully!"
```

### 2. Zero-Downtime Deployment

For zero-downtime deployments, consider:
- Blue-green deployment strategy
- Rolling updates with multiple application instances
- Database migration strategies
- Health check integration

## 🆘 Troubleshooting

### Common Issues

1. **Application won't start**: Check logs in `/var/log/soodam/`
2. **Database connection issues**: Verify PostgreSQL service and credentials
3. **Static files not loading**: Check Nginx configuration and file permissions
4. **SSL certificate issues**: Verify certificate installation and renewal

### Monitoring Commands

```bash
# Check application status
sudo supervisorctl status

# View logs
tail -f /var/log/soodam/gunicorn.log
tail -f /var/log/nginx/access.log

# Check system resources
htop
df -h
free -m
```

---

This deployment guide provides a comprehensive approach to deploying the Soodam backend in production. Choose the deployment method that best fits your infrastructure and requirements.
