# Docker Setup Guide

## 🐳 Overview

This guide covers comprehensive Docker setup for the Soodam backend, including development, staging, and production configurations with multi-stage builds, optimization, and orchestration.

## 📁 Docker File Structure

```
backend-soodam/
├── Dockerfile                    # Main application Dockerfile
├── Dockerfile.dev               # Development-specific Dockerfile
├── docker-compose.yml           # Development compose
├── docker-compose.prod.yml      # Production compose
├── docker-compose.override.yml  # Local overrides
├── .dockerignore               # Docker ignore file
└── docker/
    ├── nginx/
    │   ├── Dockerfile
    │   └── nginx.conf
    ├── postgres/
    │   ├── Dockerfile
    │   └── init.sql
    └── redis/
        └── redis.conf
```

## 🏗️ Multi-Stage Dockerfile

### Production Dockerfile
```dockerfile
# Dockerfile
# Multi-stage build for production

# Stage 1: Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libgdal-dev \
    gdal-bin \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Stage 2: Runtime stage
FROM python:3.11-slim as runtime

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH"

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    libgdal28 \
    gdal-bin \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create app user
RUN groupadd -r app && useradd -r -g app app

# Create app directory
WORKDIR /app

# Copy application code
COPY --chown=app:app . .

# Create necessary directories
RUN mkdir -p /app/media /app/static /app/logs && \
    chown -R app:app /app

# Switch to app user
USER app

# Collect static files
RUN python manage.py collectstatic --noinput

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "gevent", "config.wsgi:application"]
```

### Development Dockerfile
```dockerfile
# Dockerfile.dev
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libgdal-dev \
    gdal-bin \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt requirements-dev.txt ./
RUN pip install --upgrade pip && \
    pip install -r requirements-dev.txt

# Copy application code
COPY . .

# Create media and static directories
RUN mkdir -p media static logs

# Expose port
EXPOSE 8000

# Start development server
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

## 🐳 Docker Compose Configurations

### Development Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgis/postgis:15-3.3
    container_name: soodam_postgres_dev
    environment:
      POSTGRES_DB: soodam_dev
      POSTGRES_USER: soodam_dev
      POSTGRES_PASSWORD: dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U soodam_dev -d soodam_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: soodam_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: soodam_backend_dev
    volumes:
      - .:/app
      - media_data:/app/media
      - static_data:/app/static
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DB_HOST=postgres
      - DB_NAME=soodam_dev
      - DB_USER=soodam_dev
      - DB_PASSWORD=dev_password
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  celery:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: soodam_celery_dev
    volumes:
      - .:/app
    environment:
      - DEBUG=True
      - DB_HOST=postgres
      - DB_NAME=soodam_dev
      - DB_USER=soodam_dev
      - DB_PASSWORD=dev_password
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    command: celery -A config worker -l info

  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: soodam_celery_beat_dev
    volumes:
      - .:/app
    environment:
      - DEBUG=True
      - DB_HOST=postgres
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    command: celery -A config beat -l info

volumes:
  postgres_data:
  redis_data:
  media_data:
  static_data:

networks:
  default:
    name: soodam_network
```

### Production Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    build:
      context: ./docker/nginx
      dockerfile: Dockerfile
    container_name: soodam_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_data:/app/static:ro
      - media_data:/app/media:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped

  postgres:
    image: postgis/postgis:15-3.3
    container_name: soodam_postgres_prod
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: soodam_redis_prod
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: soodam_backend_prod
    volumes:
      - media_data:/app/media
      - static_data:/app/static
      - ./logs:/app/logs
    environment:
      - DEBUG=False
      - DB_HOST=postgres
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  celery:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: soodam_celery_prod
    volumes:
      - media_data:/app/media
      - ./logs:/app/logs
    environment:
      - DEBUG=False
      - DB_HOST=postgres
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: celery -A config worker -l info --concurrency=4

  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: soodam_celery_beat_prod
    volumes:
      - ./logs:/app/logs
    environment:
      - DEBUG=False
      - DB_HOST=postgres
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: celery -A config beat -l info

volumes:
  postgres_data:
  redis_data:
  media_data:
  static_data:

networks:
  default:
    name: soodam_prod_network
```

## 🔧 Docker Configuration Files

### .dockerignore
```
# .dockerignore
.git
.gitignore
README.md
Dockerfile*
docker-compose*
.dockerignore
.env*
.venv
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.log
logs/
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~
node_modules/
npm-debug.log*
```

### Nginx Configuration
```dockerfile
# docker/nginx/Dockerfile
FROM nginx:alpine

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create log directory
RUN mkdir -p /var/log/nginx

# Expose ports
EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# docker/nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    upstream backend {
        least_conn;
        server backend:8000 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        server_name api.soodam.com;
        client_max_body_size 100M;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # Static files
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /app/media/;
            expires 1M;
            add_header Cache-Control "public";
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

## 🚀 Docker Management Scripts

### Build and Deploy Script
```bash
#!/bin/bash
# scripts/docker-deploy.sh

set -e

# Configuration
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}
REGISTRY=${DOCKER_REGISTRY:-"your-registry.com"}
IMAGE_NAME="soodam-backend"

echo "🐳 Deploying Soodam Backend - Environment: $ENVIRONMENT, Version: $VERSION"

# Build image
echo "📦 Building Docker image..."
if [ "$ENVIRONMENT" = "development" ]; then
    docker build -f Dockerfile.dev -t $IMAGE_NAME:$VERSION .
else
    docker build -t $IMAGE_NAME:$VERSION .
fi

# Tag for registry
if [ "$ENVIRONMENT" != "development" ]; then
    echo "🏷️ Tagging image for registry..."
    docker tag $IMAGE_NAME:$VERSION $REGISTRY/$IMAGE_NAME:$VERSION
    docker tag $IMAGE_NAME:$VERSION $REGISTRY/$IMAGE_NAME:latest
    
    # Push to registry
    echo "📤 Pushing to registry..."
    docker push $REGISTRY/$IMAGE_NAME:$VERSION
    docker push $REGISTRY/$IMAGE_NAME:latest
fi

# Deploy based on environment
case $ENVIRONMENT in
    "development")
        echo "🚀 Starting development environment..."
        docker-compose up -d
        ;;
    "staging"|"production")
        echo "🚀 Deploying to $ENVIRONMENT..."
        docker-compose -f docker-compose.prod.yml up -d
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "✅ Deployment completed successfully!"
```

### Database Backup Script
```bash
#!/bin/bash
# scripts/backup-database.sh

set -e

CONTAINER_NAME="soodam_postgres_prod"
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="soodam_backup_$TIMESTAMP.sql"

echo "📦 Creating database backup..."

# Create backup directory
mkdir -p $BACKUP_DIR

# Create backup
docker exec $CONTAINER_NAME pg_dump -U $DB_USER -d $DB_NAME > $BACKUP_DIR/$BACKUP_FILE

# Compress backup
gzip $BACKUP_DIR/$BACKUP_FILE

echo "✅ Backup created: $BACKUP_DIR/$BACKUP_FILE.gz"

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "soodam_backup_*.sql.gz" -mtime +7 -delete

echo "🧹 Old backups cleaned up"
```

### Health Check Script
```bash
#!/bin/bash
# scripts/health-check.sh

set -e

SERVICES=("postgres" "redis" "backend" "nginx")
FAILED_SERVICES=()

echo "🏥 Performing health checks..."

for service in "${SERVICES[@]}"; do
    echo "Checking $service..."
    
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        FAILED_SERVICES+=($service)
    fi
done

if [ ${#FAILED_SERVICES[@]} -eq 0 ]; then
    echo "🎉 All services are healthy!"
    exit 0
else
    echo "💥 Failed services: ${FAILED_SERVICES[*]}"
    exit 1
fi
```

## 📊 Docker Monitoring

### Container Monitoring
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: soodam_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: soodam_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: soodam_cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro

volumes:
  prometheus_data:
  grafana_data:
```

## 🔧 Docker Optimization

### Image Optimization Tips
```dockerfile
# Optimized Dockerfile techniques

# 1. Use specific base image versions
FROM python:3.11.5-slim

# 2. Combine RUN commands to reduce layers
RUN apt-get update && apt-get install -y \
    package1 \
    package2 \
    && rm -rf /var/lib/apt/lists/*

# 3. Use .dockerignore effectively
# 4. Multi-stage builds for smaller images
# 5. Use non-root user for security
# 6. Order layers by change frequency
```

### Performance Tuning
```yaml
# docker-compose performance settings
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    sysctls:
      - net.core.somaxconn=1024
```

---

This Docker setup guide provides comprehensive containerization for the Soodam backend with development, staging, and production configurations, monitoring, and optimization strategies.
