# Manager Accessibility Fix

## Issue Description

**Error:** `Manager isn't accessible via CustomUserModel instances`

This error occurs when you try to access a Django model manager from an instance instead of the model class.

## 🚨 **The Problem**

### **Incorrect Usage (Causes Error):**
```python
# WRONG - Accessing manager from instance
user = CustomUserModel.objects.get(id=1)
user.objects._normalize_phone_number(phone)  # ❌ ERROR!
```

### **Root Cause:**
In the original `save()` method:
```python
def save(self, *args, **kwargs):
    if self.phone_number:
        # ❌ WRONG - self.objects tries to access manager from instance
        self.phone_number = self.objects._normalize_phone_number(self.phone_number)
```

## ✅ **The Solution**

I implemented a **three-layer approach** to fix this issue:

### **1. Standalone Utility Function**
```python
def normalize_iranian_phone_number(phone_number: str) -> str:
    """
    Utility function that can be used anywhere without needing
    access to model instances or managers.
    """
    # Remove spaces, dashes, and other characters
    cleaned = ''.join(filter(str.isdigit, phone_number))
    
    # Handle different formats with proper length validation
    if cleaned.startswith('0098') and len(cleaned) == 14:
        cleaned = '0' + cleaned[4:]
    elif cleaned.startswith('98') and len(cleaned) == 12:
        cleaned = '0' + cleaned[2:]
    elif not cleaned.startswith('0') and len(cleaned) == 10:
        cleaned = '0' + cleaned
    
    # Validate final format
    if not re.match(IRANIAN_PHONE_REGEX, cleaned):
        raise ValueError(_('فرمت شماره تلفن نامعتبر است'))
    
    return cleaned
```

### **2. Manager Method (Uses Utility)**
```python
class EnhancedUserManager(BaseUserManager):
    def _normalize_phone_number(self, phone_number: str) -> str:
        """Manager method for phone number normalization."""
        return normalize_iranian_phone_number(phone_number)
```

### **3. Instance Method (Uses Utility)**
```python
class CustomUserModel(AbstractBaseUser, PermissionsMixin, BaseModelMixin):
    def _normalize_phone_number_instance(self, phone_number: str) -> str:
        """Instance method for phone number normalization."""
        return normalize_iranian_phone_number(phone_number)
    
    def save(self, *args, **kwargs):
        """Enhanced save method with validation and logging."""
        self.full_clean()
        
        # ✅ CORRECT - Use instance method, not manager
        if self.phone_number:
            self.phone_number = self._normalize_phone_number_instance(self.phone_number)
        
        # ... rest of save logic
```

## 🔧 **Key Improvements**

### **1. Separation of Concerns**
- ✅ **Utility Function**: Pure function, no dependencies
- ✅ **Manager Method**: For class-level operations
- ✅ **Instance Method**: For instance-level operations

### **2. Correct Access Patterns**
```python
# ✅ CORRECT - Manager access from class
CustomUserModel.objects._normalize_phone_number(phone)

# ✅ CORRECT - Instance method from instance
user._normalize_phone_number_instance(phone)

# ✅ CORRECT - Utility function from anywhere
normalize_iranian_phone_number(phone)
```

### **3. No Manager Accessibility Issues**
```python
# Before (ERROR):
def save(self, *args, **kwargs):
    self.phone_number = self.objects._normalize_phone_number(phone)  # ❌

# After (WORKS):
def save(self, *args, **kwargs):
    self.phone_number = self._normalize_phone_number_instance(phone)  # ✅
```

## 📋 **Usage Examples**

### **1. Creating Users (Manager Level)**
```python
# Manager handles normalization automatically
user = CustomUserModel.objects.create_user(
    phone_number="************",  # Will be normalized to 09123456789
    email="<EMAIL>"
)
```

### **2. Updating Users (Instance Level)**
```python
# Instance method handles normalization
user = CustomUserModel.objects.get(id=1)
user.phone_number = "************"  # Will be normalized on save
user.save()
```

### **3. Direct Normalization (Utility Function)**
```python
# Use anywhere in your code
from app.models.user import normalize_iranian_phone_number

normalized = normalize_iranian_phone_number("+98 ************")
print(normalized)  # Output: 09123456789
```

### **4. Manager Methods**
```python
# Get user by phone (with automatic normalization)
user = CustomUserModel.objects.get_by_phone("************")

# Create users with various phone formats
user1 = CustomUserModel.objects.create_user(phone_number="09123456789")
user2 = CustomUserModel.objects.create_user(phone_number="************")
user3 = CustomUserModel.objects.create_user(phone_number="+98 ************")
# All will have normalized phone numbers
```

## 🛠 **Migration Compatibility**

The fix is also applied to migration functions:

```python
def normalize_phone_numbers(apps, schema_editor):
    """Normalize existing phone numbers during migration."""
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    
    for user in CustomUserModel.objects.all():
        if user.phone_number:
            # Use the utility function (no manager access issues)
            normalized = normalize_iranian_phone_number(user.phone_number)
            if normalized != user.phone_number:
                user.phone_number = normalized
                user.save()
```

## ✅ **Benefits of This Approach**

### **1. No Manager Access Errors**
- ✅ **Instance methods** don't access managers
- ✅ **Manager methods** work correctly
- ✅ **Utility function** works everywhere

### **2. Reusable Code**
- ✅ **Utility function** can be used in views, serializers, forms
- ✅ **Consistent normalization** across the entire application
- ✅ **Easy testing** of normalization logic

### **3. Clean Architecture**
- ✅ **Single responsibility** - each method has one job
- ✅ **No circular dependencies** - utility function is independent
- ✅ **Easy maintenance** - changes in one place

### **4. Performance**
- ✅ **No unnecessary manager lookups**
- ✅ **Direct function calls** are faster
- ✅ **Reduced memory overhead**

## 🧪 **Testing**

The fix includes comprehensive testing:

```python
# Test utility function
result = normalize_iranian_phone_number("************")
assert result == "09123456789"

# Test manager method
manager = CustomUserModel.objects
result = manager._normalize_phone_number("************")
assert result == "09123456789"

# Test instance method
user = CustomUserModel()
result = user._normalize_phone_number_instance("************")
assert result == "09123456789"

# Test save method (no manager access errors)
user = CustomUserModel(phone_number="************")
user.save()  # Should work without errors
assert user.phone_number == "09123456789"
```

## 📝 **Files Modified**

1. **`app/models/user.py`**:
   - Added `normalize_iranian_phone_number()` utility function
   - Updated manager `_normalize_phone_number()` method
   - Added instance `_normalize_phone_number_instance()` method
   - Fixed `save()` method to use instance method

2. **`migration_guide.py`**:
   - Updated migration functions to use utility function

3. **`test_manager_fix.py`**:
   - Comprehensive test suite for the fix

## 🚀 **Result**

✅ **No more "Manager isn't accessible via CustomUserModel instances" errors**
✅ **Phone number normalization works correctly in all contexts**
✅ **Clean, maintainable, and testable code**
✅ **Consistent behavior across the application**

The fix provides a robust solution that eliminates the manager accessibility error while maintaining all the phone number normalization functionality.
