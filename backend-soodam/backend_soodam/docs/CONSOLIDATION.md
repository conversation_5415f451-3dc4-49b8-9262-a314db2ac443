# API and Schema Consolidation

This document outlines the consolidation of API and schema files in the Soodam backend project.

## Consolidated Files

### API Consolidation

1. **Advertisement API**
   - Original files: `advertisement.py`, `advertisement_v2.py`, `advertisement_advanced.py`
   - Consolidated file: `advertisement.py`

2. **Admin API**
   - Original files: `admin.py`, `admin_enhanced.py`
   - Consolidated file: `admin.py`

### Schema Consolidation

1. **Advertisement Schema**
   - Original files: `advertising.py`, `advertisement_v2.py`
   - Consolidated file: `advertisement.py`

## Installation Requirements

After consolidation, ensure all required packages are installed. This project is designed to run inside Docker containers.

### Using Docker (Recommended)

1. For development environment:
   ```bash
   # Stop any running containers
   sudo docker-compose -f docker-compose.dev.yml down

   # Install all dependencies using requirements.txt
   # First, modify the runlocalserver.sh script to use system Python instead of Poetry
   # This has already been done in the repository

   # Start the container
   sudo docker-compose -f docker-compose.dev.yml up -d

   # Install all dependencies using requirements.txt
   sudo docker-compose -f docker-compose.dev.yml exec fastapi_dev pip install -r requirements.txt

   # Apply migrations
   sudo docker-compose -f docker-compose.dev.yml exec fastapi_dev python manage.py makemigrations
   sudo docker-compose -f docker-compose.dev.yml exec fastapi_dev python manage.py migrate

   # Restart the container to apply changes
   sudo docker-compose -f docker-compose.dev.yml restart
   ```

2. For production environment:
   ```bash
   # Stop any running containers
   sudo docker compose -f docker-compose.prod.yml down

   # Note: You'll need to modify the production server script similar to how we modified runlocalserver.sh
   # to use system Python instead of Poetry

   # Start the container
   sudo docker compose -f docker-compose.prod.yml up -d

   # Install all dependencies using requirements.txt
   sudo docker compose -f docker-compose.prod.yml exec fastapi_prod pip install -r requirements.txt

   # Apply migrations
   sudo docker compose -f docker-compose.prod.yml exec fastapi_prod python manage.py makemigrations
   sudo docker compose -f docker-compose.prod.yml exec fastapi_prod python manage.py migrate

   # Restart the container to apply changes
   sudo docker compose -f docker-compose.prod.yml restart
   ```

### Using Aliases

You can also use the provided aliases in the root directory:

```bash
# Source the alias file
source alias.sh

# Then use the aliases
up
# Wait for containers to start, then:
fastapibash
pip install -r requirements.txt
exit
makemigrations
migrate
```

## Testing the Consolidated Code

To verify that the consolidated code works correctly:

### Using Docker

1. Start the development environment:
   ```bash
   sudo docker-compose -f docker-compose.dev.yml up --build
   ```

2. Or use the alias:
   ```bash
   source alias.sh
   build
   up
   ```

### Accessing the Application

1. FastAPI application will be available at:
   - http://localhost:4000 (FastAPI)
   - http://localhost:4001 (Django)

2. Check the logs for any errors related to the consolidated modules

## Potential Issues and Solutions

### Django Model Meta Class Error

If you encounter an error like `TypeError: 'class Meta' got invalid attribute(s): index_together`:

This is because in Django 4.1+, the `index_together` option in model Meta classes has been deprecated and replaced with `indexes`. We've already fixed this in the base.py file by changing:

```python
# Old syntax (causing the error)
index_together = ('recipient', 'unread')

# New syntax (fixed)
indexes = [
    models.Index(fields=['recipient', 'unread']),
]
```

If you encounter similar errors in other model files, apply the same fix.

### Missing Module Errors

If you encounter errors like `ModuleNotFoundError: No module named 'app.utils.slugify'`:

We've created the missing `slugify.py` module in the `app/utils` directory. This module provides:
- A basic `slugify()` function that converts text to URL-friendly slugs
- A `unique_slugify()` function that creates unique slugs for model instances

If you encounter an error like `ModuleNotFoundError: No module named 'app.models.advertising'` in the GraphQL schema:

We've updated the imports in the GraphQL schema to use existing models from the `advertising.py` file:
- `MainCategoryModel` as `AdvertisementCategoryModel`
- `AdvertisingImagesModel` as `AdvertisementImageModel`
- `PropertyTextValueModel` as `AdvertisementAttributeModel`
- `AdvertisementViewModel` as `AdvertisementStatisticsModel`
- `AddressesModel` from `gis_geolocation.py` as `AdvertisementLocationModel`

For `AdvertisementTagModel`, we've defined a temporary model in the schema file itself since there's no direct equivalent in the existing models.

If you encounter an error like `Exception: Don't know how to convert the Django field app.AddressesModel.geolocation (<class 'django.contrib.gis.db.models.fields.PointField'>)`:

This error occurs because GraphQL doesn't know how to handle Django's GIS PointField. We've fixed this by:
1. Creating a custom `PointScalar` class in the GraphQL schema
2. Modifying the `AdvertisementLocationType` to explicitly handle the geolocation field
3. Adding a custom resolver for the geolocation field that converts the Point object to a simple longitude/latitude dictionary

If you encounter an error like `TypeError: Query fields cannot be resolved. 'Meta.fields' must not contain non-model field names: slug, parent_id`:

This error occurs because the GraphQL schema is trying to use fields that don't exist in the model. We've fixed this by:
1. Explicitly listing the fields that exist in each model instead of using `fields = "__all__"`
2. Updating the filter_fields to only include fields that exist in the model
3. Fixing this for all GraphQL types in the schema:
   - `AdvertisementCategoryType`
   - `AdvertisementImageType`
   - `AdvertisementAttributeType`
   - `AdvertisementTagType`
   - `AdvertisementStatisticsType`

If you encounter an error like `Query fields cannot be resolved. 'Meta.fields' must not contain non-model field names: category_id, location_id, is_featured`:

This error occurs because the GraphQL schema is trying to use fields that don't exist in the model. We've fixed this by:
1. Updating the `AdvertisementType` class to explicitly list the fields that exist in the `AdvertisementModel`
2. Updating the filter_fields to only include fields that exist in the model
3. Fixing the resolver methods to use the correct field names:
   - Changed `category_id` to `main_category_id`
   - Changed `location_id` to `address_id`
   - Changed `is_featured` to `is_active`
   - Changed string status values to use the `AdvertisementStatus` enum

If you encounter an error like `ImportError: cannot import name 'get_token_from_query' from 'app.dependencies.auth'`:

This error occurs because the websocket.py file is trying to import a function that doesn't exist in the auth.py file. We've fixed this by:
1. Adding the missing `get_token_from_query` function to the auth.py file
2. The function validates a token provided as a query parameter and returns the associated user
3. This function is similar to the existing `get_current_user` function but is designed to work with tokens from query parameters instead of from the Authorization header

If you encounter a syntax error like `SyntaxError: positional argument follows keyword argument`:

This error occurs when a positional argument is placed after a keyword argument in a function call. In Python, all positional arguments must come before any keyword arguments. We've fixed this by:
1. Reordering the arguments in the `filter()` method calls to ensure that the `Q` objects (which are positional arguments) come before the keyword arguments like `id=room_id` and `is_active=True`
2. Changing the `values()` method to `avalues()` for asynchronous operation

If you encounter an error like `ModuleNotFoundError: No module named 'pyotp'` or `ModuleNotFoundError: No module named 'elasticsearch'`:

These errors occur when required libraries are not installed. We've fixed this by:
1. Adding the following packages to the `pyproject.toml` file:
   - `pyotp="^2.9.0"`
   - `graphene-django="^3.2.3"`
   - `django-filter="^24.3"`
   - `elasticsearch="^8.12.0"`
   - `elasticsearch-dsl="^8.12.0"`
2. Adding the same packages to the `requirements.txt` file
3. Updating the Dockerfile to install dependencies from both requirements.txt and pyproject.toml

To apply these changes:
1. Rebuild your Docker image:
   ```bash
   docker-compose build
   docker-compose up -d
   ```

If you're not using Docker:
1. Install dependencies using both pip and poetry:
   ```bash
   pip install -r requirements.txt
   poetry install
   ```
2. Or install the missing packages directly:
   ```bash
   pip install pyotp graphene-django django-filter elasticsearch elasticsearch-dsl
   ```

If you encounter an error like `ImportError: cannot import name 'AdvertisementFavoriteModel' from 'app.models.advertising'`, `ImportError: cannot import name 'AdvertisementLocationModel' from 'app.models.advertising'`, or `ImportError: cannot import name 'AdvertisementTagModel' from 'app.models.advertising'`:

These errors occur because some models are missing from the advertising.py file. We've fixed this by:

1. Adding the `AdvertisementFavoriteModel` class to the advertising.py file:
   - The model is similar to the existing `FavoriteAdvertisementModel` but with a different table name and verbose name
   - This model is used by the recommendation engine and analytics service

2. Adding the `AdvertisementLocationModel` class to the advertising.py file:
   - The model provides a simplified interface to the AddressesModel
   - It includes fields for city, state, country, latitude, and longitude
   - It has a helper method `from_address` to create a location from an AddressesModel instance
   - This model is used by the Elasticsearch sync service and GraphQL API

3. Adding the `AdvertisementTagModel` class to the advertising.py file:
   - The model provides a way to tag advertisements for better search and recommendations
   - It includes fields for name, slug, advertisement, and tag_id
   - This model is used by the Elasticsearch sync service and recommendation engine

If you encounter an error like `RuntimeError: Stream consumed` when using the `/api/auth/get_ver_code` endpoint:

This error occurs because the request body is being consumed multiple times. In FastAPI/Starlette, the request body can only be read once. We've fixed this by:
1. Modifying the `PrometheusMiddleware` in `app/core/prometheus.py` to not try to read the request body
2. Instead, we're using a default value of 0 for the request size metric
3. This prevents the middleware from trying to consume the request body that has already been consumed by the endpoint handler

If you encounter a warning like `RuntimeWarning: coroutine 'setup_autocomplete' was never awaited`:

This warning occurs because an async function is being called without being awaited. We've fixed this by:
1. Adding `import asyncio` to the imports in `config/asgi.py`
2. Creating a new async function `setup_async_services()` that properly awaits both `setup_elasticsearch()` and `setup_autocomplete()`
3. Using `asyncio.get_event_loop()` to create a task for the async setup function
4. This ensures that the async functions are properly awaited and executed in the event loop

If you encounter an error like `ConnectionError: Cannot connect to host localhost:9200` when starting the application:

This error occurs because the application is trying to connect to Elasticsearch, but it's not available. We've fixed this by:
1. Modifying the `setup_elasticsearch()` and `setup_autocomplete()` functions to gracefully handle connection errors
2. Adding proper error handling in the `setup_async_services()` function in `config/asgi.py`
3. Making Elasticsearch optional by checking the `ELASTICSEARCH_ENABLED` environment variable
4. Adding proper logging to inform about the status of Elasticsearch

### Running with Docker Compose

The project already has Elasticsearch configured in the `docker-compose.dev.yml` file in the root directory. To use it:

```bash
# Run the development environment with Elasticsearch
docker-compose -f docker-compose.dev.yml up
```

This will start:
- The FastAPI backend service
- PostgreSQL database
- Elasticsearch service

The Elasticsearch service is already properly configured with:
- Single-node discovery
- Memory limits
- Port mapping (9200)
- Volume for data persistence
- Health checks

### Running without Docker

If you're running the application directly (not with Docker):

1. To run without Elasticsearch:
```bash
# Add this to your fastapi.env.dev file or set it as an environment variable
ELASTICSEARCH_ENABLED=false
```

2. To run with Elasticsearch:
   - Install Elasticsearch locally
   - Start it before running the application
   - Set the environment variable:
```bash
ELASTICSEARCH_URL=http://localhost:9200
```

If you encounter similar errors for other missing modules or field types, you'll need to create those modules or custom scalar types as needed.

### Import Errors

If you encounter other import errors:

1. Make sure all required packages are installed
2. Check that the import paths in your code match the new consolidated structure
3. Verify that the virtual environment is activated

### Database Migrations

The consolidation doesn't change the database models, so no migrations should be necessary. However, if you encounter database-related issues:

1. Run migrations to ensure the database schema is up to date:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

### API Endpoint Issues

If API endpoints aren't working as expected:

1. Check the router files to ensure they're importing from the correct consolidated files
2. Verify that all method signatures in the consolidated files match the original files
3. Test each endpoint individually to identify specific issues

## Benefits of Consolidation

1. **Simplified Maintenance**: Only one file to maintain per feature area
2. **Improved Code Organization**: Clear separation of concerns with methods grouped by functionality
3. **Enhanced Documentation**: Comprehensive docstrings for all methods
4. **Reduced Duplication**: Elimination of duplicate code across multiple files
5. **Better Discoverability**: Easier to find all related methods in one place

## Future Improvements

1. Add more comprehensive unit tests for the consolidated APIs
2. Implement additional caching strategies
3. Add support for more advanced filtering and search options
4. Enhance performance through query optimization
5. Add support for bulk operations
