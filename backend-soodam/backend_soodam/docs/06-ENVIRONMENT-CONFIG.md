# Environment Configuration Guide

## 🔧 Overview

This guide covers all environment variables and configuration options for the <PERSON>dam backend. Proper configuration is essential for security, performance, and functionality across different environments.

## 📁 Configuration Files

### Environment Files
- `fastapi.env` - Main environment configuration
- `docker-compose.yml` - Docker service configuration
- `config/settings/` - Django settings modules

### Settings Structure
```
config/settings/
├── __init__.py          # Base settings
├── development.py       # Development settings
├── production.py        # Production settings
├── staging.py          # Staging settings
└── test.py             # Test settings
```

## 🔐 Core Environment Variables

### Database Configuration
```bash
# PostgreSQL Database
DB_NAME=soodam_db                    # Database name
DB_USER=soodam_user                  # Database username
DB_PASSWORD=your_secure_password     # Database password
DB_HOST=localhost                    # Database host (postgres for Docker)
DB_PORT=5432                         # Database port
DB_ENGINE=django.contrib.gis.db.backends.postgis  # PostGIS engine

# Database Connection Pool
DB_CONN_MAX_AGE=600                  # Connection max age in seconds
DB_CONN_HEALTH_CHECKS=true           # Enable connection health checks
```

### Redis Configuration
```bash
# Redis Cache and Sessions
REDIS_URL=redis://localhost:6379/0   # Redis connection URL
REDIS_CACHE_DB=0                     # Cache database number
REDIS_SESSION_DB=1                   # Session database number
REDIS_CELERY_DB=2                    # Celery broker database number

# Redis Connection Pool
REDIS_CONNECTION_POOL_KWARGS={"max_connections": 20}
```

### Django Core Settings
```bash
# Security
SECRET_KEY=your-super-secret-key-here-min-50-chars  # Django secret key
DEBUG=False                          # Debug mode (True for development)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com  # Allowed hosts

# Environment
ENV_STATE=production                 # Environment state (development/staging/production)
DJANGO_SETTINGS_MODULE=config.settings.production  # Settings module
```

### JWT Authentication
```bash
# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here        # JWT signing key
JWT_ALGORITHM=HS256                            # JWT algorithm
ACCESS_TOKEN_EXPIRE_MINUTES=15                 # Access token lifetime
REFRESH_TOKEN_EXPIRE_DAYS=7                    # Refresh token lifetime
JWT_ISSUER=soodam-api                         # Token issuer
JWT_AUDIENCE=soodam-users                     # Token audience
```

### Email Configuration
```bash
# Email Backend
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend  # Email backend
EMAIL_HOST=smtp.gmail.com                     # SMTP host
EMAIL_PORT=587                                 # SMTP port
EMAIL_USE_TLS=True                            # Use TLS
EMAIL_USE_SSL=False                           # Use SSL
EMAIL_HOST_USER=<EMAIL>          # SMTP username
EMAIL_HOST_PASSWORD=your-app-password         # SMTP password
DEFAULT_FROM_EMAIL=<EMAIL>         # Default from email

# Development Email (Console Backend)
# EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
```

### File Storage
```bash
# Local Storage (Development)
MEDIA_URL=/media/                             # Media URL prefix
MEDIA_ROOT=/app/media                         # Media files directory
STATIC_URL=/static/                           # Static URL prefix
STATIC_ROOT=/app/static                       # Static files directory

# AWS S3 Storage (Production)
USE_S3=True                                   # Enable S3 storage
AWS_ACCESS_KEY_ID=your-aws-access-key         # AWS access key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key     # AWS secret key
AWS_STORAGE_BUCKET_NAME=soodam-media          # S3 bucket name
AWS_S3_REGION_NAME=us-east-1                  # S3 region
AWS_S3_CUSTOM_DOMAIN=cdn.soodam.com           # CloudFront domain (optional)
AWS_DEFAULT_ACL=public-read                   # Default ACL for uploads
```

## 🔒 Security Configuration

### HTTPS and Security Headers
```bash
# HTTPS Configuration (Production)
SECURE_SSL_REDIRECT=True                      # Redirect HTTP to HTTPS
SECURE_HSTS_SECONDS=31536000                  # HSTS max age (1 year)
SECURE_HSTS_INCLUDE_SUBDOMAINS=True           # Include subdomains in HSTS
SECURE_HSTS_PRELOAD=True                      # Enable HSTS preload
SECURE_CONTENT_TYPE_NOSNIFF=True              # Prevent MIME sniffing
SECURE_BROWSER_XSS_FILTER=True                # Enable XSS filter
SECURE_REFERRER_POLICY=strict-origin-when-cross-origin

# Cookie Security
SESSION_COOKIE_SECURE=True                    # Secure session cookies
SESSION_COOKIE_HTTPONLY=True                  # HTTP-only session cookies
SESSION_COOKIE_SAMESITE=Lax                   # SameSite policy
CSRF_COOKIE_SECURE=True                       # Secure CSRF cookies
CSRF_COOKIE_HTTPONLY=True                     # HTTP-only CSRF cookies
```

### CORS Configuration
```bash
# CORS Settings
CORS_ALLOWED_ORIGINS=https://soodam.com,https://www.soodam.com
CORS_ALLOW_CREDENTIALS=True                   # Allow credentials
CORS_ALLOW_ALL_ORIGINS=False                  # Don't allow all origins in production
CORS_ALLOWED_HEADERS=accept,accept-encoding,authorization,content-type,dnt,origin,user-agent,x-csrftoken,x-requested-with
```

## 📊 External Services

### Elasticsearch
```bash
# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost                  # Elasticsearch host
ELASTICSEARCH_PORT=9200                       # Elasticsearch port
ELASTICSEARCH_USE_SSL=False                   # Use SSL for Elasticsearch
ELASTICSEARCH_USERNAME=elastic                # Elasticsearch username (if auth enabled)
ELASTICSEARCH_PASSWORD=your-es-password       # Elasticsearch password
ELASTICSEARCH_INDEX_PREFIX=soodam             # Index prefix
```

### Celery Configuration
```bash
# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/2    # Celery broker URL
CELERY_RESULT_BACKEND=redis://localhost:6379/3 # Celery result backend
CELERY_TASK_SERIALIZER=json                   # Task serializer
CELERY_RESULT_SERIALIZER=json                 # Result serializer
CELERY_ACCEPT_CONTENT=["json"]                 # Accepted content types
CELERY_TIMEZONE=UTC                           # Celery timezone
CELERY_ENABLE_UTC=True                        # Enable UTC
```

### Monitoring and Logging
```bash
# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id  # Sentry DSN
SENTRY_ENVIRONMENT=production                  # Sentry environment
SENTRY_TRACES_SAMPLE_RATE=0.1                 # Traces sample rate

# Logging Configuration
LOG_LEVEL=INFO                                # Log level (DEBUG/INFO/WARNING/ERROR)
LOG_FILE=/app/logs/django.log                 # Log file path
LOG_MAX_SIZE=10MB                             # Max log file size
LOG_BACKUP_COUNT=5                            # Number of backup log files
```

## 🌍 Internationalization

### Language and Locale
```bash
# Internationalization
LANGUAGE_CODE=fa-ir                           # Default language (Persian)
TIME_ZONE=Asia/Tehran                         # Default timezone
USE_I18N=True                                 # Enable internationalization
USE_L10N=True                                 # Enable localization
USE_TZ=True                                   # Enable timezone support

# Supported Languages
LANGUAGES=en,fa                               # Supported language codes
```

## 🔧 Performance Configuration

### Caching
```bash
# Cache Configuration
CACHE_TTL=300                                 # Default cache timeout (5 minutes)
CACHE_KEY_PREFIX=soodam                       # Cache key prefix
CACHE_VERSION=1                               # Cache version

# Cache Backends
CACHE_BACKEND=django_redis.cache.RedisCache   # Cache backend
CACHE_LOCATION=redis://localhost:6379/0       # Cache location
```

### Database Optimization
```bash
# Database Performance
DB_CONN_MAX_AGE=600                           # Connection max age
DB_CONN_HEALTH_CHECKS=True                    # Health checks
DB_OPTIONS={"init_command": "SET sql_mode='STRICT_TRANS_TABLES'"}
```

## 📱 API Configuration

### Rate Limiting
```bash
# Rate Limiting
RATE_LIMIT_ANONYMOUS=100/hour                 # Anonymous user rate limit
RATE_LIMIT_AUTHENTICATED=1000/hour            # Authenticated user rate limit
RATE_LIMIT_ADMIN=5000/hour                    # Admin user rate limit
```

### File Upload Limits
```bash
# File Upload Configuration
MAX_UPLOAD_SIZE=10485760                      # Max upload size (10MB)
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp         # Allowed image types
ALLOWED_VIDEO_TYPES=mp4,mov,avi               # Allowed video types
ALLOWED_DOCUMENT_TYPES=pdf,doc,docx           # Allowed document types
```

## 🐳 Docker Configuration

### Docker Environment
```bash
# Docker-specific variables
DOCKER_ENV=True                               # Running in Docker
DOCKER_NETWORK=soodam_network                 # Docker network name
DOCKER_VOLUME_PREFIX=soodam                   # Volume prefix
```

## 📋 Environment Templates

### Development Template (`fastapi.env.development`)
```bash
# Development Configuration
ENV_STATE=development
DEBUG=True
SECRET_KEY=dev-secret-key-not-for-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database
DB_NAME=soodam_dev
DB_USER=soodam_dev
DB_PASSWORD=dev_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://localhost:6379/0

# Email (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# JWT
JWT_SECRET_KEY=dev-jwt-secret
ACCESS_TOKEN_EXPIRE_MINUTES=60

# File Storage
USE_S3=False
MEDIA_ROOT=./media
STATIC_ROOT=./static
```

### Production Template (`fastapi.env.production`)
```bash
# Production Configuration
ENV_STATE=production
DEBUG=False
SECRET_KEY=your-super-secure-production-secret-key-min-50-chars
ALLOWED_HOSTS=api.soodam.com,soodam.com

# Database
DB_NAME=soodam_prod
DB_USER=soodam_prod
DB_PASSWORD=super-secure-production-password
DB_HOST=postgres
DB_PORT=5432

# Redis
REDIS_URL=redis://redis:6379/0

# Email
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# JWT
JWT_SECRET_KEY=super-secure-jwt-secret-key-for-production
ACCESS_TOKEN_EXPIRE_MINUTES=15

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# File Storage
USE_S3=True
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=soodam-production-media

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
```

## ✅ Configuration Validation

### Required Variables Check
```python
# Add to Django settings for validation
import os
from django.core.exceptions import ImproperlyConfigured

def get_env_variable(var_name, default=None):
    """Get environment variable or raise exception."""
    try:
        return os.environ[var_name]
    except KeyError:
        if default is not None:
            return default
        error_msg = f"Set the {var_name} environment variable"
        raise ImproperlyConfigured(error_msg)

# Required variables
SECRET_KEY = get_env_variable('SECRET_KEY')
DB_PASSWORD = get_env_variable('DB_PASSWORD')
JWT_SECRET_KEY = get_env_variable('JWT_SECRET_KEY')
```

### Environment Validation Script
```bash
#!/bin/bash
# validate_env.sh - Validate environment configuration

echo "Validating environment configuration..."

# Check required variables
required_vars=(
    "SECRET_KEY"
    "DB_NAME"
    "DB_USER"
    "DB_PASSWORD"
    "REDIS_URL"
    "JWT_SECRET_KEY"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "ERROR: $var is not set"
        exit 1
    fi
done

echo "Environment validation passed!"
```

## 🔍 Troubleshooting

### Common Configuration Issues

1. **Database Connection Failed**
   - Check DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD
   - Verify PostgreSQL service is running
   - Check network connectivity

2. **Redis Connection Failed**
   - Verify REDIS_URL format
   - Check Redis service status
   - Validate Redis database numbers

3. **JWT Token Issues**
   - Ensure JWT_SECRET_KEY is set and secure
   - Check token expiration times
   - Verify algorithm matches

4. **File Upload Issues**
   - Check MEDIA_ROOT permissions
   - Verify AWS S3 credentials (if using S3)
   - Check file size limits

---

Proper environment configuration is crucial for security and functionality. Always use strong, unique values for production and never commit sensitive values to version control.
