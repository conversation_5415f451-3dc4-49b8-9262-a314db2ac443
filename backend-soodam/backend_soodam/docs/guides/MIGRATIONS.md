# Database Migration Guide

## 📊 Overview

This guide covers comprehensive database migration strategies for the Soodam backend, including Django migrations, data migrations, schema changes, and production deployment best practices.

## 🏗️ Migration Strategy

### Migration Types
```
┌─────────────────────────────────────────┐
│           Schema Migrations             │
│  ├── Model Changes                      │
│  ├── Field Additions/Removals          │
│  ├── Index Management                  │
│  └── Constraint Changes                │
├─────────────────────────────────────────┤
│           Data Migrations               │
│  ├── Data Transformation               │
│  ├── Data Cleanup                      │
│  ├── Reference Updates                 │
│  └── Bulk Operations                   │
├─────────────────────────────────────────┤
│           Deployment Migrations         │
│  ├── Zero-Downtime Migrations          │
│  ├── Rollback Strategies               │
│  ├── Performance Optimization          │
│  └── Production Safety                 │
└─────────────────────────────────────────┘
```

## 🔧 Django Migration Best Practices

### Creating Safe Migrations
```python
# migrations/0001_initial.py
from django.db import migrations, models
import django.contrib.gis.db.models.fields

class Migration(migrations.Migration):
    """
    Initial migration for Soodam backend
    
    This migration creates the core models for the application.
    Safe to run in production.
    """
    
    initial = True
    
    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]
    
    operations = [
        migrations.CreateModel(
            name='CustomUserModel',
            fields=[
                ('id', models.AutoField(primary_key=True)),
                ('username', models.CharField(max_length=150, unique=True)),
                ('email', models.EmailField(unique=True, null=True, blank=True)),
                ('phone_number', models.CharField(max_length=20, unique=True, null=True, blank=True)),
                ('first_name', models.CharField(max_length=30)),
                ('last_name', models.CharField(max_length=30)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'app_customusermodel',
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
            },
        ),
        
        # Add indexes for performance
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_email ON app_customusermodel(email);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_email;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_phone ON app_customusermodel(phone_number);",
            reverse_sql="DROP INDEX IF EXISTS idx_user_phone;"
        ),
    ]
```

### Adding Fields Safely
```python
# migrations/0002_add_user_avatar.py
from django.db import migrations, models

class Migration(migrations.Migration):
    """
    Add avatar field to user model
    
    This is a safe migration that adds a nullable field.
    No data loss risk.
    """
    
    dependencies = [
        ('app', '0001_initial'),
    ]
    
    operations = [
        # Add nullable field first
        migrations.AddField(
            model_name='customusermodel',
            name='avatar',
            field=models.ImageField(upload_to='avatars/', null=True, blank=True),
        ),
        
        # Add index for avatar field if needed
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_avatar ON app_customusermodel(avatar) WHERE avatar IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS idx_user_avatar;"
        ),
    ]
```

### Removing Fields Safely
```python
# migrations/0003_remove_deprecated_field.py
from django.db import migrations

class Migration(migrations.Migration):
    """
    Remove deprecated field from user model
    
    WARNING: This migration will cause data loss!
    Ensure the field is no longer used in code before applying.
    """
    
    dependencies = [
        ('app', '0002_add_user_avatar'),
    ]
    
    operations = [
        # First, remove any indexes on the field
        migrations.RunSQL(
            "DROP INDEX IF EXISTS idx_user_deprecated_field;",
            reverse_sql="CREATE INDEX CONCURRENTLY idx_user_deprecated_field ON app_customusermodel(deprecated_field);"
        ),
        
        # Then remove the field
        migrations.RemoveField(
            model_name='customusermodel',
            name='deprecated_field',
        ),
    ]
```

## 📊 Data Migrations

### Data Transformation Migration
```python
# migrations/0004_migrate_user_data.py
from django.db import migrations
from django.db.models import Q

def migrate_user_data_forward(apps, schema_editor):
    """
    Migrate user data to new format
    """
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    
    # Process in batches to avoid memory issues
    batch_size = 1000
    total_users = CustomUserModel.objects.count()
    
    for offset in range(0, total_users, batch_size):
        users = CustomUserModel.objects.all()[offset:offset + batch_size]
        
        for user in users:
            # Transform data
            if user.old_field:
                user.new_field = transform_data(user.old_field)
                user.save(update_fields=['new_field'])

def migrate_user_data_reverse(apps, schema_editor):
    """
    Reverse migration - restore old data format
    """
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    
    batch_size = 1000
    total_users = CustomUserModel.objects.count()
    
    for offset in range(0, total_users, batch_size):
        users = CustomUserModel.objects.all()[offset:offset + batch_size]
        
        for user in users:
            if user.new_field:
                user.old_field = reverse_transform_data(user.new_field)
                user.save(update_fields=['old_field'])

def transform_data(old_value):
    """Transform old data format to new format"""
    # Implementation depends on your specific needs
    return old_value.upper() if old_value else None

def reverse_transform_data(new_value):
    """Reverse transform for rollback"""
    return new_value.lower() if new_value else None

class Migration(migrations.Migration):
    """
    Data migration to transform user data
    
    This migration transforms existing data to match new requirements.
    Includes reverse migration for rollback capability.
    """
    
    dependencies = [
        ('app', '0003_remove_deprecated_field'),
    ]
    
    operations = [
        migrations.RunPython(
            migrate_user_data_forward,
            migrate_user_data_reverse,
        ),
    ]
```

### Bulk Data Operations
```python
# migrations/0005_bulk_data_update.py
from django.db import migrations, transaction
from django.db.models import F, Case, When, Value, CharField

def bulk_update_advertisements(apps, schema_editor):
    """
    Bulk update advertisement data for performance
    """
    AdvertisementModel = apps.get_model('app', 'AdvertisementModel')
    
    # Use bulk operations for better performance
    with transaction.atomic():
        # Bulk update using database functions
        AdvertisementModel.objects.filter(
            status=0  # Pending
        ).update(
            status=Case(
                When(created_at__lt='2024-01-01', then=Value(2)),  # Rejected
                default=Value(1),  # Approved
                output_field=CharField()
            )
        )
        
        # Update statistics in bulk
        AdvertisementModel.objects.update(
            view_count=F('view_count') + 1
        )

def reverse_bulk_update(apps, schema_editor):
    """
    Reverse the bulk update
    """
    AdvertisementModel = apps.get_model('app', 'AdvertisementModel')
    
    with transaction.atomic():
        # Reset to original state
        AdvertisementModel.objects.filter(
            status__in=[1, 2]
        ).update(status=0)
        
        AdvertisementModel.objects.update(
            view_count=F('view_count') - 1
        )

class Migration(migrations.Migration):
    """
    Bulk data update migration
    
    Updates advertisement statuses and statistics in bulk for performance.
    """
    
    dependencies = [
        ('app', '0004_migrate_user_data'),
    ]
    
    operations = [
        migrations.RunPython(
            bulk_update_advertisements,
            reverse_bulk_update,
        ),
    ]
```

## 🚀 Zero-Downtime Migrations

### Multi-Step Migration Strategy
```python
# Step 1: migrations/0006_add_new_field_nullable.py
from django.db import migrations, models

class Migration(migrations.Migration):
    """
    Step 1: Add new field as nullable
    
    This allows the application to continue running while the field is added.
    """
    
    dependencies = [
        ('app', '0005_bulk_data_update'),
    ]
    
    operations = [
        migrations.AddField(
            model_name='advertisementmodel',
            name='new_status_field',
            field=models.CharField(max_length=20, null=True, blank=True),
        ),
    ]

# Step 2: migrations/0007_populate_new_field.py
from django.db import migrations

def populate_new_field(apps, schema_editor):
    """Populate the new field with data from the old field"""
    AdvertisementModel = apps.get_model('app', 'AdvertisementModel')
    
    # Map old status values to new values
    status_mapping = {
        0: 'pending',
        1: 'approved',
        2: 'rejected',
        3: 'expired',
    }
    
    batch_size = 1000
    total_ads = AdvertisementModel.objects.count()
    
    for offset in range(0, total_ads, batch_size):
        ads = AdvertisementModel.objects.all()[offset:offset + batch_size]
        
        for ad in ads:
            ad.new_status_field = status_mapping.get(ad.status, 'pending')
            ad.save(update_fields=['new_status_field'])

class Migration(migrations.Migration):
    """
    Step 2: Populate new field with data
    """
    
    dependencies = [
        ('app', '0006_add_new_field_nullable'),
    ]
    
    operations = [
        migrations.RunPython(populate_new_field),
    ]

# Step 3: migrations/0008_make_field_required.py
from django.db import migrations, models

class Migration(migrations.Migration):
    """
    Step 3: Make the new field required and remove old field
    
    Deploy this after updating application code to use the new field.
    """
    
    dependencies = [
        ('app', '0007_populate_new_field'),
    ]
    
    operations = [
        # Make field non-nullable
        migrations.AlterField(
            model_name='advertisementmodel',
            name='new_status_field',
            field=models.CharField(max_length=20, default='pending'),
        ),
        
        # Remove old field
        migrations.RemoveField(
            model_name='advertisementmodel',
            name='status',
        ),
        
        # Rename new field to final name
        migrations.RenameField(
            model_name='advertisementmodel',
            old_name='new_status_field',
            new_name='status',
        ),
    ]
```

## 📈 Performance Optimization

### Index Management
```python
# migrations/0009_optimize_indexes.py
from django.db import migrations

class Migration(migrations.Migration):
    """
    Optimize database indexes for better performance
    
    This migration adds and removes indexes based on query patterns.
    """
    
    dependencies = [
        ('app', '0008_make_field_required'),
    ]
    
    operations = [
        # Add composite indexes for common queries
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_status_created ON app_advertisementmodel(status, created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_ad_status_created;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_user_status ON app_advertisementmodel(user_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_ad_user_status;"
        ),
        
        # Add partial indexes for specific conditions
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_featured ON app_advertisementmodel(created_at DESC) WHERE is_featured = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_ad_featured;"
        ),
        
        # Remove unused indexes
        migrations.RunSQL(
            "DROP INDEX IF EXISTS old_unused_index;",
            reverse_sql="CREATE INDEX CONCURRENTLY old_unused_index ON app_advertisementmodel(old_field);"
        ),
        
        # Add GIN indexes for full-text search
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ad_search ON app_advertisementmodel USING gin(to_tsvector('english', title || ' ' || description));",
            reverse_sql="DROP INDEX IF EXISTS idx_ad_search;"
        ),
    ]
```

### Large Table Migrations
```python
# migrations/0010_large_table_migration.py
from django.db import migrations, connection

def migrate_large_table(apps, schema_editor):
    """
    Migrate large table with minimal locking
    """
    with connection.cursor() as cursor:
        # Use pt-online-schema-change for very large tables
        # This is a placeholder - actual implementation would use external tools
        
        # For Django, we can use chunked updates
        AdvertisementModel = apps.get_model('app', 'AdvertisementModel')
        
        chunk_size = 10000
        total_count = AdvertisementModel.objects.count()
        
        for offset in range(0, total_count, chunk_size):
            # Process chunk
            chunk = AdvertisementModel.objects.all()[offset:offset + chunk_size]
            
            # Perform updates on chunk
            for item in chunk:
                # Update logic here
                pass
            
            # Commit after each chunk to avoid long-running transactions
            schema_editor.connection.commit()

class Migration(migrations.Migration):
    """
    Migration for large table changes
    
    Uses chunked processing to avoid long-running transactions.
    """
    
    dependencies = [
        ('app', '0009_optimize_indexes'),
    ]
    
    operations = [
        migrations.RunPython(migrate_large_table),
    ]
```

## 🔄 Migration Management

### Migration Deployment Script
```bash
#!/bin/bash
# scripts/deploy-migrations.sh

set -e

ENVIRONMENT=${1:-staging}
DRY_RUN=${2:-false}

echo "🚀 Deploying migrations to $ENVIRONMENT"

# Configuration
if [ "$ENVIRONMENT" = "production" ]; then
    DB_HOST=$PROD_DB_HOST
    DB_NAME=$PROD_DB_NAME
    BACKUP_REQUIRED=true
else
    DB_HOST=$STAGING_DB_HOST
    DB_NAME=$STAGING_DB_NAME
    BACKUP_REQUIRED=false
fi

# Pre-migration checks
echo "🔍 Running pre-migration checks..."

# Check for pending migrations
PENDING_MIGRATIONS=$(python manage.py showmigrations --plan | grep "\[ \]" | wc -l)
if [ $PENDING_MIGRATIONS -eq 0 ]; then
    echo "✅ No pending migrations"
    exit 0
fi

echo "📊 Found $PENDING_MIGRATIONS pending migrations"

# Check migration safety
echo "🛡️ Checking migration safety..."
python manage.py migrate --check

# Create backup if required
if [ "$BACKUP_REQUIRED" = true ]; then
    echo "💾 Creating database backup..."
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    pg_dump -h $DB_HOST -d $DB_NAME > $BACKUP_FILE
    echo "✅ Backup created: $BACKUP_FILE"
fi

# Dry run
if [ "$DRY_RUN" = true ]; then
    echo "🧪 Dry run mode - showing migration plan..."
    python manage.py migrate --plan
    exit 0
fi

# Apply migrations
echo "⚡ Applying migrations..."
python manage.py migrate --verbosity=2

# Post-migration checks
echo "🔍 Running post-migration checks..."

# Verify database integrity
python manage.py check --database default

# Run health checks
curl -f http://localhost:8000/health/ || {
    echo "❌ Health check failed after migration"
    exit 1
}

echo "✅ Migration deployment completed successfully!"
```

### Migration Rollback Script
```bash
#!/bin/bash
# scripts/rollback-migration.sh

set -e

APP_NAME=${1}
MIGRATION_NAME=${2}

if [ -z "$APP_NAME" ] || [ -z "$MIGRATION_NAME" ]; then
    echo "Usage: $0 <app_name> <migration_name>"
    echo "Example: $0 app 0008_make_field_required"
    exit 1
fi

echo "⚠️  Rolling back migration $APP_NAME.$MIGRATION_NAME"

# Confirm rollback
read -p "Are you sure you want to rollback? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Rollback cancelled"
    exit 1
fi

# Create backup before rollback
echo "💾 Creating backup before rollback..."
BACKUP_FILE="rollback_backup_$(date +%Y%m%d_%H%M%S).sql"
pg_dump -h $DB_HOST -d $DB_NAME > $BACKUP_FILE

# Perform rollback
echo "🔄 Rolling back migration..."
python manage.py migrate $APP_NAME $MIGRATION_NAME

# Verify rollback
echo "🔍 Verifying rollback..."
python manage.py check --database default

echo "✅ Rollback completed successfully!"
echo "📁 Backup saved as: $BACKUP_FILE"
```

## 📋 Migration Checklist

### Pre-Migration Checklist
- [ ] Review migration for potential data loss
- [ ] Test migration on staging environment
- [ ] Create database backup
- [ ] Check for long-running operations
- [ ] Verify migration is reversible
- [ ] Update application code if needed
- [ ] Plan maintenance window if required
- [ ] Notify stakeholders of potential downtime

### Post-Migration Checklist
- [ ] Verify migration applied successfully
- [ ] Run application health checks
- [ ] Check database performance
- [ ] Monitor error logs
- [ ] Verify data integrity
- [ ] Test critical application features
- [ ] Update documentation
- [ ] Clean up old backup files

## 🚨 Migration Troubleshooting

### Common Issues and Solutions
```python
# Common migration issues and fixes

# Issue 1: Migration conflicts
# Solution: Merge migrations
python manage.py makemigrations --merge

# Issue 2: Fake migration for existing tables
# Solution: Mark migration as applied without running
python manage.py migrate --fake app 0001_initial

# Issue 3: Reset migrations
# Solution: Reset and recreate migrations
python manage.py migrate app zero
python manage.py makemigrations app
python manage.py migrate app

# Issue 4: Check migration status
python manage.py showmigrations

# Issue 5: SQL for migration
python manage.py sqlmigrate app 0001_initial
```

---

This migration guide provides comprehensive strategies for safely managing database changes in the Soodam backend with minimal downtime and maximum data integrity.
