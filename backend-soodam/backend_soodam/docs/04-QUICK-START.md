# Quick Start Guide

## 🚀 Get Soodam Backend Running in 5 Minutes

This guide will get you up and running with the Soodam backend as quickly as possible using Docker.

## 📋 Prerequisites

- **Docker** and **Docker Compose** installed
- **Git** for cloning the repository
- **8GB RAM** minimum recommended

## ⚡ Quick Setup

### 1. Clone and Navigate
```bash
git clone https://github.com/soodamApp/backend-soodam.git
cd backend-soodam/backend_soodam
```

### 2. Environment Setup
```bash
# Copy environment template
cp fastapi.env.example fastapi.env

# Quick edit for development (optional)
nano fastapi.env
```

### 3. Start Everything
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 4. Initialize Database
```bash
# Run migrations
docker-compose exec backend python manage.py migrate

# Create admin user
docker-compose exec backend python manage.py createsuperuser
```

### 5. Load Sample Data (Optional)
```bash
# Load initial categories and sample data
docker-compose exec backend python manage.py loaddata fixtures/initial_data.json
```

## 🎯 Test Your Setup

### Check API Health
```bash
curl http://localhost:8000/api/health
```

### Access Documentation
- **API Docs**: http://localhost:8000/api/docs
- **Admin Panel**: http://localhost:8000/admin

### Test API Endpoints
```bash
# Get advertisements
curl http://localhost:8000/api/advertisements

# Get provinces
curl http://localhost:8000/api/geolocation/get_provinces
```

## 🔐 Quick Authentication Test

### 1. Register a User
```bash
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "first_name": "Test",
    "last_name": "User",
    "phone_number": "+**********"
  }'
```

### 2. Login
```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "testpassword123"
  }'
```

### 3. Use Token
```bash
# Save token from login response
TOKEN="your-token-here"

# Make authenticated request
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/user/profile
```

## 📱 Create Your First Advertisement

```bash
# Create advertisement (replace TOKEN)
curl -X POST "http://localhost:8000/api/advertisements" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "My First Advertisement",
    "description": "This is a test advertisement",
    "category_id": 1,
    "price": {
      "amount": 100000,
      "currency": "USD",
      "is_negotiable": true
    },
    "full_address": {
      "province_id": 1,
      "city_id": 1,
      "address": "123 Test Street",
      "zip_code": "12345",
      "longitude": 51.3890,
      "latitude": 35.6892
    }
  }'
```

## 🛠️ Common Quick Fixes

### Services Not Starting?
```bash
# Check logs
docker-compose logs backend

# Restart services
docker-compose restart
```

### Database Issues?
```bash
# Reset database (development only)
docker-compose down -v
docker-compose up -d
docker-compose exec backend python manage.py migrate
```

### Port Already in Use?
```bash
# Change port in docker-compose.yml
ports:
  - "8001:8000"  # Use 8001 instead of 8000
```

## 📚 Next Steps

Now that you have Soodam running:

1. **Explore the API**: Visit http://localhost:8000/api/docs
2. **Read the Architecture**: [Architecture Guide](./02-ARCHITECTURE.md)
3. **Set up Development**: [Development Setup](./05-DEVELOPMENT-SETUP.md)
4. **Learn the APIs**: [API Documentation](./api/README.md)

## 🆘 Need Help?

- **Documentation**: Check the [main README](./README.md)
- **Common Issues**: [Troubleshooting Guide](./troubleshooting/COMMON-ISSUES.md)
- **GitHub Issues**: Report problems on GitHub

---

🎉 **Congratulations!** You now have Soodam backend running locally!
