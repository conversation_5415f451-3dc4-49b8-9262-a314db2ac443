# Development Setup Guide

## 🚀 Quick Start

This guide will help you set up the Soodam backend development environment on your local machine.

## 📋 Prerequisites

### Required Software
- **Python 3.10+**: Latest Python version
- **PostgreSQL 14+**: Database server with PostGIS extension
- **Redis 6+**: Caching and session storage
- **Git**: Version control
- **Docker & Docker Compose**: Containerization (recommended)

### Optional Software
- **Poetry**: Python dependency management (recommended)
- **VS Code**: Code editor with Python extensions
- **Postman**: API testing tool
- **pgAdmin**: PostgreSQL administration tool

## 🐳 Option 1: Docker Setup (Recommended)

### 1. Clone the Repository
```bash
git clone https://github.com/soodamApp/backend-soodam.git
cd backend-soodam/backend_soodam
```

### 2. Create Required Directories
```bash
mkdir -p data/redis logs media static
```

### 3. Environment Configuration
```bash
# Copy environment template
cp fastapi.env.example fastapi.env

# Edit environment variables
nano fastapi.env
```

### 4. Environment Variables
```bash
# Database Configuration
DB_NAME=soodam_db
DB_USER=soodam_user
DB_PASSWORD=your_secure_password
DB_HOST=postgres
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Django Configuration
SECRET_KEY=your-super-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# Email Configuration (optional for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# File Storage
MEDIA_URL=/media/
STATIC_URL=/static/
```

### 5. Start Development Environment
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Check service status
docker-compose ps
```

### 6. Initialize Database
```bash
# Run migrations
docker-compose exec backend python manage.py migrate

# Create superuser
docker-compose exec backend python manage.py createsuperuser

# Load initial data (optional)
docker-compose exec backend python manage.py loaddata initial_data.json
```

### 7. Access the Application
- **API Documentation**: http://localhost:8000/api/docs
- **Admin Interface**: http://localhost:8000/admin
- **API Base URL**: http://localhost:8000/api

## 💻 Option 2: Local Development Setup

### 1. Clone and Setup Virtual Environment
```bash
# Clone repository
git clone https://github.com/soodamApp/backend-soodam.git
cd backend-soodam/backend_soodam

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate
```

### 2. Install Dependencies
```bash
# Using Poetry (recommended)
poetry install

# Or using pip
pip install -r requirements.txt
```

### 3. Database Setup
```bash
# Install PostgreSQL and PostGIS
# Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib postgis

# macOS (using Homebrew):
brew install postgresql postgis

# Create database
sudo -u postgres createdb soodam_db
sudo -u postgres createuser soodam_user
sudo -u postgres psql -c "ALTER USER soodam_user PASSWORD 'your_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE soodam_db TO soodam_user;"

# Enable PostGIS extension
sudo -u postgres psql soodam_db -c "CREATE EXTENSION postgis;"
```

### 4. Redis Setup
```bash
# Install Redis
# Ubuntu/Debian:
sudo apt-get install redis-server

# macOS (using Homebrew):
brew install redis

# Start Redis
redis-server
```

### 5. Environment Configuration
```bash
# Copy and edit environment file
cp fastapi.env.example fastapi.env
nano fastapi.env

# Update database connection
DB_HOST=localhost
REDIS_URL=redis://localhost:6379/0
```

### 6. Run Migrations and Setup
```bash
# Run database migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Load initial data
python manage.py loaddata fixtures/initial_data.json
```

### 7. Start Development Server
```bash
# Start Django development server
python manage.py runserver

# Or start with Uvicorn for FastAPI features
uvicorn config.asgi:application --host 0.0.0.0 --port 8000 --reload
```

## 🔧 Development Tools Setup

### VS Code Configuration
Create `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### Pre-commit Hooks
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

### Code Quality Tools
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## 🧪 Testing Setup

### Run Tests
```bash
# Run all tests
python -m pytest

# Run with coverage
python -m pytest --cov=app

# Run specific test file
python -m pytest tests/test_advertisements.py

# Run with verbose output
python -m pytest -v
```

### Test Database
```bash
# Create test database
sudo -u postgres createdb test_soodam_db

# Run tests with test database
python manage.py test --settings=config.settings.test
```

## 📊 Database Management

### Common Django Commands
```bash
# Create new migration
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Show migration status
python manage.py showmigrations

# Create superuser
python manage.py createsuperuser

# Load fixtures
python manage.py loaddata fixtures/categories.json

# Export data
python manage.py dumpdata app.CategoryModel --indent 2 > categories.json
```

### Database Backup and Restore
```bash
# Backup database
pg_dump -U soodam_user -h localhost soodam_db > backup.sql

# Restore database
psql -U soodam_user -h localhost soodam_db < backup.sql
```

## 🔍 Debugging

### Django Debug Toolbar
```python
# Add to INSTALLED_APPS in development settings
INSTALLED_APPS = [
    # ...
    'debug_toolbar',
]

# Add to middleware
MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    # ...
]

# Configure internal IPs
INTERNAL_IPS = ['127.0.0.1']
```

### Logging Configuration
```python
# Add to settings for detailed logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Error
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Check connection
psql -U soodam_user -h localhost -d soodam_db
```

#### Redis Connection Error
```bash
# Check Redis status
redis-cli ping

# Restart Redis
sudo systemctl restart redis-server
```

#### Migration Issues
```bash
# Reset migrations (development only)
python manage.py migrate --fake-initial

# Show migration SQL
python manage.py sqlmigrate app 0001
```

#### Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000

# Kill process
kill -9 <PID>
```

### Performance Issues
```bash
# Check slow queries
python manage.py shell
>>> from django.db import connection
>>> print(connection.queries)

# Profile code
python -m cProfile manage.py runserver
```

## 📚 Next Steps

After setting up your development environment:

1. **Read the API Documentation**: [API Overview](./api/README.md)
2. **Understand the Architecture**: [Architecture Guide](./02-ARCHITECTURE.md)
3. **Learn Testing**: [Testing Guide](./development/TESTING.md)
4. **Follow Best Practices**: [Best Practices](./guides/BEST-PRACTICES.md)

## 🆘 Getting Help

- **Documentation**: Check the docs folder for detailed guides
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Ask questions in GitHub Discussions
- **Code Review**: Submit pull requests for review

---

You're now ready to start developing with the Soodam backend! 🎉
