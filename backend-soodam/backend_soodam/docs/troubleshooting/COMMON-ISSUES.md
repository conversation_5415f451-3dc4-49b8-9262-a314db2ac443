# Common Issues & Troubleshooting Guide

## 🔧 Quick Diagnostics

Before diving into specific issues, run these quick diagnostic commands:

```bash
# Check application status
docker-compose ps  # For Docker setup
sudo supervisorctl status  # For traditional setup

# Check logs
docker-compose logs backend  # Docker
tail -f /var/log/soodam/gunicorn.log  # Traditional

# Check database connectivity
python manage.py dbshell

# Check Redis connectivity
redis-cli ping

# Test API health
curl http://localhost:8000/api/health
```

## 🚨 Application Startup Issues

### Issue: Application Won't Start

**Symptoms:**
- Container/service fails to start
- Import errors in logs
- Module not found errors

**Solutions:**

1. **Check Dependencies**
```bash
# Verify Python version
python --version  # Should be 3.10+

# Check installed packages
pip list
pip check  # Check for dependency conflicts

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

2. **Check Environment Variables**
```bash
# Verify environment file exists
ls -la fastapi.env

# Check critical variables
echo $SECRET_KEY
echo $DB_NAME
echo $REDIS_URL
```

3. **Check File Permissions**
```bash
# Fix ownership (Docker)
sudo chown -R $USER:$USER .

# Fix permissions
chmod +x manage.py
chmod +x scripts/*.sh
```

### Issue: Import Errors

**Symptoms:**
- `ModuleNotFoundError`
- `ImportError`
- Python path issues

**Solutions:**

1. **Check Python Path**
```bash
# Add current directory to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or in Django settings
import sys
sys.path.append('/path/to/your/project')
```

2. **Verify Package Installation**
```bash
# Check if package is installed
pip show django
pip show fastapi

# Reinstall specific package
pip uninstall package_name
pip install package_name
```

## 🗄️ Database Issues

### Issue: Database Connection Failed

**Symptoms:**
- `FATAL: password authentication failed`
- `could not connect to server`
- `database does not exist`

**Solutions:**

1. **Check Database Service**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql
docker-compose ps postgres

# Restart PostgreSQL
sudo systemctl restart postgresql
docker-compose restart postgres
```

2. **Verify Connection Parameters**
```bash
# Test connection manually
psql -h localhost -U soodam_user -d soodam_db

# Check environment variables
echo $DB_HOST $DB_PORT $DB_NAME $DB_USER
```

3. **Reset Database Password**
```bash
# Connect as postgres user
sudo -u postgres psql

# Reset password
ALTER USER soodam_user PASSWORD 'new_password';
```

### Issue: Migration Errors

**Symptoms:**
- `Migration is being reversed`
- `Inconsistent migration history`
- `Table already exists`

**Solutions:**

1. **Check Migration Status**
```bash
python manage.py showmigrations
python manage.py migrate --plan
```

2. **Fix Migration Issues**
```bash
# Fake initial migration (development only)
python manage.py migrate --fake-initial

# Reset migrations (DANGER: data loss)
python manage.py migrate app zero
python manage.py migrate
```

3. **Manual Migration Fix**
```bash
# Show SQL for migration
python manage.py sqlmigrate app 0001

# Apply specific migration
python manage.py migrate app 0001
```

## 🔴 Redis Connection Issues

### Issue: Redis Connection Failed

**Symptoms:**
- `Connection refused`
- `Redis server not available`
- Caching not working

**Solutions:**

1. **Check Redis Service**
```bash
# Check Redis status
redis-cli ping
sudo systemctl status redis-server

# Restart Redis
sudo systemctl restart redis-server
docker-compose restart redis
```

2. **Verify Redis Configuration**
```bash
# Check Redis config
redis-cli CONFIG GET "*"

# Test connection with URL
redis-cli -u redis://localhost:6379/0 ping
```

3. **Clear Redis Cache**
```bash
# Flush all Redis data
redis-cli FLUSHALL

# Flush specific database
redis-cli -n 0 FLUSHDB
```

## 🔐 Authentication Issues

### Issue: JWT Token Problems

**Symptoms:**
- `Token has expired`
- `Invalid token`
- `Authentication credentials were not provided`

**Solutions:**

1. **Check Token Configuration**
```python
# Verify JWT settings
print(settings.JWT_SECRET_KEY)
print(settings.ACCESS_TOKEN_EXPIRE_MINUTES)
```

2. **Debug Token Issues**
```bash
# Decode JWT token (for debugging)
python -c "
import jwt
token = 'your-token-here'
print(jwt.decode(token, verify=False))
"
```

3. **Reset User Authentication**
```bash
# Create new superuser
python manage.py createsuperuser

# Reset user password
python manage.py changepassword username
```

### Issue: Permission Denied

**Symptoms:**
- `403 Forbidden`
- `You do not have permission`
- Admin access denied

**Solutions:**

1. **Check User Permissions**
```bash
# Django shell
python manage.py shell
>>> from app.models import CustomUserModel
>>> user = CustomUserModel.objects.get(username='admin')
>>> user.is_staff = True
>>> user.is_superuser = True
>>> user.save()
```

2. **Verify Admin Configuration**
```python
# Check admin user exists
python manage.py shell
>>> from app.models import SpecialAdmin
>>> SpecialAdmin.objects.all()
```

## 📁 File Upload Issues

### Issue: File Upload Fails

**Symptoms:**
- `413 Request Entity Too Large`
- `File not found`
- Upload timeout

**Solutions:**

1. **Check File Size Limits**
```python
# Django settings
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
```

2. **Verify Media Directory**
```bash
# Check media directory permissions
ls -la media/
chmod 755 media/
chown -R www-data:www-data media/  # For production
```

3. **Nginx Configuration**
```nginx
# Increase client max body size
client_max_body_size 100M;
```

## 🔍 Search Issues

### Issue: Elasticsearch Not Working

**Symptoms:**
- Search returns no results
- `Connection refused` to Elasticsearch
- Index not found errors

**Solutions:**

1. **Check Elasticsearch Service**
```bash
# Check Elasticsearch status
curl http://localhost:9200/_cluster/health

# Restart Elasticsearch
sudo systemctl restart elasticsearch
docker-compose restart elasticsearch
```

2. **Rebuild Search Index**
```bash
# Django management command
python manage.py rebuild_index

# Or manually
python manage.py shell
>>> from app.core.elasticsearch import rebuild_index
>>> rebuild_index()
```

## 🐌 Performance Issues

### Issue: Slow API Responses

**Symptoms:**
- High response times
- Database query timeouts
- Memory usage spikes

**Solutions:**

1. **Database Query Optimization**
```python
# Enable query logging
LOGGING = {
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['console'],
        }
    }
}

# Check slow queries
python manage.py shell
>>> from django.db import connection
>>> print(connection.queries)
```

2. **Add Database Indexes**
```bash
# Create migration for indexes
python manage.py makemigrations --empty app

# Add index in migration
operations = [
    migrations.RunSQL(
        "CREATE INDEX idx_advertisement_status ON app_advertisementmodel(status);"
    ),
]
```

3. **Enable Caching**
```python
# Check cache configuration
python manage.py shell
>>> from django.core.cache import cache
>>> cache.set('test', 'value', 30)
>>> print(cache.get('test'))
```

## 🔄 Background Task Issues

### Issue: Celery Tasks Not Running

**Symptoms:**
- Tasks stuck in pending state
- Celery worker not processing
- Email notifications not sent

**Solutions:**

1. **Check Celery Status**
```bash
# Check active workers
celery -A config inspect active

# Check registered tasks
celery -A config inspect registered

# Purge all tasks
celery -A config purge
```

2. **Restart Celery Services**
```bash
# Docker
docker-compose restart celery celery-beat

# Traditional
sudo supervisorctl restart soodam-celery
sudo supervisorctl restart soodam-celery-beat
```

## 🌐 CORS Issues

### Issue: CORS Errors in Browser

**Symptoms:**
- `Access-Control-Allow-Origin` errors
- Preflight request failures
- Frontend can't access API

**Solutions:**

1. **Check CORS Configuration**
```python
# Django settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "https://your-domain.com",
]

CORS_ALLOW_CREDENTIALS = True
```

2. **Verify Nginx Configuration**
```nginx
# Add CORS headers
add_header Access-Control-Allow-Origin *;
add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
add_header Access-Control-Allow-Headers "Authorization, Content-Type";
```

## 🔧 Development Tools Issues

### Issue: Debug Toolbar Not Showing

**Symptoms:**
- Debug toolbar missing in development
- No debug information displayed

**Solutions:**

1. **Check Debug Toolbar Configuration**
```python
# Settings
DEBUG = True
INTERNAL_IPS = ['127.0.0.1', '::1']

# Installed apps
INSTALLED_APPS = [
    'debug_toolbar',
    # ...
]

# Middleware
MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    # ...
]
```

## 📊 Monitoring & Logging

### Issue: Logs Not Appearing

**Symptoms:**
- No log output
- Log files empty
- Missing error information

**Solutions:**

1. **Check Logging Configuration**
```python
# Verify logging settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

2. **Check Log File Permissions**
```bash
# Create logs directory
mkdir -p logs
chmod 755 logs

# Check log file permissions
ls -la logs/
```

## 🆘 Getting Help

### When to Seek Help

1. **Check Documentation First**: Review relevant documentation sections
2. **Search Issues**: Look for similar issues in GitHub Issues
3. **Provide Details**: Include error messages, logs, and environment info
4. **Minimal Reproduction**: Create minimal example that reproduces the issue

### Information to Include

```bash
# System information
python --version
pip list
docker --version  # If using Docker

# Error logs
tail -n 50 logs/django.log
docker-compose logs --tail=50 backend

# Configuration (sanitized)
cat fastapi.env | grep -v PASSWORD | grep -v SECRET
```

---

This troubleshooting guide covers the most common issues encountered with the Soodam backend. For specific error codes and their meanings, refer to the [Error Codes Documentation](./ERROR-CODES.md).
