# Soodam Backend Architecture

## 🏗️ System Architecture Overview

The Soodam backend employs a modern, hybrid architecture that combines the best of Django's ORM capabilities with FastAPI's high-performance async features. This design provides both developer productivity and runtime performance.

## 🔧 Architecture Patterns

### Hybrid Django + FastAPI Pattern
```
┌─────────────────────────────────────────────────────────────┐
│                    ASGI Application                         │
├─────────────────────────────────────────────────────────────┤
│  FastAPI App                    │  Django App               │
│  ├── API Endpoints              │  ├── ORM Models           │
│  ├── Pydantic Schemas           │  ├── Admin Interface      │
│  ├── Async Operations           │  ├── Migrations           │
│  └── WebSocket Support          │  └── Management Commands  │
└─────────────────────────────────────────────────────────────┘
```

### Layered Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                 Presentation Layer                          │
│  FastAPI Routers │ WebSocket │ GraphQL │ Admin Interface   │
├─────────────────────────────────────────────────────────────┤
│                   Service Layer                             │
│  Business Logic │ External APIs │ Background Tasks         │
├─────────────────────────────────────────────────────────────┤
│                 Data Access Layer                           │
│  Django ORM │ Redis Cache │ Elasticsearch │ File Storage   │
├─────────────────────────────────────────────────────────────┤
│                   Data Layer                                │
│  PostgreSQL │ PostGIS │ Redis │ Elasticsearch │ S3/Local   │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

### Core Application Structure
```
backend_soodam/
├── app/                          # Main Django application
│   ├── api/                      # API business logic
│   │   ├── v1/                   # Version 1 API implementations
│   │   │   ├── advertisement/    # Advertisement API logic
│   │   │   └── user/            # User API logic
│   │   └── admin.py             # Admin API logic
│   ├── routers/                  # FastAPI route definitions
│   │   ├── advertisement.py     # Advertisement endpoints
│   │   ├── admin.py             # Admin endpoints
│   │   ├── auth.py              # Authentication endpoints
│   │   └── users.py             # User endpoints
│   ├── models/                   # Django ORM models
│   │   ├── user.py              # User models
│   │   ├── advertisement.py     # Advertisement models
│   │   └── admin.py             # Admin models
│   ├── schemas/                  # Pydantic schemas
│   │   ├── v1/                  # Version 1 schemas
│   │   ├── v2/                  # Version 2 schemas
│   │   └── admin/               # Admin schemas
│   ├── services/                 # Business services
│   ├── utils/                    # Utility functions
│   ├── core/                     # Core functionality
│   ├── dependencies/             # FastAPI dependencies
│   ├── middleware/               # Custom middleware
│   └── exceptions/               # Custom exceptions
├── config/                       # Configuration
│   ├── settings/                 # Django settings
│   ├── asgi.py                  # ASGI configuration
│   └── urls.py                  # Django URL routing
└── soodam_app/                  # Secondary FastAPI app
    ├── api/                     # Additional API endpoints
    ├── models/                  # Additional models
    └── main.py                  # FastAPI entry point
```

## 🔄 Request Flow

### API Request Lifecycle
```
1. Client Request
   ↓
2. ASGI Server (Uvicorn/Gunicorn)
   ↓
3. FastAPI Application
   ↓
4. Middleware Stack
   ├── CORS Middleware
   ├── Authentication Middleware
   ├── Rate Limiting Middleware
   └── Logging Middleware
   ↓
5. Router Matching
   ↓
6. Dependency Injection
   ├── Authentication
   ├── Database Session
   └── Validation
   ↓
7. API Handler (Router Function)
   ↓
8. Service Layer
   ├── Business Logic
   ├── External API Calls
   └── Background Tasks
   ↓
9. Data Access Layer
   ├── Django ORM Queries
   ├── Cache Operations
   └── Search Operations
   ↓
10. Response Serialization
    ↓
11. Client Response
```

## 🗄️ Data Architecture

### Database Design
```
┌─────────────────────────────────────────────────────────────┐
│                    PostgreSQL Database                      │
├─────────────────────────────────────────────────────────────┤
│  Core Tables                                                │
│  ├── Users & Authentication                                 │
│  ├── Advertisements & Media                                 │
│  ├── Categories & Attributes                                │
│  ├── Locations & Geography (PostGIS)                       │
│  └── Admin & Permissions                                    │
├─────────────────────────────────────────────────────────────┤
│  Relationship Tables                                        │
│  ├── User Roles & Permissions                              │
│  ├── Advertisement Attributes                               │
│  ├── Favorites & Interactions                              │
│  └── Admin Activities                                       │
└─────────────────────────────────────────────────────────────┘
```

### Caching Strategy
```
┌─────────────────────────────────────────────────────────────┐
│                      Redis Cache                            │
├─────────────────────────────────────────────────────────────┤
│  L1 Cache (Application Level)                              │
│  ├── Frequently accessed data                              │
│  ├── User sessions                                         │
│  └── API response cache                                     │
├─────────────────────────────────────────────────────────────┤
│  L2 Cache (Database Query Cache)                           │
│  ├── Complex query results                                 │
│  ├── Aggregated data                                       │
│  └── Search results                                        │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 Search Architecture

### Elasticsearch Integration
```
┌─────────────────────────────────────────────────────────────┐
│                    Elasticsearch                            │
├─────────────────────────────────────────────────────────────┤
│  Indices                                                    │
│  ├── advertisements_index                                   │
│  ├── users_index                                           │
│  ├── blogs_index                                           │
│  └── autocomplete_index                                     │
├─────────────────────────────────────────────────────────────┤
│  Features                                                   │
│  ├── Full-text search                                      │
│  ├── Geospatial search                                     │
│  ├── Faceted search                                        │
│  ├── Autocomplete                                          │
│  └── Analytics                                             │
└─────────────────────────────────────────────────────────────┘
```

## 🔐 Security Architecture

### Authentication & Authorization Flow
```
┌─────────────────────────────────────────────────────────────┐
│                  Security Layers                            │
├─────────────────────────────────────────────────────────────┤
│  1. Network Security                                        │
│     ├── HTTPS/TLS                                          │
│     ├── CORS Configuration                                 │
│     └── Rate Limiting                                      │
├─────────────────────────────────────────────────────────────┤
│  2. Authentication                                          │
│     ├── JWT Tokens                                         │
│     ├── Refresh Tokens                                     │
│     └── Multi-factor Authentication                        │
├─────────────────────────────────────────────────────────────┤
│  3. Authorization                                           │
│     ├── Role-based Access Control                          │
│     ├── Permission System                                  │
│     └── Resource-level Security                            │
├─────────────────────────────────────────────────────────────┤
│  4. Data Security                                           │
│     ├── Input Validation                                   │
│     ├── SQL Injection Prevention                           │
│     └── XSS Protection                                     │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Performance Architecture

### Async Operations
```python
# Example of async architecture pattern
class AdvertisementAPI:
    @classmethod
    async def get_advertisements(cls, request: Request):
        # Async database operations
        advertisements = await AdvertisementModel.objects.select_related(
            "user", "category"
        ).prefetch_related("images", "attributes").all()
        
        # Async cache operations
        await cache.set(cache_key, advertisements, timeout=300)
        
        # Async external API calls
        analytics_data = await external_api.get_analytics(advertisement_ids)
        
        return advertisements
```

### Database Optimization
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Use of select_related and prefetch_related
- **Indexing Strategy**: Strategic database indexes for performance
- **Read Replicas**: Separate read/write database instances

## 🔄 Background Processing

### Celery Task Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Celery Workers                           │
├─────────────────────────────────────────────────────────────┤
│  Task Types                                                 │
│  ├── Email Notifications                                   │
│  ├── Image Processing                                      │
│  ├── Search Index Updates                                  │
│  ├── Analytics Processing                                  │
│  └── Data Cleanup                                          │
├─────────────────────────────────────────────────────────────┤
│  Queue Management                                           │
│  ├── High Priority Queue                                   │
│  ├── Normal Priority Queue                                 │
│  └── Low Priority Queue                                    │
└─────────────────────────────────────────────────────────────┘
```

## 📊 Monitoring Architecture

### Observability Stack
```
┌─────────────────────────────────────────────────────────────┐
│                   Monitoring Stack                          │
├─────────────────────────────────────────────────────────────┤
│  Metrics (Prometheus)                                      │
│  ├── Application Metrics                                   │
│  ├── Database Metrics                                      │
│  ├── Cache Metrics                                         │
│  └── Custom Business Metrics                               │
├─────────────────────────────────────────────────────────────┤
│  Logging (Structured Logging)                              │
│  ├── Application Logs                                      │
│  ├── Access Logs                                           │
│  ├── Error Logs                                            │
│  └── Audit Logs                                            │
├─────────────────────────────────────────────────────────────┤
│  Health Checks                                             │
│  ├── Database Health                                       │
│  ├── Cache Health                                          │
│  ├── External Service Health                               │
│  └── Application Health                                    │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Design Principles

### SOLID Principles
- **Single Responsibility**: Each module has a single, well-defined purpose
- **Open/Closed**: Open for extension, closed for modification
- **Liskov Substitution**: Subtypes must be substitutable for their base types
- **Interface Segregation**: Many client-specific interfaces
- **Dependency Inversion**: Depend on abstractions, not concretions

### API Design Principles
- **RESTful Design**: Standard HTTP methods and status codes
- **Versioning**: API versioning for backward compatibility
- **Consistency**: Consistent naming and response formats
- **Documentation**: Comprehensive API documentation
- **Error Handling**: Standardized error responses

### Database Design Principles
- **Normalization**: Proper database normalization
- **Indexing**: Strategic indexing for performance
- **Constraints**: Data integrity through constraints
- **Migrations**: Version-controlled schema changes

---

This architecture documentation provides the foundation for understanding how the Soodam backend is structured and operates. For implementation details, refer to the specific component documentation.
