# Monitoring and Maintenance Setup

This guide provides instructions for setting up monitoring and maintenance for your VPS server.

## Monitoring with Prometheus and Grafana

### 1. Create Docker Compose Configuration

Create `/opt/soodam/monitoring/docker-compose.yml`:

```yaml
version: '3.7'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - monitoring

  node_exporter:
    image: prom/node-exporter:latest
    container_name: node_exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: unless-stopped
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=secure_password_change_me
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    networks:
      - monitoring

networks:
  monitoring:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
```

### 2. Create Prometheus Configuration

Create `/opt/soodam/monitoring/prometheus/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node_exporter'
    static_configs:
      - targets: ['node_exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'soodam_dev'
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['fastapi_dev:8000']

  - job_name: 'soodam_prod'
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['fastapi_prod:8000']
```

### 3. Start Monitoring Services

```bash
cd /opt/soodam/monitoring
docker-compose up -d
```

## Log Management with Filebeat and ELK Stack

### 1. Create Docker Compose Configuration

Create `/opt/soodam/elk/docker-compose.yml`:

```yaml
version: '3.7'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    container_name: elasticsearch_logs
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9202:9200"
    networks:
      - elk

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_URL: http://elasticsearch_logs:9200
      ELASTICSEARCH_HOSTS: http://elasticsearch_logs:9200
    networks:
      - elk
    depends_on:
      - elasticsearch

  filebeat:
    image: docker.elastic.co/beats/filebeat:7.14.0
    container_name: filebeat
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /opt/soodam/dev/logs:/logs/dev:ro
      - /opt/soodam/prod/logs:/logs/prod:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    networks:
      - elk
    depends_on:
      - elasticsearch

networks:
  elk:
    driver: bridge

volumes:
  elasticsearch_data:
```

### 2. Create Filebeat Configuration

Create `/opt/soodam/elk/filebeat/filebeat.yml`:

```yaml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /logs/dev/*.log
  fields:
    environment: development
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /logs/prod/*.log
  fields:
    environment: production
  fields_under_root: true

- type: container
  paths:
    - /var/lib/docker/containers/*/*.log
  processors:
    - add_docker_metadata: ~

output.elasticsearch:
  hosts: ["elasticsearch_logs:9200"]
  indices:
    - index: "filebeat-dev-%{+yyyy.MM.dd}"
      when.equals:
        environment: "development"
    - index: "filebeat-prod-%{+yyyy.MM.dd}"
      when.equals:
        environment: "production"
    - index: "filebeat-docker-%{+yyyy.MM.dd}"
      when.has_fields: ["container.id"]

setup.kibana:
  host: "kibana:5601"

setup.ilm.enabled: false
```

### 3. Start ELK Stack

```bash
cd /opt/soodam/elk
docker-compose up -d
```

## Automatic Updates

### 1. Create Update Script

Create `/opt/soodam/update.sh`:

```bash
#!/bin/bash
set -e

# Update system packages
apt update && apt upgrade -y

# Clean up unused packages
apt autoremove -y

# Update Docker images
docker system prune -af --volumes
cd /opt/soodam/dev && docker-compose pull
cd /opt/soodam/prod && docker-compose pull
cd /opt/soodam/monitoring && docker-compose pull
cd /opt/soodam/elk && docker-compose pull

# Restart services
cd /opt/soodam/dev && docker-compose up -d
cd /opt/soodam/prod && docker-compose up -d
cd /opt/soodam/monitoring && docker-compose up -d
cd /opt/soodam/elk && docker-compose up -d

echo "System update completed successfully!"
```

Make the script executable:

```bash
chmod +x /opt/soodam/update.sh
```

Add to crontab:

```bash
# Run updates weekly on Sunday at 3 AM
echo "0 3 * * 0 /opt/soodam/update.sh >> /var/log/soodam_update.log 2>&1" | sudo tee -a /etc/crontab
```

## Security Hardening

### 1. Fail2Ban Installation

```bash
apt install -y fail2ban
```

### 2. Configure Fail2Ban

Create `/etc/fail2ban/jail.local`:

```
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
```

Restart Fail2Ban:

```bash
systemctl restart fail2ban
```

### 3. Secure Shared Memory

Edit `/etc/fstab` and add:

```
tmpfs /run/shm tmpfs defaults,noexec,nosuid 0 0
```

Apply changes:

```bash
mount -o remount /run/shm
```
