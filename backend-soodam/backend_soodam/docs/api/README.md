# Soodam API Documentation

## 🚀 API Overview

The Soodam API is a RESTful web service built with FastAPI and Django, providing comprehensive functionality for real estate and classified advertisement management. The API follows modern standards and best practices for reliability, security, and performance.

## 🏗️ API Architecture

### Hybrid Framework Approach
- **FastAPI**: High-performance async endpoints for real-time operations
- **Django ORM**: Robust database operations and admin interface
- **Pydantic**: Type-safe request/response validation
- **OpenAPI 3.0**: Comprehensive API documentation and testing

### Base URL Structure
```
Production:  https://api.soodam.com
Staging:     https://staging-api.soodam.com
Development: http://localhost:8000
```

### API Versioning
```
/api/v1/     - Version 1 (Current stable)
/api/v2/     - Version 2 (Enhanced features)
/api/admin/  - Administrative endpoints
```

## 🔐 Authentication

### JWT Token Authentication
All protected endpoints require JWT authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### Authentication Flow
1. **Login**: `POST /api/auth/login` - Get access and refresh tokens
2. **Refresh**: `POST /api/auth/refresh` - Refresh expired access token
3. **Logout**: `POST /api/auth/logout` - Invalidate tokens

### Token Types
- **Access Token**: Short-lived (15 minutes) for API access
- **Refresh Token**: Long-lived (7 days) for token renewal

## 📋 API Endpoints Overview

### 👤 Authentication & Users
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/auth/register` | POST | User registration | ❌ |
| `/api/auth/login` | POST | User login | ❌ |
| `/api/auth/logout` | POST | User logout | ✅ |
| `/api/auth/refresh` | POST | Refresh token | ✅ |
| `/api/user/profile` | GET | Get user profile | ✅ |
| `/api/user/profile` | PUT | Update user profile | ✅ |

### 🏠 Advertisements
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/advertisements` | GET | List advertisements | ❌ |
| `/api/advertisements` | POST | Create advertisement | ✅ |
| `/api/advertisements/{id}` | GET | Get advertisement details | ❌ |
| `/api/advertisements/{id}` | PUT | Update advertisement | ✅ |
| `/api/advertisements/{id}` | DELETE | Delete advertisement | ✅ |
| `/api/advertisements/my-edit-requests` | GET | Get user's edit requests | ✅ |

### 🛡️ Admin Operations
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/admin/dashboard/stats` | GET | Dashboard statistics | ✅ Admin |
| `/api/admin/users` | GET | List all users | ✅ Admin |
| `/api/admin/advertisements` | GET | List all advertisements | ✅ Admin |
| `/api/admin/advertisement-edits` | GET | Pending advertisement edits | ✅ Admin |
| `/api/admin/advertisement-edits/{id}/review` | POST | Approve/reject edits | ✅ Admin |

### 📝 Blog & Content
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/blogs` | GET | List blog posts | ❌ |
| `/api/blogs/{id}` | GET | Get blog post | ❌ |
| `/api/blogs` | POST | Create blog post | ✅ Admin |
| `/api/blogs/{id}` | PUT | Update blog post | ✅ Admin |

### 🌍 Geolocation
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/geolocation/provinces` | GET | List provinces | ❌ |
| `/api/geolocation/cities` | GET | List cities | ❌ |
| `/api/geolocation/nearby` | GET | Find nearby advertisements | ❌ |

## 📊 Request/Response Format

### Standard Response Structure
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation successful",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response Structure
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": ["This field is required"]
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Pagination Structure
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "limit": 10,
  "pages": 10,
  "has_next": true,
  "has_prev": false
}
```

## 🔍 Query Parameters

### Common Parameters
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `search`: Search query string
- `sort_by`: Sort field (e.g., "created_at", "price")
- `sort_order`: Sort direction ("asc" or "desc")

### Advertisement Filtering
- `category_id`: Filter by category
- `location`: Filter by location
- `price_min`: Minimum price
- `price_max`: Maximum price
- `featured`: Show only featured ads

## 📝 Content Types

### Supported Media Types
- **Request**: `application/json`, `multipart/form-data`
- **Response**: `application/json`
- **File Upload**: `multipart/form-data`

### File Upload Limits
- **Images**: Max 10MB per file, JPEG/PNG formats
- **Videos**: Max 100MB per file, MP4/MOV formats
- **Documents**: Max 5MB per file, PDF format

## 🚦 HTTP Status Codes

### Success Codes
- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `204 No Content`: Request successful, no content returned

### Client Error Codes
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (e.g., duplicate)
- `422 Unprocessable Entity`: Validation error

### Server Error Codes
- `500 Internal Server Error`: Server error
- `502 Bad Gateway`: Upstream server error
- `503 Service Unavailable`: Service temporarily unavailable

## 🔒 Rate Limiting

### Rate Limits
- **Anonymous users**: 100 requests per hour
- **Authenticated users**: 1000 requests per hour
- **Admin users**: 5000 requests per hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 🌐 CORS Configuration

### Allowed Origins
- Production: `https://soodam.com`
- Staging: `https://staging.soodam.com`
- Development: `http://localhost:3000`

### Allowed Methods
- GET, POST, PUT, DELETE, OPTIONS

### Allowed Headers
- Authorization, Content-Type, X-Requested-With

## 📚 API Documentation Access

### Interactive Documentation
- **Swagger UI**: `/api/docs` - Interactive API testing
- **ReDoc**: `/api/redoc` - Clean API documentation
- **OpenAPI Spec**: `/api/openapi.json` - Machine-readable spec

### Postman Collection
Download the Postman collection for easy API testing:
- [Soodam API Collection](./postman/soodam-api.json)

## 🧪 Testing the API

### Using cURL
```bash
# Get advertisements
curl -X GET "http://localhost:8000/api/advertisements" \
  -H "Accept: application/json"

# Login
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password"}'

# Create advertisement (authenticated)
curl -X POST "http://localhost:8000/api/advertisements" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"title": "Test Ad", "description": "Test Description"}'
```

### Using Python Requests
```python
import requests

# Base URL
base_url = "http://localhost:8000/api"

# Login
response = requests.post(f"{base_url}/auth/login", json={
    "username": "<EMAIL>",
    "password": "password"
})
token = response.json()["access_token"]

# Make authenticated request
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(f"{base_url}/advertisements", headers=headers)
```

## 📖 Detailed Endpoint Documentation

For detailed documentation of each API endpoint, refer to the specific documentation files:

- [**Authentication API**](./AUTHENTICATION.md) - User authentication and authorization
- [**Advertisement API**](./ADVERTISEMENT.md) - Advertisement management
- [**Admin API**](./ADMIN.md) - Administrative functions
- [**User Management API**](./USER-MANAGEMENT.md) - User profiles and management
- [**Blog API**](./BLOG.md) - Blog and content management
- [**Geolocation API**](./GEOLOCATION.md) - Location-based services

## 🐛 Error Handling

### Common Error Scenarios
1. **Authentication Errors**: Invalid or expired tokens
2. **Validation Errors**: Invalid request data
3. **Permission Errors**: Insufficient user permissions
4. **Resource Errors**: Resource not found or conflicts
5. **Rate Limit Errors**: Too many requests

### Error Response Examples
See individual endpoint documentation for specific error responses and handling strategies.

---

This API documentation provides a comprehensive overview of the Soodam API. For implementation details and examples, refer to the specific endpoint documentation files.
