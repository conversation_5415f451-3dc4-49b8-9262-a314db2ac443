# Advertisement API Documentation

## 🏠 Overview

The Advertisement API provides comprehensive functionality for managing real estate and classified advertisements. It supports creating, reading, updating, and deleting advertisements with rich media, geolocation, and advanced filtering capabilities.

## 🔗 Base Endpoints

All advertisement endpoints are prefixed with `/api/advertisements`

## 📋 Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List advertisements | ❌ |
| POST | `/` | Create advertisement | ✅ |
| GET | `/{id}` | Get advertisement details | ❌ |
| PUT | `/{id}` | Update advertisement (creates edit request) | ✅ |
| DELETE | `/{id}` | Delete advertisement | ✅ |
| GET | `/my-advertisements` | Get user's advertisements | ✅ |
| GET | `/my-edit-requests` | Get user's edit requests | ✅ |
| POST | `/{id}/favorite` | Toggle favorite status | ✅ |
| GET | `/favorites` | Get user's favorite advertisements | ✅ |
| GET | `/nearby` | Find nearby advertisements | ❌ |

## 📖 Detailed Endpoints

### 1. List Advertisements

**GET** `/api/advertisements`

Get a paginated list of approved advertisements with filtering and sorting options.

#### Query Parameters
```
page: integer (default: 1) - Page number
limit: integer (default: 10, max: 100) - Items per page
search: string - Search in title and description
category_id: integer - Filter by category ID
location: string - Filter by location (city or province)
price_min: number - Minimum price filter
price_max: number - Maximum price filter
sort_by: string (default: "created_at") - Sort field
sort_order: string (default: "desc") - Sort direction (asc/desc)
featured: boolean - Show only featured advertisements
```

#### Response Example
```json
{
  "items": [
    {
      "id": 1,
      "title": "Beautiful 3-Bedroom Apartment",
      "description": "Spacious apartment in downtown area...",
      "price": {
        "amount": 250000,
        "currency": "USD",
        "is_negotiable": true
      },
      "category": {
        "id": 1,
        "name": "Real Estate",
        "key": "real_estate"
      },
      "location": {
        "province": "Tehran",
        "city": "Tehran",
        "address": "Valiasr Street"
      },
      "images": [
        {
          "id": 1,
          "url": "https://example.com/image1.jpg",
          "is_primary": true,
          "alt_text": "Living room"
        }
      ],
      "user": {
        "id": 1,
        "username": "john_doe",
        "full_name": "John Doe",
        "is_verified": true
      },
      "status": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10,
  "pages": 10,
  "has_next": true,
  "has_prev": false
}
```

### 2. Create Advertisement

**POST** `/api/advertisements`

Create a new advertisement. Requires authentication.

#### Request Body
```json
{
  "title": "Beautiful 3-Bedroom Apartment",
  "description": "Spacious apartment in downtown area with modern amenities...",
  "category_id": 1,
  "price": {
    "amount": 250000,
    "currency": "USD",
    "is_negotiable": true,
    "deposit": 50000,
    "rent": 2000
  },
  "full_address": {
    "province_id": 1,
    "city_id": 1,
    "address": "123 Main Street, Downtown",
    "zip_code": "12345",
    "longitude": 51.3890,
    "latitude": 35.6892
  },
  "attributes": [
    {
      "id": 1,
      "name": "Bedrooms",
      "type": "choice",
      "value": {"id": 3, "value": "3"}
    },
    {
      "id": 2,
      "name": "Area",
      "type": "text",
      "value": "120 sqm"
    },
    {
      "id": 3,
      "name": "Parking",
      "type": "bool",
      "value": true
    }
  ],
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "is_primary": true,
      "alt_text": "Living room"
    }
  ],
  "videos": [
    {
      "url": "https://example.com/video1.mp4",
      "thumbnail_url": "https://example.com/thumb1.jpg",
      "title": "Property Tour"
    }
  ]
}
```

#### Response
```json
{
  "id": 1,
  "title": "Beautiful 3-Bedroom Apartment",
  "status": 0,
  "message": "Advertisement created successfully and is pending approval",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 3. Get Advertisement Details

**GET** `/api/advertisements/{id}`

Get detailed information about a specific advertisement.

#### Path Parameters
- `id`: Advertisement ID (integer)

#### Response Example
```json
{
  "id": 1,
  "title": "Beautiful 3-Bedroom Apartment",
  "description": "Spacious apartment in downtown area...",
  "price": {
    "amount": 250000,
    "currency": "USD",
    "is_negotiable": true,
    "deposit": 50000,
    "rent": 2000
  },
  "category": {
    "id": 1,
    "name": "Real Estate",
    "key": "real_estate",
    "main_category": {
      "id": 1,
      "name": "Property",
      "key": "property"
    }
  },
  "full_address": {
    "province": "Tehran",
    "city": "Tehran",
    "address": "123 Main Street, Downtown",
    "zip_code": "12345",
    "coordinates": {
      "longitude": 51.3890,
      "latitude": 35.6892
    }
  },
  "attributes": [
    {
      "id": 1,
      "name": "Bedrooms",
      "key": "bedrooms",
      "type": "choice",
      "value": {"id": 3, "value": "3"}
    }
  ],
  "images": [...],
  "videos": [...],
  "user": {...},
  "statistics": {
    "views": 150,
    "favorites": 12,
    "contacts": 5
  },
  "has_pending_edit": false,
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "expiry_date": "2024-02-01T00:00:00Z"
}
```

### 4. Update Advertisement (Create Edit Request)

**PUT** `/api/advertisements/{id}`

Create an edit request for an existing advertisement. The original advertisement remains visible until admin approval.

#### Path Parameters
- `id`: Advertisement ID (integer)

#### Request Body
Same structure as create advertisement, but all fields are optional.

#### Response
```json
{
  "id": 1,
  "title": "Original Title",
  "has_pending_edit": true,
  "message": "Edit request created successfully. Changes will be visible after admin approval.",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### Error Responses
```json
// Existing pending edit
{
  "error": {
    "code": "CONFLICT",
    "message": "There is already a pending edit for this advertisement. Please wait for admin approval."
  }
}

// Not authorized
{
  "error": {
    "code": "FORBIDDEN",
    "message": "Not authorized to update this advertisement"
  }
}
```

### 5. Delete Advertisement

**DELETE** `/api/advertisements/{id}`

Delete an advertisement. Only the owner can delete their advertisement.

#### Path Parameters
- `id`: Advertisement ID (integer)

#### Response
```json
{
  "message": "Advertisement deleted successfully"
}
```

### 6. Get User's Edit Requests

**GET** `/api/advertisements/my-edit-requests`

Get the current user's advertisement edit requests with their status.

#### Query Parameters
```
page: integer (default: 1) - Page number
limit: integer (default: 10) - Items per page
```

#### Response Example
```json
{
  "items": [
    {
      "id": 456,
      "original_advertisement_id": 123,
      "original_advertisement_title": "Original Title",
      "edit_status": 0,
      "title": "Updated Title",
      "description": "Updated Description",
      "admin_notes": null,
      "reviewed_at": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10,
  "pages": 1,
  "has_next": false,
  "has_prev": false
}
```

### 7. Toggle Favorite

**POST** `/api/advertisements/{id}/favorite`

Add or remove an advertisement from user's favorites.

#### Path Parameters
- `id`: Advertisement ID (integer)

#### Response
```json
{
  "is_favorite": true,
  "message": "Advertisement added to favorites"
}
```

### 8. Get Nearby Advertisements

**GET** `/api/advertisements/nearby`

Find advertisements near a specific location.

#### Query Parameters
```
latitude: number (required) - Latitude coordinate
longitude: number (required) - Longitude coordinate
radius: number (default: 10) - Search radius in kilometers
page: integer (default: 1) - Page number
limit: integer (default: 10) - Items per page
category_id: integer - Filter by category
```

#### Response
Same structure as list advertisements with additional distance information.

## 🏷️ Advertisement Status Codes

| Code | Status | Description |
|------|--------|-------------|
| 0 | PENDING | Waiting for admin approval |
| 1 | APPROVED | Approved and visible |
| 2 | REJECTED | Rejected by admin |
| 3 | EXPIRED | Advertisement expired |
| 4 | DELETED | Soft deleted |

## 🏷️ Edit Status Codes

| Code | Status | Description |
|------|--------|-------------|
| 0 | PENDING | Edit waiting for admin review |
| 1 | APPROVED | Edit approved and applied |
| 2 | REJECTED | Edit rejected by admin |

## 📝 Validation Rules

### Required Fields
- `title`: 3-200 characters
- `description`: 10-5000 characters
- `category_id`: Valid category ID
- `full_address`: Complete address information

### Optional Fields
- `price`: Price information (required for some categories)
- `attributes`: Category-specific attributes
- `images`: Maximum 10 images per advertisement
- `videos`: Maximum 3 videos per advertisement

### File Upload Limits
- **Images**: Max 10MB, JPEG/PNG formats
- **Videos**: Max 100MB, MP4/MOV formats

## 🚨 Error Handling

### Common Errors
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Not authorized to perform action
- `404 Not Found`: Advertisement not found
- `409 Conflict`: Existing pending edit
- `422 Unprocessable Entity`: Validation errors

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "title": ["This field is required"],
      "price.amount": ["Must be a positive number"]
    }
  }
}
```

---

This documentation covers all advertisement-related API endpoints. For additional features like media upload and advanced search, refer to the respective API documentation sections.
