# Notification API Documentation

## 🔔 Overview

The Notification API provides comprehensive notification management including push notifications, email notifications, SMS alerts, and in-app messaging. It supports real-time delivery, notification preferences, and delivery tracking.

## 🔗 Base Endpoint

All notification endpoints are prefixed with `/api/notifications`

## 📋 Endpoints Summary

### User Notification Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | Get user notifications | ✅ |
| GET | `/{id}` | Get notification details | ✅ |
| PUT | `/{id}/read` | Mark notification as read | ✅ |
| PUT | `/mark-all-read` | Mark all notifications as read | ✅ |
| DELETE | `/{id}` | Delete notification | ✅ |
| GET | `/unread-count` | Get unread notification count | ✅ |
| GET | `/preferences` | Get notification preferences | ✅ |
| PUT | `/preferences` | Update notification preferences | ✅ |

### Device Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/devices` | Register device for push notifications | ✅ |
| PUT | `/devices/{token}` | Update device information | ✅ |
| DELETE | `/devices/{token}` | Unregister device | ✅ |
| GET | `/devices` | Get registered devices | ✅ |

### Admin Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/send` | Send notification to users | ✅ Admin |
| POST | `/broadcast` | Broadcast notification | ✅ Admin |
| GET | `/analytics` | Get notification analytics | ✅ Admin |
| GET | `/templates` | Get notification templates | ✅ Admin |
| POST | `/templates` | Create notification template | ✅ Admin |

## 📖 Detailed Endpoints

### 1. Get User Notifications

**GET** `/api/notifications`

Get paginated list of notifications for the current user.

#### Query Parameters
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `type` (string): Filter by notification type
- `read` (boolean): Filter by read status
- `category` (string): Filter by category

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "type": "advertisement_approved",
        "category": "advertisement",
        "title": "Advertisement Approved",
        "message": "Your advertisement 'Beautiful Apartment' has been approved and is now live.",
        "data": {
          "advertisement_id": 123,
          "advertisement_title": "Beautiful Apartment",
          "action_url": "/advertisements/123"
        },
        "is_read": false,
        "priority": "normal",
        "delivery_channels": ["push", "email"],
        "delivery_status": {
          "push": {
            "status": "delivered",
            "delivered_at": "2024-01-01T10:00:00Z"
          },
          "email": {
            "status": "sent",
            "sent_at": "2024-01-01T10:00:05Z"
          }
        },
        "created_at": "2024-01-01T10:00:00Z",
        "read_at": null,
        "expires_at": "2024-01-08T10:00:00Z"
      },
      {
        "id": 2,
        "type": "new_message",
        "category": "chat",
        "title": "New Message",
        "message": "You have a new message from John Doe about your advertisement.",
        "data": {
          "sender_id": 456,
          "sender_name": "John Doe",
          "chat_id": 789,
          "message_preview": "Hi, I'm interested in your apartment..."
        },
        "is_read": true,
        "priority": "high",
        "delivery_channels": ["push", "sms"],
        "created_at": "2024-01-01T09:30:00Z",
        "read_at": "2024-01-01T09:35:00Z"
      }
    ],
    "metadata": {
      "total": 50,
      "unread_count": 15,
      "page": 1,
      "limit": 20,
      "pages": 3
    }
  }
}
```

### 2. Get Notification Details

**GET** `/api/notifications/{id}`

Get detailed information about a specific notification.

#### Path Parameters
- `id` (int): Notification ID

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "type": "advertisement_approved",
    "category": "advertisement",
    "title": "Advertisement Approved",
    "message": "Your advertisement 'Beautiful Apartment' has been approved and is now live.",
    "rich_content": {
      "html": "<p>Your advertisement <strong>Beautiful Apartment</strong> has been approved...</p>",
      "images": [
        {
          "url": "https://example.com/notification-image.jpg",
          "alt": "Advertisement approved"
        }
      ],
      "actions": [
        {
          "label": "View Advertisement",
          "url": "/advertisements/123",
          "type": "primary"
        },
        {
          "label": "Edit Advertisement",
          "url": "/advertisements/123/edit",
          "type": "secondary"
        }
      ]
    },
    "data": {
      "advertisement_id": 123,
      "advertisement_title": "Beautiful Apartment",
      "admin_notes": "Approved after review"
    },
    "is_read": false,
    "priority": "normal",
    "delivery_channels": ["push", "email"],
    "delivery_status": {
      "push": {
        "status": "delivered",
        "device_tokens": ["token1", "token2"],
        "delivered_at": "2024-01-01T10:00:00Z",
        "click_count": 1
      },
      "email": {
        "status": "delivered",
        "email_address": "<EMAIL>",
        "sent_at": "2024-01-01T10:00:05Z",
        "opened_at": "2024-01-01T10:05:00Z",
        "clicked_at": "2024-01-01T10:06:00Z"
      }
    },
    "created_at": "2024-01-01T10:00:00Z",
    "read_at": null,
    "expires_at": "2024-01-08T10:00:00Z"
  }
}
```

### 3. Mark Notification as Read

**PUT** `/api/notifications/{id}/read`

Mark a specific notification as read.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "is_read": true,
    "read_at": "2024-01-01T10:30:00Z"
  },
  "message": "Notification marked as read"
}
```

### 4. Get Notification Preferences

**GET** `/api/notifications/preferences`

Get user's notification preferences and settings.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "global_settings": {
      "notifications_enabled": true,
      "quiet_hours": {
        "enabled": true,
        "start_time": "22:00",
        "end_time": "08:00",
        "timezone": "Asia/Tehran"
      }
    },
    "channels": {
      "push": {
        "enabled": true,
        "device_count": 2
      },
      "email": {
        "enabled": true,
        "email_address": "<EMAIL>",
        "verified": true
      },
      "sms": {
        "enabled": false,
        "phone_number": "+1234567890",
        "verified": true
      },
      "in_app": {
        "enabled": true
      }
    },
    "categories": {
      "advertisement": {
        "enabled": true,
        "channels": ["push", "email"],
        "types": {
          "advertisement_approved": true,
          "advertisement_rejected": true,
          "advertisement_expired": true,
          "advertisement_featured": true
        }
      },
      "chat": {
        "enabled": true,
        "channels": ["push", "sms"],
        "types": {
          "new_message": true,
          "message_reply": true
        }
      },
      "system": {
        "enabled": true,
        "channels": ["push", "email"],
        "types": {
          "account_security": true,
          "system_maintenance": true,
          "policy_updates": false
        }
      },
      "marketing": {
        "enabled": false,
        "channels": ["email"],
        "types": {
          "promotional_offers": false,
          "newsletter": false,
          "product_updates": false
        }
      }
    }
  }
}
```

### 5. Update Notification Preferences

**PUT** `/api/notifications/preferences`

Update user's notification preferences.

#### Request Body
```json
{
  "global_settings": {
    "notifications_enabled": true,
    "quiet_hours": {
      "enabled": true,
      "start_time": "23:00",
      "end_time": "07:00"
    }
  },
  "channels": {
    "push": {
      "enabled": true
    },
    "email": {
      "enabled": false
    },
    "sms": {
      "enabled": true
    }
  },
  "categories": {
    "advertisement": {
      "enabled": true,
      "channels": ["push"],
      "types": {
        "advertisement_approved": true,
        "advertisement_rejected": false
      }
    },
    "marketing": {
      "enabled": false
    }
  }
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Notification preferences updated successfully"
}
```

### 6. Register Device for Push Notifications

**POST** `/api/notifications/devices`

Register a device for push notifications.

#### Request Body
```json
{
  "device_token": "device_push_token_here",
  "device_type": "ios",
  "device_name": "iPhone 13",
  "app_version": "1.2.0",
  "os_version": "15.0",
  "language": "en",
  "timezone": "Asia/Tehran"
}
```

#### Response (201 Created)
```json
{
  "success": true,
  "data": {
    "device_id": "uuid-device-id",
    "device_token": "device_push_token_here",
    "device_type": "ios",
    "status": "active",
    "registered_at": "2024-01-01T10:00:00Z"
  },
  "message": "Device registered successfully"
}
```

### 7. Send Notification (Admin)

**POST** `/api/notifications/send`

Send notification to specific users or groups.

#### Request Body
```json
{
  "recipients": {
    "type": "users",
    "user_ids": [1, 2, 3],
    "filters": {
      "location": "Tehran",
      "verified_only": true
    }
  },
  "notification": {
    "type": "custom",
    "category": "system",
    "title": "System Maintenance Notice",
    "message": "Scheduled maintenance will occur tonight from 2-4 AM.",
    "rich_content": {
      "html": "<p>Scheduled maintenance...</p>",
      "actions": [
        {
          "label": "Learn More",
          "url": "/maintenance-info",
          "type": "primary"
        }
      ]
    },
    "data": {
      "maintenance_id": "maint-001",
      "start_time": "2024-01-02T02:00:00Z",
      "end_time": "2024-01-02T04:00:00Z"
    },
    "priority": "high",
    "expires_at": "2024-01-02T06:00:00Z"
  },
  "delivery": {
    "channels": ["push", "email"],
    "schedule": {
      "send_at": "2024-01-01T18:00:00Z"
    },
    "batch_size": 100
  }
}
```

#### Response (202 Accepted)
```json
{
  "success": true,
  "data": {
    "notification_job_id": "job-uuid-123",
    "estimated_recipients": 1500,
    "scheduled_at": "2024-01-01T18:00:00Z",
    "status": "queued"
  },
  "message": "Notification queued for delivery"
}
```

### 8. Get Notification Analytics (Admin)

**GET** `/api/notifications/analytics`

Get notification delivery and engagement analytics.

#### Query Parameters
- `period` (string): Time period ("day", "week", "month")
- `type` (string): Notification type filter
- `channel` (string): Delivery channel filter

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_sent": 50000,
      "total_delivered": 48500,
      "total_opened": 15000,
      "total_clicked": 3500,
      "delivery_rate": 0.97,
      "open_rate": 0.31,
      "click_rate": 0.23
    },
    "by_channel": {
      "push": {
        "sent": 30000,
        "delivered": 29500,
        "opened": 12000,
        "clicked": 2800,
        "delivery_rate": 0.98,
        "open_rate": 0.41,
        "click_rate": 0.23
      },
      "email": {
        "sent": 20000,
        "delivered": 19000,
        "opened": 3000,
        "clicked": 700,
        "delivery_rate": 0.95,
        "open_rate": 0.16,
        "click_rate": 0.23
      }
    },
    "by_type": {
      "advertisement_approved": {
        "sent": 5000,
        "engagement_rate": 0.45
      },
      "new_message": {
        "sent": 15000,
        "engagement_rate": 0.78
      }
    },
    "trends": [
      {
        "date": "2024-01-01",
        "sent": 1500,
        "delivered": 1450,
        "opened": 450,
        "clicked": 105
      }
    ]
  }
}
```

## 🔔 Notification Types

### System Notifications
- `account_created` - Welcome message
- `account_verified` - Account verification complete
- `password_changed` - Password change confirmation
- `login_alert` - Suspicious login attempt

### Advertisement Notifications
- `advertisement_approved` - Advertisement approved
- `advertisement_rejected` - Advertisement rejected
- `advertisement_expired` - Advertisement expired
- `advertisement_featured` - Advertisement featured
- `edit_request_approved` - Edit request approved
- `edit_request_rejected` - Edit request rejected

### Chat Notifications
- `new_message` - New chat message
- `message_reply` - Reply to message
- `chat_request` - New chat request

### Marketing Notifications
- `promotional_offer` - Special offers
- `newsletter` - Newsletter updates
- `product_update` - New features

## 📱 Push Notification Payload

### iOS (APNs) Format
```json
{
  "aps": {
    "alert": {
      "title": "Advertisement Approved",
      "body": "Your advertisement 'Beautiful Apartment' is now live"
    },
    "badge": 5,
    "sound": "default",
    "category": "advertisement_approved"
  },
  "custom_data": {
    "notification_id": 123,
    "advertisement_id": 456,
    "action_url": "/advertisements/456"
  }
}
```

### Android (FCM) Format
```json
{
  "notification": {
    "title": "Advertisement Approved",
    "body": "Your advertisement 'Beautiful Apartment' is now live",
    "icon": "notification_icon",
    "color": "#3B82F6"
  },
  "data": {
    "notification_id": "123",
    "advertisement_id": "456",
    "action_url": "/advertisements/456",
    "type": "advertisement_approved"
  }
}
```

## 🚨 Error Responses

### Common Notification Errors
```json
// Device token invalid
{
  "success": false,
  "error": {
    "code": "INVALID_DEVICE_TOKEN",
    "message": "The provided device token is invalid or expired"
  }
}

// Notification not found
{
  "success": false,
  "error": {
    "code": "NOTIFICATION_NOT_FOUND",
    "message": "Notification with ID 999 does not exist"
  }
}

// Rate limit exceeded
{
  "success": false,
  "error": {
    "code": "RATE_LIMITED",
    "message": "Too many notification requests. Please try again later."
  }
}
```

## 📱 Integration Examples

### JavaScript/React Notification Component
```javascript
// Notification service
class NotificationService {
  async getNotifications(page = 1) {
    const response = await fetch(`/api/notifications?page=${page}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    return response.json();
  }

  async markAsRead(notificationId) {
    const response = await fetch(`/api/notifications/${notificationId}/read`, {
      method: 'PUT',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    return response.json();
  }

  async registerDevice(deviceToken) {
    const response = await fetch('/api/notifications/devices', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        device_token: deviceToken,
        device_type: 'web',
        device_name: navigator.userAgent
      })
    });
    return response.json();
  }
}
```

### Python Notification Client
```python
import requests

class NotificationClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_notifications(self, page=1, unread_only=False):
        params = {'page': page}
        if unread_only:
            params['read'] = False
        
        response = requests.get(
            f'{self.base_url}/notifications',
            params=params,
            headers=self.headers
        )
        return response.json()
    
    def update_preferences(self, preferences):
        response = requests.put(
            f'{self.base_url}/notifications/preferences',
            json=preferences,
            headers=self.headers
        )
        return response.json()
```

---

The Notification API provides comprehensive notification management with multi-channel delivery, rich content support, and detailed analytics for effective user engagement.
