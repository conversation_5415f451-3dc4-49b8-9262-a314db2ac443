# Advertisement Edit System - API Endpoints

## Overview
This document describes the API endpoints for the advertisement edit system with admin approval.

## User Endpoints

### 1. Update Advertisement (Create Edit Request)
```
PUT /api/v1/advertisement/{advertisement_id}
```
**Description:** Creates an edit request instead of directly updating the advertisement.

**Request Body:**
```json
{
  "title": "Updated Title",
  "description": "Updated Description", 
  "price": {
    "amount": 1000,
    "currency": "USD",
    "is_negotiable": true
  },
  "full_address": {
    "province_id": 1,
    "city_id": 1,
    "address": "New Address",
    "zip_code": "1234567890",
    "longitude": 51.3890,
    "latitude": 35.6892
  },
  "attributes": [
    {
      "id": "1",
      "name": "Bedrooms",
      "key": "bedrooms",
      "type": "choice",
      "value": {"id": "1", "value": "3"}
    }
  ]
}
```

**Response:**
```json
{
  "id": 123,
  "title": "Original Title",
  "description": "Original Description",
  "has_pending_edit": true,
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 2. Get User's Edit Requests
```
GET /api/v1/advertisement/my-edit-requests?page=1&limit=10
```
**Description:** Get user's own advertisement edit requests.

**Response:**
```json
{
  "items": [
    {
      "id": 456,
      "original_advertisement_id": 123,
      "original_advertisement_title": "Original Title",
      "edit_status": 0,
      "title": "Updated Title",
      "description": "Updated Description",
      "admin_notes": null,
      "reviewed_at": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10,
  "pages": 1,
  "has_next": false,
  "has_prev": false
}
```

## Admin Endpoints

### 1. Get Pending Advertisement Edits
```
GET /api/v1/admin/advertisement-edits?page=1&limit=10&status_filter=pending
```
**Description:** Get list of advertisement edits for admin review.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status_filter`: Filter by status (pending, approved, rejected)

**Response:**
```json
{
  "items": [
    {
      "id": 456,
      "original_advertisement_id": 123,
      "edit_status": 0,
      "title": "Updated Title",
      "description": "Updated Description",
      "admin_notes": null,
      "reviewed_by": null,
      "reviewed_at": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "metadata": {
    "page": 1,
    "limit": 10,
    "total_count": 1,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

### 2. Get Advertisement Edit Details
```
GET /api/v1/admin/advertisement-edits/{edit_id}
```
**Description:** Get detailed information about a specific advertisement edit.

**Response:**
```json
{
  "id": 456,
  "original_advertisement_id": 123,
  "edit_status": 0,
  "title": "Updated Title",
  "description": "Updated Description",
  "category": {
    "id": 1,
    "name": "Real Estate",
    "key": "real_estate"
  },
  "price": {
    "amount": 1000,
    "currency": "USD",
    "is_negotiable": true
  },
  "full_address": {
    "province": "Tehran",
    "city": "Tehran",
    "address": "New Address"
  },
  "attributes": [...],
  "admin_notes": null,
  "reviewed_by": null,
  "reviewed_at": null,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 3. Approve or Reject Advertisement Edit
```
POST /api/v1/admin/advertisement-edits/{edit_id}/review
```
**Description:** Approve or reject an advertisement edit.

**Request Body:**
```json
{
  "action": "approve",
  "admin_notes": "Changes look good"
}
```

**Response:**
```json
{
  "id": 456,
  "status": 1,
  "message": "Advertisement edit approved and applied successfully"
}
```

### 4. Admin Dashboard Stats (Updated)
```
GET /api/v1/admin/dashboard/stats
```
**Description:** Get admin dashboard statistics including edit stats.

**Response:**
```json
{
  "total_users": 1000,
  "active_users": 950,
  "total_advertisements": 500,
  "pending_advertisements": 10,
  "approved_advertisements": 480,
  "rejected_advertisements": 10,
  "pending_advertisement_edits": 5,
  "approved_advertisement_edits": 20,
  "rejected_advertisement_edits": 3,
  "advertisements_by_category": [...]
}
```

## Status Codes

### Edit Status Values
- `0`: PENDING - Edit is waiting for admin review
- `1`: APPROVED - Edit has been approved and applied
- `2`: REJECTED - Edit has been rejected by admin

### Advertisement Status Values
- `0`: PENDING - Advertisement is waiting for approval
- `1`: APPROVED - Advertisement is approved and visible
- `2`: REJECTED - Advertisement has been rejected
- `3`: DELETED - Advertisement has been deleted

## Error Responses

### 409 Conflict - Existing Pending Edit
```json
{
  "detail": "There is already a pending edit for this advertisement. Please wait for admin approval."
}
```

### 403 Forbidden - Not Authorized
```json
{
  "detail": "Not authorized to update this advertisement"
}
```

### 404 Not Found - Advertisement/Edit Not Found
```json
{
  "detail": "Advertisement not found"
}
```

### 400 Bad Request - Invalid Action
```json
{
  "detail": "Action must be 'approve' or 'reject'"
}
```

## Implementation Notes

1. **Original Advertisement Visibility**: The original advertisement remains visible to all users with status APPROVED while the edit is PENDING.

2. **Single Pending Edit**: Only one pending edit is allowed per advertisement (enforced by database constraint).

3. **Admin Permissions**: Only users with admin privileges can approve/reject edits.

4. **Audit Trail**: All admin actions are logged in the AdminActivity table.

5. **Cache Invalidation**: Related caches are invalidated when edits are approved/rejected.

6. **Data Integrity**: The `apply_to_original()` method ensures all related data (price, location, attributes) is properly updated when an edit is approved.
