# Analytics API Documentation

## 📊 Overview

The Analytics API provides comprehensive data analytics and reporting functionality for the Soodam platform. It includes user behavior analytics, advertisement performance metrics, business intelligence, and real-time statistics.

## 🔗 Base Endpoint

All analytics endpoints are prefixed with `/api/analytics`

## 📋 Endpoints Summary

### Public Analytics
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/public/stats` | Public platform statistics | ❌ |
| GET | `/public/trends` | Market trends and insights | ❌ |

### User Analytics
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/user/dashboard` | User analytics dashboard | ✅ |
| GET | `/user/advertisements` | User's advertisement analytics | ✅ |
| GET | `/user/engagement` | User engagement metrics | ✅ |
| POST | `/user/track-event` | Track user event | ✅ |

### Admin Analytics
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/admin/overview` | Platform overview analytics | ✅ Admin |
| GET | `/admin/users` | User analytics | ✅ Admin |
| GET | `/admin/advertisements` | Advertisement analytics | ✅ Admin |
| GET | `/admin/revenue` | Revenue analytics | ✅ Admin |
| GET | `/admin/performance` | System performance metrics | ✅ Admin |
| GET | `/admin/reports` | Generate custom reports | ✅ Admin |

## 📖 Detailed Endpoints

### 1. Public Platform Statistics

**GET** `/api/analytics/public/stats`

Get public platform statistics and market overview.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "platform_stats": {
      "total_advertisements": 50000,
      "active_advertisements": 35000,
      "total_users": 25000,
      "verified_users": 18000,
      "cities_covered": 150,
      "provinces_covered": 31
    },
    "market_overview": {
      "average_price": {
        "real_estate": {
          "amount": 250000,
          "currency": "USD",
          "change_percentage": 5.2
        },
        "vehicles": {
          "amount": 15000,
          "currency": "USD",
          "change_percentage": -2.1
        }
      },
      "popular_categories": [
        {
          "id": 1,
          "name": "Real Estate",
          "percentage": 65,
          "growth": 8.5
        },
        {
          "id": 2,
          "name": "Vehicles",
          "percentage": 20,
          "growth": 3.2
        }
      ],
      "top_locations": [
        {
          "city": "Tehran",
          "province": "Tehran",
          "advertisement_count": 15000,
          "percentage": 30
        },
        {
          "city": "Isfahan",
          "province": "Isfahan",
          "advertisement_count": 5000,
          "percentage": 10
        }
      ]
    },
    "activity_summary": {
      "daily_new_ads": 150,
      "daily_new_users": 50,
      "daily_searches": 5000,
      "daily_page_views": 25000
    },
    "last_updated": "2024-01-01T12:00:00Z"
  }
}
```

### 2. User Analytics Dashboard

**GET** `/api/analytics/user/dashboard`

Get personalized analytics dashboard for the current user.

#### Query Parameters
- `period` (string): Time period ("7d", "30d", "90d", "1y")

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "period": "30d",
    "summary": {
      "total_advertisements": 15,
      "active_advertisements": 12,
      "total_views": 2500,
      "total_favorites": 85,
      "total_contacts": 25,
      "profile_views": 150
    },
    "advertisement_performance": {
      "top_performing": [
        {
          "id": 123,
          "title": "Beautiful Apartment",
          "views": 450,
          "favorites": 25,
          "contacts": 8,
          "conversion_rate": 0.056
        }
      ],
      "recent_activity": [
        {
          "date": "2024-01-01",
          "views": 85,
          "favorites": 3,
          "contacts": 1
        }
      ]
    },
    "engagement_metrics": {
      "view_to_favorite_rate": 0.034,
      "view_to_contact_rate": 0.01,
      "favorite_to_contact_rate": 0.29,
      "average_time_on_listing": 120
    },
    "audience_insights": {
      "viewer_locations": [
        {"city": "Tehran", "count": 800, "percentage": 32},
        {"city": "Isfahan", "count": 400, "percentage": 16}
      ],
      "viewer_demographics": {
        "age_groups": [
          {"range": "25-34", "percentage": 35},
          {"range": "35-44", "percentage": 28}
        ],
        "interests": [
          {"category": "Real Estate", "percentage": 65},
          {"category": "Investment", "percentage": 25}
        ]
      }
    },
    "recommendations": [
      {
        "type": "optimization",
        "title": "Improve Photo Quality",
        "description": "Listings with high-quality photos get 40% more views",
        "priority": "high"
      },
      {
        "type": "pricing",
        "title": "Price Adjustment",
        "description": "Your listing is 15% above market average",
        "priority": "medium"
      }
    ]
  }
}
```

### 3. User Advertisement Analytics

**GET** `/api/analytics/user/advertisements`

Get detailed analytics for user's advertisements.

#### Query Parameters
- `advertisement_id` (int, optional): Specific advertisement ID
- `period` (string): Time period ("7d", "30d", "90d")
- `metrics` (string): Comma-separated metrics to include

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "advertisements": [
      {
        "id": 123,
        "title": "Beautiful Apartment",
        "status": "active",
        "created_at": "2023-12-01T00:00:00Z",
        "metrics": {
          "views": {
            "total": 450,
            "unique": 380,
            "daily_average": 15,
            "trend": "up",
            "change_percentage": 12.5
          },
          "favorites": {
            "total": 25,
            "conversion_rate": 0.056,
            "trend": "stable"
          },
          "contacts": {
            "total": 8,
            "conversion_rate": 0.018,
            "response_rate": 0.75
          },
          "shares": {
            "total": 12,
            "platforms": {
              "whatsapp": 8,
              "telegram": 3,
              "email": 1
            }
          }
        },
        "performance_score": 85,
        "ranking_position": {
          "category_rank": 15,
          "location_rank": 8,
          "overall_rank": 45
        },
        "time_series": [
          {
            "date": "2024-01-01",
            "views": 15,
            "favorites": 1,
            "contacts": 0
          }
        ],
        "geographic_distribution": [
          {
            "city": "Tehran",
            "views": 200,
            "percentage": 44.4
          }
        ],
        "device_breakdown": {
          "mobile": 0.65,
          "desktop": 0.30,
          "tablet": 0.05
        },
        "traffic_sources": {
          "search": 0.45,
          "direct": 0.25,
          "social": 0.15,
          "referral": 0.15
        }
      }
    ],
    "summary": {
      "total_advertisements": 15,
      "average_performance_score": 78,
      "best_performing_category": "Real Estate",
      "optimization_opportunities": 5
    }
  }
}
```

### 4. Admin Platform Overview

**GET** `/api/analytics/admin/overview`

Get comprehensive platform analytics overview for administrators.

#### Query Parameters
- `period` (string): Time period ("day", "week", "month", "quarter", "year")
- `compare_previous` (boolean): Include comparison with previous period

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "period": "month",
    "key_metrics": {
      "total_users": {
        "current": 25000,
        "previous": 23500,
        "change": 1500,
        "change_percentage": 6.38,
        "trend": "up"
      },
      "active_users": {
        "current": 18000,
        "previous": 17200,
        "change": 800,
        "change_percentage": 4.65,
        "trend": "up"
      },
      "total_advertisements": {
        "current": 50000,
        "previous": 47500,
        "change": 2500,
        "change_percentage": 5.26,
        "trend": "up"
      },
      "revenue": {
        "current": 125000,
        "previous": 118000,
        "change": 7000,
        "change_percentage": 5.93,
        "trend": "up",
        "currency": "USD"
      }
    },
    "user_analytics": {
      "new_registrations": {
        "total": 1500,
        "daily_average": 50,
        "conversion_rate": 0.75
      },
      "user_retention": {
        "day_1": 0.85,
        "day_7": 0.65,
        "day_30": 0.45
      },
      "user_engagement": {
        "average_session_duration": 480,
        "pages_per_session": 5.2,
        "bounce_rate": 0.35
      }
    },
    "advertisement_analytics": {
      "new_listings": {
        "total": 2500,
        "daily_average": 83,
        "approval_rate": 0.92
      },
      "category_distribution": [
        {"category": "Real Estate", "count": 32500, "percentage": 65},
        {"category": "Vehicles", "count": 10000, "percentage": 20}
      ],
      "geographic_distribution": [
        {"province": "Tehran", "count": 15000, "percentage": 30},
        {"province": "Isfahan", "count": 7500, "percentage": 15}
      ]
    },
    "financial_metrics": {
      "revenue_streams": {
        "featured_ads": 75000,
        "premium_subscriptions": 35000,
        "advertising": 15000
      },
      "average_revenue_per_user": 5.0,
      "customer_lifetime_value": 125.0
    },
    "system_performance": {
      "average_response_time": 145,
      "uptime_percentage": 99.95,
      "error_rate": 0.002,
      "api_calls_per_day": 500000
    }
  }
}
```

### 5. Revenue Analytics (Admin)

**GET** `/api/analytics/admin/revenue`

Get detailed revenue analytics and financial metrics.

#### Query Parameters
- `period` (string): Time period
- `breakdown` (string): Breakdown type ("daily", "weekly", "monthly")
- `currency` (string): Currency code (default: "USD")

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_revenue": 125000,
      "recurring_revenue": 85000,
      "one_time_revenue": 40000,
      "currency": "USD",
      "growth_rate": 8.5
    },
    "revenue_streams": [
      {
        "name": "Featured Advertisements",
        "revenue": 75000,
        "percentage": 60,
        "transactions": 1500,
        "average_value": 50
      },
      {
        "name": "Premium Subscriptions",
        "revenue": 35000,
        "percentage": 28,
        "subscribers": 700,
        "monthly_recurring": 35000
      },
      {
        "name": "Advertising Revenue",
        "revenue": 15000,
        "percentage": 12,
        "impressions": 5000000,
        "cpm": 3.0
      }
    ],
    "time_series": [
      {
        "date": "2024-01-01",
        "revenue": 4500,
        "transactions": 90,
        "new_customers": 25
      }
    ],
    "customer_metrics": {
      "total_paying_customers": 2200,
      "new_customers": 150,
      "churned_customers": 25,
      "customer_lifetime_value": 125.0,
      "average_revenue_per_user": 5.68
    },
    "forecasting": {
      "next_month_projection": 135000,
      "confidence_interval": [128000, 142000],
      "growth_factors": [
        "Seasonal increase in real estate activity",
        "New premium features launch"
      ]
    }
  }
}
```

### 6. Track User Event

**POST** `/api/analytics/user/track-event`

Track custom user events for analytics.

#### Request Body
```json
{
  "event_name": "advertisement_viewed",
  "event_category": "engagement",
  "properties": {
    "advertisement_id": 123,
    "advertisement_title": "Beautiful Apartment",
    "category": "Real Estate",
    "price": 250000,
    "location": "Tehran",
    "view_duration": 45,
    "source": "search_results",
    "device_type": "mobile"
  },
  "timestamp": "2024-01-01T10:30:00Z"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "event_id": "evt_123456789",
    "status": "recorded",
    "timestamp": "2024-01-01T10:30:00Z"
  },
  "message": "Event tracked successfully"
}
```

### 7. Generate Custom Reports (Admin)

**GET** `/api/analytics/admin/reports`

Generate custom analytics reports with flexible parameters.

#### Query Parameters
- `report_type` (string): Type of report ("user_activity", "revenue", "performance")
- `date_from` (string): Start date (YYYY-MM-DD)
- `date_to` (string): End date (YYYY-MM-DD)
- `filters` (string): JSON-encoded filters
- `format` (string): Output format ("json", "csv", "pdf")

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "report_id": "rpt_123456",
    "report_type": "user_activity",
    "parameters": {
      "date_from": "2024-01-01",
      "date_to": "2024-01-31",
      "filters": {
        "user_type": "premium",
        "location": "Tehran"
      }
    },
    "results": {
      "total_records": 5000,
      "summary": {
        "active_users": 3500,
        "total_sessions": 15000,
        "average_session_duration": 420
      },
      "data": [
        {
          "date": "2024-01-01",
          "active_users": 120,
          "sessions": 450,
          "page_views": 2250
        }
      ]
    },
    "export_urls": {
      "csv": "https://example.com/reports/rpt_123456.csv",
      "pdf": "https://example.com/reports/rpt_123456.pdf"
    },
    "generated_at": "2024-01-01T12:00:00Z",
    "expires_at": "2024-01-08T12:00:00Z"
  }
}
```

## 📊 Analytics Features

### Real-time Analytics
- **Live user activity**: Current active users and sessions
- **Real-time events**: Live event tracking and processing
- **Instant metrics**: Up-to-date key performance indicators
- **Live dashboards**: Real-time data visualization

### Advanced Segmentation
- **User segments**: Demographic, behavioral, and custom segments
- **Cohort analysis**: User retention and lifecycle analysis
- **Funnel analysis**: Conversion funnel tracking
- **A/B testing**: Experiment tracking and analysis

### Predictive Analytics
- **Trend forecasting**: Predict future metrics based on historical data
- **Churn prediction**: Identify users at risk of churning
- **Revenue forecasting**: Predict future revenue streams
- **Demand prediction**: Forecast advertisement demand

## 🔍 Event Tracking

### Standard Events
```javascript
// Page view
{
  "event_name": "page_view",
  "properties": {
    "page_url": "/advertisements/123",
    "page_title": "Beautiful Apartment",
    "referrer": "https://google.com"
  }
}

// Advertisement interaction
{
  "event_name": "advertisement_contact",
  "properties": {
    "advertisement_id": 123,
    "contact_method": "phone",
    "user_intent": "purchase"
  }
}

// Search event
{
  "event_name": "search_performed",
  "properties": {
    "query": "apartment tehran",
    "results_count": 150,
    "filters_applied": ["price_range", "location"]
  }
}
```

## 🚨 Error Responses

### Common Analytics Errors
```json
// Invalid date range
{
  "success": false,
  "error": {
    "code": "INVALID_DATE_RANGE",
    "message": "End date must be after start date"
  }
}

// Insufficient permissions
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "Admin access required for this analytics endpoint"
  }
}

// Rate limit exceeded
{
  "success": false,
  "error": {
    "code": "ANALYTICS_RATE_LIMITED",
    "message": "Too many analytics requests. Please try again later."
  }
}
```

## 📱 Integration Examples

### JavaScript Analytics Client
```javascript
class AnalyticsClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async trackEvent(eventName, properties) {
    const response = await fetch(`${this.baseUrl}/analytics/user/track-event`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event_name: eventName,
        properties: properties,
        timestamp: new Date().toISOString()
      })
    });
    return response.json();
  }

  async getUserDashboard(period = '30d') {
    const response = await fetch(
      `${this.baseUrl}/analytics/user/dashboard?period=${period}`,
      {
        headers: { 'Authorization': `Bearer ${this.token}` }
      }
    );
    return response.json();
  }
}

// Usage
const analytics = new AnalyticsClient('/api', userToken);
analytics.trackEvent('advertisement_viewed', {
  advertisement_id: 123,
  view_duration: 45
});
```

### Python Analytics Integration
```python
import requests
from datetime import datetime

class AnalyticsAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_admin_overview(self, period='month'):
        response = requests.get(
            f'{self.base_url}/analytics/admin/overview',
            params={'period': period},
            headers=self.headers
        )
        return response.json()
    
    def generate_report(self, report_type, date_from, date_to, filters=None):
        params = {
            'report_type': report_type,
            'date_from': date_from,
            'date_to': date_to
        }
        if filters:
            params['filters'] = json.dumps(filters)
        
        response = requests.get(
            f'{self.base_url}/analytics/admin/reports',
            params=params,
            headers=self.headers
        )
        return response.json()
```

---

The Analytics API provides comprehensive data insights with real-time tracking, advanced segmentation, and predictive analytics for data-driven decision making.
