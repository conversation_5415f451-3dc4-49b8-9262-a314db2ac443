# User Management API Documentation

## 👤 Overview

The User Management API provides comprehensive functionality for user profile management, account settings, preferences, and user-related operations. This API handles both self-service user operations and administrative user management.

## 🔗 Base Endpoint

All user management endpoints are prefixed with `/api/user`

## 📋 Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/profile` | Get current user profile | ✅ |
| PUT | `/profile` | Update user profile | ✅ |
| POST | `/profile/avatar` | Upload profile avatar | ✅ |
| DELETE | `/profile/avatar` | Remove profile avatar | ✅ |
| GET | `/settings` | Get user settings | ✅ |
| PUT | `/settings` | Update user settings | ✅ |
| GET | `/addresses` | Get user addresses | ✅ |
| POST | `/addresses` | Add new address | ✅ |
| PUT | `/addresses/{id}` | Update address | ✅ |
| DELETE | `/addresses/{id}` | Delete address | ✅ |
| GET | `/favorites` | Get favorite advertisements | ✅ |
| POST | `/favorites/{ad_id}` | Add/remove favorite | ✅ |
| GET | `/my-advertisements` | Get user's advertisements | ✅ |
| GET | `/activity` | Get user activity log | ✅ |
| POST | `/deactivate` | Deactivate account | ✅ |

## 📖 Detailed Endpoints

### 1. Get User Profile

**GET** `/api/user/profile`

Get the current authenticated user's profile information.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "phone_number": "+**********",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "avatar": "https://example.com/avatars/user1.jpg",
    "bio": "Real estate enthusiast and property investor",
    "date_of_birth": "1990-01-15",
    "gender": "male",
    "is_verified": true,
    "is_active": true,
    "verification_status": {
      "email_verified": true,
      "phone_verified": true,
      "identity_verified": false
    },
    "location": {
      "province_id": 1,
      "province_name": "Tehran",
      "city_id": 1,
      "city_name": "Tehran"
    },
    "statistics": {
      "total_advertisements": 15,
      "active_advertisements": 12,
      "total_views": 1250,
      "total_favorites": 45,
      "member_since": "2023-01-15"
    },
    "preferences": {
      "language": "fa",
      "timezone": "Asia/Tehran",
      "currency": "IRR",
      "notifications_enabled": true
    },
    "created_at": "2023-01-15T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-02T10:30:00Z"
  }
}
```

### 2. Update User Profile

**PUT** `/api/user/profile`

Update the current user's profile information.

#### Request Body
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "bio": "Updated bio description",
  "date_of_birth": "1990-01-15",
  "gender": "male",
  "location": {
    "province_id": 1,
    "city_id": 2
  },
  "preferences": {
    "language": "en",
    "timezone": "Asia/Tehran",
    "currency": "USD"
  }
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Smith",
    "full_name": "John Smith",
    "bio": "Updated bio description",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Profile updated successfully"
}
```

### 3. Upload Profile Avatar

**POST** `/api/user/profile/avatar`

Upload or update the user's profile avatar image.

#### Request Body (multipart/form-data)
```
avatar: File (image file - JPEG, PNG, WebP)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "avatar_url": "https://example.com/avatars/user1_new.jpg",
    "thumbnail_url": "https://example.com/avatars/thumbs/user1_new.jpg",
    "uploaded_at": "2024-01-01T00:00:00Z"
  },
  "message": "Avatar uploaded successfully"
}
```

### 4. Get User Settings

**GET** `/api/user/settings`

Get user's application settings and preferences.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "notifications": {
      "email_notifications": true,
      "sms_notifications": false,
      "push_notifications": true,
      "marketing_emails": false,
      "new_message_alerts": true,
      "advertisement_updates": true
    },
    "privacy": {
      "profile_visibility": "public",
      "show_phone_number": false,
      "show_email": false,
      "allow_contact": true
    },
    "display": {
      "language": "fa",
      "theme": "light",
      "currency": "IRR",
      "date_format": "jalali",
      "items_per_page": 20
    },
    "location": {
      "auto_detect_location": true,
      "default_search_radius": 10,
      "preferred_provinces": [1, 2, 3]
    }
  }
}
```

### 5. Update User Settings

**PUT** `/api/user/settings`

Update user's application settings and preferences.

#### Request Body
```json
{
  "notifications": {
    "email_notifications": false,
    "push_notifications": true,
    "marketing_emails": false
  },
  "privacy": {
    "profile_visibility": "private",
    "show_phone_number": false
  },
  "display": {
    "language": "en",
    "theme": "dark",
    "currency": "USD"
  }
}
```

### 6. Get User Addresses

**GET** `/api/user/addresses`

Get all saved addresses for the current user.

#### Response (200 OK)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Home",
      "province_id": 1,
      "province_name": "Tehran",
      "city_id": 1,
      "city_name": "Tehran",
      "address": "Valiasr Street, No. 123",
      "postal_code": "12345",
      "coordinates": {
        "latitude": 35.6892,
        "longitude": 51.3890
      },
      "is_default": true,
      "created_at": "2023-01-15T00:00:00Z"
    },
    {
      "id": 2,
      "title": "Office",
      "province_id": 1,
      "province_name": "Tehran",
      "city_id": 1,
      "city_name": "Tehran",
      "address": "Azadi Square, Building 5",
      "postal_code": "54321",
      "coordinates": {
        "latitude": 35.6995,
        "longitude": 51.3375
      },
      "is_default": false,
      "created_at": "2023-02-01T00:00:00Z"
    }
  ]
}
```

### 7. Add New Address

**POST** `/api/user/addresses`

Add a new address to the user's address book.

#### Request Body
```json
{
  "title": "Summer House",
  "province_id": 2,
  "city_id": 15,
  "address": "Seaside Boulevard, Villa 42",
  "postal_code": "67890",
  "coordinates": {
    "latitude": 36.5659,
    "longitude": 53.0586
  },
  "is_default": false
}
```

#### Response (201 Created)
```json
{
  "success": true,
  "data": {
    "id": 3,
    "title": "Summer House",
    "province_name": "Mazandaran",
    "city_name": "Sari",
    "address": "Seaside Boulevard, Villa 42",
    "is_default": false,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "Address added successfully"
}
```

### 8. Get Favorite Advertisements

**GET** `/api/user/favorites`

Get the user's favorite advertisements with pagination.

#### Query Parameters
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 10)
- `category_id` (int, optional): Filter by category
- `sort_by` (string): Sort by ("created_at", "price", "title")

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "advertisement": {
          "id": 123,
          "title": "Beautiful Apartment",
          "price": {
            "amount": 250000,
            "currency": "USD"
          },
          "location": {
            "city": "Tehran",
            "province": "Tehran"
          },
          "images": [
            {
              "url": "https://example.com/image1.jpg",
              "is_primary": true
            }
          ],
          "status": 1,
          "created_at": "2023-12-01T00:00:00Z"
        },
        "favorited_at": "2023-12-15T00:00:00Z"
      }
    ],
    "metadata": {
      "total": 45,
      "page": 1,
      "limit": 10,
      "pages": 5
    }
  }
}
```

### 9. Get User's Advertisements

**GET** `/api/user/my-advertisements`

Get all advertisements created by the current user.

#### Query Parameters
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 10)
- `status` (string): Filter by status ("pending", "approved", "rejected", "expired")
- `category_id` (int, optional): Filter by category

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "My Apartment for Sale",
        "description": "Beautiful apartment...",
        "status": 1,
        "status_text": "Approved",
        "category": {
          "id": 1,
          "name": "Real Estate"
        },
        "price": {
          "amount": 300000,
          "currency": "USD"
        },
        "statistics": {
          "views": 150,
          "favorites": 12,
          "contacts": 5
        },
        "has_pending_edit": false,
        "created_at": "2023-11-01T00:00:00Z",
        "updated_at": "2023-11-01T00:00:00Z",
        "expires_at": "2024-02-01T00:00:00Z"
      }
    ],
    "metadata": {
      "total": 15,
      "page": 1,
      "limit": 10
    },
    "summary": {
      "total_advertisements": 15,
      "pending": 2,
      "approved": 12,
      "rejected": 1,
      "expired": 0
    }
  }
}
```

### 10. Get User Activity Log

**GET** `/api/user/activity`

Get the user's recent activity and actions.

#### Query Parameters
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `activity_type` (string, optional): Filter by type

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "activity_type": "advertisement_created",
        "description": "Created advertisement 'Beautiful Apartment'",
        "metadata": {
          "advertisement_id": 123,
          "advertisement_title": "Beautiful Apartment"
        },
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0...",
        "created_at": "2024-01-01T10:30:00Z"
      },
      {
        "id": 2,
        "activity_type": "profile_updated",
        "description": "Updated profile information",
        "metadata": {
          "fields_changed": ["first_name", "bio"]
        },
        "ip_address": "***********",
        "created_at": "2024-01-01T09:15:00Z"
      }
    ],
    "metadata": {
      "total": 50,
      "page": 1,
      "limit": 20
    }
  }
}
```

## 🔒 Security Features

### Profile Privacy Controls
- **Profile Visibility**: Public, private, or contacts only
- **Contact Information**: Control visibility of email/phone
- **Activity Privacy**: Control what activities are visible

### Data Protection
- **Personal Data Encryption**: Sensitive data encrypted at rest
- **Access Logging**: All profile access logged
- **Data Export**: Users can export their data
- **Account Deletion**: Secure account deactivation

## 📱 File Upload Specifications

### Avatar Upload
- **Supported Formats**: JPEG, PNG, WebP
- **Maximum Size**: 5MB
- **Dimensions**: Minimum 100x100px, Maximum 2000x2000px
- **Processing**: Automatic resizing and thumbnail generation

## 🚨 Error Responses

### Common User Management Errors
```json
// Profile update validation error
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid profile data",
    "details": {
      "date_of_birth": ["Date cannot be in the future"],
      "phone_number": ["Invalid phone number format"]
    }
  }
}

// Avatar upload error
{
  "success": false,
  "error": {
    "code": "FILE_UPLOAD_ERROR",
    "message": "Avatar upload failed",
    "details": {
      "file_size": ["File size exceeds 5MB limit"],
      "file_type": ["Only JPEG, PNG, WebP files are allowed"]
    }
  }
}

// Address limit exceeded
{
  "success": false,
  "error": {
    "code": "LIMIT_EXCEEDED",
    "message": "Maximum number of addresses (10) exceeded"
  }
}
```

## 📊 Usage Analytics

### Profile Completion Score
```json
{
  "profile_completion": {
    "score": 85,
    "max_score": 100,
    "missing_fields": [
      "date_of_birth",
      "bio"
    ],
    "suggestions": [
      "Add a profile picture",
      "Complete your bio",
      "Verify your phone number"
    ]
  }
}
```

## 📱 Integration Examples

### JavaScript/React
```javascript
// Update user profile
const updateProfile = async (profileData) => {
  const response = await fetch('/api/user/profile', {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(profileData)
  });
  return response.json();
};

// Upload avatar
const uploadAvatar = async (file) => {
  const formData = new FormData();
  formData.append('avatar', file);
  
  const response = await fetch('/api/user/profile/avatar', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  return response.json();
};
```

### Python/Requests
```python
import requests

# Get user profile
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('/api/user/profile', headers=headers)
profile = response.json()

# Update settings
settings_data = {
    'notifications': {
        'email_notifications': False,
        'push_notifications': True
    }
}
response = requests.put('/api/user/settings', 
                      json=settings_data, headers=headers)
```

---

The User Management API provides comprehensive user profile and preference management with strong privacy controls and security features.
