# Authentication API Documentation

## 🔐 Overview

The Authentication API provides secure user registration, login, logout, and token management functionality using JWT (JSON Web Tokens). The system supports dual authentication methods using either email or phone number.

## 🔗 Base Endpoint

All authentication endpoints are prefixed with `/api/auth`

## 📋 Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/register` | Register new user | ❌ |
| POST | `/login` | User login | ❌ |
| POST | `/logout` | User logout | ✅ |
| POST | `/refresh` | Refresh access token | ✅ |
| POST | `/verify-email` | Verify email address | ✅ |
| POST | `/verify-phone` | Verify phone number | ✅ |
| POST | `/forgot-password` | Request password reset | ❌ |
| POST | `/reset-password` | Reset password | ❌ |
| POST | `/change-password` | Change password | ✅ |

## 📖 Detailed Endpoints

### 1. User Registration

**POST** `/api/auth/register`

Register a new user account with email and phone number.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "phone_number": "+**********",
  "password": "secure_password123",
  "first_name": "John",
  "last_name": "Doe"
}
```

#### Validation Rules
- **Email**: Valid email format, unique
- **Phone**: Valid international format (+country_code), unique
- **Password**: Minimum 8 characters, must contain letters and numbers
- **Names**: 2-50 characters each

#### Response (201 Created)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "phone_number": "+**********",
    "first_name": "John",
    "last_name": "Doe",
    "is_verified": false,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "User registered successfully. Please verify your email and phone number."
}
```

#### Error Responses
```json
// Email already exists
{
  "success": false,
  "error": {
    "code": "EMAIL_EXISTS",
    "message": "User with this email already exists",
    "details": {
      "email": ["This email is already registered"]
    }
  }
}

// Validation error
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "password": ["Password must be at least 8 characters long"],
      "phone_number": ["Invalid phone number format"]
    }
  }
}
```

### 2. User Login

**POST** `/api/auth/login`

Authenticate user and receive access and refresh tokens.

#### Request Body
```json
{
  "username": "<EMAIL>",  // Can be email or phone number
  "password": "secure_password123"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 900,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "phone_number": "+**********",
      "first_name": "John",
      "last_name": "Doe",
      "is_verified": true,
      "last_login": "2024-01-01T00:00:00Z"
    }
  },
  "message": "Login successful"
}
```

#### Error Responses
```json
// Invalid credentials
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid username or password"
  }
}

// Account not verified
{
  "success": false,
  "error": {
    "code": "ACCOUNT_NOT_VERIFIED",
    "message": "Please verify your email and phone number before logging in"
  }
}

// Account disabled
{
  "success": false,
  "error": {
    "code": "ACCOUNT_DISABLED",
    "message": "Your account has been disabled. Please contact support."
  }
}
```

### 3. Token Refresh

**POST** `/api/auth/refresh`

Refresh an expired access token using a valid refresh token.

#### Request Body
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 900
  },
  "message": "Token refreshed successfully"
}
```

### 4. User Logout

**POST** `/api/auth/logout`

Logout user and invalidate tokens. Requires authentication.

#### Headers
```http
Authorization: Bearer <access_token>
```

#### Request Body
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### 5. Email Verification

**POST** `/api/auth/verify-email`

Verify user's email address using verification code.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "verification_code": "123456"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### 6. Phone Verification

**POST** `/api/auth/verify-phone`

Verify user's phone number using SMS verification code.

#### Request Body
```json
{
  "phone_number": "+**********",
  "verification_code": "123456"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Phone number verified successfully"
}
```

### 7. Forgot Password

**POST** `/api/auth/forgot-password`

Request password reset link/code.

#### Request Body
```json
{
  "email": "<EMAIL>"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Password reset instructions sent to your email"
}
```

### 8. Reset Password

**POST** `/api/auth/reset-password`

Reset password using reset token.

#### Request Body
```json
{
  "reset_token": "reset-token-from-email",
  "new_password": "new_secure_password123"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

### 9. Change Password

**POST** `/api/auth/change-password`

Change password for authenticated user.

#### Headers
```http
Authorization: Bearer <access_token>
```

#### Request Body
```json
{
  "current_password": "current_password123",
  "new_password": "new_secure_password123"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

## 🔑 JWT Token Details

### Token Structure
- **Access Token**: Short-lived (15 minutes), used for API authentication
- **Refresh Token**: Long-lived (7 days), used to refresh access tokens

### Token Usage
```http
Authorization: Bearer <access_token>
```

### Token Payload
```json
{
  "user_id": 1,
  "email": "<EMAIL>",
  "exp": 1640995200,
  "iat": 1640994300,
  "token_type": "access"
}
```

## 🔒 Security Features

### Password Requirements
- Minimum 8 characters
- Must contain letters and numbers
- Cannot be common passwords
- Cannot be similar to user information

### Rate Limiting
- **Login attempts**: 5 attempts per 15 minutes per IP
- **Registration**: 3 attempts per hour per IP
- **Password reset**: 3 attempts per hour per email

### Security Headers
- CSRF protection enabled
- Secure cookie settings in production
- HTTPS enforcement in production

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| `EMAIL_EXISTS` | Email already registered |
| `PHONE_EXISTS` | Phone number already registered |
| `INVALID_CREDENTIALS` | Wrong username/password |
| `ACCOUNT_NOT_VERIFIED` | Account needs verification |
| `ACCOUNT_DISABLED` | Account has been disabled |
| `TOKEN_EXPIRED` | JWT token has expired |
| `TOKEN_INVALID` | JWT token is invalid |
| `RATE_LIMITED` | Too many requests |
| `VALIDATION_ERROR` | Input validation failed |

## 📱 Usage Examples

### JavaScript/Fetch
```javascript
// Register
const registerResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123',
    first_name: 'John',
    last_name: 'Doe',
    phone_number: '+**********'
  })
});

// Login
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: '<EMAIL>',
    password: 'password123'
  })
});

const { access_token } = await loginResponse.json();

// Use token
const profileResponse = await fetch('/api/user/profile', {
  headers: { 'Authorization': `Bearer ${access_token}` }
});
```

### Python/Requests
```python
import requests

# Register
register_data = {
    'email': '<EMAIL>',
    'password': 'password123',
    'first_name': 'John',
    'last_name': 'Doe',
    'phone_number': '+**********'
}
response = requests.post('/api/auth/register', json=register_data)

# Login
login_data = {
    'username': '<EMAIL>',
    'password': 'password123'
}
response = requests.post('/api/auth/login', json=login_data)
token = response.json()['access_token']

# Use token
headers = {'Authorization': f'Bearer {token}'}
profile = requests.get('/api/user/profile', headers=headers)
```

---

This authentication system provides secure, scalable user management with modern JWT-based authentication and comprehensive verification features.
