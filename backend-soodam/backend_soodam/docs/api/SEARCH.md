# Search & Autocomplete API Documentation

## 🔍 Overview

The Search & Autocomplete API provides powerful search functionality across advertisements, users, and content using Elasticsearch. It includes full-text search, faceted filtering, autocomplete suggestions, and advanced search analytics.

## 🔗 Base Endpoint

All search endpoints are prefixed with `/api/search`

## 📋 Endpoints Summary

### Search Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/advertisements` | Search advertisements | ❌ |
| GET | `/users` | Search users | ❌ |
| GET | `/blogs` | Search blog posts | ❌ |
| GET | `/global` | Global search across all content | ❌ |
| GET | `/suggestions` | Get search suggestions | ❌ |

### Autocomplete Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/autocomplete/advertisements` | Advertisement autocomplete | ❌ |
| GET | `/autocomplete/locations` | Location autocomplete | ❌ |
| GET | `/autocomplete/categories` | Category autocomplete | ❌ |
| GET | `/autocomplete/global` | Global autocomplete | ❌ |

### Analytics Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/analytics/popular` | Popular search terms | ✅ Admin |
| GET | `/analytics/trends` | Search trends | ✅ Admin |
| POST | `/analytics/track` | Track search event | ❌ |

## 📖 Detailed Endpoints

### 1. Search Advertisements

**GET** `/api/search/advertisements`

Search advertisements with advanced filtering and faceting.

#### Query Parameters
- `q` (string): Search query
- `category_id` (int): Filter by category
- `location` (string): Location filter (city or province)
- `price_min` (float): Minimum price
- `price_max` (float): Maximum price
- `latitude` (float): Latitude for location-based search
- `longitude` (float): Longitude for location-based search
- `radius` (float): Search radius in km (default: 10)
- `sort_by` (string): Sort by ("relevance", "price", "date", "distance")
- `sort_order` (string): Sort direction ("asc", "desc")
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `facets` (boolean): Include facet data (default: false)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "apartment tehran",
    "items": [
      {
        "id": 1,
        "title": "Beautiful Apartment in Tehran",
        "description": "Spacious 3-bedroom apartment...",
        "price": {
          "amount": 250000,
          "currency": "USD"
        },
        "location": {
          "city": "Tehran",
          "province": "Tehran",
          "coordinates": {
            "latitude": 35.6892,
            "longitude": 51.3890
          },
          "distance_km": 2.5
        },
        "category": {
          "id": 1,
          "name": "Real Estate"
        },
        "images": [
          {
            "url": "https://example.com/image1.jpg",
            "is_primary": true
          }
        ],
        "user": {
          "id": 1,
          "name": "John Doe",
          "is_verified": true
        },
        "statistics": {
          "views": 150,
          "favorites": 12
        },
        "relevance_score": 0.95,
        "highlighted_fields": {
          "title": "Beautiful <mark>Apartment</mark> in <mark>Tehran</mark>",
          "description": "Spacious 3-bedroom <mark>apartment</mark>..."
        },
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "metadata": {
      "total": 150,
      "page": 1,
      "limit": 20,
      "pages": 8,
      "search_time_ms": 45,
      "max_score": 0.95
    },
    "facets": {
      "categories": [
        {
          "id": 1,
          "name": "Real Estate",
          "count": 120
        },
        {
          "id": 2,
          "name": "Vehicles",
          "count": 30
        }
      ],
      "locations": [
        {
          "city": "Tehran",
          "count": 80
        },
        {
          "city": "Isfahan",
          "count": 25
        }
      ],
      "price_ranges": [
        {
          "range": "0-100000",
          "count": 45
        },
        {
          "range": "100000-500000",
          "count": 85
        }
      ]
    },
    "suggestions": [
      "apartment tehran rent",
      "apartment tehran buy",
      "apartment tehran furnished"
    ]
  }
}
```

### 2. Global Search

**GET** `/api/search/global`

Search across all content types (advertisements, blogs, users).

#### Query Parameters
- `q` (string, required): Search query
- `types` (string): Comma-separated content types ("advertisements", "blogs", "users")
- `page` (int): Page number
- `limit` (int): Items per page

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "real estate",
    "results": {
      "advertisements": {
        "total": 150,
        "items": [
          {
            "type": "advertisement",
            "id": 1,
            "title": "Real Estate Investment Opportunity",
            "excerpt": "Prime location property...",
            "url": "/advertisements/1",
            "relevance_score": 0.92
          }
        ]
      },
      "blogs": {
        "total": 25,
        "items": [
          {
            "type": "blog",
            "id": 1,
            "title": "Real Estate Market Trends 2024",
            "excerpt": "Latest market analysis...",
            "url": "/blog/real-estate-market-trends-2024",
            "relevance_score": 0.88
          }
        ]
      },
      "users": {
        "total": 5,
        "items": [
          {
            "type": "user",
            "id": 1,
            "name": "Real Estate Expert",
            "bio": "Professional real estate agent...",
            "url": "/users/1",
            "relevance_score": 0.75
          }
        ]
      }
    },
    "metadata": {
      "total_results": 180,
      "search_time_ms": 67
    }
  }
}
```

### 3. Advertisement Autocomplete

**GET** `/api/search/autocomplete/advertisements`

Get autocomplete suggestions for advertisement search.

#### Query Parameters
- `q` (string, required): Partial search query (minimum 2 characters)
- `limit` (int): Maximum suggestions (default: 10)
- `category_id` (int, optional): Filter by category

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "apar",
    "suggestions": [
      {
        "text": "apartment",
        "type": "keyword",
        "count": 1250,
        "category": "Real Estate"
      },
      {
        "text": "apartment tehran",
        "type": "phrase",
        "count": 450,
        "category": "Real Estate"
      },
      {
        "text": "apartment for rent",
        "type": "phrase",
        "count": 380,
        "category": "Real Estate"
      },
      {
        "text": "apartment furnished",
        "type": "keyword",
        "count": 220,
        "category": "Real Estate"
      }
    ],
    "categories": [
      {
        "id": 1,
        "name": "Real Estate",
        "count": 1250
      }
    ],
    "locations": [
      {
        "city": "Tehran",
        "province": "Tehran",
        "count": 450
      }
    ]
  }
}
```

### 4. Location Autocomplete

**GET** `/api/search/autocomplete/locations`

Get location suggestions for search queries.

#### Query Parameters
- `q` (string, required): Partial location name
- `type` (string): Location type ("cities", "provinces", "all")
- `limit` (int): Maximum suggestions (default: 10)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "teh",
    "suggestions": [
      {
        "id": 1,
        "name": "Tehran",
        "name_en": "Tehran",
        "type": "city",
        "province": "Tehran",
        "coordinates": {
          "latitude": 35.6892,
          "longitude": 51.3890
        },
        "advertisement_count": 1250,
        "match_score": 0.95
      },
      {
        "id": 1,
        "name": "Tehran Province",
        "name_en": "Tehran",
        "type": "province",
        "cities_count": 22,
        "advertisement_count": 1500,
        "match_score": 0.90
      }
    ]
  }
}
```

### 5. Search Suggestions

**GET** `/api/search/suggestions`

Get search suggestions based on popular queries and user behavior.

#### Query Parameters
- `q` (string, optional): Base query for related suggestions
- `type` (string): Suggestion type ("popular", "trending", "related")
- `limit` (int): Maximum suggestions (default: 10)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "popular_searches": [
      {
        "query": "apartment tehran",
        "count": 5000,
        "trend": "up"
      },
      {
        "query": "house for sale",
        "count": 3500,
        "trend": "stable"
      }
    ],
    "trending_searches": [
      {
        "query": "luxury villa",
        "count": 1200,
        "growth_rate": 25.5
      }
    ],
    "related_searches": [
      {
        "query": "apartment rent tehran",
        "relevance": 0.85
      }
    ]
  }
}
```

### 6. Search Analytics (Admin)

**GET** `/api/search/analytics/popular`

Get popular search terms and analytics data.

#### Query Parameters
- `period` (string): Time period ("day", "week", "month", "year")
- `limit` (int): Number of results (default: 50)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "period": "month",
    "popular_terms": [
      {
        "query": "apartment tehran",
        "count": 5000,
        "unique_users": 3200,
        "click_through_rate": 0.75,
        "conversion_rate": 0.12
      },
      {
        "query": "house for sale",
        "count": 3500,
        "unique_users": 2800,
        "click_through_rate": 0.68,
        "conversion_rate": 0.08
      }
    ],
    "search_volume": {
      "total_searches": 50000,
      "unique_searches": 15000,
      "average_per_day": 1613
    },
    "performance_metrics": {
      "average_response_time_ms": 45,
      "zero_results_rate": 0.05,
      "refinement_rate": 0.25
    }
  }
}
```

## 🔧 Search Features

### Full-Text Search
- **Multi-field search**: Title, description, tags, location
- **Fuzzy matching**: Handle typos and variations
- **Phrase matching**: Exact phrase searches with quotes
- **Boolean operators**: AND, OR, NOT support
- **Wildcard search**: Partial word matching

### Advanced Filtering
- **Faceted search**: Category, location, price range facets
- **Range filters**: Price, date, distance ranges
- **Multi-select filters**: Multiple categories/locations
- **Nested filters**: Complex filter combinations

### Geospatial Search
- **Location-based search**: Find items near coordinates
- **Radius search**: Configurable search radius
- **Distance sorting**: Sort by proximity
- **Bounding box**: Search within geographic bounds

### Search Personalization
- **User preferences**: Personalized results based on history
- **Location bias**: Boost results near user's location
- **Category preferences**: Boost preferred categories
- **Behavioral signals**: Click-through and engagement data

## 📊 Search Analytics

### Query Analytics
- **Search volume**: Track search frequency
- **Popular terms**: Most searched queries
- **Zero results**: Queries with no results
- **Query refinement**: Search modification patterns

### Performance Metrics
- **Response time**: Search performance monitoring
- **Click-through rate**: Result engagement
- **Conversion rate**: Search to action conversion
- **User satisfaction**: Search success metrics

### Search Insights
```json
{
  "insights": {
    "top_categories": [
      {"category": "Real Estate", "percentage": 65},
      {"category": "Vehicles", "percentage": 20}
    ],
    "search_patterns": {
      "mobile_searches": 0.75,
      "voice_searches": 0.15,
      "image_searches": 0.10
    },
    "user_behavior": {
      "average_query_length": 3.2,
      "refinement_rate": 0.25,
      "session_duration": 180
    }
  }
}
```

## 🚨 Error Responses

### Common Search Errors
```json
// Invalid search query
{
  "success": false,
  "error": {
    "code": "INVALID_QUERY",
    "message": "Search query must be at least 2 characters long"
  }
}

// Search service unavailable
{
  "success": false,
  "error": {
    "code": "SEARCH_UNAVAILABLE",
    "message": "Search service is temporarily unavailable"
  }
}

// Too many requests
{
  "success": false,
  "error": {
    "code": "RATE_LIMITED",
    "message": "Too many search requests. Please try again later."
  }
}
```

## 📱 Integration Examples

### JavaScript/React Search Component
```javascript
// Search with autocomplete
const SearchComponent = () => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [results, setResults] = useState([]);

  // Autocomplete
  const handleInputChange = async (value) => {
    setQuery(value);
    if (value.length >= 2) {
      const response = await fetch(
        `/api/search/autocomplete/advertisements?q=${value}`
      );
      const data = await response.json();
      setSuggestions(data.suggestions);
    }
  };

  // Search
  const handleSearch = async () => {
    const response = await fetch(
      `/api/search/advertisements?q=${query}&facets=true`
    );
    const data = await response.json();
    setResults(data.items);
  };

  return (
    <div>
      <input
        value={query}
        onChange={(e) => handleInputChange(e.target.value)}
        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
      />
      {/* Render suggestions and results */}
    </div>
  );
};
```

### Python Search Client
```python
import requests

class SearchClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def search_advertisements(self, query, filters=None):
        params = {'q': query}
        if filters:
            params.update(filters)
        
        response = requests.get(
            f'{self.base_url}/search/advertisements',
            params=params
        )
        return response.json()
    
    def autocomplete(self, query, type='advertisements'):
        response = requests.get(
            f'{self.base_url}/search/autocomplete/{type}',
            params={'q': query}
        )
        return response.json()

# Usage
client = SearchClient('http://localhost:8000/api')
results = client.search_advertisements('apartment tehran', {
    'price_min': 100000,
    'price_max': 500000
})
```

---

The Search & Autocomplete API provides powerful, fast, and intelligent search capabilities with comprehensive analytics and personalization features.
