# Admin API Documentation

## 🛡️ Overview

The Admin API provides comprehensive administrative functionality for managing users, advertisements, content moderation, and system analytics. All admin endpoints require admin-level authentication.

## 🔗 Base Endpoint

All admin endpoints are prefixed with `/api/admin`

## 🔐 Authentication

Admin endpoints require JWT authentication with admin privileges:

```http
Authorization: Bearer <admin_access_token>
```

## 📋 Endpoints Summary

### Dashboard & Analytics
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/dashboard/stats` | Get dashboard statistics |
| GET | `/analytics/users` | User analytics data |
| GET | `/analytics/advertisements` | Advertisement analytics |

### User Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/users` | List all users |
| GET | `/users/{id}` | Get user details |
| PUT | `/users/{id}` | Update user |
| DELETE | `/users/{id}` | Delete/disable user |
| POST | `/users/{id}/verify` | Verify user account |

### Advertisement Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/advertisements` | List all advertisements |
| GET | `/advertisements/{id}` | Get advertisement details |
| PUT | `/advertisements/{id}/status` | Update advertisement status |
| DELETE | `/advertisements/{id}` | Delete advertisement |

### Advertisement Edit Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/advertisement-edits` | List pending edits |
| GET | `/advertisement-edits/{id}` | Get edit details |
| POST | `/advertisement-edits/{id}/review` | Approve/reject edit |

### Content Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/blogs` | List all blog posts |
| POST | `/blogs` | Create blog post |
| PUT | `/blogs/{id}` | Update blog post |
| DELETE | `/blogs/{id}` | Delete blog post |

## 📖 Detailed Endpoints

### 1. Dashboard Statistics

**GET** `/api/admin/dashboard/stats`

Get comprehensive dashboard statistics for admin overview.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "users": {
      "total_users": 1000,
      "active_users": 950,
      "new_users_today": 15,
      "new_users_this_week": 87,
      "verified_users": 850
    },
    "advertisements": {
      "total_advertisements": 500,
      "pending_advertisements": 10,
      "approved_advertisements": 480,
      "rejected_advertisements": 10,
      "new_ads_today": 25,
      "new_ads_this_week": 120
    },
    "advertisement_edits": {
      "pending_advertisement_edits": 5,
      "approved_advertisement_edits": 20,
      "rejected_advertisement_edits": 3
    },
    "system": {
      "total_views": 15000,
      "total_searches": 5000,
      "active_sessions": 150
    }
  }
}
```

### 2. User Management

#### GET `/api/admin/users`

List all users with filtering and pagination.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `search` (string): Search by name, email, or phone
- `status` (string): Filter by status (active, inactive, banned)
- `verified` (boolean): Filter by verification status
- `sort_by` (string): Sort field (created_at, last_login, email)
- `sort_order` (string): Sort direction (asc, desc)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "phone_number": "+1234567890",
        "first_name": "John",
        "last_name": "Doe",
        "is_active": true,
        "is_verified": true,
        "is_staff": false,
        "date_joined": "2024-01-01T00:00:00Z",
        "last_login": "2024-01-02T00:00:00Z",
        "advertisement_count": 5,
        "total_views": 150
      }
    ],
    "metadata": {
      "total": 1000,
      "page": 1,
      "limit": 20,
      "pages": 50,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### PUT `/api/admin/users/{id}`

Update user information and status.

**Request Body:**
```json
{
  "is_active": true,
  "is_verified": true,
  "is_staff": false,
  "admin_notes": "User verified manually"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "is_active": true,
    "is_verified": true,
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "User updated successfully"
}
```

### 3. Advertisement Management

#### GET `/api/admin/advertisements`

List all advertisements with admin-specific information.

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status` (string): Filter by status (pending, approved, rejected)
- `user_id` (int): Filter by user
- `category_id` (int): Filter by category
- `search` (string): Search in title/description
- `date_from` (string): Filter from date (YYYY-MM-DD)
- `date_to` (string): Filter to date (YYYY-MM-DD)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Beautiful Apartment",
        "description": "Spacious apartment...",
        "status": 0,
        "user": {
          "id": 1,
          "email": "<EMAIL>",
          "full_name": "John Doe"
        },
        "category": {
          "id": 1,
          "name": "Real Estate"
        },
        "price": {
          "amount": 250000,
          "currency": "USD"
        },
        "statistics": {
          "views": 150,
          "favorites": 12,
          "contacts": 5
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "admin_notes": null
      }
    ],
    "metadata": {
      "total": 500,
      "page": 1,
      "limit": 20
    }
  }
}
```

#### PUT `/api/admin/advertisements/{id}/status`

Update advertisement status (approve, reject, etc.).

**Request Body:**
```json
{
  "status": 1,  // 0=pending, 1=approved, 2=rejected
  "admin_notes": "Advertisement approved after review"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "status": 1,
    "admin_notes": "Advertisement approved after review",
    "reviewed_at": "2024-01-01T00:00:00Z",
    "reviewed_by": "<EMAIL>"
  },
  "message": "Advertisement status updated successfully"
}
```

### 4. Advertisement Edit Management

#### GET `/api/admin/advertisement-edits`

Get list of pending advertisement edits for review.

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status_filter` (string): Filter by status (pending, approved, rejected)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 456,
        "original_advertisement_id": 123,
        "original_advertisement_title": "Original Title",
        "edit_status": 0,
        "title": "Updated Title",
        "description": "Updated Description",
        "changes_summary": {
          "title_changed": true,
          "description_changed": true,
          "price_changed": false,
          "location_changed": false
        },
        "user": {
          "id": 1,
          "email": "<EMAIL>",
          "full_name": "John Doe"
        },
        "admin_notes": null,
        "reviewed_at": null,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "metadata": {
      "total": 5,
      "page": 1,
      "limit": 10
    }
  }
}
```

#### POST `/api/admin/advertisement-edits/{id}/review`

Approve or reject an advertisement edit.

**Query Parameters:**
- `action` (string): "approve" or "reject"
- `admin_notes` (string, optional): Admin review notes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": 456,
    "status": 1,
    "action_taken": "approve",
    "admin_notes": "Changes look good",
    "reviewed_at": "2024-01-01T00:00:00Z",
    "reviewed_by": "<EMAIL>"
  },
  "message": "Advertisement edit approved and applied successfully"
}
```

### 5. Analytics Endpoints

#### GET `/api/admin/analytics/users`

Get detailed user analytics.

**Query Parameters:**
- `period` (string): "day", "week", "month", "year"
- `date_from` (string): Start date (YYYY-MM-DD)
- `date_to` (string): End date (YYYY-MM-DD)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_users": 1000,
      "new_users": 87,
      "active_users": 650,
      "growth_rate": 8.7
    },
    "daily_stats": [
      {
        "date": "2024-01-01",
        "new_users": 15,
        "active_users": 120,
        "registrations": 15,
        "logins": 89
      }
    ],
    "demographics": {
      "by_location": [
        {"province": "Tehran", "count": 450},
        {"province": "Isfahan", "count": 200}
      ],
      "by_verification": {
        "verified": 850,
        "unverified": 150
      }
    }
  }
}
```

## 🔒 Admin Permissions

### Permission Levels
- **Super Admin**: Full system access
- **Content Moderator**: Advertisement and content management
- **User Manager**: User management and support
- **Analytics Viewer**: Read-only analytics access

### Permission Checks
All admin endpoints verify:
1. Valid JWT token
2. User has admin privileges (`is_staff=True`)
3. Specific permission for the action
4. Rate limiting for admin actions

## 🚨 Error Responses

### Common Admin Errors
```json
// Insufficient permissions
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "You don't have permission to perform this action"
  }
}

// Resource not found
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found"
  }
}

// Invalid status transition
{
  "success": false,
  "error": {
    "code": "INVALID_STATUS_TRANSITION",
    "message": "Cannot change status from approved to pending"
  }
}
```

## 📊 Admin Dashboard Integration

### Real-time Updates
- WebSocket connections for real-time notifications
- Live statistics updates
- Instant notification of new pending items

### Bulk Operations
```json
// Bulk approve advertisements
POST /api/admin/advertisements/bulk-action
{
  "action": "approve",
  "advertisement_ids": [1, 2, 3, 4, 5],
  "admin_notes": "Bulk approval after review"
}

// Bulk user operations
POST /api/admin/users/bulk-action
{
  "action": "verify",
  "user_ids": [1, 2, 3],
  "admin_notes": "Manual verification"
}
```

## 🔍 Audit Trail

All admin actions are logged with:
- Admin user ID and email
- Action performed
- Target resource
- Timestamp
- IP address
- Additional context

Access audit logs via:
```
GET /api/admin/audit-logs?page=1&limit=50
```

---

The Admin API provides comprehensive tools for managing the Soodam platform with proper security, audit trails, and efficient bulk operations for administrative tasks.
