# Blog API Documentation

## 📝 Overview

The Blog API provides comprehensive content management functionality for articles, news, and blog posts. It supports rich content creation, media management, categorization, and public/admin content operations.

## 🔗 Base Endpoint

All blog endpoints are prefixed with `/api/blogs`

## 📋 Endpoints Summary

### Public Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List published blog posts | ❌ |
| GET | `/{id}` | Get blog post details | ❌ |
| GET | `/categories` | Get blog categories | ❌ |
| GET | `/tags` | Get popular tags | ❌ |
| GET | `/search` | Search blog posts | ❌ |

### Admin Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/` | Create new blog post | ✅ Admin |
| PUT | `/{id}` | Update blog post | ✅ Admin |
| DELETE | `/{id}` | Delete blog post | ✅ Admin |
| POST | `/{id}/publish` | Publish blog post | ✅ Admin |
| POST | `/{id}/unpublish` | Unpublish blog post | ✅ Admin |
| GET | `/admin/drafts` | Get draft posts | ✅ Admin |

## 📖 Detailed Endpoints

### 1. List Blog Posts

**GET** `/api/blogs`

Get a paginated list of published blog posts.

#### Query Parameters
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 10, max: 50)
- `category` (string): Filter by category slug
- `tag` (string): Filter by tag
- `search` (string): Search in title and content
- `sort_by` (string): Sort by ("created_at", "updated_at", "views")
- `sort_order` (string): Sort direction ("asc", "desc")
- `featured` (boolean): Show only featured posts

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Real Estate Market Trends in 2024",
        "slug": "real-estate-market-trends-2024",
        "excerpt": "Discover the latest trends shaping the real estate market...",
        "content": "Full article content here...",
        "featured_image": {
          "url": "https://example.com/blog/images/market-trends.jpg",
          "alt_text": "Real estate market chart",
          "caption": "Market trends visualization"
        },
        "author": {
          "id": 1,
          "name": "Admin User",
          "avatar": "https://example.com/avatars/admin.jpg",
          "bio": "Real estate expert and market analyst"
        },
        "category": {
          "id": 1,
          "name": "Market Analysis",
          "slug": "market-analysis",
          "color": "#3B82F6"
        },
        "tags": [
          {
            "id": 1,
            "name": "Real Estate",
            "slug": "real-estate"
          },
          {
            "id": 2,
            "name": "Market Trends",
            "slug": "market-trends"
          }
        ],
        "statistics": {
          "views": 1250,
          "likes": 45,
          "comments": 12,
          "shares": 8
        },
        "seo": {
          "meta_title": "Real Estate Market Trends 2024 - Soodam Blog",
          "meta_description": "Comprehensive analysis of real estate market trends...",
          "keywords": ["real estate", "market trends", "2024"]
        },
        "is_featured": true,
        "is_published": true,
        "published_at": "2024-01-01T00:00:00Z",
        "created_at": "2023-12-28T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "reading_time": 8
      }
    ],
    "metadata": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 2. Get Blog Post Details

**GET** `/api/blogs/{id}`

Get detailed information about a specific blog post.

#### Path Parameters
- `id` (int): Blog post ID

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Real Estate Market Trends in 2024",
    "slug": "real-estate-market-trends-2024",
    "content": "Full detailed article content with HTML formatting...",
    "excerpt": "Discover the latest trends...",
    "featured_image": {
      "url": "https://example.com/blog/images/market-trends.jpg",
      "alt_text": "Real estate market chart",
      "caption": "Market trends visualization"
    },
    "gallery": [
      {
        "url": "https://example.com/blog/gallery/image1.jpg",
        "alt_text": "Chart 1",
        "caption": "Price trends by region"
      }
    ],
    "author": {
      "id": 1,
      "name": "Admin User",
      "avatar": "https://example.com/avatars/admin.jpg",
      "bio": "Real estate expert and market analyst",
      "social_links": {
        "twitter": "@admin_user",
        "linkedin": "admin-user"
      }
    },
    "category": {
      "id": 1,
      "name": "Market Analysis",
      "slug": "market-analysis",
      "description": "In-depth market analysis and insights"
    },
    "tags": [...],
    "related_posts": [
      {
        "id": 2,
        "title": "Investment Strategies for 2024",
        "slug": "investment-strategies-2024",
        "featured_image": "https://example.com/blog/images/investment.jpg",
        "published_at": "2023-12-15T00:00:00Z"
      }
    ],
    "statistics": {
      "views": 1250,
      "unique_views": 980,
      "likes": 45,
      "comments": 12,
      "shares": 8,
      "average_reading_time": 8
    },
    "seo": {...},
    "is_featured": true,
    "is_published": true,
    "published_at": "2024-01-01T00:00:00Z",
    "created_at": "2023-12-28T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. Create Blog Post (Admin)

**POST** `/api/blogs`

Create a new blog post. Requires admin authentication.

#### Request Body
```json
{
  "title": "New Blog Post Title",
  "content": "Full blog post content with HTML formatting...",
  "excerpt": "Brief description of the blog post...",
  "category_id": 1,
  "tags": ["real-estate", "market-analysis", "2024"],
  "featured_image": {
    "url": "https://example.com/images/new-post.jpg",
    "alt_text": "Blog post featured image",
    "caption": "Image caption"
  },
  "gallery": [
    {
      "url": "https://example.com/images/gallery1.jpg",
      "alt_text": "Gallery image 1",
      "caption": "Gallery caption 1"
    }
  ],
  "seo": {
    "meta_title": "Custom meta title",
    "meta_description": "Custom meta description",
    "keywords": ["keyword1", "keyword2"]
  },
  "is_featured": false,
  "is_published": false,
  "publish_at": "2024-01-15T00:00:00Z"
}
```

#### Response (201 Created)
```json
{
  "success": true,
  "data": {
    "id": 5,
    "title": "New Blog Post Title",
    "slug": "new-blog-post-title",
    "status": "draft",
    "is_published": false,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "Blog post created successfully"
}
```

### 4. Update Blog Post (Admin)

**PUT** `/api/blogs/{id}`

Update an existing blog post. Requires admin authentication.

#### Path Parameters
- `id` (int): Blog post ID

#### Request Body
Same structure as create, all fields optional.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Updated Blog Post Title",
    "slug": "updated-blog-post-title",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Blog post updated successfully"
}
```

### 5. Publish/Unpublish Blog Post (Admin)

**POST** `/api/blogs/{id}/publish`
**POST** `/api/blogs/{id}/unpublish`

Publish or unpublish a blog post.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": 1,
    "is_published": true,
    "published_at": "2024-01-01T00:00:00Z"
  },
  "message": "Blog post published successfully"
}
```

### 6. Get Blog Categories

**GET** `/api/blogs/categories`

Get all blog categories with post counts.

#### Response (200 OK)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Market Analysis",
      "slug": "market-analysis",
      "description": "In-depth market analysis and insights",
      "color": "#3B82F6",
      "icon": "chart-line",
      "post_count": 15,
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Investment Tips",
      "slug": "investment-tips",
      "description": "Practical investment advice and strategies",
      "color": "#10B981",
      "icon": "money-bill",
      "post_count": 8,
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### 7. Search Blog Posts

**GET** `/api/blogs/search`

Search blog posts with advanced filtering.

#### Query Parameters
- `q` (string, required): Search query
- `category` (string): Filter by category slug
- `tags` (string): Comma-separated tags
- `date_from` (string): Start date (YYYY-MM-DD)
- `date_to` (string): End date (YYYY-MM-DD)
- `page` (int): Page number
- `limit` (int): Items per page

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "real estate trends",
    "items": [
      {
        "id": 1,
        "title": "Real Estate Market Trends in 2024",
        "excerpt": "Discover the latest trends...",
        "match_score": 0.95,
        "highlighted_content": "...latest <mark>real estate trends</mark>...",
        "published_at": "2024-01-01T00:00:00Z"
      }
    ],
    "metadata": {
      "total": 5,
      "page": 1,
      "limit": 10
    },
    "suggestions": [
      "real estate market",
      "property trends",
      "investment analysis"
    ]
  }
}
```

### 8. Get Draft Posts (Admin)

**GET** `/api/blogs/admin/drafts`

Get all draft blog posts for admin review.

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 3,
        "title": "Draft Blog Post",
        "status": "draft",
        "author": {
          "id": 1,
          "name": "Admin User"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "word_count": 1250,
        "completion_score": 85
      }
    ],
    "metadata": {
      "total": 3,
      "page": 1,
      "limit": 10
    }
  }
}
```

## 📊 Blog Statistics

### Analytics Endpoint
**GET** `/api/blogs/analytics` (Admin only)

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_posts": 50,
      "published_posts": 45,
      "draft_posts": 5,
      "total_views": 125000,
      "total_likes": 2500,
      "total_comments": 450
    },
    "popular_posts": [
      {
        "id": 1,
        "title": "Real Estate Market Trends",
        "views": 5000,
        "likes": 150
      }
    ],
    "categories_performance": [
      {
        "category": "Market Analysis",
        "posts": 15,
        "views": 45000,
        "engagement_rate": 8.5
      }
    ],
    "monthly_stats": [
      {
        "month": "2024-01",
        "posts_published": 8,
        "total_views": 15000,
        "new_subscribers": 120
      }
    ]
  }
}
```

## 🔍 SEO Features

### Automatic SEO Generation
- **Meta titles**: Auto-generated from post title
- **Meta descriptions**: Auto-generated from excerpt
- **Keywords**: Extracted from content and tags
- **Open Graph**: Social media sharing optimization
- **Schema markup**: Structured data for search engines

### URL Structure
- **Clean URLs**: `/blog/category/post-slug`
- **Canonical URLs**: Prevent duplicate content
- **Sitemap**: Automatic sitemap generation

## 📱 Rich Content Support

### Content Features
- **HTML Editor**: Rich text editing with formatting
- **Image Gallery**: Multiple images per post
- **Video Embeds**: YouTube, Vimeo integration
- **Code Blocks**: Syntax highlighting
- **Tables**: Responsive table support
- **Quotes**: Styled blockquotes

### Media Management
- **Image Optimization**: Automatic resizing and compression
- **CDN Integration**: Fast image delivery
- **Alt Text**: Accessibility support
- **Lazy Loading**: Performance optimization

## 🚨 Error Responses

### Common Blog API Errors
```json
// Post not found
{
  "success": false,
  "error": {
    "code": "POST_NOT_FOUND",
    "message": "Blog post with ID 999 does not exist"
  }
}

// Unauthorized access
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Admin access required to create blog posts"
  }
}

// Validation error
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid blog post data",
    "details": {
      "title": ["Title is required"],
      "content": ["Content must be at least 100 characters"]
    }
  }
}
```

## 📱 Integration Examples

### JavaScript/React
```javascript
// Get blog posts
const getBlogPosts = async (page = 1, category = null) => {
  const params = new URLSearchParams({ page });
  if (category) params.append('category', category);
  
  const response = await fetch(`/api/blogs?${params}`);
  return response.json();
};

// Create blog post (admin)
const createBlogPost = async (postData, token) => {
  const response = await fetch('/api/blogs', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(postData)
  });
  return response.json();
};
```

### Python/Requests
```python
import requests

# Search blog posts
response = requests.get('/api/blogs/search', params={
    'q': 'real estate trends',
    'category': 'market-analysis',
    'limit': 5
})
search_results = response.json()

# Get blog categories
response = requests.get('/api/blogs/categories')
categories = response.json()
```

---

The Blog API provides comprehensive content management with SEO optimization, rich media support, and detailed analytics for effective content marketing.
