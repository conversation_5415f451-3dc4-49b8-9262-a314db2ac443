# Geolocation API Documentation

## 🌍 Overview

The Geolocation API provides geographic data and location-based services for the Soodam platform. It includes provinces, cities, spatial queries, and location-based advertisement search functionality.

## 🔗 Base Endpoint

All geolocation endpoints are prefixed with `/api/geolocation`

## 📋 Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/get_provinces` | Get list of provinces | ❌ |
| GET | `/get_cites_by_id/{province_id}` | Get cities by province | ❌ |
| GET | `/provinces` | Get provinces (alternative) | ❌ |
| GET | `/cities` | Get cities with filtering | ❌ |
| GET | `/nearby` | Find nearby locations | ❌ |
| POST | `/distance` | Calculate distance between points | ❌ |
| GET | `/search` | Search locations by name | ❌ |

## 📖 Detailed Endpoints

### 1. Get Provinces

**GET** `/api/geolocation/get_provinces`

Get a list of all provinces/states in the system.

#### Response (200 OK)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "تهران",
      "name_en": "Tehran",
      "code": "TEH",
      "center_latitude": 35.6892,
      "center_longitude": 51.3890,
      "population": 13267637,
      "area_km2": 18814,
      "cities_count": 22
    },
    {
      "id": 2,
      "name": "اصفهان",
      "name_en": "Isfahan",
      "code": "ISF",
      "center_latitude": 32.6546,
      "center_longitude": 51.6680,
      "population": 5120850,
      "area_km2": 107027,
      "cities_count": 24
    }
  ]
}
```

### 2. Get Cities by Province

**GET** `/api/geolocation/get_cites_by_id/{province_id}`

Get all cities within a specific province.

#### Path Parameters
- `province_id` (int): Province ID

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "province": {
      "id": 1,
      "name": "تهران",
      "name_en": "Tehran"
    },
    "cities": [
      {
        "id": 1,
        "name": "تهران",
        "name_en": "Tehran",
        "province_id": 1,
        "latitude": 35.6892,
        "longitude": 51.3890,
        "population": 8693706,
        "is_capital": true,
        "postal_codes": ["11111", "12345"]
      },
      {
        "id": 2,
        "name": "کرج",
        "name_en": "Karaj",
        "province_id": 1,
        "latitude": 35.8327,
        "longitude": 50.9916,
        "population": 1967005,
        "is_capital": false,
        "postal_codes": ["31111", "31234"]
      }
    ]
  }
}
```

### 3. Get Cities with Filtering

**GET** `/api/geolocation/cities`

Get cities with advanced filtering options.

#### Query Parameters
- `province_id` (int, optional): Filter by province
- `search` (string, optional): Search by city name
- `min_population` (int, optional): Minimum population
- `is_capital` (boolean, optional): Filter capital cities
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 50)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "تهران",
        "name_en": "Tehran",
        "province": {
          "id": 1,
          "name": "تهران",
          "name_en": "Tehran"
        },
        "coordinates": {
          "latitude": 35.6892,
          "longitude": 51.3890
        },
        "population": 8693706,
        "is_capital": true,
        "advertisement_count": 1250
      }
    ],
    "metadata": {
      "total": 1000,
      "page": 1,
      "limit": 50,
      "pages": 20
    }
  }
}
```

### 4. Find Nearby Locations

**GET** `/api/geolocation/nearby`

Find cities or points of interest near a specific location.

#### Query Parameters
- `latitude` (float, required): Latitude coordinate
- `longitude` (float, required): Longitude coordinate
- `radius` (float): Search radius in kilometers (default: 10)
- `type` (string): Type of locations ("cities", "advertisements", "all")
- `limit` (int): Maximum results (default: 20)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "center": {
      "latitude": 35.6892,
      "longitude": 51.3890
    },
    "radius_km": 10,
    "results": [
      {
        "type": "city",
        "id": 1,
        "name": "تهران",
        "name_en": "Tehran",
        "coordinates": {
          "latitude": 35.6892,
          "longitude": 51.3890
        },
        "distance_km": 0.0,
        "population": 8693706
      },
      {
        "type": "city",
        "id": 15,
        "name": "ری",
        "name_en": "Rey",
        "coordinates": {
          "latitude": 35.6011,
          "longitude": 51.4339
        },
        "distance_km": 8.5,
        "population": 681000
      }
    ]
  }
}
```

### 5. Calculate Distance

**POST** `/api/geolocation/distance`

Calculate distance between two geographic points.

#### Request Body
```json
{
  "point1": {
    "latitude": 35.6892,
    "longitude": 51.3890
  },
  "point2": {
    "latitude": 32.6546,
    "longitude": 51.6680
  },
  "unit": "km"  // "km", "miles", "meters"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "distance": 340.25,
    "unit": "km",
    "point1": {
      "latitude": 35.6892,
      "longitude": 51.3890,
      "location_name": "Tehran"
    },
    "point2": {
      "latitude": 32.6546,
      "longitude": 51.6680,
      "location_name": "Isfahan"
    },
    "straight_line_distance": true
  }
}
```

### 6. Search Locations

**GET** `/api/geolocation/search`

Search for locations by name with autocomplete functionality.

#### Query Parameters
- `q` (string, required): Search query
- `type` (string): Search type ("provinces", "cities", "all")
- `limit` (int): Maximum results (default: 10)

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "query": "تهر",
    "results": [
      {
        "type": "province",
        "id": 1,
        "name": "تهران",
        "name_en": "Tehran",
        "match_score": 0.95,
        "cities_count": 22
      },
      {
        "type": "city",
        "id": 1,
        "name": "تهران",
        "name_en": "Tehran",
        "province": "تهران",
        "match_score": 0.98,
        "coordinates": {
          "latitude": 35.6892,
          "longitude": 51.3890
        }
      }
    ]
  }
}
```

## 🗺️ Geographic Data Structure

### Province Model
```json
{
  "id": 1,
  "name": "تهران",           // Persian name
  "name_en": "Tehran",       // English name
  "code": "TEH",             // Province code
  "center_latitude": 35.6892,
  "center_longitude": 51.3890,
  "population": 13267637,
  "area_km2": 18814,
  "cities_count": 22,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### City Model
```json
{
  "id": 1,
  "name": "تهران",
  "name_en": "Tehran",
  "province_id": 1,
  "latitude": 35.6892,
  "longitude": 51.3890,
  "population": 8693706,
  "is_capital": true,
  "postal_codes": ["11111", "12345"],
  "area_km2": 730,
  "elevation_m": 1200,
  "time_zone": "Asia/Tehran"
}
```

## 🎯 Location-Based Features

### Advertisement Integration
```json
// Get advertisements near a location
GET /api/advertisements?latitude=35.6892&longitude=51.3890&radius=5

// Response includes distance information
{
  "items": [
    {
      "id": 1,
      "title": "Apartment for Sale",
      "location": {
        "city": "Tehran",
        "address": "Valiasr Street",
        "coordinates": {
          "latitude": 35.6950,
          "longitude": 51.3900
        },
        "distance_km": 0.8
      }
    }
  ]
}
```

### Geospatial Queries
The API supports advanced geospatial queries using PostGIS:

- **Point-in-polygon**: Check if coordinates are within city boundaries
- **Distance calculations**: Accurate distance using spherical geometry
- **Bounding box queries**: Find locations within rectangular areas
- **Radius searches**: Find all locations within a circular area

## 📊 Performance Considerations

### Spatial Indexing
- All geographic data uses spatial indexes (GIST)
- Optimized for distance and proximity queries
- Efficient bounding box searches

### Caching Strategy
- Province and city data cached for 24 hours
- Search results cached for 1 hour
- Nearby queries cached based on location and radius

### Query Optimization
```sql
-- Example optimized spatial query
SELECT c.*, ST_Distance(
  ST_Point(c.longitude, c.latitude)::geography,
  ST_Point(51.3890, 35.6892)::geography
) / 1000 as distance_km
FROM app_citymodel c
WHERE ST_DWithin(
  ST_Point(c.longitude, c.latitude)::geography,
  ST_Point(51.3890, 35.6892)::geography,
  10000  -- 10km in meters
)
ORDER BY distance_km;
```

## 🌐 Coordinate Systems

### Supported Formats
- **WGS84 (EPSG:4326)**: Standard GPS coordinates
- **Web Mercator (EPSG:3857)**: Web mapping projection
- **UTM**: Universal Transverse Mercator (for specific regions)

### Coordinate Validation
```json
// Valid coordinate ranges
{
  "latitude": {
    "min": -90.0,
    "max": 90.0
  },
  "longitude": {
    "min": -180.0,
    "max": 180.0
  }
}
```

## 🚨 Error Responses

### Common Errors
```json
// Invalid coordinates
{
  "success": false,
  "error": {
    "code": "INVALID_COORDINATES",
    "message": "Latitude must be between -90 and 90 degrees"
  }
}

// Province not found
{
  "success": false,
  "error": {
    "code": "PROVINCE_NOT_FOUND",
    "message": "Province with ID 999 does not exist"
  }
}

// Invalid radius
{
  "success": false,
  "error": {
    "code": "INVALID_RADIUS",
    "message": "Radius must be between 0.1 and 1000 kilometers"
  }
}
```

## 📱 Usage Examples

### JavaScript/Fetch
```javascript
// Get provinces
const provinces = await fetch('/api/geolocation/get_provinces')
  .then(response => response.json());

// Get cities by province
const cities = await fetch('/api/geolocation/get_cites_by_id/1')
  .then(response => response.json());

// Find nearby locations
const nearby = await fetch(
  '/api/geolocation/nearby?latitude=35.6892&longitude=51.3890&radius=10'
).then(response => response.json());
```

### Python/Requests
```python
import requests

# Search locations
response = requests.get('/api/geolocation/search', params={
    'q': 'تهران',
    'type': 'cities',
    'limit': 5
})
locations = response.json()

# Calculate distance
distance_data = {
    'point1': {'latitude': 35.6892, 'longitude': 51.3890},
    'point2': {'latitude': 32.6546, 'longitude': 51.6680},
    'unit': 'km'
}
response = requests.post('/api/geolocation/distance', json=distance_data)
distance = response.json()
```

---

The Geolocation API provides comprehensive geographic functionality with optimized spatial queries and caching for high-performance location-based services.
