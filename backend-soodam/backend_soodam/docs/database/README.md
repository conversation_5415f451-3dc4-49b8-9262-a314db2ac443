# Database Schema Documentation

## 🗄️ Database Overview

The Soodam backend uses PostgreSQL with PostGIS extension as the primary database. The database is designed with a focus on scalability, data integrity, and performance optimization for real estate and classified advertisement management.

## 🏗️ Database Architecture

### Technology Stack
- **PostgreSQL 14+**: Primary relational database
- **PostGIS**: Spatial database extension for geographic data
- **Django ORM**: Object-relational mapping for database operations
- **Connection Pooling**: Optimized database connections

### Design Principles
- **Normalization**: Proper database normalization to reduce redundancy
- **Indexing**: Strategic indexing for query performance
- **Constraints**: Data integrity through foreign keys and constraints
- **Migrations**: Version-controlled schema changes

## 📊 Database Schema Overview

### Core Entity Relationships
```
┌─────────────────────────────────────────────────────────────┐
│                    Core Entities                            │
├─────────────────────────────────────────────────────────────┤
│  Users                                                      │
│  ├── CustomUserModel (Primary user data)                   │
│  ├── UserRoleModel (User roles and permissions)            │
│  └── SpecialAdmin (Admin-specific data)                    │
├─────────────────────────────────────────────────────────────┤
│  Advertisements                                             │
│  ├── AdvertisementModel (Main advertisement data)          │
│  ├── AdvertisementEditModel (Pending edits)                │
│  ├── AdvertisementImagesModel (Media files)                │
│  ├── AdvertisementPriceModel (Pricing information)         │
│  └── AdvertisementLocationModel (Geographic data)          │
├─────────────────────────────────────────────────────────────┤
│  Categories & Attributes                                    │
│  ├── MainCategoryModel (Top-level categories)              │
│  ├── SubCategoryModel (Sub-categories)                     │
│  ├── ChoiceAttributeModel (Selectable attributes)          │
│  ├── TextAttributeModel (Text-based attributes)            │
│  └── BooleanAttributeModel (Yes/No attributes)             │
├─────────────────────────────────────────────────────────────┤
│  Geographic Data                                            │
│  ├── ProvinceModel (Provinces/States)                      │
│  ├── CityModel (Cities)                                    │
│  └── AdvertisementLocationModel (Spatial data)             │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Table Summary

### User Management Tables
| Table Name | Purpose | Key Relationships |
|------------|---------|-------------------|
| `app_customusermodel` | Primary user data | → UserRoles, Advertisements |
| `app_userrolemodel` | User role assignments | → Users, Roles |
| `app_rolemodel` | Available roles | → Permissions |
| `app_permissionmodel` | System permissions | → Roles |
| `app_specialadmin` | Admin-specific data | → Users, Cities, Provinces |

### Advertisement Tables
| Table Name | Purpose | Key Relationships |
|------------|---------|-------------------|
| `app_advertisementmodel` | Main advertisement data | → Users, Categories, Location |
| `advertisement_edit` | Pending advertisement edits | → Advertisements, Users |
| `app_advertisementimagesmodel` | Advertisement images | → Advertisements |
| `app_advertisementvideomodel` | Advertisement videos | → Advertisements |
| `app_advertisementpricemodel` | Pricing information | → Advertisements |
| `app_advertisementlocationmodel` | Geographic data | → Advertisements, Cities |

### Category & Attribute Tables
| Table Name | Purpose | Key Relationships |
|------------|---------|-------------------|
| `app_maincategorymodel` | Top-level categories | → SubCategories |
| `app_subcategorymodel` | Sub-categories | → MainCategories, Advertisements |
| `app_choiceattributemodel` | Selectable attributes | → Categories |
| `app_choiceoptionmodel` | Attribute options | → ChoiceAttributes |
| `app_textattributemodel` | Text attributes | → Categories |
| `app_booleanattributemodel` | Boolean attributes | → Categories |

### Geographic Tables
| Table Name | Purpose | Key Relationships |
|------------|---------|-------------------|
| `app_provincemodel` | Provinces/States | → Cities, Locations |
| `app_citymodel` | Cities | → Provinces, Locations |
| `app_advertisementlocationmodel` | Spatial coordinates | → Advertisements, Cities |

## 🔑 Key Database Features

### Primary Keys
- All tables use auto-incrementing integer primary keys (`id`)
- UUIDs are used for external references where needed
- Composite keys for many-to-many relationships

### Foreign Key Relationships
- **Cascading Deletes**: Configured for dependent data
- **Soft Deletes**: Used for user-generated content
- **Referential Integrity**: Enforced through database constraints

### Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_advertisement_status ON app_advertisementmodel(status);
CREATE INDEX idx_advertisement_category ON app_advertisementmodel(sub_category_id);
CREATE INDEX idx_advertisement_user ON app_advertisementmodel(user_id);
CREATE INDEX idx_advertisement_created ON app_advertisementmodel(created_at);

-- Geospatial indexes
CREATE INDEX idx_location_geolocation ON app_advertisementlocationmodel USING GIST(geolocation);
CREATE INDEX idx_location_coordinates ON app_advertisementlocationmodel(longitude, latitude);

-- Search indexes
CREATE INDEX idx_advertisement_title ON app_advertisementmodel USING gin(to_tsvector('english', title));
CREATE INDEX idx_advertisement_description ON app_advertisementmodel USING gin(to_tsvector('english', description));
```

### Constraints
```sql
-- Unique constraints
ALTER TABLE app_customusermodel ADD CONSTRAINT unique_email UNIQUE(email);
ALTER TABLE app_customusermodel ADD CONSTRAINT unique_phone UNIQUE(phone_number);

-- Check constraints
ALTER TABLE app_advertisementpricemodel ADD CONSTRAINT positive_amount CHECK(amount >= 0);
ALTER TABLE app_advertisementmodel ADD CONSTRAINT valid_status CHECK(status IN (0, 1, 2, 3));

-- Custom constraints
ALTER TABLE advertisement_edit ADD CONSTRAINT unique_pending_edit_per_advertisement 
    UNIQUE(original_advertisement_id) WHERE edit_status = 0;
```

## 📈 Performance Optimizations

### Query Optimization
- **Select Related**: Efficient foreign key queries
- **Prefetch Related**: Optimized many-to-many queries
- **Database Functions**: Use of database-level functions
- **Query Profiling**: Built-in query performance monitoring

### Connection Management
- **Connection Pooling**: Efficient database connections
- **Read Replicas**: Separate read/write instances (production)
- **Connection Limits**: Configured connection limits

### Caching Strategy
- **Query Result Caching**: Cache expensive queries
- **Model Instance Caching**: Cache frequently accessed objects
- **Invalidation Strategy**: Smart cache invalidation

## 🔒 Data Security

### Sensitive Data Protection
- **Password Hashing**: Argon2 password hashing
- **Personal Data**: Encrypted storage for sensitive fields
- **Audit Trails**: Comprehensive logging of data changes

### Access Control
- **Row-Level Security**: User-specific data access
- **Column-Level Permissions**: Restricted field access
- **API-Level Authorization**: Multi-layer security

## 📊 Data Types

### Standard Types
- **Text Fields**: VARCHAR, TEXT for string data
- **Numeric Fields**: INTEGER, DECIMAL for numbers
- **Date/Time**: TIMESTAMP with timezone support
- **Boolean**: BOOLEAN for true/false values
- **JSON**: JSONB for flexible data structures

### Spatial Types (PostGIS)
- **Point**: Geographic coordinates
- **Polygon**: Area boundaries
- **LineString**: Route data
- **Geography**: Earth-based calculations

### Custom Types
- **Enums**: Status codes and categories
- **Arrays**: Multiple values in single field
- **Composite Types**: Complex data structures

## 🔄 Migration Strategy

### Migration Management
- **Version Control**: All migrations in version control
- **Rollback Support**: Safe rollback procedures
- **Data Migrations**: Separate data and schema migrations
- **Testing**: Migration testing in staging environment

### Migration Best Practices
- **Backward Compatibility**: Non-breaking changes when possible
- **Batch Processing**: Large data migrations in batches
- **Monitoring**: Migration performance monitoring
- **Documentation**: Comprehensive migration documentation

## 📋 Detailed Model Documentation

For detailed information about specific models and their fields, refer to:

- [**User Models**](./USER-MODELS.md) - User authentication and profile models
- [**Advertisement Models**](./ADVERTISEMENT-MODELS.md) - Advertisement and related models
- [**Admin Models**](./ADMIN-MODELS.md) - Administrative and permission models
- [**Category Models**](./CATEGORY-MODELS.md) - Category and attribute models
- [**Geographic Models**](./GEOGRAPHIC-MODELS.md) - Location and spatial models

## 🛠️ Database Maintenance

### Regular Maintenance Tasks
- **VACUUM**: Regular table maintenance
- **ANALYZE**: Statistics updates for query optimization
- **REINDEX**: Index maintenance and optimization
- **Backup**: Regular database backups

### Monitoring
- **Query Performance**: Slow query monitoring
- **Connection Monitoring**: Database connection tracking
- **Storage Monitoring**: Disk space and growth tracking
- **Index Usage**: Index effectiveness monitoring

## 📊 Database Statistics

### Current Schema Stats (Example)
- **Total Tables**: 45+
- **Total Indexes**: 120+
- **Foreign Key Constraints**: 80+
- **Check Constraints**: 25+
- **Unique Constraints**: 15+

### Performance Metrics
- **Average Query Time**: < 50ms
- **Connection Pool Size**: 20 connections
- **Cache Hit Ratio**: > 95%
- **Index Usage**: > 90%

---

This database documentation provides a comprehensive overview of the Soodam database schema. For specific implementation details and field definitions, refer to the individual model documentation files.
