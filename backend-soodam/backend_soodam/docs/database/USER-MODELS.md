# User Models Documentation

## 👤 Overview

This document provides detailed information about all user-related database models in the Soodam backend. These models handle user authentication, profiles, roles, permissions, and user-specific data.

## 📊 User Models Structure

### Model Relationships
```
CustomUserModel (Main User)
├── UserRoleModel (User Roles)
├── SpecialAdmin (Admin Data)
├── UserAddressModel (User Addresses)
├── UserPreferencesModel (User Settings)
├── UserActivityLogModel (Activity Tracking)
└── UserVerificationModel (Verification Data)
```

## 🔧 Core User Models

### 1. CustomUserModel

The main user model extending Django's AbstractUser with additional fields.

#### Table: `app_customusermodel`

#### Fields
```python
class CustomUserModel(AbstractUser):
    # Basic Information
    id = AutoField(primary_key=True)
    username = Char<PERSON><PERSON>(max_length=150, unique=True)  # Email or phone
    email = EmailField(unique=True, null=True, blank=True)
    phone_number = Char<PERSON>ield(max_length=20, unique=True, null=True, blank=True)
    
    # Personal Information
    first_name = Char<PERSON><PERSON>(max_length=30)
    last_name = Char<PERSON><PERSON>(max_length=30)
    full_name = Char<PERSON><PERSON>(max_length=100, blank=True)  # Computed field
    date_of_birth = DateField(null=True, blank=True)
    gender = CharField(max_length=10, choices=GENDER_CHOICES, null=True, blank=True)
    
    # Profile Information
    avatar = ImageField(upload_to='avatars/', null=True, blank=True)
    bio = TextField(max_length=500, blank=True)
    
    # Location
    province = ForeignKey('ProvinceModel', null=True, blank=True)
    city = ForeignKey('CityModel', null=True, blank=True)
    
    # Status Fields
    is_active = BooleanField(default=True)
    is_staff = BooleanField(default=False)
    is_superuser = BooleanField(default=False)
    is_verified = BooleanField(default=False)
    
    # Verification Status
    email_verified = BooleanField(default=False)
    phone_verified = BooleanField(default=False)
    identity_verified = BooleanField(default=False)
    
    # Timestamps
    date_joined = DateTimeField(auto_now_add=True)
    last_login = DateTimeField(null=True, blank=True)
    updated_at = DateTimeField(auto_now=True)
    
    # Preferences
    language = CharField(max_length=10, default='fa')
    timezone = CharField(max_length=50, default='Asia/Tehran')
    currency = CharField(max_length=3, default='IRR')
    
    # Privacy Settings
    profile_visibility = CharField(max_length=20, default='public')
    show_phone_number = BooleanField(default=False)
    show_email = BooleanField(default=False)
    allow_contact = BooleanField(default=True)
```

#### Indexes
```sql
CREATE INDEX idx_user_email ON app_customusermodel(email);
CREATE INDEX idx_user_phone ON app_customusermodel(phone_number);
CREATE INDEX idx_user_verified ON app_customusermodel(is_verified);
CREATE INDEX idx_user_active ON app_customusermodel(is_active);
CREATE INDEX idx_user_location ON app_customusermodel(province_id, city_id);
```

#### Constraints
```sql
ALTER TABLE app_customusermodel ADD CONSTRAINT unique_email UNIQUE(email);
ALTER TABLE app_customusermodel ADD CONSTRAINT unique_phone UNIQUE(phone_number);
ALTER TABLE app_customusermodel ADD CONSTRAINT check_gender 
    CHECK(gender IN ('male', 'female', 'other', 'prefer_not_to_say'));
```

### 2. UserRoleModel

Manages user roles and permissions within the system.

#### Table: `app_userrolemodel`

#### Fields
```python
class UserRoleModel(models.Model):
    id = AutoField(primary_key=True)
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='roles')
    role = ForeignKey('RoleModel', on_delete=CASCADE)
    
    # Assignment Details
    assigned_by = ForeignKey('CustomUserModel', on_delete=SET_NULL, null=True)
    assigned_at = DateTimeField(auto_now_add=True)
    expires_at = DateTimeField(null=True, blank=True)
    
    # Status
    is_active = BooleanField(default=True)
    notes = TextField(blank=True)
    
    class Meta:
        unique_together = ('user', 'role')
```

### 3. SpecialAdmin

Extended information for admin users.

#### Table: `app_specialadmin`

#### Fields
```python
class SpecialAdmin(models.Model):
    id = AutoField(primary_key=True)
    user = OneToOneField('CustomUserModel', on_delete=CASCADE, related_name='admin_profile')
    
    # Admin Details
    admin_level = CharField(max_length=20, choices=ADMIN_LEVEL_CHOICES)
    department = CharField(max_length=50, blank=True)
    employee_id = CharField(max_length=20, unique=True, null=True, blank=True)
    
    # Permissions
    can_approve_ads = BooleanField(default=False)
    can_manage_users = BooleanField(default=False)
    can_access_analytics = BooleanField(default=False)
    can_manage_content = BooleanField(default=False)
    
    # Location Access (for regional admins)
    accessible_provinces = ManyToManyField('ProvinceModel', blank=True)
    accessible_cities = ManyToManyField('CityModel', blank=True)
    
    # Admin Metadata
    hire_date = DateField(null=True, blank=True)
    supervisor = ForeignKey('self', on_delete=SET_NULL, null=True, blank=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
```

### 4. UserAddressModel

User's saved addresses for quick access.

#### Table: `app_useraddressmodel`

#### Fields
```python
class UserAddressModel(models.Model):
    id = AutoField(primary_key=True)
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='addresses')
    
    # Address Information
    title = CharField(max_length=50)  # e.g., "Home", "Office"
    province = ForeignKey('ProvinceModel', on_delete=CASCADE)
    city = ForeignKey('CityModel', on_delete=CASCADE)
    address = TextField()
    postal_code = CharField(max_length=20, blank=True)
    
    # Coordinates
    latitude = DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    
    # Status
    is_default = BooleanField(default=False)
    is_active = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'title')
```

### 5. UserPreferencesModel

Detailed user preferences and settings.

#### Table: `app_userpreferencesmodel`

#### Fields
```python
class UserPreferencesModel(models.Model):
    id = AutoField(primary_key=True)
    user = OneToOneField('CustomUserModel', on_delete=CASCADE, related_name='preferences')
    
    # Notification Preferences
    email_notifications = BooleanField(default=True)
    sms_notifications = BooleanField(default=False)
    push_notifications = BooleanField(default=True)
    marketing_emails = BooleanField(default=False)
    
    # Display Preferences
    theme = CharField(max_length=20, default='light')
    items_per_page = IntegerField(default=20)
    date_format = CharField(max_length=20, default='jalali')
    
    # Search Preferences
    default_search_radius = IntegerField(default=10)  # kilometers
    auto_detect_location = BooleanField(default=True)
    preferred_categories = JSONField(default=list)
    
    # Privacy Preferences
    profile_visibility = CharField(max_length=20, default='public')
    show_activity = BooleanField(default=True)
    allow_friend_requests = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
```

### 6. UserActivityLogModel

Tracks user activities for analytics and security.

#### Table: `app_useractivitylogmodel`

#### Fields
```python
class UserActivityLogModel(models.Model):
    id = AutoField(primary_key=True)
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='activity_logs')
    
    # Activity Information
    activity_type = CharField(max_length=50)
    description = TextField()
    metadata = JSONField(default=dict)
    
    # Request Information
    ip_address = GenericIPAddressField()
    user_agent = TextField(blank=True)
    referer = URLField(blank=True)
    
    # Location Information
    country = CharField(max_length=100, blank=True)
    city = CharField(max_length=100, blank=True)
    
    # Timestamp
    created_at = DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['activity_type', 'created_at']),
            models.Index(fields=['ip_address']),
        ]
```

### 7. UserVerificationModel

Handles user verification processes.

#### Table: `app_userverificationmodel`

#### Fields
```python
class UserVerificationModel(models.Model):
    id = AutoField(primary_key=True)
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='verifications')
    
    # Verification Type
    verification_type = CharField(max_length=20, choices=VERIFICATION_TYPE_CHOICES)
    
    # Verification Data
    verification_code = CharField(max_length=10)
    verification_token = CharField(max_length=255, unique=True)
    
    # Contact Information
    email = EmailField(null=True, blank=True)
    phone_number = CharField(max_length=20, null=True, blank=True)
    
    # Status
    is_verified = BooleanField(default=False)
    attempts = IntegerField(default=0)
    max_attempts = IntegerField(default=3)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    verified_at = DateTimeField(null=True, blank=True)
    expires_at = DateTimeField()
    
    class Meta:
        unique_together = ('user', 'verification_type')
```

## 🔐 Authentication Models

### 8. UserSessionModel

Tracks user sessions for security and analytics.

#### Table: `app_usersessionmodel`

#### Fields
```python
class UserSessionModel(models.Model):
    id = AutoField(primary_key=True)
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='sessions')
    
    # Session Information
    session_key = CharField(max_length=40, unique=True)
    session_data = TextField()
    
    # Device Information
    device_type = CharField(max_length=20)
    device_name = CharField(max_length=100, blank=True)
    browser = CharField(max_length=100, blank=True)
    os = CharField(max_length=100, blank=True)
    
    # Location
    ip_address = GenericIPAddressField()
    country = CharField(max_length=100, blank=True)
    city = CharField(max_length=100, blank=True)
    
    # Status
    is_active = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    last_activity = DateTimeField(auto_now=True)
    expires_at = DateTimeField()
```

## 📊 User Statistics Models

### 9. UserStatisticsModel

Aggregated user statistics for performance.

#### Table: `app_userstatisticsmodel`

#### Fields
```python
class UserStatisticsModel(models.Model):
    id = AutoField(primary_key=True)
    user = OneToOneField('CustomUserModel', on_delete=CASCADE, related_name='statistics')
    
    # Advertisement Statistics
    total_advertisements = IntegerField(default=0)
    active_advertisements = IntegerField(default=0)
    featured_advertisements = IntegerField(default=0)
    
    # Engagement Statistics
    total_views = IntegerField(default=0)
    total_favorites = IntegerField(default=0)
    total_contacts = IntegerField(default=0)
    profile_views = IntegerField(default=0)
    
    # Activity Statistics
    login_count = IntegerField(default=0)
    search_count = IntegerField(default=0)
    message_count = IntegerField(default=0)
    
    # Timestamps
    last_calculated = DateTimeField(auto_now=True)
    created_at = DateTimeField(auto_now_add=True)
```

## 🔍 Model Methods and Properties

### CustomUserModel Methods
```python
class CustomUserModel(AbstractUser):
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()
    
    def get_short_name(self):
        return self.first_name
    
    def is_admin(self):
        return hasattr(self, 'admin_profile')
    
    def has_role(self, role_name):
        return self.roles.filter(role__name=role_name, is_active=True).exists()
    
    def get_verification_status(self):
        return {
            'email_verified': self.email_verified,
            'phone_verified': self.phone_verified,
            'identity_verified': self.identity_verified,
            'fully_verified': all([
                self.email_verified,
                self.phone_verified,
                self.identity_verified
            ])
        }
    
    def update_statistics(self):
        # Update user statistics
        stats, created = UserStatisticsModel.objects.get_or_create(user=self)
        stats.total_advertisements = self.advertisements.count()
        stats.active_advertisements = self.advertisements.filter(status=1).count()
        stats.save()
```

## 📈 Performance Considerations

### Database Indexes
```sql
-- User lookup indexes
CREATE INDEX idx_user_email_active ON app_customusermodel(email, is_active);
CREATE INDEX idx_user_phone_active ON app_customusermodel(phone_number, is_active);
CREATE INDEX idx_user_verification ON app_customusermodel(is_verified, is_active);

-- Activity log indexes
CREATE INDEX idx_activity_user_date ON app_useractivitylogmodel(user_id, created_at DESC);
CREATE INDEX idx_activity_type_date ON app_useractivitylogmodel(activity_type, created_at DESC);

-- Session indexes
CREATE INDEX idx_session_user_active ON app_usersessionmodel(user_id, is_active);
CREATE INDEX idx_session_expires ON app_usersessionmodel(expires_at);
```

### Query Optimization
```python
# Efficient user queries with related data
users = CustomUserModel.objects.select_related(
    'province', 'city', 'admin_profile'
).prefetch_related(
    'roles__role', 'addresses', 'preferences'
)

# Optimized activity log queries
activities = UserActivityLogModel.objects.filter(
    user=user,
    created_at__gte=timezone.now() - timedelta(days=30)
).order_by('-created_at')[:50]
```

## 🔒 Security Considerations

### Data Protection
- **Password Hashing**: Using Argon2 for password storage
- **Personal Data**: Encrypted storage for sensitive fields
- **Access Logging**: All user data access is logged
- **Data Retention**: Configurable data retention policies

### Privacy Controls
- **Profile Visibility**: Users control who can see their profile
- **Contact Information**: Optional visibility of email/phone
- **Activity Privacy**: Users can control activity visibility
- **Data Export**: Users can export their data
- **Account Deletion**: Secure account deactivation process

---

This user model documentation provides comprehensive information about all user-related database structures, relationships, and implementation details in the Soodam backend system.
