# Advertisement Models Documentation

## 🏠 Overview

This document provides detailed information about all advertisement-related database models in the Soodam backend. These models handle advertisement creation, management, media, pricing, location data, and edit requests.

## 📊 Advertisement Models Structure

### Model Relationships
```
AdvertisementModel (Main Advertisement)
├── AdvertisementEditModel (Edit Requests)
├── AdvertisementImagesModel (Images)
├── AdvertisementVideoModel (Videos)
├── AdvertisementPriceModel (Pricing)
├── AdvertisementLocationModel (Location/GIS)
├── AdvertisementAttributeValueModel (Attributes)
├── AdvertisementStatisticsModel (Analytics)
└── AdvertisementFavoriteModel (User Favorites)
```

## 🔧 Core Advertisement Models

### 1. AdvertisementModel

The main advertisement model containing core advertisement data.

#### Table: `app_advertisementmodel`

#### Fields
```python
class AdvertisementModel(models.Model):
    # Primary Key
    id = AutoField(primary_key=True)
    
    # Basic Information
    title = CharField(max_length=200)
    description = TextField()
    slug = SlugField(max_length=250, unique=True, blank=True)
    
    # Relationships
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='advertisements')
    sub_category = ForeignKey('SubCategoryModel', on_delete=CASCADE)
    
    # Status and Visibility
    status = IntegerField(choices=STATUS_CHOICES, default=0)  # 0=pending, 1=approved, 2=rejected, 3=expired
    is_featured = BooleanField(default=False)
    is_urgent = BooleanField(default=False)
    is_active = BooleanField(default=True)
    
    # SEO and Metadata
    meta_title = CharField(max_length=200, blank=True)
    meta_description = TextField(max_length=500, blank=True)
    keywords = JSONField(default=list)
    
    # Contact Information
    contact_phone = CharField(max_length=20, blank=True)
    contact_email = EmailField(blank=True)
    contact_name = CharField(max_length=100, blank=True)
    
    # Moderation
    admin_notes = TextField(blank=True)
    rejection_reason = TextField(blank=True)
    reviewed_by = ForeignKey('CustomUserModel', on_delete=SET_NULL, null=True, blank=True, related_name='reviewed_ads')
    reviewed_at = DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    published_at = DateTimeField(null=True, blank=True)
    expires_at = DateTimeField(null=True, blank=True)
    
    # Analytics Fields
    view_count = IntegerField(default=0)
    favorite_count = IntegerField(default=0)
    contact_count = IntegerField(default=0)
    share_count = IntegerField(default=0)
    
    class Meta:
        indexes = [
            models.Index(fields=['status', 'is_active']),
            models.Index(fields=['sub_category', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_featured', 'status']),
        ]
```

#### Status Choices
```python
STATUS_CHOICES = [
    (0, 'Pending'),
    (1, 'Approved'),
    (2, 'Rejected'),
    (3, 'Expired'),
    (4, 'Deleted'),
]
```

### 2. AdvertisementEditModel

Handles edit requests for existing advertisements.

#### Table: `advertisement_edit`

#### Fields
```python
class AdvertisementEditModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    original_advertisement = ForeignKey('AdvertisementModel', on_delete=CASCADE, related_name='edit_requests')
    user = ForeignKey('CustomUserModel', on_delete=CASCADE)
    
    # Edit Status
    edit_status = IntegerField(choices=EDIT_STATUS_CHOICES, default=0)  # 0=pending, 1=approved, 2=rejected
    
    # Updated Fields (JSON format for flexibility)
    title = CharField(max_length=200, blank=True)
    description = TextField(blank=True)
    price_data = JSONField(null=True, blank=True)
    location_data = JSONField(null=True, blank=True)
    attribute_data = JSONField(null=True, blank=True)
    image_data = JSONField(null=True, blank=True)
    
    # Change Summary
    changed_fields = JSONField(default=list)
    change_summary = TextField(blank=True)
    
    # Admin Review
    admin_notes = TextField(blank=True)
    reviewed_by = ForeignKey('CustomUserModel', on_delete=SET_NULL, null=True, blank=True, related_name='reviewed_edits')
    reviewed_at = DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('original_advertisement', 'edit_status')
        constraints = [
            models.UniqueConstraint(
                fields=['original_advertisement'],
                condition=models.Q(edit_status=0),
                name='unique_pending_edit_per_advertisement'
            )
        ]
```

### 3. AdvertisementImagesModel

Manages advertisement images and media.

#### Table: `app_advertisementimagesmodel`

#### Fields
```python
class AdvertisementImagesModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = ForeignKey('AdvertisementModel', on_delete=CASCADE, related_name='images')
    
    # Image Information
    image = ImageField(upload_to='advertisements/images/')
    thumbnail = ImageField(upload_to='advertisements/thumbnails/', blank=True)
    
    # Image Metadata
    original_filename = CharField(max_length=255, blank=True)
    file_size = IntegerField(null=True, blank=True)  # in bytes
    width = IntegerField(null=True, blank=True)
    height = IntegerField(null=True, blank=True)
    format = CharField(max_length=10, blank=True)  # JPEG, PNG, etc.
    
    # Display Properties
    alt_text = CharField(max_length=200, blank=True)
    caption = TextField(blank=True)
    is_primary = BooleanField(default=False)
    display_order = IntegerField(default=0)
    
    # Status
    is_active = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['display_order', 'created_at']
        indexes = [
            models.Index(fields=['advertisement', 'is_active']),
            models.Index(fields=['is_primary']),
        ]
```

### 4. AdvertisementVideoModel

Manages advertisement videos.

#### Table: `app_advertisementvideomodel`

#### Fields
```python
class AdvertisementVideoModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = ForeignKey('AdvertisementModel', on_delete=CASCADE, related_name='videos')
    
    # Video Information
    video = FileField(upload_to='advertisements/videos/')
    thumbnail = ImageField(upload_to='advertisements/video_thumbnails/', blank=True)
    
    # Video Metadata
    original_filename = CharField(max_length=255, blank=True)
    file_size = IntegerField(null=True, blank=True)  # in bytes
    duration = IntegerField(null=True, blank=True)  # in seconds
    width = IntegerField(null=True, blank=True)
    height = IntegerField(null=True, blank=True)
    format = CharField(max_length=10, blank=True)  # MP4, MOV, etc.
    
    # Display Properties
    title = CharField(max_length=200, blank=True)
    description = TextField(blank=True)
    display_order = IntegerField(default=0)
    
    # Status
    is_active = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['display_order', 'created_at']
```

### 5. AdvertisementPriceModel

Handles advertisement pricing information.

#### Table: `app_advertisementpricemodel`

#### Fields
```python
class AdvertisementPriceModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = OneToOneField('AdvertisementModel', on_delete=CASCADE, related_name='price')
    
    # Price Information
    amount = DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    currency = CharField(max_length=3, default='IRR')
    is_negotiable = BooleanField(default=False)
    
    # Additional Price Fields (for real estate)
    deposit = DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    rent = DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    maintenance_fee = DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Price Type
    price_type = CharField(max_length=20, choices=PRICE_TYPE_CHOICES, default='sale')
    payment_terms = TextField(blank=True)
    
    # Price History (for tracking changes)
    price_history = JSONField(default=list)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
```

#### Price Type Choices
```python
PRICE_TYPE_CHOICES = [
    ('sale', 'For Sale'),
    ('rent', 'For Rent'),
    ('lease', 'For Lease'),
    ('exchange', 'For Exchange'),
    ('free', 'Free'),
    ('auction', 'Auction'),
]
```

### 6. AdvertisementLocationModel

Handles geographic and location data with PostGIS support.

#### Table: `app_advertisementlocationmodel`

#### Fields
```python
class AdvertisementLocationModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = OneToOneField('AdvertisementModel', on_delete=CASCADE, related_name='location')
    province = ForeignKey('ProvinceModel', on_delete=CASCADE)
    city = ForeignKey('CityModel', on_delete=CASCADE)
    
    # Address Information
    address = TextField()
    postal_code = CharField(max_length=20, blank=True)
    neighborhood = CharField(max_length=100, blank=True)
    
    # Geographic Coordinates
    latitude = DecimalField(max_digits=10, decimal_places=8)
    longitude = DecimalField(max_digits=11, decimal_places=8)
    geolocation = PointField(geography=True, null=True, blank=True)  # PostGIS Point
    
    # Location Metadata
    accuracy = IntegerField(null=True, blank=True)  # GPS accuracy in meters
    elevation = IntegerField(null=True, blank=True)  # elevation in meters
    
    # Visibility
    show_exact_location = BooleanField(default=False)
    location_verified = BooleanField(default=False)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['province', 'city']),
            models.Index(fields=['latitude', 'longitude']),
        ]
```

### 7. AdvertisementAttributeValueModel

Stores dynamic attribute values for advertisements.

#### Table: `app_advertisementattributevaluemodel`

#### Fields
```python
class AdvertisementAttributeValueModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = ForeignKey('AdvertisementModel', on_delete=CASCADE, related_name='attribute_values')
    
    # Attribute Reference
    attribute_id = IntegerField()  # References different attribute models
    attribute_type = CharField(max_length=20)  # 'choice', 'text', 'boolean', 'number'
    attribute_name = CharField(max_length=100)
    
    # Value Storage (flexible JSON field)
    value = JSONField()
    
    # Display Properties
    display_order = IntegerField(default=0)
    is_searchable = BooleanField(default=True)
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('advertisement', 'attribute_id', 'attribute_type')
        indexes = [
            models.Index(fields=['advertisement', 'attribute_type']),
            models.Index(fields=['attribute_id', 'attribute_type']),
        ]
```

### 8. AdvertisementStatisticsModel

Aggregated statistics for advertisements.

#### Table: `app_advertisementstatisticsmodel`

#### Fields
```python
class AdvertisementStatisticsModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    advertisement = OneToOneField('AdvertisementModel', on_delete=CASCADE, related_name='statistics')
    
    # View Statistics
    total_views = IntegerField(default=0)
    unique_views = IntegerField(default=0)
    daily_views = IntegerField(default=0)
    weekly_views = IntegerField(default=0)
    monthly_views = IntegerField(default=0)
    
    # Engagement Statistics
    total_favorites = IntegerField(default=0)
    total_contacts = IntegerField(default=0)
    total_shares = IntegerField(default=0)
    total_reports = IntegerField(default=0)
    
    # Performance Metrics
    click_through_rate = DecimalField(max_digits=5, decimal_places=4, default=0)
    conversion_rate = DecimalField(max_digits=5, decimal_places=4, default=0)
    engagement_score = DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Time-based Analytics
    average_view_duration = IntegerField(default=0)  # in seconds
    bounce_rate = DecimalField(max_digits=5, decimal_places=4, default=0)
    
    # Geographic Analytics
    top_viewer_cities = JSONField(default=list)
    viewer_demographics = JSONField(default=dict)
    
    # Device Analytics
    mobile_views = IntegerField(default=0)
    desktop_views = IntegerField(default=0)
    tablet_views = IntegerField(default=0)
    
    # Timestamps
    last_calculated = DateTimeField(auto_now=True)
    created_at = DateTimeField(auto_now_add=True)
```

### 9. AdvertisementFavoriteModel

Tracks user favorites for advertisements.

#### Table: `app_advertisementfavoritemodel`

#### Fields
```python
class AdvertisementFavoriteModel(models.Model):
    id = AutoField(primary_key=True)
    
    # Relationships
    user = ForeignKey('CustomUserModel', on_delete=CASCADE, related_name='favorites')
    advertisement = ForeignKey('AdvertisementModel', on_delete=CASCADE, related_name='favorited_by')
    
    # Metadata
    notes = TextField(blank=True)  # User's private notes
    
    # Timestamps
    created_at = DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('user', 'advertisement')
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['advertisement']),
        ]
```

## 🔍 Model Methods and Properties

### AdvertisementModel Methods
```python
class AdvertisementModel(models.Model):
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return f"/advertisements/{self.id}/{self.slug}/"
    
    def is_approved(self):
        return self.status == 1
    
    def is_expired(self):
        return self.expires_at and self.expires_at < timezone.now()
    
    def get_primary_image(self):
        return self.images.filter(is_primary=True, is_active=True).first()
    
    def get_price_display(self):
        if hasattr(self, 'price') and self.price.amount:
            return f"{self.price.amount:,.0f} {self.price.currency}"
        return "Price on request"
    
    def increment_view_count(self):
        self.view_count = F('view_count') + 1
        self.save(update_fields=['view_count'])
    
    def has_pending_edit(self):
        return self.edit_requests.filter(edit_status=0).exists()
```

## 📈 Performance Optimizations

### Database Indexes
```sql
-- Advertisement lookup indexes
CREATE INDEX idx_ad_status_active ON app_advertisementmodel(status, is_active);
CREATE INDEX idx_ad_category_status ON app_advertisementmodel(sub_category_id, status);
CREATE INDEX idx_ad_featured_status ON app_advertisementmodel(is_featured, status, created_at DESC);
CREATE INDEX idx_ad_location ON app_advertisementmodel(user_id, status);

-- Geographic indexes (PostGIS)
CREATE INDEX idx_ad_location_geom ON app_advertisementlocationmodel USING GIST(geolocation);
CREATE INDEX idx_ad_location_coords ON app_advertisementlocationmodel(latitude, longitude);

-- Search indexes
CREATE INDEX idx_ad_title_search ON app_advertisementmodel USING gin(to_tsvector('english', title));
CREATE INDEX idx_ad_desc_search ON app_advertisementmodel USING gin(to_tsvector('english', description));

-- Statistics indexes
CREATE INDEX idx_ad_stats_views ON app_advertisementstatisticsmodel(total_views DESC);
CREATE INDEX idx_ad_stats_engagement ON app_advertisementstatisticsmodel(engagement_score DESC);
```

### Query Optimization
```python
# Efficient advertisement queries
advertisements = AdvertisementModel.objects.select_related(
    'user', 'sub_category__main_category', 'price', 'location__city__province'
).prefetch_related(
    'images', 'attribute_values', 'statistics'
).filter(status=1, is_active=True)

# Optimized search with geographic filtering
from django.contrib.gis.measure import Distance
nearby_ads = AdvertisementModel.objects.filter(
    location__geolocation__distance_lte=(point, Distance(km=10)),
    status=1
).distance(point).order_by('distance')
```

## 🔒 Security and Validation

### Data Validation
```python
# Custom validators
def validate_price(value):
    if value < 0:
        raise ValidationError("Price cannot be negative")
    if value > 999999999999:
        raise ValidationError("Price is too high")

def validate_coordinates(latitude, longitude):
    if not (-90 <= latitude <= 90):
        raise ValidationError("Invalid latitude")
    if not (-180 <= longitude <= 180):
        raise ValidationError("Invalid longitude")
```

### Access Control
- **Owner Permissions**: Only owners can edit their advertisements
- **Admin Moderation**: Admins can approve/reject advertisements
- **Status Validation**: Proper status transitions enforced
- **Edit Requests**: Controlled edit process with approval workflow

---

This advertisement model documentation provides comprehensive information about all advertisement-related database structures, relationships, and implementation details in the Soodam backend system.
