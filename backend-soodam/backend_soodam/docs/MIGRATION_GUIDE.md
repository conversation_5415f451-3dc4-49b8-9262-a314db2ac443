# Migration Guide for Enhanced User Model

## Overview

This guide helps you migrate your existing user model to the enhanced version with proper handling of Jalali date fields and default values.

## 🚨 **Important: Backup Your Database First!**

```bash
# For PostgreSQL
pg_dump your_database_name > backup_before_migration.sql

# For MySQL
mysqldump -u username -p database_name > backup_before_migration.sql

# For SQLite
cp db.sqlite3 db_backup_before_migration.sqlite3
```

## 📋 **Step-by-Step Migration Process**

### **Step 1: Prepare for Migration**

Before running migrations, ensure you have the required packages:

```bash
# Install required packages if not already installed
pip install django-jalali
pip install jdatetime
```

### **Step 2: Create Migration Files**

```bash
python manage.py makemigrations
```

### **Step 3: Handle Default Value Prompts**

When Django prompts for default values, use these responses:

#### **For `created_at_jalali` field:**
```
You are trying to add a non-nullable field 'created_at_jalali' to customusermodel without a default; we can't do that (the database needs something to populate existing rows).
Please select a fix:
 1) Provide a one-off default now (will be set on all existing rows with a null value for this column)
 2) Quit, and let me add a default in models.py

Select an option: 2
```

Then add this to your model temporarily:
```python
created_at_jalali = jmodels.jDateTimeField(
    auto_now_add=True,
    default=jdatetime.datetime.now,  # Add this line
    verbose_name=_("تاریخ ایجاد (شمسی)")
)
```

#### **For `updated_at_jalali` field:**
```
Select an option: 2
```

Add default:
```python
updated_at_jalali = jmodels.jDateTimeField(
    auto_now=True,
    default=jdatetime.datetime.now,  # Add this line
    verbose_name=_("تاریخ آپدیت (شمسی)")
)
```

#### **For `verification_uuid` field:**
```
Select an option: 2
```

This field already has `default=uuid.uuid4`, so it should be fine.

#### **For `birthday` field (if changing from DateTime to Date):**
```
Select an option: 1
>>> jdatetime.date.today()
```

### **Step 4: Create Custom Migration (Recommended)**

Instead of dealing with prompts, create a custom migration:

```bash
python manage.py makemigrations --empty app
```

Then edit the migration file with this content:

```python
# app/migrations/XXXX_enhance_user_model.py

from django.db import migrations, models
import django.db.models.deletion
import django_jalali.db.models
import jdatetime
import uuid
from django.core.validators import RegexValidator


def create_user_wallets(apps, schema_editor):
    """Create wallet for existing users."""
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    UserWallet = apps.get_model('app', 'UserWallet')
    
    for user in CustomUserModel.objects.all():
        UserWallet.objects.get_or_create(
            user=user,
            defaults={
                'amount': 0,
                'is_active': True
            }
        )


def create_user_ratings(apps, schema_editor):
    """Create rating for existing users."""
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    UserRating = apps.get_model('app', 'UserRating')
    
    for user in CustomUserModel.objects.all():
        UserRating.objects.get_or_create(
            user=user,
            defaults={
                'positive_rating': 0.0,
                'negative_rating': 0.0,
                'total_reviews': 0,
                'average_rating': 0.0
            }
        )


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),  # Replace with your last migration
    ]

    operations = [
        # Add new fields with defaults
        migrations.AddField(
            model_name='customusermodel',
            name='verification_uuid',
            field=models.UUIDField(
                default=uuid.uuid4,
                unique=True,
                verbose_name='شناسه تایید'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='country_code',
            field=models.CharField(
                max_length=5,
                blank=True,
                default='IR',
                verbose_name='کد کشور'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='user_group',
            field=models.IntegerField(
                choices=[(1, 'سوپر ادمین'), (2, 'ادمین'), (3, 'کاربر اشتراکی'), (4, 'کاربر عادی')],
                default=4,
                verbose_name='گروه کاربری'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='is_admin',
            field=models.BooleanField(
                default=False,
                verbose_name='ادمین'
            ),
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='created_at_jalali',
            field=django_jalali.db.models.jDateTimeField(
                auto_now_add=True,
                default=jdatetime.datetime.now,
                verbose_name='تاریخ ایجاد (شمسی)'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customusermodel',
            name='updated_at_jalali',
            field=django_jalali.db.models.jDateTimeField(
                auto_now=True,
                verbose_name='تاریخ آپدیت (شمسی)'
            ),
        ),
        
        # Modify existing fields
        migrations.AlterField(
            model_name='customusermodel',
            name='phone_number',
            field=models.CharField(
                max_length=11,
                unique=True,
                db_index=True,
                validators=[RegexValidator(
                    regex='^09[0-9]{9}$',
                    message='شماره تلفن باید با 09 شروع شده و 11 رقم باشد',
                    code='invalid_phone'
                )],
                verbose_name='شماره تلفن'
            ),
        ),
        
        # Create new models
        migrations.CreateModel(
            name='UserWallet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=0, default=0, max_digits=15, verbose_name='موجودی')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('last_transaction_at', models.DateTimeField(blank=True, null=True, verbose_name='آخرین تراکنش')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('modified_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wallet', to='app.customusermodel', verbose_name='کاربر')),
            ],
            options={
                'verbose_name': 'کیف پول کاربر',
                'verbose_name_plural': 'کیف پول‌های کاربران',
                'db_table': 'custom_user_wallet',
            },
        ),
        
        migrations.CreateModel(
            name='UserRating',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('positive_rating', models.FloatField(default=0.0, verbose_name='امتیاز مثبت')),
                ('negative_rating', models.FloatField(default=0.0, verbose_name='امتیاز منفی')),
                ('total_reviews', models.PositiveIntegerField(default=0, verbose_name='تعداد نظرات')),
                ('average_rating', models.FloatField(default=0.0, verbose_name='میانگین امتیاز')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='app.customusermodel', verbose_name='کاربر')),
            ],
            options={
                'verbose_name': 'امتیاز کاربر',
                'verbose_name_plural': 'امتیازات کاربران',
                'db_table': 'custom_user_rating',
            },
        ),
        
        # Add indexes
        migrations.AddIndex(
            model_name='customusermodel',
            index=models.Index(fields=['phone_number'], name='user_phone_idx'),
        ),
        migrations.AddIndex(
            model_name='customusermodel',
            index=models.Index(fields=['email'], name='user_email_idx'),
        ),
        
        # Create wallets and ratings for existing users
        migrations.RunPython(
            code=create_user_wallets,
            reverse_code=migrations.RunPython.noop,
        ),
        migrations.RunPython(
            code=create_user_ratings,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
```

### **Step 5: Apply Migrations**

```bash
# Check migration plan
python manage.py showmigrations

# Apply migrations
python manage.py migrate

# If you encounter issues, you can fake the migration and fix manually
python manage.py migrate --fake-initial
```

## 🔧 **Common Issues and Solutions**

### **Issue 1: Jalali DateTime Import Error**

**Error:**
```
ImportError: cannot import name 'jdatetime' from 'jdatetime'
```

**Solution:**
```python
# In your migration file, use:
import jdatetime as jdt

# Then use:
default=jdt.datetime.now
```

### **Issue 2: Existing Data Conflicts**

**Error:**
```
IntegrityError: duplicate key value violates unique constraint
```

**Solution:**
```bash
# Check for duplicate phone numbers first
python manage.py shell
>>> from app.models import CustomUserModel
>>> duplicates = CustomUserModel.objects.values('phone_number').annotate(count=models.Count('phone_number')).filter(count__gt=1)
>>> print(duplicates)

# Clean up duplicates before migration
```

### **Issue 3: Phone Number Validation Fails**

**Error:**
```
ValidationError: شماره تلفن باید با 09 شروع شده و 11 رقم باشد
```

**Solution:**
Create a data migration to normalize phone numbers:

```python
def normalize_phone_numbers(apps, schema_editor):
    CustomUserModel = apps.get_model('app', 'CustomUserModel')
    
    for user in CustomUserModel.objects.all():
        if user.phone_number:
            phone = user.phone_number
            cleaned = ''.join(filter(str.isdigit, phone))
            
            if cleaned.startswith('98'):
                cleaned = '0' + cleaned[2:]
            elif cleaned.startswith('0098'):
                cleaned = '0' + cleaned[4:]
            elif not cleaned.startswith('0'):
                cleaned = '0' + cleaned
            
            if len(cleaned) == 11 and cleaned.startswith('09'):
                user.phone_number = cleaned
                user.save()
```

## ✅ **Post-Migration Verification**

After successful migration, verify everything works:

```python
# Test in Django shell
python manage.py shell

>>> from app.models import CustomUserModel, UserWallet, UserRating
>>> 
>>> # Check if all users have wallets and ratings
>>> users_without_wallet = CustomUserModel.objects.filter(wallet__isnull=True)
>>> print(f"Users without wallet: {users_without_wallet.count()}")
>>> 
>>> users_without_rating = CustomUserModel.objects.filter(rating__isnull=True)
>>> print(f"Users without rating: {users_without_rating.count()}")
>>> 
>>> # Test phone number validation
>>> user = CustomUserModel.objects.first()
>>> print(f"Phone: {user.phone_number}")
>>> print(f"Display name: {user.get_display_name()}")
>>> 
>>> # Test wallet operations
>>> if user.wallet:
>>>     print(f"Wallet balance: {user.wallet.get_formatted_amount()}")
```

## 🔄 **Rollback Plan**

If you need to rollback:

```bash
# Rollback to previous migration
python manage.py migrate app 0001  # Replace with your previous migration number

# Restore from backup
# For PostgreSQL:
psql your_database_name < backup_before_migration.sql

# For SQLite:
cp db_backup_before_migration.sqlite3 db.sqlite3
```

## 📝 **Notes**

1. **Always backup your database before migration**
2. **Test migrations on a copy of production data first**
3. **The migration may take time if you have many users**
4. **Monitor database performance during migration**
5. **Consider running migrations during low-traffic periods**

This guide ensures a smooth migration to your enhanced user model with proper handling of Jalali dates and default values.
