# Soodam Backup and Restore Guide

This document provides comprehensive instructions for backing up, restoring, and initializing data in the Soodam application for both development (DEV) and production (PROD) environments.

## Server Structure

The Soodam server is organized as follows:

```
soodam/
├── dev/
│   ├── app/         # DEV application files
│   └── backup/      # DEV backup storage
└── prod/
    ├── app/         # PROD application files
    └── backup/      # PROD backup storage
```

## Available Scripts

The following scripts are available for managing backups and data initialization:

### Backup Scripts

1. **DEV Daily Backup**: `scripts/dev_daily_backup.sh`
   - Creates database backups and exports model fixtures for the DEV environment
   - Stores backups in `/opt/soodam/dev/backup/`

2. **PROD Daily Backup**: `scripts/prod_daily_backup.sh`
   - Creates database backups and exports model fixtures for the PROD environment
   - Stores backups in `/opt/soodam/prod/backup/`

### Restore Scripts

3. **DEV Restore**: `scripts/dev_restore_backup.sh`
   - Restores the DEV database from a backup
   - Lists available backups

4. **PROD Restore**: `scripts/prod_restore_backup.sh`
   - Restores the PROD database from a backup
   - Lists available backups

### Export Scripts

5. **DEV Export Models**: `scripts/dev_export_models.sh`
   - Exports model data from the DEV environment as JSON fixtures

6. **PROD Export Models**: `scripts/prod_export_models.sh`
   - Exports model data from the PROD environment as JSON fixtures

### Initialize Scripts

7. **DEV Initialize Models**: `scripts/dev_initialize_models.sh`
   - Initializes models in the DEV environment from fixtures or detail folder

8. **PROD Initialize Models**: `scripts/prod_initialize_models.sh`
   - Initializes models in the PROD environment from fixtures or detail folder

## Setup Instructions

### 1. Make Scripts Executable

```bash
chmod +x backend_soodam/scripts/dev_daily_backup.sh
chmod +x backend_soodam/scripts/prod_daily_backup.sh
chmod +x backend_soodam/scripts/dev_restore_backup.sh
chmod +x backend_soodam/scripts/prod_restore_backup.sh
chmod +x backend_soodam/scripts/dev_export_models.sh
chmod +x backend_soodam/scripts/prod_export_models.sh
chmod +x backend_soodam/scripts/dev_initialize_models.sh
chmod +x backend_soodam/scripts/prod_initialize_models.sh
```

### 2. Set Up Cron Jobs for Daily Backups

Add the following lines to your crontab (`crontab -e`):

```bash
# Run DEV backup at 2 AM every day
0 2 * * * /path/to/backend_soodam/scripts/dev_daily_backup.sh >> /var/log/soodam_dev_backup.log 2>&1

# Run PROD backup at 3 AM every day
0 3 * * * /path/to/backend_soodam/scripts/prod_daily_backup.sh >> /var/log/soodam_prod_backup.log 2>&1
```

## Usage Guide

### Backup Operations

#### Running Manual Backups

For DEV environment:
```bash
./scripts/dev_daily_backup.sh
```

For PROD environment:
```bash
./scripts/prod_daily_backup.sh
```

Each backup includes:
- A PostgreSQL database dump (`.backup` format)
- JSON fixtures for key models
- Automatic cleanup of backups older than 7 days

### Restore Operations

#### Listing Available Backups

For DEV environment:
```bash
./scripts/dev_restore_backup.sh --list
```

For PROD environment:
```bash
./scripts/prod_restore_backup.sh --list
```

#### Restoring from a Backup

For DEV environment:
```bash
./scripts/dev_restore_backup.sh --date YYYY-MM-DD
```

For PROD environment:
```bash
./scripts/prod_restore_backup.sh --date YYYY-MM-DD
```

### Export Operations

#### Exporting Model Data

For DEV environment:
```bash
./scripts/dev_export_models.sh
```

For PROD environment:
```bash
./scripts/prod_export_models.sh
```

This will export the following models as JSON fixtures:
- MainCategoryModel
- SubCategoryModel
- SubCategoryLevelTwoModel
- ChoiceAttributeModel
- ChoiceOptionModel
- BooleanAttributeModel
- TextAttributeModel
- PropertyModel
- HighlightAttributeModel
- ProvinceModel
- CityModel

### Initialize Operations

#### Initializing Models from Fixtures

For DEV environment:
```bash
# Initialize from latest fixtures in backup
./scripts/dev_initialize_models.sh --source fixtures

# Initialize from specific fixtures directory
./scripts/dev_initialize_models.sh --source fixtures --fixtures-dir /path/to/fixtures

# Skip models that already have data
./scripts/dev_initialize_models.sh --source fixtures --skip-existing
```

For PROD environment:
```bash
# Initialize from latest fixtures in backup
./scripts/prod_initialize_models.sh --source fixtures

# Initialize from specific fixtures directory
./scripts/prod_initialize_models.sh --source fixtures --fixtures-dir /path/to/fixtures

# Skip models that already have data
./scripts/prod_initialize_models.sh --source fixtures --skip-existing
```

#### Initializing Models from Detail Folder

For DEV environment:
```bash
./scripts/dev_initialize_models.sh --source detail --detail-dir app/detail
```

For PROD environment:
```bash
./scripts/prod_initialize_models.sh --source detail --detail-dir app/detail
```

## Django Management Commands

The scripts use the following Django management commands:

### export_models_json

Exports specific models with relationships as JSON files.

```bash
python manage.py export_models_json --output-dir=fixtures
```

### initialize_models

Initializes models with data from fixtures or detail folder.

```bash
python manage.py initialize_models --source=fixtures --fixtures-dir=/path/to/fixtures
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure scripts are executable: `chmod +x script_name.sh`

2. **Backup Directory Not Found**
   - Create the directory manually: `mkdir -p /opt/soodam/dev/backup`

3. **Docker Container Not Found**
   - Ensure container names match in scripts (e.g., `dev_backend_soodam_postgres`)
   - Check if containers are running: `docker ps`

4. **Database Connection Failed**
   - Check database credentials in Docker environment variables
   - Verify PostgreSQL service is running in the container

5. **Model Not Found**
   - Ensure model names in scripts match your actual models
   - Check for typos in model names

### Logs

- Backup logs are stored in `/var/log/soodam_dev_backup.log` and `/var/log/soodam_prod_backup.log`
- Check these logs for detailed error messages

## Data Migration Between Environments

To migrate data from DEV to PROD:

1. Create a backup of the DEV environment:
   ```bash
   ./scripts/dev_daily_backup.sh
   ```

2. Copy the fixtures to the PROD backup directory:
   ```bash
   cp -r /opt/soodam/dev/backup/fixtures_YYYY-MM-DD /opt/soodam/prod/backup/
   ```

3. Initialize the PROD environment with the DEV fixtures:
   ```bash
   ./scripts/prod_initialize_models.sh --source fixtures --fixtures-dir /opt/soodam/prod/backup/fixtures_YYYY-MM-DD
   ```

## Best Practices

1. **Regular Backups**: Ensure cron jobs are running correctly for daily backups
2. **Test Restores**: Periodically test the restore process to verify backup integrity
3. **Version Control**: Keep backup scripts in version control with your application code
4. **Monitoring**: Set up alerts for backup failures
5. **Documentation**: Keep this guide updated with any changes to the backup process

## Security Considerations

1. **Backup Encryption**: Consider encrypting sensitive backup data
2. **Access Control**: Restrict access to backup directories
3. **Secure Transfer**: Use secure methods when transferring backups between environments
4. **Credentials**: Do not hardcode database credentials in scripts

## Maintenance

- Review and update the list of models to export as your application evolves
- Adjust retention periods based on your storage capacity and compliance requirements
- Periodically check backup sizes and optimize if necessary