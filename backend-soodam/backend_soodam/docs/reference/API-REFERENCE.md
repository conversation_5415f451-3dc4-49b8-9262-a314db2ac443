# Complete API Reference

## 📚 Overview

This document provides a comprehensive reference for all Soodam API endpoints, including request/response formats, authentication requirements, and error codes.

## 🔗 Base URLs

- **Production**: `https://api.soodam.com`
- **Staging**: `https://staging-api.soodam.com`
- **Development**: `http://localhost:8000`

## 🔐 Authentication

### JWT Token Authentication
```http
Authorization: Bearer <access_token>
```

### Authentication Endpoints

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "phone_number": "+**********",
  "password": "secure_password",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response (201):**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "phone_number": "+**********",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "is_verified": false,
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### POST /api/auth/login
Authenticate user and get tokens.

**Request Body:**
```json
{
  "username": "<EMAIL>",  // Email or phone number
  "password": "secure_password"
}
```

**Response (200):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 900,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

#### POST /api/auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 900
}
```

## 👤 User Management

#### GET /api/user/profile
Get current user profile. **Requires authentication.**

**Response (200):**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "phone_number": "+**********",
  "first_name": "John",
  "last_name": "Doe",
  "avatar": "https://example.com/avatar.jpg",
  "is_verified": true,
  "date_joined": "2024-01-01T00:00:00Z",
  "last_login": "2024-01-02T00:00:00Z"
}
```

#### PUT /api/user/profile
Update user profile. **Requires authentication.**

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

## 🏠 Advertisements

#### GET /api/advertisements
List advertisements with filtering and pagination.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 10, max: 100)
- `search` (string): Search query
- `category_id` (int): Filter by category
- `price_min` (float): Minimum price
- `price_max` (float): Maximum price
- `sort_by` (string): Sort field (default: "created_at")
- `sort_order` (string): "asc" or "desc" (default: "desc")

**Response (200):**
```json
{
  "items": [
    {
      "id": 1,
      "title": "Beautiful Apartment",
      "description": "Spacious 3-bedroom apartment...",
      "price": {
        "amount": 250000,
        "currency": "USD",
        "is_negotiable": true
      },
      "category": {
        "id": 1,
        "name": "Real Estate",
        "key": "real_estate"
      },
      "location": {
        "city": "Tehran",
        "province": "Tehran"
      },
      "images": [
        {
          "id": 1,
          "url": "https://example.com/image1.jpg",
          "is_primary": true
        }
      ],
      "user": {
        "id": 1,
        "username": "john_doe",
        "full_name": "John Doe"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10,
  "pages": 10,
  "has_next": true,
  "has_prev": false
}
```

#### POST /api/advertisements
Create new advertisement. **Requires authentication.**

**Request Body:**
```json
{
  "title": "Beautiful Apartment",
  "description": "Spacious 3-bedroom apartment in downtown area...",
  "category_id": 1,
  "price": {
    "amount": 250000,
    "currency": "USD",
    "is_negotiable": true
  },
  "full_address": {
    "province_id": 1,
    "city_id": 1,
    "address": "123 Main Street",
    "zip_code": "12345",
    "longitude": 51.3890,
    "latitude": 35.6892
  },
  "attributes": [
    {
      "id": 1,
      "type": "choice",
      "value": {"id": 3, "value": "3"}
    }
  ],
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "is_primary": true,
      "alt_text": "Living room"
    }
  ]
}
```

**Response (201):**
```json
{
  "id": 1,
  "title": "Beautiful Apartment",
  "status": 0,
  "message": "Advertisement created successfully and is pending approval",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### GET /api/advertisements/{id}
Get advertisement details by ID.

**Response (200):**
```json
{
  "id": 1,
  "title": "Beautiful Apartment",
  "description": "Spacious 3-bedroom apartment...",
  "price": {
    "amount": 250000,
    "currency": "USD",
    "is_negotiable": true
  },
  "category": {
    "id": 1,
    "name": "Real Estate",
    "key": "real_estate"
  },
  "full_address": {
    "province": "Tehran",
    "city": "Tehran",
    "address": "123 Main Street",
    "coordinates": {
      "longitude": 51.3890,
      "latitude": 35.6892
    }
  },
  "attributes": [...],
  "images": [...],
  "videos": [...],
  "user": {...},
  "statistics": {
    "views": 150,
    "favorites": 12
  },
  "has_pending_edit": false,
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### PUT /api/advertisements/{id}
Update advertisement (creates edit request). **Requires authentication.**

**Request Body:** Same as POST, all fields optional.

**Response (200):**
```json
{
  "id": 1,
  "title": "Original Title",
  "has_pending_edit": true,
  "message": "Edit request created successfully",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### DELETE /api/advertisements/{id}
Delete advertisement. **Requires authentication.**

**Response (204):** No content

## 🛡️ Admin Endpoints

#### GET /api/admin/dashboard/stats
Get dashboard statistics. **Requires admin authentication.**

**Response (200):**
```json
{
  "total_users": 1000,
  "active_users": 950,
  "total_advertisements": 500,
  "pending_advertisements": 10,
  "approved_advertisements": 480,
  "rejected_advertisements": 10,
  "pending_advertisement_edits": 5,
  "approved_advertisement_edits": 20,
  "rejected_advertisement_edits": 3
}
```

#### GET /api/admin/advertisement-edits
Get pending advertisement edits. **Requires admin authentication.**

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `status_filter` (string): "pending", "approved", "rejected"

**Response (200):**
```json
{
  "items": [
    {
      "id": 456,
      "original_advertisement_id": 123,
      "edit_status": 0,
      "title": "Updated Title",
      "description": "Updated Description",
      "admin_notes": null,
      "reviewed_at": null,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "metadata": {
    "page": 1,
    "limit": 10,
    "total_count": 1,
    "total_pages": 1
  }
}
```

#### POST /api/admin/advertisement-edits/{id}/review
Approve or reject advertisement edit. **Requires admin authentication.**

**Query Parameters:**
- `action` (string): "approve" or "reject"
- `admin_notes` (string, optional): Admin notes

**Response (200):**
```json
{
  "id": 456,
  "status": 1,
  "message": "Advertisement edit approved and applied successfully"
}
```

## 🌍 Geolocation

#### GET /api/geolocation/provinces
Get list of provinces.

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "Tehran",
    "code": "TEH"
  }
]
```

#### GET /api/geolocation/cities
Get list of cities.

**Query Parameters:**
- `province_id` (int, optional): Filter by province

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "Tehran",
    "province_id": 1,
    "province_name": "Tehran"
  }
]
```

## 📝 Blog

#### GET /api/blogs
Get list of blog posts.

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `search` (string): Search query

**Response (200):**
```json
{
  "items": [
    {
      "id": 1,
      "title": "Real Estate Market Trends",
      "slug": "real-estate-market-trends",
      "excerpt": "Latest trends in the real estate market...",
      "featured_image": "https://example.com/blog-image.jpg",
      "author": {
        "id": 1,
        "name": "Admin User"
      },
      "published_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 10,
  "page": 1,
  "limit": 10
}
```

## 🚦 HTTP Status Codes

### Success Codes
- `200 OK`: Request successful
- `201 Created`: Resource created
- `204 No Content`: Request successful, no content

### Client Error Codes
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Validation error
- `429 Too Many Requests`: Rate limit exceeded

### Server Error Codes
- `500 Internal Server Error`: Server error
- `502 Bad Gateway`: Upstream server error
- `503 Service Unavailable`: Service unavailable

## 🚨 Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field_name": ["Field specific error message"]
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 📊 Rate Limiting

### Rate Limits
- **Anonymous**: 100 requests/hour
- **Authenticated**: 1000 requests/hour
- **Admin**: 5000 requests/hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 🔍 Search & Filtering

### Search Parameters
- `search`: Full-text search across title and description
- `category_id`: Filter by category ID
- `location`: Filter by city or province name
- `price_min`/`price_max`: Price range filtering
- `sort_by`: Sort field (created_at, price, title)
- `sort_order`: asc or desc

### Pagination
All list endpoints support pagination:
- `page`: Page number (1-based)
- `limit`: Items per page (max 100)

Response includes pagination metadata:
```json
{
  "total": 100,
  "page": 1,
  "limit": 10,
  "pages": 10,
  "has_next": true,
  "has_prev": false
}
```

---

This API reference provides comprehensive documentation for all Soodam API endpoints. For interactive testing, visit the [Swagger UI](http://localhost:8000/api/docs) in your development environment.
