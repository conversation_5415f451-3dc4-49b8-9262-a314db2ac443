# Advertisement Edit System with Admin Approval

## Overview

This document describes the implementation of an advertisement edit system that allows users to edit their advertisements while keeping the original version visible to other users until admin approval.

## Problem Statement

When users update their advertisements, we want to:
1. Keep the original advertisement visible to other users
2. Allow the user to submit edit requests
3. Require admin approval before changes are applied
4. Maintain a history of edit requests

## Solution Architecture

### 1. Database Models

#### AdvertisementEditModel
- Stores pending advertisement edits
- References the original advertisement
- Contains all editable fields from AdvertisementModel
- Tracks edit status (PENDING, APPROVED, REJECTED)
- Includes admin review fields

#### Related Edit Models
- `AdvertisementEditTextValueModel`: Text attribute values for edits
- `AdvertisementEditBooleanValueModel`: Boolean attribute values for edits
- `AdvertisementEditPriceModel`: Price information for edits
- `AdvertisementEditLocationModel`: Location information for edits

### 2. Workflow

#### User Edit Request
1. User calls `update_advertisement` API
2. System checks for existing pending edits (only one pending edit per advertisement)
3. Creates `AdvertisementEditModel` record with new values
4. Original advertisement remains unchanged and visible
5. Returns original advertisement with `has_pending_edit: true`

#### Admin Review Process
1. Admin views pending edits via `get_pending_edits` API
2. Admin can see detailed comparison via `get_edit_detail` API
3. Admin approves or rejects via `approve_or_reject_edit` API
4. If approved: `apply_to_original()` method updates the original advertisement
5. If rejected: Edit status is marked as REJECTED

#### User Experience
- Users can view their edit requests via `get_user_edit_requests` API
- Original advertisement shows `has_pending_edit: true` when there's a pending edit
- Other users continue to see the original advertisement until changes are approved

### 3. API Endpoints

#### User Endpoints
- `PUT /api/v1/advertisement/{id}` - Create edit request (modified)
- `GET /api/v1/advertisement/{id}` - Get advertisement (includes has_pending_edit flag)
- `GET /api/v1/advertisement/my-edit-requests` - Get user's edit requests

#### Admin Endpoints (Integrated into existing AdminAPI)
- `GET /api/v1/admin/advertisement-edits` - List pending edits
- `GET /api/v1/admin/advertisement-edits/{edit_id}` - Get edit details
- `POST /api/v1/admin/advertisement-edits/{edit_id}/review` - Approve/reject edit

### 4. Database Schema Changes

```sql
-- Main edit table
CREATE TABLE advertisement_edit (
    id BIGINT PRIMARY KEY,
    original_advertisement_id BIGINT REFERENCES advertisement(id),
    edit_status INTEGER DEFAULT 0, -- 0=PENDING, 1=APPROVED, 2=REJECTED
    title VARCHAR(200),
    description TEXT,
    security_code_owner_building VARCHAR(11),
    phone_number_owner_building VARCHAR(11),
    main_category_id BIGINT,
    sub_category_id BIGINT,
    sub_category_level_two_id BIGINT,
    admin_notes TEXT,
    reviewed_by_id BIGINT,
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT unique_pending_edit_per_advertisement 
        UNIQUE (original_advertisement_id) 
        WHERE edit_status = 0
);

-- Related tables for attributes, price, location
CREATE TABLE advertisement_edit_text_value (...);
CREATE TABLE advertisement_edit_boolean_value (...);
CREATE TABLE advertisement_edit_price (...);
CREATE TABLE advertisement_edit_location (...);
```

### 5. Key Features

#### Constraint Enforcement
- Only one pending edit per advertisement (database constraint)
- User authorization checks (only owner can edit)
- Admin authorization checks (only admins can approve/reject)

#### Data Integrity
- All edit data is stored separately from original
- Original advertisement remains untouched until approval
- Complete audit trail of all edit requests

#### Performance Considerations
- Indexes on frequently queried fields
- Efficient queries with proper select_related/prefetch_related
- Pagination for admin edit lists

### 6. Usage Examples

#### User Editing Advertisement
```python
# User submits edit request
response = await update_advertisement(
    advertisement_id=123,
    schema=AdvertisementUpdateSchemaV2(
        title="New Title",
        description="New Description",
        price=PriceSchema(amount=1000)
    ),
    current_user=user
)
# Returns original advertisement with has_pending_edit=True
```

#### Admin Approving Edit
```python
# Admin approves edit
response = await approve_or_reject_edit(
    edit_id=456,
    schema=AdvertisementEditApprovalSchemaV2(
        action="approve",
        admin_notes="Looks good"
    ),
    admin_user=admin
)
# Original advertisement is updated with new values
```

### 7. Benefits

1. **User Experience**: Original advertisement remains visible during review
2. **Content Moderation**: All changes require admin approval
3. **Audit Trail**: Complete history of edit requests
4. **Data Safety**: Original data is never lost
5. **Scalability**: Efficient database design with proper indexing

### 8. Migration Steps

1. Run database migration to create new tables
2. Update API endpoints to use new edit system
3. Update frontend to handle edit workflow
4. Train admins on new review process

### 9. Future Enhancements

- Email notifications for edit status changes
- Bulk approval/rejection for admins
- Edit comparison UI for admins
- Automatic approval for trusted users
- Edit expiration (auto-reject old edits)
