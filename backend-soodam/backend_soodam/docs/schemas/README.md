# Pydantic Schemas Documentation

## 📋 Overview

This document provides comprehensive information about all Pydantic schemas used in the Soodam backend API. These schemas handle request/response validation, serialization, and data transformation between the API layer and the database models.

## 🏗️ Schema Architecture

### Schema Organization
```
schemas/
├── v1/                          # Version 1 schemas
│   ├── authentication/          # Auth-related schemas
│   ├── advertisement/           # Advertisement schemas
│   ├── user/                   # User management schemas
│   └── common/                 # Shared schemas
├── v2/                         # Version 2 schemas (future)
├── admin/                      # Admin-specific schemas
└── base/                       # Base schema classes
```

### Schema Types
- **Request Schemas**: Validate incoming API requests
- **Response Schemas**: Structure API responses
- **Internal Schemas**: Data transformation between layers
- **Validation Schemas**: Complex validation logic

## 🔧 Base Schema Classes

### BaseSchema
```python
from pydantic import BaseModel, ConfigDict
from typing import Optional
from datetime import datetime

class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
        use_enum_values=True
    )

class TimestampMixin(BaseModel):
    """Mixin for timestamp fields"""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class PaginationSchema(BaseModel):
    """Standard pagination schema"""
    page: int = 1
    limit: int = 10
    total: Optional[int] = None
    pages: Optional[int] = None
    has_next: Optional[bool] = None
    has_prev: Optional[bool] = None

class ResponseSchema(BaseModel):
    """Standard API response wrapper"""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = datetime.utcnow()
```

## 📊 Common Schemas

### Location Schemas
```python
from pydantic import BaseModel, Field, validator
from typing import Optional
from decimal import Decimal

class CoordinatesSchema(BaseModel):
    """Geographic coordinates"""
    latitude: Decimal = Field(..., ge=-90, le=90, decimal_places=8)
    longitude: Decimal = Field(..., ge=-180, le=180, decimal_places=8)
    
    @validator('latitude', 'longitude')
    def validate_coordinates(cls, v):
        if v is None:
            raise ValueError('Coordinates cannot be null')
        return v

class AddressSchema(BaseModel):
    """Address information"""
    province_id: int = Field(..., gt=0)
    city_id: int = Field(..., gt=0)
    address: str = Field(..., min_length=10, max_length=500)
    postal_code: Optional[str] = Field(None, regex=r'^\d{5,10}$')
    coordinates: Optional[CoordinatesSchema] = None

class LocationResponseSchema(BaseModel):
    """Location response with names"""
    province: str
    city: str
    address: str
    postal_code: Optional[str] = None
    coordinates: Optional[CoordinatesSchema] = None
```

### Price Schemas
```python
from enum import Enum
from decimal import Decimal

class CurrencyEnum(str, Enum):
    IRR = "IRR"
    USD = "USD"
    EUR = "EUR"

class PriceTypeEnum(str, Enum):
    SALE = "sale"
    RENT = "rent"
    LEASE = "lease"
    EXCHANGE = "exchange"
    FREE = "free"

class PriceSchema(BaseModel):
    """Price information"""
    amount: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    currency: CurrencyEnum = CurrencyEnum.IRR
    is_negotiable: bool = False
    price_type: PriceTypeEnum = PriceTypeEnum.SALE
    
    # Additional price fields
    deposit: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    rent: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    maintenance_fee: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    
    @validator('amount')
    def validate_amount(cls, v, values):
        if values.get('price_type') != PriceTypeEnum.FREE and v is None:
            raise ValueError('Amount is required for non-free items')
        return v
```

### Media Schemas
```python
from typing import List

class ImageSchema(BaseModel):
    """Image information"""
    url: str = Field(..., regex=r'^https?://.+')
    alt_text: Optional[str] = Field(None, max_length=200)
    caption: Optional[str] = Field(None, max_length=500)
    is_primary: bool = False
    width: Optional[int] = Field(None, gt=0)
    height: Optional[int] = Field(None, gt=0)

class VideoSchema(BaseModel):
    """Video information"""
    url: str = Field(..., regex=r'^https?://.+')
    thumbnail_url: Optional[str] = Field(None, regex=r'^https?://.+')
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None, max_length=500)
    duration: Optional[int] = Field(None, gt=0)  # seconds

class MediaUploadSchema(BaseModel):
    """Media upload request"""
    file_type: str = Field(..., regex=r'^(image|video)$')
    file_name: str = Field(..., min_length=1, max_length=255)
    file_size: int = Field(..., gt=0, le=100*1024*1024)  # 100MB max
    content_type: str = Field(..., regex=r'^(image|video)/.+')
```

## 🔍 Validation Schemas

### Custom Validators
```python
from pydantic import validator, root_validator
import re

class PhoneNumberValidator:
    """Phone number validation"""
    @validator('phone_number')
    def validate_phone(cls, v):
        if v and not re.match(r'^\+[1-9]\d{1,14}$', v):
            raise ValueError('Invalid phone number format')
        return v

class EmailValidator:
    """Email validation"""
    @validator('email')
    def validate_email(cls, v):
        if v and not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
            raise ValueError('Invalid email format')
        return v

class PasswordValidator:
    """Password validation"""
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain letters')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain numbers')
        return v

class SlugValidator:
    """Slug validation"""
    @validator('slug')
    def validate_slug(cls, v):
        if v and not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError('Slug can only contain lowercase letters, numbers, and hyphens')
        return v
```

### Complex Validation
```python
class AdvertisementCreateSchema(BaseModel):
    """Advertisement creation with complex validation"""
    title: str = Field(..., min_length=3, max_length=200)
    description: str = Field(..., min_length=10, max_length=5000)
    category_id: int = Field(..., gt=0)
    price: Optional[PriceSchema] = None
    location: AddressSchema
    images: List[ImageSchema] = Field(default_factory=list, max_items=10)
    videos: List[VideoSchema] = Field(default_factory=list, max_items=3)
    
    @root_validator
    def validate_advertisement(cls, values):
        # Validate category-specific requirements
        category_id = values.get('category_id')
        price = values.get('price')
        
        # Real estate requires price
        if category_id == 1 and not price:
            raise ValueError('Price is required for real estate listings')
        
        # Validate image requirements
        images = values.get('images', [])
        if not images:
            raise ValueError('At least one image is required')
        
        # Check for primary image
        primary_images = [img for img in images if img.is_primary]
        if len(primary_images) != 1:
            raise ValueError('Exactly one primary image is required')
        
        return values
```

## 📝 Request/Response Patterns

### Standard Request Pattern
```python
class CreateRequestSchema(BaseModel):
    """Standard creation request"""
    # Required fields
    # Optional fields
    # Validation rules

class UpdateRequestSchema(BaseModel):
    """Standard update request (all fields optional)"""
    # All fields optional for partial updates
    
    class Config:
        extra = "forbid"  # Prevent extra fields

class FilterRequestSchema(BaseModel):
    """Standard filtering request"""
    page: int = Field(1, ge=1)
    limit: int = Field(10, ge=1, le=100)
    search: Optional[str] = Field(None, max_length=200)
    sort_by: Optional[str] = None
    sort_order: Optional[str] = Field("desc", regex=r'^(asc|desc)$')
```

### Standard Response Pattern
```python
class ItemResponseSchema(BaseModel):
    """Single item response"""
    id: int
    # Other fields
    created_at: datetime
    updated_at: datetime

class ListResponseSchema(BaseModel):
    """List response with pagination"""
    items: List[ItemResponseSchema]
    metadata: PaginationSchema

class StandardResponseSchema(BaseModel):
    """Wrapped response"""
    success: bool = True
    data: Union[ItemResponseSchema, ListResponseSchema, dict]
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
```

## 🔄 Schema Versioning

### Version Management
```python
# v1/base.py
class BaseSchemaV1(BaseModel):
    """Base schema for API v1"""
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True
    )

# v2/base.py
class BaseSchemaV2(BaseModel):
    """Base schema for API v2 with enhanced features"""
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v)
        }
    )

# Version-specific schemas
class UserSchemaV1(BaseSchemaV1):
    id: int
    username: str
    email: str

class UserSchemaV2(BaseSchemaV2):
    id: int
    username: str
    email: str
    profile: Optional[UserProfileSchema] = None  # Enhanced in v2
```

## 🛠️ Schema Utilities

### Schema Factories
```python
from typing import Type, TypeVar

T = TypeVar('T', bound=BaseModel)

class SchemaFactory:
    """Factory for creating schema instances"""
    
    @staticmethod
    def create_response_schema(data_schema: Type[T]) -> Type[BaseModel]:
        """Create a response schema wrapper"""
        class ResponseSchema(BaseModel):
            success: bool = True
            data: data_schema
            message: Optional[str] = None
            timestamp: datetime = Field(default_factory=datetime.utcnow)
        
        return ResponseSchema
    
    @staticmethod
    def create_list_schema(item_schema: Type[T]) -> Type[BaseModel]:
        """Create a list schema with pagination"""
        class ListSchema(BaseModel):
            items: List[item_schema]
            metadata: PaginationSchema
        
        return ListSchema
```

### Schema Converters
```python
class SchemaConverter:
    """Convert between different schema versions"""
    
    @staticmethod
    def model_to_schema(model_instance, schema_class: Type[T]) -> T:
        """Convert Django model to Pydantic schema"""
        return schema_class.from_orm(model_instance)
    
    @staticmethod
    def schema_to_dict(schema_instance: BaseModel) -> dict:
        """Convert schema to dictionary"""
        return schema_instance.dict(exclude_unset=True, by_alias=True)
    
    @staticmethod
    def merge_schemas(base_schema: BaseModel, update_schema: BaseModel) -> dict:
        """Merge two schemas for updates"""
        base_data = base_schema.dict()
        update_data = update_schema.dict(exclude_unset=True)
        base_data.update(update_data)
        return base_data
```

## 📊 Performance Considerations

### Schema Optimization
```python
# Use Field aliases for database field mapping
class OptimizedSchema(BaseModel):
    id: int
    name: str = Field(alias='full_name')
    created: datetime = Field(alias='created_at')
    
    class Config:
        allow_population_by_field_name = True

# Exclude heavy fields by default
class LightweightSchema(BaseModel):
    id: int
    title: str
    # Exclude heavy fields like description, images
    
    class Config:
        fields = {'description': {'exclude': True}}

# Use lazy loading for related data
class LazyLoadSchema(BaseModel):
    id: int
    title: str
    images: Optional[List[ImageSchema]] = None  # Load separately if needed
```

### Validation Performance
```python
# Cache compiled regex patterns
import re
from functools import lru_cache

@lru_cache(maxsize=128)
def get_phone_regex():
    return re.compile(r'^\+[1-9]\d{1,14}$')

class OptimizedValidator:
    @validator('phone_number')
    def validate_phone(cls, v):
        if v and not get_phone_regex().match(v):
            raise ValueError('Invalid phone number')
        return v
```

## 🚨 Error Handling

### Validation Error Schemas
```python
class ValidationErrorSchema(BaseModel):
    """Validation error response"""
    success: bool = False
    error: dict = Field(..., example={
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "field_name": ["Error message"]
        }
    })
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorDetailSchema(BaseModel):
    """Detailed error information"""
    field: str
    message: str
    code: str
    value: Optional[str] = None
```

## 📚 Schema Documentation

### Auto-generated Documentation
```python
class DocumentedSchema(BaseModel):
    """Well-documented schema with examples"""
    
    title: str = Field(
        ...,
        title="Advertisement Title",
        description="The main title of the advertisement",
        example="Beautiful 3-bedroom apartment in downtown",
        min_length=3,
        max_length=200
    )
    
    price: Optional[Decimal] = Field(
        None,
        title="Price",
        description="Price in the specified currency",
        example=250000.00,
        ge=0
    )
    
    class Config:
        schema_extra = {
            "example": {
                "title": "Beautiful apartment",
                "price": 250000.00
            }
        }
```

## 📋 Schema Testing

### Test Utilities
```python
import pytest
from pydantic import ValidationError

class SchemaTestCase:
    """Base test case for schema validation"""
    
    def test_valid_data(self, schema_class, valid_data):
        """Test schema with valid data"""
        instance = schema_class(**valid_data)
        assert instance.dict() == valid_data
    
    def test_invalid_data(self, schema_class, invalid_data, expected_error):
        """Test schema with invalid data"""
        with pytest.raises(ValidationError) as exc_info:
            schema_class(**invalid_data)
        assert expected_error in str(exc_info.value)
```

---

This Pydantic schemas documentation provides comprehensive information about the schema architecture, validation patterns, and best practices used throughout the Soodam backend API system.
