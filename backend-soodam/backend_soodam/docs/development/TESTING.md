# Testing Guide

## 🧪 Overview

This guide covers comprehensive testing strategies, tools, and best practices for the Soodam backend. We use pytest as the primary testing framework with additional tools for coverage, mocking, and integration testing.

## 🏗️ Testing Architecture

### Test Structure
```
tests/
├── unit/                        # Unit tests
│   ├── test_models/            # Model tests
│   ├── test_api/               # API endpoint tests
│   ├── test_services/          # Service layer tests
│   └── test_utils/             # Utility function tests
├── integration/                 # Integration tests
│   ├── test_database/          # Database integration
│   ├── test_external_apis/     # External API integration
│   └── test_workflows/         # End-to-end workflows
├── performance/                 # Performance tests
├── fixtures/                    # Test data fixtures
├── factories/                   # Model factories
└── conftest.py                 # Pytest configuration
```

### Testing Pyramid
```
    E2E Tests (Few)
   ┌─────────────────┐
   │  Integration    │
   │     Tests       │
   │   (Some)        │
   ├─────────────────┤
   │   Unit Tests    │
   │    (Many)       │
   └─────────────────┘
```

## 🔧 Testing Setup

### Dependencies
```toml
[tool.poetry.group.test.dependencies]
pytest = "^7.4.0"
pytest-django = "^4.7.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.0"
pytest-xdist = "^3.3.0"
factory-boy = "^3.3.0"
faker = "^19.6.0"
freezegun = "^1.2.0"
responses = "^0.23.0"
httpx = "^0.24.0"
```

### Pytest Configuration
```python
# conftest.py
import pytest
import asyncio
from django.conf import settings
from django.test import override_settings
from rest_framework.test import APIClient
from factory import Faker

# Configure test settings
@pytest.fixture(scope='session')
def django_db_setup():
    settings.DATABASES['default'] = {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': 'test_soodam',
        'USER': 'test_user',
        'PASSWORD': 'test_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }

# Async test support
@pytest.fixture(scope='session')
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# API client fixture
@pytest.fixture
def api_client():
    return APIClient()

# Authenticated API client
@pytest.fixture
def authenticated_client(api_client, user_factory):
    user = user_factory()
    api_client.force_authenticate(user=user)
    return api_client

# Mock settings for testing
@pytest.fixture
def test_settings():
    with override_settings(
        CELERY_TASK_ALWAYS_EAGER=True,
        EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend',
        CACHES={'default': {'BACKEND': 'django.core.cache.backends.dummy.DummyCache'}},
        MEDIA_ROOT='/tmp/test_media',
    ):
        yield
```

## 🏭 Test Factories

### Model Factories
```python
# factories/user_factories.py
import factory
from factory.django import DjangoModelFactory
from factory import Faker, SubFactory
from app.models import CustomUserModel, SpecialAdmin

class UserFactory(DjangoModelFactory):
    class Meta:
        model = CustomUserModel
    
    username = Faker('user_name')
    email = Faker('email')
    phone_number = Faker('phone_number')
    first_name = Faker('first_name')
    last_name = Faker('last_name')
    is_active = True
    is_verified = True
    
    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        if not create:
            return
        password = extracted or 'testpassword123'
        self.set_password(password)
        self.save()

class AdminUserFactory(UserFactory):
    is_staff = True
    is_superuser = True
    
    admin_profile = SubFactory('factories.SpecialAdminFactory')

class SpecialAdminFactory(DjangoModelFactory):
    class Meta:
        model = SpecialAdmin
    
    user = SubFactory(UserFactory)
    admin_level = 'super_admin'
    can_approve_ads = True
    can_manage_users = True
```

```python
# factories/advertisement_factories.py
import factory
from factory.django import DjangoModelFactory
from app.models import AdvertisementModel, AdvertisementPriceModel

class AdvertisementFactory(DjangoModelFactory):
    class Meta:
        model = AdvertisementModel
    
    title = Faker('sentence', nb_words=4)
    description = Faker('text', max_nb_chars=500)
    user = SubFactory(UserFactory)
    sub_category = SubFactory('factories.SubCategoryFactory')
    status = 1  # Approved
    is_active = True
    
    @factory.post_generation
    def price(self, create, extracted, **kwargs):
        if not create:
            return
        AdvertisementPriceFactory(advertisement=self, **kwargs)

class AdvertisementPriceFactory(DjangoModelFactory):
    class Meta:
        model = AdvertisementPriceModel
    
    advertisement = SubFactory(AdvertisementFactory)
    amount = Faker('pydecimal', left_digits=8, right_digits=2, positive=True)
    currency = 'USD'
    is_negotiable = False
```

## 🧪 Unit Testing

### Model Tests
```python
# tests/unit/test_models/test_user_models.py
import pytest
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from app.models import CustomUserModel

@pytest.mark.django_db
class TestCustomUserModel:
    
    def test_create_user_with_email(self, user_factory):
        """Test creating user with email"""
        user = user_factory(email='<EMAIL>')
        assert user.email == '<EMAIL>'
        assert user.is_active is True
        assert user.is_verified is True
    
    def test_create_user_with_phone(self, user_factory):
        """Test creating user with phone number"""
        user = user_factory(phone_number='+1234567890')
        assert user.phone_number == '+1234567890'
    
    def test_unique_email_constraint(self, user_factory):
        """Test email uniqueness constraint"""
        user_factory(email='<EMAIL>')
        
        with pytest.raises(IntegrityError):
            user_factory(email='<EMAIL>')
    
    def test_user_full_name_property(self, user_factory):
        """Test full name property"""
        user = user_factory(first_name='John', last_name='Doe')
        assert user.get_full_name() == 'John Doe'
    
    def test_user_verification_status(self, user_factory):
        """Test verification status method"""
        user = user_factory(
            email_verified=True,
            phone_verified=True,
            identity_verified=False
        )
        
        status = user.get_verification_status()
        assert status['email_verified'] is True
        assert status['phone_verified'] is True
        assert status['identity_verified'] is False
        assert status['fully_verified'] is False
    
    @pytest.mark.parametrize('email,expected', [
        ('<EMAIL>', True),
        ('invalid-email', False),
        ('', False),
    ])
    def test_email_validation(self, email, expected):
        """Test email validation"""
        user = CustomUserModel(email=email)
        if expected:
            user.full_clean()  # Should not raise
        else:
            with pytest.raises(ValidationError):
                user.full_clean()
```

### API Tests
```python
# tests/unit/test_api/test_advertisement_api.py
import pytest
from django.urls import reverse
from rest_framework import status
from app.models import AdvertisementModel

@pytest.mark.django_db
class TestAdvertisementAPI:
    
    def test_list_advertisements(self, api_client, advertisement_factory):
        """Test listing advertisements"""
        # Create test data
        advertisement_factory.create_batch(5, status=1)  # Approved ads
        advertisement_factory.create_batch(2, status=0)  # Pending ads
        
        url = reverse('advertisement-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data['items']) == 5  # Only approved ads
        assert data['metadata']['total'] == 5
    
    def test_create_advertisement_authenticated(self, authenticated_client):
        """Test creating advertisement with authentication"""
        data = {
            'title': 'Test Advertisement',
            'description': 'Test description for advertisement',
            'category_id': 1,
            'price': {
                'amount': 100000,
                'currency': 'USD',
                'is_negotiable': True
            },
            'location': {
                'province_id': 1,
                'city_id': 1,
                'address': '123 Test Street',
                'latitude': 35.6892,
                'longitude': 51.3890
            }
        }
        
        url = reverse('advertisement-create')
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert AdvertisementModel.objects.count() == 1
        
        ad = AdvertisementModel.objects.first()
        assert ad.title == 'Test Advertisement'
        assert ad.status == 0  # Pending approval
    
    def test_create_advertisement_unauthenticated(self, api_client):
        """Test creating advertisement without authentication"""
        data = {'title': 'Test Ad'}
        
        url = reverse('advertisement-create')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_update_advertisement_owner(self, authenticated_client, advertisement_factory):
        """Test updating advertisement by owner"""
        user = authenticated_client.handler._force_user
        advertisement = advertisement_factory(user=user)
        
        data = {'title': 'Updated Title'}
        url = reverse('advertisement-update', kwargs={'pk': advertisement.id})
        response = authenticated_client.put(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        # Should create edit request, not update directly
        assert advertisement.edit_requests.count() == 1
    
    def test_update_advertisement_non_owner(self, authenticated_client, advertisement_factory):
        """Test updating advertisement by non-owner"""
        advertisement = advertisement_factory()  # Different user
        
        data = {'title': 'Updated Title'}
        url = reverse('advertisement-update', kwargs={'pk': advertisement.id})
        response = authenticated_client.put(url, data, format='json')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    @pytest.mark.parametrize('status_filter,expected_count', [
        ('approved', 3),
        ('pending', 2),
        ('rejected', 1),
    ])
    def test_filter_advertisements_by_status(self, api_client, advertisement_factory, status_filter, expected_count):
        """Test filtering advertisements by status"""
        advertisement_factory.create_batch(3, status=1)  # Approved
        advertisement_factory.create_batch(2, status=0)  # Pending
        advertisement_factory.create_batch(1, status=2)  # Rejected
        
        url = reverse('advertisement-list')
        response = api_client.get(url, {'status': status_filter})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data['items']) == expected_count
```

### Service Tests
```python
# tests/unit/test_services/test_advertisement_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.advertisement_service import AdvertisementService
from app.models import AdvertisementModel

@pytest.mark.django_db
class TestAdvertisementService:
    
    def test_create_advertisement(self, user_factory):
        """Test advertisement creation service"""
        user = user_factory()
        data = {
            'title': 'Test Ad',
            'description': 'Test description',
            'category_id': 1,
            'user_id': user.id
        }
        
        service = AdvertisementService()
        advertisement = service.create_advertisement(data)
        
        assert advertisement.title == 'Test Ad'
        assert advertisement.user == user
        assert advertisement.status == 0  # Pending
    
    @patch('app.services.notification_service.NotificationService.send_notification')
    def test_approve_advertisement(self, mock_send_notification, advertisement_factory, admin_user_factory):
        """Test advertisement approval with notification"""
        advertisement = advertisement_factory(status=0)  # Pending
        admin = admin_user_factory()
        
        service = AdvertisementService()
        result = service.approve_advertisement(advertisement.id, admin.id, 'Approved after review')
        
        advertisement.refresh_from_db()
        assert advertisement.status == 1  # Approved
        assert advertisement.admin_notes == 'Approved after review'
        assert advertisement.reviewed_by == admin
        
        # Check notification was sent
        mock_send_notification.assert_called_once()
        call_args = mock_send_notification.call_args
        assert call_args[0][0] == advertisement.user.id
        assert 'approved' in call_args[0][1].lower()
    
    def test_get_nearby_advertisements(self, advertisement_factory):
        """Test getting nearby advertisements"""
        # Create advertisements with locations
        ad1 = advertisement_factory()
        ad2 = advertisement_factory()
        
        service = AdvertisementService()
        nearby_ads = service.get_nearby_advertisements(
            latitude=35.6892,
            longitude=51.3890,
            radius=10
        )
        
        assert len(nearby_ads) >= 0  # Depends on test data locations
```

## 🔗 Integration Testing

### Database Integration
```python
# tests/integration/test_database/test_advertisement_queries.py
import pytest
from django.test import TransactionTestCase
from django.db import transaction
from app.models import AdvertisementModel

@pytest.mark.django_db
class TestAdvertisementQueries:
    
    def test_complex_advertisement_query(self, advertisement_factory):
        """Test complex query with joins and filtering"""
        # Create test data
        ads = advertisement_factory.create_batch(10, status=1)
        
        # Complex query with joins
        queryset = AdvertisementModel.objects.select_related(
            'user', 'sub_category__main_category', 'price', 'location'
        ).prefetch_related(
            'images', 'attribute_values'
        ).filter(
            status=1,
            is_active=True,
            price__amount__gte=50000
        )
        
        # Execute query and verify
        results = list(queryset)
        assert len(results) > 0
        
        # Verify no additional queries for related objects
        with pytest.assertNumQueries(0):
            for ad in results:
                _ = ad.user.username
                _ = ad.sub_category.name
                _ = ad.price.amount if hasattr(ad, 'price') else None

class TestDatabaseTransactions(TransactionTestCase):
    
    def test_advertisement_creation_transaction(self):
        """Test advertisement creation in transaction"""
        from app.services.advertisement_service import AdvertisementService
        
        service = AdvertisementService()
        
        # Test successful transaction
        with transaction.atomic():
            data = {
                'title': 'Test Ad',
                'description': 'Test description',
                'user_id': 1
            }
            ad = service.create_advertisement(data)
            assert ad.id is not None
        
        # Verify data persisted
        assert AdvertisementModel.objects.filter(id=ad.id).exists()
        
        # Test rollback on error
        with pytest.raises(Exception):
            with transaction.atomic():
                service.create_advertisement({'invalid': 'data'})
                raise Exception("Force rollback")
        
        # Verify no partial data
        assert AdvertisementModel.objects.count() == 1  # Only the first one
```

### API Integration
```python
# tests/integration/test_workflows/test_advertisement_workflow.py
import pytest
from django.urls import reverse
from rest_framework import status

@pytest.mark.django_db
class TestAdvertisementWorkflow:
    
    def test_complete_advertisement_lifecycle(self, api_client, user_factory, admin_user_factory):
        """Test complete advertisement lifecycle"""
        user = user_factory()
        admin = admin_user_factory()
        
        # 1. User creates advertisement
        api_client.force_authenticate(user=user)
        create_data = {
            'title': 'Test Advertisement',
            'description': 'Test description',
            'category_id': 1,
            'price': {'amount': 100000, 'currency': 'USD'}
        }
        
        response = api_client.post(reverse('advertisement-create'), create_data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        ad_id = response.json()['id']
        
        # 2. Admin approves advertisement
        api_client.force_authenticate(user=admin)
        approve_data = {'status': 1, 'admin_notes': 'Approved'}
        
        response = api_client.put(
            reverse('admin-advertisement-status', kwargs={'pk': ad_id}),
            approve_data,
            format='json'
        )
        assert response.status_code == status.HTTP_200_OK
        
        # 3. Advertisement appears in public listing
        api_client.force_authenticate(user=None)
        response = api_client.get(reverse('advertisement-list'))
        
        assert response.status_code == status.HTTP_200_OK
        ads = response.json()['items']
        assert any(ad['id'] == ad_id for ad in ads)
        
        # 4. User creates edit request
        api_client.force_authenticate(user=user)
        edit_data = {'title': 'Updated Title'}
        
        response = api_client.put(
            reverse('advertisement-update', kwargs={'pk': ad_id}),
            edit_data,
            format='json'
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()['has_pending_edit'] is True
        
        # 5. Admin approves edit
        api_client.force_authenticate(user=admin)
        edit_requests = api_client.get(reverse('admin-advertisement-edits')).json()
        edit_id = edit_requests['items'][0]['id']
        
        response = api_client.post(
            reverse('admin-advertisement-edit-review', kwargs={'pk': edit_id}),
            {'action': 'approve'},
            format='json'
        )
        assert response.status_code == status.HTTP_200_OK
```

## 🚀 Performance Testing

### Load Testing
```python
# tests/performance/test_api_performance.py
import pytest
import time
from concurrent.futures import ThreadPoolExecutor
from django.test import Client

@pytest.mark.performance
class TestAPIPerformance:
    
    def test_advertisement_list_performance(self, advertisement_factory):
        """Test advertisement list API performance"""
        # Create test data
        advertisement_factory.create_batch(1000, status=1)
        
        client = Client()
        
        # Measure response time
        start_time = time.time()
        response = client.get('/api/advertisements/')
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second
    
    def test_concurrent_requests(self, advertisement_factory):
        """Test handling concurrent requests"""
        advertisement_factory.create_batch(100, status=1)
        
        def make_request():
            client = Client()
            response = client.get('/api/advertisements/')
            return response.status_code
        
        # Test 10 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # All requests should succeed
        assert all(status == 200 for status in results)
```

### Database Performance
```python
# tests/performance/test_database_performance.py
import pytest
from django.test.utils import override_settings
from django.db import connection
from app.models import AdvertisementModel

@pytest.mark.django_db
class TestDatabasePerformance:
    
    def test_query_count_optimization(self, advertisement_factory):
        """Test query count for optimized queries"""
        advertisement_factory.create_batch(10, status=1)
        
        # Test optimized query
        with pytest.assertNumQueries(1):
            ads = list(AdvertisementModel.objects.select_related(
                'user', 'sub_category', 'price'
            ).filter(status=1)[:10])
        
        # Verify no additional queries when accessing related objects
        with pytest.assertNumQueries(0):
            for ad in ads:
                _ = ad.user.username
                _ = ad.sub_category.name
                _ = ad.price.amount if hasattr(ad, 'price') else None
    
    @override_settings(DEBUG=True)
    def test_slow_query_detection(self, advertisement_factory):
        """Test detection of slow queries"""
        advertisement_factory.create_batch(1000, status=1)
        
        # Reset query log
        connection.queries_log.clear()
        
        # Execute potentially slow query
        list(AdvertisementModel.objects.filter(
            title__icontains='test'
        ).order_by('-created_at')[:100])
        
        # Check query execution time
        queries = connection.queries
        if queries:
            query_time = float(queries[-1]['time'])
            assert query_time < 0.1  # Should execute within 100ms
```

## 🎭 Mocking and Fixtures

### External Service Mocking
```python
# tests/unit/test_external_services.py
import pytest
import responses
from app.services.external_api_service import ExternalAPIService

class TestExternalAPIService:
    
    @responses.activate
    def test_geocoding_service(self):
        """Test geocoding service with mocked response"""
        # Mock external API response
        responses.add(
            responses.GET,
            'https://api.geocoding.com/v1/geocode',
            json={
                'latitude': 35.6892,
                'longitude': 51.3890,
                'address': 'Tehran, Iran'
            },
            status=200
        )
        
        service = ExternalAPIService()
        result = service.geocode_address('Tehran, Iran')
        
        assert result['latitude'] == 35.6892
        assert result['longitude'] == 51.3890
    
    @responses.activate
    def test_geocoding_service_error(self):
        """Test geocoding service error handling"""
        responses.add(
            responses.GET,
            'https://api.geocoding.com/v1/geocode',
            json={'error': 'Address not found'},
            status=404
        )
        
        service = ExternalAPIService()
        result = service.geocode_address('Invalid Address')
        
        assert result is None
```

### Time-based Testing
```python
# tests/unit/test_time_based_features.py
import pytest
from freezegun import freeze_time
from datetime import datetime, timedelta
from app.models import AdvertisementModel

@pytest.mark.django_db
class TestTimeBased:
    
    @freeze_time("2024-01-01 12:00:00")
    def test_advertisement_expiry(self, advertisement_factory):
        """Test advertisement expiry logic"""
        # Create advertisement that expires in 30 days
        expiry_date = datetime.now() + timedelta(days=30)
        ad = advertisement_factory(expires_at=expiry_date)
        
        assert not ad.is_expired()
        
        # Move time forward 31 days
        with freeze_time("2024-02-01 12:00:00"):
            assert ad.is_expired()
```

## 📊 Test Coverage

### Coverage Configuration
```ini
# .coveragerc
[run]
source = app
omit = 
    */migrations/*
    */tests/*
    */venv/*
    manage.py
    */settings/*
    */conftest.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

### Coverage Commands
```bash
# Run tests with coverage
pytest --cov=app --cov-report=html --cov-report=term

# Generate coverage report
coverage run -m pytest
coverage report
coverage html

# Check coverage threshold
pytest --cov=app --cov-fail-under=80
```

## 🚀 Running Tests

### Test Commands
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/test_models/test_user_models.py

# Run tests with specific marker
pytest -m "not performance"

# Run tests in parallel
pytest -n auto

# Run tests with verbose output
pytest -v

# Run tests and stop on first failure
pytest -x

# Run only failed tests from last run
pytest --lf
```

### Test Configuration
```python
# pytest.ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings.test
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow
    integration: marks tests as integration tests
    performance: marks tests as performance tests
    unit: marks tests as unit tests
addopts = 
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
```

---

This testing guide provides comprehensive coverage of testing strategies, tools, and best practices for maintaining high-quality, reliable code in the Soodam backend system.
