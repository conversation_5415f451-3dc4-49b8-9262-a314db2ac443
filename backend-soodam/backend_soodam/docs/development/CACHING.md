# Caching Strategy Guide

## 🚀 Overview

This guide covers comprehensive caching strategies for the Soodam backend, including Redis configuration, cache patterns, invalidation strategies, and performance optimization through intelligent caching.

## 🏗️ Caching Architecture

### Multi-Layer Caching Strategy
```
┌─────────────────────────────────────────┐
│           Browser Cache                 │
│  ├── Static Assets (CSS, JS, Images)   │
│  ├── API Response Headers              │
│  └── Service Worker Cache              │
├─────────────────────────────────────────┤
│           CDN/Proxy Cache               │
│  ├── Static Content                    │
│  ├── Image Optimization                │
│  └── Geographic Distribution           │
├─────────────────────────────────────────┤
│           Application Cache             │
│  ├── Redis (Primary)                   │
│  ├── Database Query Cache              │
│  ├── Session Cache                     │
│  └── Template Fragment Cache           │
├─────────────────────────────────────────┤
│           Database Cache                │
│  ├── Query Result Cache                │
│  ├── Connection Pool                   │
│  └── Prepared Statement Cache          │
└─────────────────────────────────────────┘
```

## ⚙️ Redis Configuration

### Redis Setup and Configuration
```python
# config/redis_config.py
import redis
import json
from django.conf import settings
from django.core.cache import cache
from redis.sentinel import Sentinel

class RedisConfig:
    """Redis configuration and connection management"""
    
    # Redis instances for different purposes
    REDIS_INSTANCES = {
        'cache': {
            'host': settings.REDIS_HOST,
            'port': settings.REDIS_PORT,
            'db': 0,
            'max_connections': 50,
            'socket_keepalive': True,
            'socket_keepalive_options': {},
            'retry_on_timeout': True,
            'health_check_interval': 30,
        },
        'sessions': {
            'host': settings.REDIS_HOST,
            'port': settings.REDIS_PORT,
            'db': 1,
            'max_connections': 20,
        },
        'celery': {
            'host': settings.REDIS_HOST,
            'port': settings.REDIS_PORT,
            'db': 2,
            'max_connections': 30,
        },
        'rate_limiting': {
            'host': settings.REDIS_HOST,
            'port': settings.REDIS_PORT,
            'db': 3,
            'max_connections': 15,
        }
    }
    
    @classmethod
    def get_redis_connection(cls, instance_name='cache'):
        """Get Redis connection for specific instance"""
        config = cls.REDIS_INSTANCES[instance_name]
        pool = redis.ConnectionPool(**config)
        return redis.Redis(connection_pool=pool)
    
    @classmethod
    def setup_redis_sentinel(cls):
        """Setup Redis Sentinel for high availability"""
        if settings.USE_REDIS_SENTINEL:
            sentinel = Sentinel([
                (settings.REDIS_SENTINEL_HOST_1, 26379),
                (settings.REDIS_SENTINEL_HOST_2, 26379),
                (settings.REDIS_SENTINEL_HOST_3, 26379),
            ])
            
            return {
                'master': sentinel.master_for('mymaster', socket_timeout=0.1),
                'slave': sentinel.slave_for('mymaster', socket_timeout=0.1),
            }
        
        return None

# Django cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/0",
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
                'socket_keepalive': True,
                'socket_keepalive_options': {},
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'IGNORE_EXCEPTIONS': True,
        },
        'KEY_PREFIX': 'soodam',
        'TIMEOUT': 300,  # 5 minutes default
        'VERSION': 1,
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/1",
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'session',
        'TIMEOUT': 86400,  # 24 hours
    },
    'long_term': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/4",
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'long_term',
        'TIMEOUT': 86400 * 7,  # 1 week
    }
}
```

## 🎯 Cache Patterns

### Cache-Aside Pattern
```python
# app/caching/patterns.py
import json
import hashlib
from django.core.cache import cache
from django.conf import settings
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class CacheAside:
    """Cache-aside pattern implementation"""
    
    @staticmethod
    def get_or_set(cache_key, fetch_function, timeout=300, version=None):
        """Get from cache or fetch and set"""
        try:
            # Try to get from cache
            cached_data = cache.get(cache_key, version=version)
            if cached_data is not None:
                logger.debug(f"Cache HIT: {cache_key}")
                return cached_data
            
            # Cache miss - fetch data
            logger.debug(f"Cache MISS: {cache_key}")
            data = fetch_function()
            
            # Set in cache
            cache.set(cache_key, data, timeout, version=version)
            return data
            
        except Exception as e:
            logger.error(f"Cache error for key {cache_key}: {e}")
            # Fallback to direct fetch
            return fetch_function()
    
    @staticmethod
    def invalidate(cache_key, version=None):
        """Invalidate cache entry"""
        try:
            cache.delete(cache_key, version=version)
            logger.debug(f"Cache INVALIDATED: {cache_key}")
        except Exception as e:
            logger.error(f"Cache invalidation error for key {cache_key}: {e}")

# Cache decorators
def cache_result(timeout=300, key_prefix='', version=None):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = generate_cache_key(key_prefix, func.__name__, args, kwargs)
            
            def fetch_data():
                return func(*args, **kwargs)
            
            return CacheAside.get_or_set(cache_key, fetch_data, timeout, version)
        
        return wrapper
    return decorator

def generate_cache_key(prefix, func_name, args, kwargs):
    """Generate consistent cache key"""
    # Create a string representation of arguments
    args_str = ':'.join(str(arg) for arg in args)
    kwargs_str = ':'.join(f"{k}={v}" for k, v in sorted(kwargs.items()))
    
    # Combine all parts
    key_parts = [prefix, func_name, args_str, kwargs_str]
    key_string = ':'.join(filter(None, key_parts))
    
    # Hash if too long
    if len(key_string) > 200:
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{func_name}:{key_hash}"
    
    return key_string
```

### Write-Through Cache Pattern
```python
class WriteThrough:
    """Write-through cache pattern"""
    
    @staticmethod
    def save_with_cache(model_instance, cache_key, timeout=300):
        """Save model and update cache"""
        try:
            # Save to database
            model_instance.save()
            
            # Update cache
            cache.set(cache_key, model_instance, timeout)
            
            logger.debug(f"Write-through cache updated: {cache_key}")
            return model_instance
            
        except Exception as e:
            logger.error(f"Write-through cache error: {e}")
            # Invalidate cache on error
            cache.delete(cache_key)
            raise

class WriteBack:
    """Write-back (write-behind) cache pattern"""
    
    @staticmethod
    def queue_write(cache_key, data, timeout=300):
        """Queue data for background write"""
        # Store in cache immediately
        cache.set(cache_key, data, timeout)
        
        # Queue for background database write
        from app.tasks import write_to_database
        write_to_database.delay(cache_key, data)
```

## 📊 Application-Specific Caching

### Advertisement Caching
```python
# app/caching/advertisement_cache.py
from django.core.cache import cache
from app.models import AdvertisementModel
import json

class AdvertisementCache:
    """Advertisement-specific caching strategies"""
    
    CACHE_TIMEOUTS = {
        'advertisement_detail': 1800,    # 30 minutes
        'advertisement_list': 600,       # 10 minutes
        'featured_ads': 3600,           # 1 hour
        'user_ads': 900,                # 15 minutes
        'search_results': 300,          # 5 minutes
    }
    
    @staticmethod
    def get_advertisement(ad_id):
        """Get advertisement with caching"""
        cache_key = f"advertisement:{ad_id}"
        
        def fetch_advertisement():
            return AdvertisementModel.objects.select_related(
                'user', 'sub_category', 'price', 'location'
            ).prefetch_related('images', 'videos').get(id=ad_id)
        
        return CacheAside.get_or_set(
            cache_key, 
            fetch_advertisement, 
            AdvertisementCache.CACHE_TIMEOUTS['advertisement_detail']
        )
    
    @staticmethod
    def get_advertisement_list(filters, page=1, limit=20):
        """Get advertisement list with caching"""
        # Create cache key from filters
        filter_hash = hashlib.md5(
            json.dumps(filters, sort_keys=True).encode()
        ).hexdigest()
        cache_key = f"ad_list:{filter_hash}:{page}:{limit}"
        
        def fetch_advertisements():
            queryset = AdvertisementModel.objects.filter(**filters)
            start = (page - 1) * limit
            end = start + limit
            return list(queryset[start:end])
        
        return CacheAside.get_or_set(
            cache_key,
            fetch_advertisements,
            AdvertisementCache.CACHE_TIMEOUTS['advertisement_list']
        )
    
    @staticmethod
    def get_featured_advertisements():
        """Get featured advertisements with long-term caching"""
        cache_key = "featured_advertisements"
        
        def fetch_featured():
            return list(AdvertisementModel.objects.filter(
                is_featured=True, status=1
            ).order_by('-created_at')[:10])
        
        return CacheAside.get_or_set(
            cache_key,
            fetch_featured,
            AdvertisementCache.CACHE_TIMEOUTS['featured_ads']
        )
    
    @staticmethod
    def invalidate_advertisement_cache(ad_id):
        """Invalidate all caches related to an advertisement"""
        # Direct advertisement cache
        cache.delete(f"advertisement:{ad_id}")
        
        # List caches (pattern-based deletion)
        cache.delete_pattern("ad_list:*")
        
        # Featured ads cache
        cache.delete("featured_advertisements")
        
        # User-specific caches
        ad = AdvertisementModel.objects.get(id=ad_id)
        cache.delete(f"user_ads:{ad.user_id}")
        
        logger.info(f"Invalidated caches for advertisement {ad_id}")

# Cache warming
class CacheWarmer:
    """Warm up frequently accessed caches"""
    
    @staticmethod
    def warm_advertisement_caches():
        """Warm up advertisement caches"""
        # Warm featured advertisements
        AdvertisementCache.get_featured_advertisements()
        
        # Warm popular advertisements
        popular_ads = AdvertisementModel.objects.filter(
            status=1
        ).order_by('-view_count')[:50]
        
        for ad in popular_ads:
            AdvertisementCache.get_advertisement(ad.id)
        
        logger.info("Advertisement caches warmed up")
    
    @staticmethod
    def warm_category_caches():
        """Warm up category-related caches"""
        from app.models import MainCategoryModel, SubCategoryModel
        
        # Cache categories
        categories = list(MainCategoryModel.objects.all())
        cache.set('categories', categories, 86400)  # 24 hours
        
        # Cache subcategories
        subcategories = list(SubCategoryModel.objects.select_related('main_category'))
        cache.set('subcategories', subcategories, 86400)
        
        logger.info("Category caches warmed up")
```

### User Session Caching
```python
# app/caching/session_cache.py
class SessionCache:
    """User session and profile caching"""
    
    @staticmethod
    def cache_user_profile(user):
        """Cache user profile data"""
        cache_key = f"user_profile:{user.id}"
        
        profile_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name(),
            'is_verified': user.is_verified,
            'avatar_url': user.avatar.url if user.avatar else None,
            'preferences': {
                'language': user.language,
                'timezone': user.timezone,
                'currency': user.currency,
            }
        }
        
        cache.set(cache_key, profile_data, 3600)  # 1 hour
        return profile_data
    
    @staticmethod
    def get_user_profile(user_id):
        """Get cached user profile"""
        cache_key = f"user_profile:{user_id}"
        
        cached_profile = cache.get(cache_key)
        if cached_profile:
            return cached_profile
        
        # Fetch from database
        from app.models import CustomUserModel
        try:
            user = CustomUserModel.objects.get(id=user_id)
            return SessionCache.cache_user_profile(user)
        except CustomUserModel.DoesNotExist:
            return None
    
    @staticmethod
    def invalidate_user_cache(user_id):
        """Invalidate user-related caches"""
        cache.delete(f"user_profile:{user_id}")
        cache.delete(f"user_ads:{user_id}")
        cache.delete(f"user_favorites:{user_id}")
        cache.delete(f"user_dashboard:{user_id}")
```

## 🔄 Cache Invalidation Strategies

### Smart Cache Invalidation
```python
# app/caching/invalidation.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from app.models import AdvertisementModel, CustomUserModel

class CacheInvalidator:
    """Intelligent cache invalidation"""
    
    @staticmethod
    def invalidate_by_pattern(pattern):
        """Invalidate caches by pattern"""
        try:
            cache.delete_pattern(pattern)
            logger.info(f"Invalidated cache pattern: {pattern}")
        except Exception as e:
            logger.error(f"Pattern invalidation error: {e}")
    
    @staticmethod
    def invalidate_related_caches(model_instance, action='update'):
        """Invalidate caches related to model changes"""
        if isinstance(model_instance, AdvertisementModel):
            CacheInvalidator._invalidate_advertisement_caches(model_instance, action)
        elif isinstance(model_instance, CustomUserModel):
            CacheInvalidator._invalidate_user_caches(model_instance, action)
    
    @staticmethod
    def _invalidate_advertisement_caches(ad, action):
        """Invalidate advertisement-related caches"""
        # Direct caches
        cache.delete(f"advertisement:{ad.id}")
        
        # List caches
        CacheInvalidator.invalidate_by_pattern("ad_list:*")
        
        # Category-specific caches
        if ad.sub_category:
            cache.delete(f"category_ads:{ad.sub_category.main_category.id}")
        
        # Location-specific caches
        if hasattr(ad, 'location') and ad.location:
            cache.delete(f"location_ads:{ad.location.city_id}")
        
        # User-specific caches
        cache.delete(f"user_ads:{ad.user_id}")
        
        # Featured ads if applicable
        if ad.is_featured:
            cache.delete("featured_advertisements")
    
    @staticmethod
    def _invalidate_user_caches(user, action):
        """Invalidate user-related caches"""
        cache.delete(f"user_profile:{user.id}")
        cache.delete(f"user_dashboard:{user.id}")
        cache.delete(f"user_ads:{user.id}")
        cache.delete(f"user_favorites:{user.id}")

# Signal handlers for automatic cache invalidation
@receiver(post_save, sender=AdvertisementModel)
def invalidate_advertisement_cache_on_save(sender, instance, created, **kwargs):
    """Invalidate advertisement caches on save"""
    CacheInvalidator.invalidate_related_caches(instance, 'create' if created else 'update')

@receiver(post_delete, sender=AdvertisementModel)
def invalidate_advertisement_cache_on_delete(sender, instance, **kwargs):
    """Invalidate advertisement caches on delete"""
    CacheInvalidator.invalidate_related_caches(instance, 'delete')

@receiver(post_save, sender=CustomUserModel)
def invalidate_user_cache_on_save(sender, instance, created, **kwargs):
    """Invalidate user caches on save"""
    CacheInvalidator.invalidate_related_caches(instance, 'create' if created else 'update')
```

### Time-Based Invalidation
```python
# app/caching/time_based.py
from datetime import datetime, timedelta
from celery import shared_task

class TimeBasedInvalidation:
    """Time-based cache invalidation strategies"""
    
    @staticmethod
    def set_with_expiry(cache_key, data, base_timeout, jitter_percent=10):
        """Set cache with jittered expiry to prevent thundering herd"""
        import random
        
        # Add random jitter to prevent simultaneous expiry
        jitter = random.uniform(-jitter_percent/100, jitter_percent/100)
        timeout = int(base_timeout * (1 + jitter))
        
        cache.set(cache_key, data, timeout)
        
        # Schedule refresh before expiry
        refresh_time = timeout - (timeout * 0.1)  # Refresh at 90% of timeout
        schedule_cache_refresh.apply_async(
            args=[cache_key],
            countdown=refresh_time
        )

@shared_task
def schedule_cache_refresh(cache_key):
    """Background task to refresh cache before expiry"""
    # Check if cache still exists
    if cache.get(cache_key) is not None:
        # Trigger refresh logic based on cache key pattern
        if cache_key.startswith('advertisement:'):
            ad_id = cache_key.split(':')[1]
            AdvertisementCache.get_advertisement(ad_id)
        elif cache_key.startswith('user_profile:'):
            user_id = cache_key.split(':')[1]
            SessionCache.get_user_profile(user_id)

@shared_task
def cleanup_expired_caches():
    """Periodic task to clean up expired cache entries"""
    # This would typically be handled by Redis automatically,
    # but we can add custom cleanup logic here
    logger.info("Cache cleanup task executed")
```

## 📈 Cache Monitoring & Analytics

### Cache Performance Monitoring
```python
# app/caching/monitoring.py
import time
from django.core.cache import cache
from django.conf import settings

class CacheMonitor:
    """Monitor cache performance and statistics"""
    
    @staticmethod
    def get_cache_stats():
        """Get cache statistics"""
        try:
            # Redis-specific stats
            redis_client = cache._cache.get_client(1)
            info = redis_client.info()
            
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': CacheMonitor._calculate_hit_rate(
                    info.get('keyspace_hits', 0),
                    info.get('keyspace_misses', 0)
                ),
                'total_commands_processed': info.get('total_commands_processed', 0),
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}
    
    @staticmethod
    def _calculate_hit_rate(hits, misses):
        """Calculate cache hit rate"""
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0
    
    @staticmethod
    def monitor_cache_operation(operation_name):
        """Decorator to monitor cache operations"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                except Exception as e:
                    result = None
                    success = False
                    logger.error(f"Cache operation {operation_name} failed: {e}")
                
                end_time = time.time()
                duration = end_time - start_time
                
                # Log performance metrics
                logger.info(
                    f"Cache operation: {operation_name}, "
                    f"Duration: {duration:.3f}s, "
                    f"Success: {success}"
                )
                
                return result
            
            return wrapper
        return decorator

# Cache health check
class CacheHealthCheck:
    """Health check for cache system"""
    
    @staticmethod
    def check_cache_health():
        """Perform cache health check"""
        try:
            # Test basic operations
            test_key = "health_check_test"
            test_value = "test_value"
            
            # Set operation
            cache.set(test_key, test_value, 60)
            
            # Get operation
            retrieved_value = cache.get(test_key)
            
            # Delete operation
            cache.delete(test_key)
            
            # Verify operations
            if retrieved_value == test_value:
                return {
                    'status': 'healthy',
                    'message': 'Cache is working properly'
                }
            else:
                return {
                    'status': 'unhealthy',
                    'message': 'Cache get operation failed'
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Cache health check failed: {str(e)}'
            }
```

## 🛠️ Cache Management Commands

### Django Management Commands
```python
# management/commands/cache_management.py
from django.core.management.base import BaseCommand
from django.core.cache import cache
from app.caching.advertisement_cache import CacheWarmer

class Command(BaseCommand):
    help = 'Cache management utilities'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['warm', 'clear', 'stats', 'health'],
            help='Cache management action'
        )
        parser.add_argument(
            '--pattern',
            type=str,
            help='Cache key pattern for selective operations'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'warm':
            self.warm_caches()
        elif action == 'clear':
            self.clear_caches(options.get('pattern'))
        elif action == 'stats':
            self.show_stats()
        elif action == 'health':
            self.check_health()
    
    def warm_caches(self):
        """Warm up application caches"""
        self.stdout.write("Warming up caches...")
        
        CacheWarmer.warm_advertisement_caches()
        CacheWarmer.warm_category_caches()
        
        self.stdout.write(
            self.style.SUCCESS("Cache warming completed")
        )
    
    def clear_caches(self, pattern=None):
        """Clear caches"""
        if pattern:
            cache.delete_pattern(pattern)
            self.stdout.write(f"Cleared caches matching pattern: {pattern}")
        else:
            cache.clear()
            self.stdout.write("All caches cleared")
    
    def show_stats(self):
        """Show cache statistics"""
        from app.caching.monitoring import CacheMonitor
        
        stats = CacheMonitor.get_cache_stats()
        
        self.stdout.write("Cache Statistics:")
        for key, value in stats.items():
            self.stdout.write(f"  {key}: {value}")
    
    def check_health(self):
        """Check cache health"""
        from app.caching.monitoring import CacheHealthCheck
        
        health = CacheHealthCheck.check_cache_health()
        
        if health['status'] == 'healthy':
            self.stdout.write(
                self.style.SUCCESS(f"Cache Health: {health['message']}")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"Cache Health: {health['message']}")
            )
```

---

This caching strategy guide provides comprehensive caching implementation patterns, Redis configuration, intelligent invalidation strategies, and monitoring tools for optimal performance in the Soodam backend system.
