# Security Guidelines

## 🔒 Overview

This document outlines comprehensive security guidelines, best practices, and implementation details for the Soodam backend. Security is implemented at multiple layers including authentication, authorization, data protection, and infrastructure security.

## 🛡️ Security Architecture

### Defense in Depth
```
┌─────────────────────────────────────────┐
│           Infrastructure               │
│  ├── Firewall & Network Security      │
│  ├── Load Balancer & DDoS Protection  │
│  └── SSL/TLS Termination              │
├─────────────────────────────────────────┤
│           Application Layer            │
│  ├── Authentication & Authorization   │
│  ├── Input Validation & Sanitization  │
│  ├── Rate Limiting & Throttling       │
│  └── Security Headers                 │
├─────────────────────────────────────────┤
│           Data Layer                   │
│  ├── Encryption at Rest               │
│  ├── Database Security                │
│  ├── Backup Encryption                │
│  └── Access Controls                  │
└─────────────────────────────────────────┘
```

## 🔐 Authentication & Authorization

### JWT Security Implementation
```python
# config/jwt_config.py
import jwt
from datetime import datetime, timedelta
from django.conf import settings
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class JWTSecurity:
    """Secure JWT implementation"""
    
    @staticmethod
    def generate_secure_key():
        """Generate cryptographically secure key"""
        import secrets
        return secrets.token_urlsafe(64)
    
    @staticmethod
    def create_token(payload, token_type='access'):
        """Create secure JWT token"""
        now = datetime.utcnow()
        
        # Token expiration based on type
        if token_type == 'access':
            exp = now + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        elif token_type == 'refresh':
            exp = now + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        else:
            exp = now + timedelta(hours=1)
        
        # Standard claims
        token_payload = {
            'iat': now,
            'exp': exp,
            'iss': settings.JWT_ISSUER,
            'aud': settings.JWT_AUDIENCE,
            'jti': secrets.token_urlsafe(16),  # Unique token ID
            'token_type': token_type,
            **payload
        }
        
        return jwt.encode(
            token_payload,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
    
    @staticmethod
    def verify_token(token, token_type='access'):
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM],
                audience=settings.JWT_AUDIENCE,
                issuer=settings.JWT_ISSUER
            )
            
            # Verify token type
            if payload.get('token_type') != token_type:
                raise jwt.InvalidTokenError('Invalid token type')
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise jwt.InvalidTokenError('Token has expired')
        except jwt.InvalidTokenError as e:
            raise jwt.InvalidTokenError(f'Invalid token: {str(e)}')
```

### Password Security
```python
# app/security/password_security.py
import hashlib
import secrets
from django.contrib.auth.hashers import Argon2PasswordHasher
from django.core.exceptions import ValidationError
import re

class SecurePasswordHasher(Argon2PasswordHasher):
    """Enhanced Argon2 password hasher"""
    
    # Increased security parameters
    time_cost = 3
    memory_cost = 102400  # 100MB
    parallelism = 2

class PasswordValidator:
    """Comprehensive password validation"""
    
    MIN_LENGTH = 8
    MAX_LENGTH = 128
    
    @staticmethod
    def validate_password(password, user=None):
        """Validate password strength"""
        errors = []
        
        # Length check
        if len(password) < PasswordValidator.MIN_LENGTH:
            errors.append(f'Password must be at least {PasswordValidator.MIN_LENGTH} characters long')
        
        if len(password) > PasswordValidator.MAX_LENGTH:
            errors.append(f'Password must be no more than {PasswordValidator.MAX_LENGTH} characters long')
        
        # Complexity checks
        if not re.search(r'[A-Z]', password):
            errors.append('Password must contain at least one uppercase letter')
        
        if not re.search(r'[a-z]', password):
            errors.append('Password must contain at least one lowercase letter')
        
        if not re.search(r'\d', password):
            errors.append('Password must contain at least one digit')
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append('Password must contain at least one special character')
        
        # Common password check
        if PasswordValidator.is_common_password(password):
            errors.append('Password is too common')
        
        # User-specific checks
        if user:
            if PasswordValidator.is_similar_to_user_info(password, user):
                errors.append('Password is too similar to your personal information')
        
        if errors:
            raise ValidationError(errors)
        
        return True
    
    @staticmethod
    def is_common_password(password):
        """Check against common passwords list"""
        common_passwords = {
            'password', '123456', 'password123', 'admin', 'qwerty',
            'letmein', 'welcome', 'monkey', '1234567890'
        }
        return password.lower() in common_passwords
    
    @staticmethod
    def is_similar_to_user_info(password, user):
        """Check if password is similar to user information"""
        user_info = [
            user.username.lower(),
            user.email.lower().split('@')[0],
            user.first_name.lower(),
            user.last_name.lower()
        ]
        
        password_lower = password.lower()
        return any(info in password_lower or password_lower in info 
                  for info in user_info if info)
```

### Multi-Factor Authentication
```python
# app/security/mfa.py
import pyotp
import qrcode
from io import BytesIO
import base64

class MFAManager:
    """Multi-Factor Authentication manager"""
    
    @staticmethod
    def generate_secret():
        """Generate TOTP secret"""
        return pyotp.random_base32()
    
    @staticmethod
    def generate_qr_code(user, secret):
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user.email,
            issuer_name="Soodam"
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    @staticmethod
    def verify_totp(secret, token):
        """Verify TOTP token"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)  # Allow 30s window
```

## 🔍 Input Validation & Sanitization

### Request Validation
```python
# app/security/validation.py
import re
import html
from django.core.exceptions import ValidationError
from django.utils.html import strip_tags

class InputValidator:
    """Comprehensive input validation"""
    
    # Regex patterns
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^\+[1-9]\d{1,14}$')
    SLUG_PATTERN = re.compile(r'^[a-z0-9-]+$')
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not InputValidator.EMAIL_PATTERN.match(email):
            raise ValidationError('Invalid email format')
        return email.lower().strip()
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        if not InputValidator.PHONE_PATTERN.match(phone):
            raise ValidationError('Invalid phone number format')
        return phone
    
    @staticmethod
    def sanitize_html(content):
        """Sanitize HTML content"""
        # Remove all HTML tags
        content = strip_tags(content)
        # Escape remaining HTML entities
        content = html.escape(content)
        return content.strip()
    
    @staticmethod
    def validate_file_upload(file):
        """Validate file upload"""
        # Check file size (10MB limit)
        if file.size > 10 * 1024 * 1024:
            raise ValidationError('File size exceeds 10MB limit')
        
        # Check file type
        allowed_types = {
            'image/jpeg', 'image/png', 'image/webp',
            'video/mp4', 'video/quicktime',
            'application/pdf'
        }
        
        if file.content_type not in allowed_types:
            raise ValidationError('File type not allowed')
        
        # Check file extension
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.mp4', '.mov', '.pdf'}
        file_extension = file.name.lower().split('.')[-1]
        
        if f'.{file_extension}' not in allowed_extensions:
            raise ValidationError('File extension not allowed')
        
        return True
```

### SQL Injection Prevention
```python
# app/security/sql_security.py
from django.db import connection
from django.core.exceptions import ValidationError

class SQLSecurity:
    """SQL injection prevention utilities"""
    
    @staticmethod
    def validate_order_by(field_name, allowed_fields):
        """Validate ORDER BY field to prevent SQL injection"""
        # Remove potential SQL injection attempts
        clean_field = field_name.strip().lower()
        
        # Check for SQL keywords
        sql_keywords = ['union', 'select', 'insert', 'update', 'delete', 'drop', '--', ';']
        if any(keyword in clean_field for keyword in sql_keywords):
            raise ValidationError('Invalid field name')
        
        # Check against allowed fields
        if clean_field not in allowed_fields:
            raise ValidationError('Field not allowed for ordering')
        
        return clean_field
    
    @staticmethod
    def execute_safe_query(query, params):
        """Execute parameterized query safely"""
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            return cursor.fetchall()
```

## 🚦 Rate Limiting & DDoS Protection

### Rate Limiting Implementation
```python
# app/security/rate_limiting.py
import time
from django.core.cache import cache
from django.http import HttpResponseTooManyRequests
from functools import wraps

class RateLimiter:
    """Advanced rate limiting"""
    
    @staticmethod
    def rate_limit(max_requests=100, window=3600, key_func=None):
        """Rate limiting decorator"""
        def decorator(view_func):
            @wraps(view_func)
            def wrapper(request, *args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(request)
                else:
                    cache_key = f"rate_limit:{request.META.get('REMOTE_ADDR')}"
                
                # Get current request count
                current_requests = cache.get(cache_key, 0)
                
                if current_requests >= max_requests:
                    return HttpResponseTooManyRequests(
                        "Rate limit exceeded. Try again later."
                    )
                
                # Increment counter
                cache.set(cache_key, current_requests + 1, window)
                
                return view_func(request, *args, **kwargs)
            
            return wrapper
        return decorator
    
    @staticmethod
    def sliding_window_rate_limit(max_requests=100, window=3600):
        """Sliding window rate limiting"""
        def decorator(view_func):
            @wraps(view_func)
            def wrapper(request, *args, **kwargs):
                cache_key = f"sliding_rate_limit:{request.META.get('REMOTE_ADDR')}"
                current_time = time.time()
                
                # Get request timestamps
                timestamps = cache.get(cache_key, [])
                
                # Remove old timestamps
                timestamps = [ts for ts in timestamps if current_time - ts < window]
                
                if len(timestamps) >= max_requests:
                    return HttpResponseTooManyRequests(
                        "Rate limit exceeded. Try again later."
                    )
                
                # Add current timestamp
                timestamps.append(current_time)
                cache.set(cache_key, timestamps, window)
                
                return view_func(request, *args, **kwargs)
            
            return wrapper
        return decorator
```

## 🔒 Data Protection & Encryption

### Data Encryption
```python
# app/security/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
import base64
import os

class DataEncryption:
    """Data encryption utilities"""
    
    @staticmethod
    def generate_key():
        """Generate encryption key"""
        return Fernet.generate_key()
    
    @staticmethod
    def get_encryption_key():
        """Get encryption key from settings"""
        password = settings.ENCRYPTION_PASSWORD.encode()
        salt = settings.ENCRYPTION_SALT.encode()
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    @staticmethod
    def encrypt_data(data):
        """Encrypt sensitive data"""
        if not data:
            return data
        
        key = DataEncryption.get_encryption_key()
        f = Fernet(key)
        
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    @staticmethod
    def decrypt_data(encrypted_data):
        """Decrypt sensitive data"""
        if not encrypted_data:
            return encrypted_data
        
        key = DataEncryption.get_encryption_key()
        f = Fernet(key)
        
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception:
            return None

# Custom model field for encrypted data
from django.db import models

class EncryptedTextField(models.TextField):
    """Encrypted text field"""
    
    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return DataEncryption.decrypt_data(value)
    
    def to_python(self, value):
        if isinstance(value, str):
            return value
        if value is None:
            return value
        return DataEncryption.decrypt_data(value)
    
    def get_prep_value(self, value):
        if value is None:
            return value
        return DataEncryption.encrypt_data(value)
```

### Personal Data Protection (GDPR Compliance)
```python
# app/security/privacy.py
from django.db import models
from datetime import datetime, timedelta

class PersonalDataManager:
    """Personal data management for GDPR compliance"""
    
    @staticmethod
    def anonymize_user_data(user):
        """Anonymize user personal data"""
        user.email = f"deleted_user_{user.id}@example.com"
        user.phone_number = None
        user.first_name = "Deleted"
        user.last_name = "User"
        user.bio = ""
        user.avatar = None
        user.is_active = False
        user.save()
    
    @staticmethod
    def export_user_data(user):
        """Export all user data for GDPR compliance"""
        data = {
            'personal_info': {
                'email': user.email,
                'phone_number': user.phone_number,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined.isoformat(),
            },
            'advertisements': [
                {
                    'title': ad.title,
                    'description': ad.description,
                    'created_at': ad.created_at.isoformat(),
                }
                for ad in user.advertisements.all()
            ],
            'activity_logs': [
                {
                    'activity_type': log.activity_type,
                    'description': log.description,
                    'created_at': log.created_at.isoformat(),
                }
                for log in user.activity_logs.all()
            ]
        }
        return data
    
    @staticmethod
    def schedule_data_deletion(user, days=30):
        """Schedule user data deletion"""
        deletion_date = datetime.now() + timedelta(days=days)
        # Create deletion task
        from app.tasks import schedule_user_deletion
        schedule_user_deletion.apply_async(
            args=[user.id],
            eta=deletion_date
        )
```

## 🛡️ Security Headers & HTTPS

### Security Headers Configuration
```python
# config/security_middleware.py
from django.utils.deprecation import MiddlewareMixin

class SecurityHeadersMiddleware(MiddlewareMixin):
    """Add security headers to all responses"""
    
    def process_response(self, request, response):
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://fonts.gstatic.com; "
            "connect-src 'self' https://api.soodam.com; "
            "frame-ancestors 'none';"
        )
        
        # Security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # HSTS (only in production with HTTPS)
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
        
        return response
```

### HTTPS Configuration
```python
# config/settings/production.py
# Force HTTPS in production
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# HSTS settings
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Cookie security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax'

# Content type protection
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
```

## 🔍 Security Monitoring & Logging

### Security Event Logging
```python
# app/security/logging.py
import logging
from django.contrib.auth.signals import user_logged_in, user_login_failed
from django.dispatch import receiver

# Configure security logger
security_logger = logging.getLogger('security')

class SecurityEventLogger:
    """Log security-related events"""
    
    @staticmethod
    def log_login_attempt(user, success, ip_address, user_agent):
        """Log login attempt"""
        event_type = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
        security_logger.info(
            f"{event_type}: User {user.username if user else 'unknown'} "
            f"from {ip_address} using {user_agent}"
        )
    
    @staticmethod
    def log_permission_denied(user, resource, ip_address):
        """Log permission denied events"""
        security_logger.warning(
            f"PERMISSION_DENIED: User {user.username} attempted to access "
            f"{resource} from {ip_address}"
        )
    
    @staticmethod
    def log_suspicious_activity(user, activity, details, ip_address):
        """Log suspicious activity"""
        security_logger.error(
            f"SUSPICIOUS_ACTIVITY: User {user.username} - {activity} "
            f"Details: {details} from {ip_address}"
        )

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login"""
    SecurityEventLogger.log_login_attempt(
        user, True,
        request.META.get('REMOTE_ADDR'),
        request.META.get('HTTP_USER_AGENT')
    )

@receiver(user_login_failed)
def log_user_login_failed(sender, credentials, request, **kwargs):
    """Log failed user login"""
    SecurityEventLogger.log_login_attempt(
        None, False,
        request.META.get('REMOTE_ADDR'),
        request.META.get('HTTP_USER_AGENT')
    )
```

## 🚨 Security Testing

### Security Test Cases
```python
# tests/security/test_security.py
import pytest
from django.test import Client
from django.urls import reverse

class TestSecurityFeatures:
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection"""
        client = Client()
        
        # Attempt SQL injection in search parameter
        malicious_input = "'; DROP TABLE users; --"
        response = client.get(
            reverse('advertisement-list'),
            {'search': malicious_input}
        )
        
        # Should not cause server error
        assert response.status_code in [200, 400]
    
    def test_xss_protection(self):
        """Test XSS protection"""
        client = Client()
        
        # Attempt XSS in form data
        xss_payload = "<script>alert('XSS')</script>"
        response = client.post(
            reverse('advertisement-create'),
            {'title': xss_payload, 'description': 'Test'},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        # XSS should be sanitized
        if response.status_code == 201:
            # Check that script tags are removed
            assert '<script>' not in response.content.decode()
    
    def test_rate_limiting(self):
        """Test rate limiting"""
        client = Client()
        
        # Make multiple requests rapidly
        for i in range(110):  # Exceed rate limit
            response = client.get(reverse('advertisement-list'))
            if response.status_code == 429:
                break
        
        # Should eventually hit rate limit
        assert response.status_code == 429
    
    def test_csrf_protection(self):
        """Test CSRF protection"""
        client = Client()
        
        # Attempt POST without CSRF token
        response = client.post(
            reverse('advertisement-create'),
            {'title': 'Test', 'description': 'Test'}
        )
        
        # Should be rejected
        assert response.status_code == 403
```

## 📋 Security Checklist

### Pre-deployment Security Checklist
- [ ] All secrets stored in environment variables
- [ ] Database credentials rotated
- [ ] HTTPS enforced in production
- [ ] Security headers configured
- [ ] Rate limiting implemented
- [ ] Input validation in place
- [ ] SQL injection protection verified
- [ ] XSS protection implemented
- [ ] CSRF protection enabled
- [ ] File upload restrictions configured
- [ ] Error messages don't leak sensitive information
- [ ] Logging configured for security events
- [ ] Backup encryption enabled
- [ ] Access controls reviewed
- [ ] Dependency vulnerabilities scanned

### Regular Security Maintenance
- [ ] Security patches applied monthly
- [ ] Dependency updates reviewed
- [ ] Access logs monitored
- [ ] Failed login attempts tracked
- [ ] SSL certificates renewed
- [ ] Security headers tested
- [ ] Penetration testing conducted annually
- [ ] Security training completed by team

---

This security guide provides comprehensive protection strategies and implementation details for maintaining a secure Soodam backend system. Regular review and updates of these security measures are essential for ongoing protection.
