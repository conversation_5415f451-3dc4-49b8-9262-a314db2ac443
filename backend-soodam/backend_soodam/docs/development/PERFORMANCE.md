# Performance Optimization Guide

## ⚡ Overview

This guide covers comprehensive performance optimization strategies for the Soodam backend, including database optimization, caching strategies, API performance, and infrastructure scaling.

## 📊 Performance Monitoring

### Key Performance Metrics
```python
# app/monitoring/performance_metrics.py
import time
import psutil
from django.db import connection
from django.core.cache import cache
from django.conf import settings

class PerformanceMonitor:
    """Monitor application performance metrics"""
    
    @staticmethod
    def get_system_metrics():
        """Get system performance metrics"""
        return {
            'cpu_usage': psutil.cpu_percent(interval=1),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg(),
        }
    
    @staticmethod
    def get_database_metrics():
        """Get database performance metrics"""
        with connection.cursor() as cursor:
            # Active connections
            cursor.execute("SELECT count(*) FROM pg_stat_activity;")
            active_connections = cursor.fetchone()[0]
            
            # Database size
            cursor.execute("""
                SELECT pg_size_pretty(pg_database_size(current_database()));
            """)
            db_size = cursor.fetchone()[0]
            
            # Slow queries
            cursor.execute("""
                SELECT count(*) FROM pg_stat_statements 
                WHERE mean_time > 1000;
            """)
            slow_queries = cursor.fetchone()[0] if cursor.fetchone() else 0
            
        return {
            'active_connections': active_connections,
            'database_size': db_size,
            'slow_queries': slow_queries,
            'query_count': len(connection.queries),
        }
    
    @staticmethod
    def get_cache_metrics():
        """Get cache performance metrics"""
        cache_stats = cache.get_stats() if hasattr(cache, 'get_stats') else {}
        return {
            'cache_hits': cache_stats.get('hits', 0),
            'cache_misses': cache_stats.get('misses', 0),
            'cache_hit_ratio': cache_stats.get('hit_ratio', 0),
        }

# Performance monitoring middleware
class PerformanceMiddleware:
    """Middleware to track request performance"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Log slow requests
        if response_time > 1.0:  # Requests taking more than 1 second
            logger.warning(
                f"Slow request: {request.method} {request.path} "
                f"took {response_time:.2f}s"
            )
        
        # Add performance headers
        response['X-Response-Time'] = f"{response_time:.3f}s"
        response['X-DB-Queries'] = str(len(connection.queries))
        
        return response
```

## 🗄️ Database Optimization

### Query Optimization
```python
# app/optimization/database.py
from django.db import models
from django.db.models import Prefetch, F, Count, Q

class OptimizedQueryManager:
    """Optimized database queries"""
    
    @staticmethod
    def get_advertisements_optimized(filters=None):
        """Optimized advertisement listing query"""
        queryset = AdvertisementModel.objects.select_related(
            'user',
            'sub_category__main_category',
            'price',
            'location__city__province'
        ).prefetch_related(
            Prefetch(
                'images',
                queryset=AdvertisementImagesModel.objects.filter(
                    is_active=True
                ).order_by('display_order')
            ),
            'attribute_values'
        ).filter(
            status=1,
            is_active=True
        )
        
        # Apply filters efficiently
        if filters:
            if 'category_id' in filters:
                queryset = queryset.filter(sub_category__main_category_id=filters['category_id'])
            
            if 'price_min' in filters:
                queryset = queryset.filter(price__amount__gte=filters['price_min'])
            
            if 'price_max' in filters:
                queryset = queryset.filter(price__amount__lte=filters['price_max'])
            
            if 'location' in filters:
                queryset = queryset.filter(
                    Q(location__city__name__icontains=filters['location']) |
                    Q(location__province__name__icontains=filters['location'])
                )
        
        return queryset
    
    @staticmethod
    def get_user_dashboard_data(user):
        """Optimized user dashboard query"""
        # Use aggregation to get counts efficiently
        stats = user.advertisements.aggregate(
            total_ads=Count('id'),
            active_ads=Count('id', filter=Q(status=1)),
            pending_ads=Count('id', filter=Q(status=0)),
            total_views=models.Sum('view_count'),
            total_favorites=models.Sum('favorite_count')
        )
        
        # Get recent advertisements with minimal data
        recent_ads = user.advertisements.select_related('price').only(
            'id', 'title', 'status', 'created_at', 'view_count'
        ).order_by('-created_at')[:5]
        
        return {
            'statistics': stats,
            'recent_advertisements': recent_ads
        }

# Database indexes for performance
class DatabaseIndexes:
    """Database index recommendations"""
    
    RECOMMENDED_INDEXES = [
        # Advertisement indexes
        "CREATE INDEX CONCURRENTLY idx_ad_status_active ON app_advertisementmodel(status, is_active);",
        "CREATE INDEX CONCURRENTLY idx_ad_category_status ON app_advertisementmodel(sub_category_id, status);",
        "CREATE INDEX CONCURRENTLY idx_ad_user_status ON app_advertisementmodel(user_id, status);",
        "CREATE INDEX CONCURRENTLY idx_ad_created_status ON app_advertisementmodel(created_at DESC, status);",
        "CREATE INDEX CONCURRENTLY idx_ad_featured ON app_advertisementmodel(is_featured, status, created_at DESC);",
        
        # Price indexes
        "CREATE INDEX CONCURRENTLY idx_price_amount ON app_advertisementpricemodel(amount);",
        "CREATE INDEX CONCURRENTLY idx_price_range ON app_advertisementpricemodel(amount, currency);",
        
        # Location indexes (PostGIS)
        "CREATE INDEX CONCURRENTLY idx_location_geom ON app_advertisementlocationmodel USING GIST(geolocation);",
        "CREATE INDEX CONCURRENTLY idx_location_city ON app_advertisementlocationmodel(city_id);",
        
        # Search indexes
        "CREATE INDEX CONCURRENTLY idx_ad_title_search ON app_advertisementmodel USING gin(to_tsvector('english', title));",
        "CREATE INDEX CONCURRENTLY idx_ad_desc_search ON app_advertisementmodel USING gin(to_tsvector('english', description));",
        
        # User indexes
        "CREATE INDEX CONCURRENTLY idx_user_email_active ON app_customusermodel(email, is_active);",
        "CREATE INDEX CONCURRENTLY idx_user_phone_active ON app_customusermodel(phone_number, is_active);",
        
        # Activity log indexes
        "CREATE INDEX CONCURRENTLY idx_activity_user_date ON app_useractivitylogmodel(user_id, created_at DESC);",
        "CREATE INDEX CONCURRENTLY idx_activity_type_date ON app_useractivitylogmodel(activity_type, created_at DESC);",
    ]
```

### Connection Pooling
```python
# config/database_pool.py
import os
from django.conf import settings

# Database connection pooling configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
            'CONN_MAX_AGE': 600,  # 10 minutes
            'CONN_HEALTH_CHECKS': True,
        },
        'CONN_MAX_AGE': 600,
    }
}

# Read replica configuration for read-heavy operations
if settings.USE_READ_REPLICA:
    DATABASES['replica'] = {
        **DATABASES['default'],
        'HOST': os.getenv('DB_REPLICA_HOST'),
    }
    
    DATABASE_ROUTERS = ['app.routers.DatabaseRouter']

# Database router for read/write splitting
class DatabaseRouter:
    """Route reads to replica, writes to primary"""
    
    def db_for_read(self, model, **hints):
        """Reading from the replica database."""
        if settings.USE_READ_REPLICA:
            return 'replica'
        return 'default'
    
    def db_for_write(self, model, **hints):
        """Writing to the primary database."""
        return 'default'
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that migrations go to primary."""
        return db == 'default'
```

## 🚀 Caching Strategies

### Multi-Level Caching
```python
# app/caching/cache_manager.py
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.conf import settings
import hashlib
import json

class CacheManager:
    """Advanced caching manager"""
    
    # Cache timeouts
    CACHE_TIMEOUTS = {
        'short': 300,      # 5 minutes
        'medium': 1800,    # 30 minutes
        'long': 3600,      # 1 hour
        'very_long': 86400, # 24 hours
    }
    
    @staticmethod
    def get_cache_key(prefix, *args, **kwargs):
        """Generate consistent cache key"""
        key_data = f"{prefix}:{':'.join(map(str, args))}"
        if kwargs:
            key_data += f":{hashlib.md5(json.dumps(kwargs, sort_keys=True).encode()).hexdigest()}"
        return key_data
    
    @staticmethod
    def cache_advertisement_list(filters, page, limit):
        """Cache advertisement list with filters"""
        cache_key = CacheManager.get_cache_key(
            'ad_list', page, limit, **filters
        )
        
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data
        
        # Generate data if not cached
        from app.services.advertisement_service import AdvertisementService
        service = AdvertisementService()
        data = service.get_advertisements(filters, page, limit)
        
        # Cache for 30 minutes
        cache.set(cache_key, data, CacheManager.CACHE_TIMEOUTS['medium'])
        return data
    
    @staticmethod
    def cache_user_profile(user_id):
        """Cache user profile data"""
        cache_key = f"user_profile:{user_id}"
        
        cached_profile = cache.get(cache_key)
        if cached_profile:
            return cached_profile
        
        from app.models import CustomUserModel
        try:
            user = CustomUserModel.objects.select_related(
                'province', 'city'
            ).get(id=user_id)
            
            profile_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.get_full_name(),
                'is_verified': user.is_verified,
            }
            
            # Cache for 1 hour
            cache.set(cache_key, profile_data, CacheManager.CACHE_TIMEOUTS['long'])
            return profile_data
            
        except CustomUserModel.DoesNotExist:
            return None
    
    @staticmethod
    def invalidate_advertisement_cache(advertisement_id):
        """Invalidate advertisement-related caches"""
        # Invalidate specific advertisement
        cache.delete(f"advertisement:{advertisement_id}")
        
        # Invalidate list caches (pattern-based deletion)
        cache.delete_pattern("ad_list:*")
        
        # Invalidate user's advertisement cache
        cache.delete_pattern(f"user_ads:*")
    
    @staticmethod
    def warm_cache():
        """Warm up frequently accessed caches"""
        # Cache popular advertisements
        popular_ads = AdvertisementModel.objects.filter(
            status=1, is_featured=True
        ).order_by('-view_count')[:20]
        
        for ad in popular_ads:
            cache_key = f"advertisement:{ad.id}"
            cache.set(cache_key, ad, CacheManager.CACHE_TIMEOUTS['long'])
        
        # Cache categories
        from app.models import MainCategoryModel
        categories = list(MainCategoryModel.objects.all())
        cache.set('categories', categories, CacheManager.CACHE_TIMEOUTS['very_long'])

# Cache decorators
from functools import wraps

def cache_result(timeout=300, key_prefix=''):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = CacheManager.get_cache_key(
                f"{key_prefix}:{func.__name__}", *args, **kwargs
            )
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result
        
        return wrapper
    return decorator
```

### Redis Configuration
```python
# config/redis_config.py
import redis
from django.conf import settings

# Redis connection pools
REDIS_POOLS = {
    'default': redis.ConnectionPool(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=0,
        max_connections=20,
        retry_on_timeout=True,
        socket_keepalive=True,
        socket_keepalive_options={},
    ),
    'sessions': redis.ConnectionPool(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=1,
        max_connections=10,
    ),
    'celery': redis.ConnectionPool(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=2,
        max_connections=15,
    ),
}

# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/0",
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 20,
                'retry_on_timeout': True,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'soodam',
        'TIMEOUT': 300,
    }
}
```

## 🔄 API Performance

### Response Optimization
```python
# app/optimization/api_optimization.py
from django.http import JsonResponse
from django.core.serializers.json import DjangoJSONEncoder
from rest_framework.pagination import PageNumberPagination
import gzip
import json

class OptimizedPagination(PageNumberPagination):
    """Optimized pagination with performance metrics"""
    
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        return JsonResponse({
            'items': data,
            'metadata': {
                'total': self.page.paginator.count,
                'page': self.page.number,
                'limit': self.get_page_size(self.request),
                'pages': self.page.paginator.num_pages,
                'has_next': self.page.has_next(),
                'has_prev': self.page.has_previous(),
            }
        })

class ResponseOptimizer:
    """Optimize API responses"""
    
    @staticmethod
    def compress_response(response):
        """Compress response data"""
        if len(response.content) > 1024:  # Only compress if > 1KB
            compressed_content = gzip.compress(response.content)
            if len(compressed_content) < len(response.content):
                response.content = compressed_content
                response['Content-Encoding'] = 'gzip'
                response['Content-Length'] = str(len(compressed_content))
        return response
    
    @staticmethod
    def optimize_json_response(data):
        """Optimize JSON serialization"""
        return json.dumps(
            data,
            cls=DjangoJSONEncoder,
            separators=(',', ':'),  # Remove whitespace
            ensure_ascii=False
        )

# API response middleware
class APIOptimizationMiddleware:
    """Middleware for API optimization"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # Only optimize API responses
        if request.path.startswith('/api/'):
            # Add caching headers
            if request.method == 'GET':
                response['Cache-Control'] = 'public, max-age=300'
                response['ETag'] = f'"{hash(response.content)}"'
            
            # Compress response
            if 'gzip' in request.META.get('HTTP_ACCEPT_ENCODING', ''):
                response = ResponseOptimizer.compress_response(response)
        
        return response
```

### Async Processing
```python
# app/optimization/async_processing.py
import asyncio
from asgiref.sync import sync_to_async
from django.db import models

class AsyncQueryManager:
    """Async database operations"""
    
    @sync_to_async
    def get_advertisements_async(self, filters):
        """Async advertisement retrieval"""
        return list(AdvertisementModel.objects.filter(**filters))
    
    @sync_to_async
    def get_user_async(self, user_id):
        """Async user retrieval"""
        return CustomUserModel.objects.get(id=user_id)
    
    async def get_dashboard_data_async(self, user_id):
        """Async dashboard data aggregation"""
        # Run multiple queries concurrently
        user_task = self.get_user_async(user_id)
        ads_task = self.get_advertisements_async({'user_id': user_id})
        
        user, advertisements = await asyncio.gather(user_task, ads_task)
        
        return {
            'user': user,
            'advertisements': advertisements,
            'stats': {
                'total_ads': len(advertisements),
                'active_ads': len([ad for ad in advertisements if ad.status == 1])
            }
        }

# Background task processing
from celery import shared_task

@shared_task
def process_heavy_computation(data):
    """Process heavy computations in background"""
    # Heavy processing logic
    result = perform_complex_calculation(data)
    
    # Cache result for quick access
    cache.set(f"computation_result:{data['id']}", result, 3600)
    
    return result

@shared_task
def update_advertisement_statistics():
    """Update advertisement statistics in background"""
    from app.models import AdvertisementModel, AdvertisementStatisticsModel
    
    # Batch update statistics
    ads = AdvertisementModel.objects.filter(status=1)
    
    for ad in ads.iterator(chunk_size=100):
        stats, created = AdvertisementStatisticsModel.objects.get_or_create(
            advertisement=ad
        )
        
        # Update statistics
        stats.total_views = ad.view_count
        stats.total_favorites = ad.favorite_count
        stats.save()
```

## 📈 Monitoring & Profiling

### Performance Profiling
```python
# app/monitoring/profiling.py
import cProfile
import pstats
from django.conf import settings
from functools import wraps

def profile_function(func):
    """Decorator to profile function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if settings.DEBUG:
            profiler = cProfile.Profile()
            profiler.enable()
            
            result = func(*args, **kwargs)
            
            profiler.disable()
            stats = pstats.Stats(profiler)
            stats.sort_stats('cumulative')
            stats.print_stats(10)  # Top 10 functions
            
            return result
        else:
            return func(*args, **kwargs)
    
    return wrapper

# Database query profiling
class QueryProfiler:
    """Profile database queries"""
    
    def __init__(self):
        self.queries = []
    
    def __enter__(self):
        from django.db import connection
        self.initial_queries = len(connection.queries)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        from django.db import connection
        self.queries = connection.queries[self.initial_queries:]
        
        if settings.DEBUG:
            total_time = sum(float(q['time']) for q in self.queries)
            print(f"Executed {len(self.queries)} queries in {total_time:.3f}s")
            
            # Show slow queries
            slow_queries = [q for q in self.queries if float(q['time']) > 0.1]
            for query in slow_queries:
                print(f"Slow query ({query['time']}s): {query['sql'][:100]}...")

# Usage example
@profile_function
def get_advertisement_data(ad_id):
    with QueryProfiler() as profiler:
        ad = AdvertisementModel.objects.select_related(
            'user', 'price', 'location'
        ).get(id=ad_id)
        return ad
```

## 🏗️ Infrastructure Optimization

### Load Balancing Configuration
```nginx
# nginx.conf - Load balancing
upstream backend_servers {
    least_conn;
    server backend1:8000 weight=3 max_fails=3 fail_timeout=30s;
    server backend2:8000 weight=3 max_fails=3 fail_timeout=30s;
    server backend3:8000 weight=2 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    server_name api.soodam.com;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
    
    # Static file caching
    location /static/ {
        alias /app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    location /media/ {
        alias /app/media/;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # API endpoints
    location /api/ {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection pooling
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
}
```

### Auto-scaling Configuration
```yaml
# docker-compose.yml - Auto-scaling
version: '3.8'

services:
  backend:
    build: .
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
    depends_on:
      - postgres
      - redis
```

## 📊 Performance Testing

### Load Testing Scripts
```python
# tests/performance/load_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class LoadTester:
    """Load testing utilities"""
    
    def __init__(self, base_url):
        self.base_url = base_url
        self.results = []
    
    async def make_request(self, session, endpoint):
        """Make async HTTP request"""
        start_time = time.time()
        try:
            async with session.get(f"{self.base_url}{endpoint}") as response:
                await response.text()
                end_time = time.time()
                
                self.results.append({
                    'endpoint': endpoint,
                    'status': response.status,
                    'response_time': end_time - start_time
                })
                
        except Exception as e:
            end_time = time.time()
            self.results.append({
                'endpoint': endpoint,
                'status': 'error',
                'response_time': end_time - start_time,
                'error': str(e)
            })
    
    async def run_load_test(self, endpoint, concurrent_requests=10, total_requests=100):
        """Run load test on endpoint"""
        connector = aiohttp.TCPConnector(limit=concurrent_requests)
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = []
            
            for _ in range(total_requests):
                task = self.make_request(session, endpoint)
                tasks.append(task)
            
            await asyncio.gather(*tasks)
        
        return self.analyze_results()
    
    def analyze_results(self):
        """Analyze load test results"""
        successful_requests = [r for r in self.results if r['status'] == 200]
        failed_requests = [r for r in self.results if r['status'] != 200]
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        return {
            'total_requests': len(self.results),
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': len(successful_requests) / len(self.results) * 100,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
        }

# Usage
async def test_api_performance():
    tester = LoadTester('http://localhost:8000')
    results = await tester.run_load_test('/api/advertisements/', 50, 1000)
    print(f"Success rate: {results['success_rate']:.2f}%")
    print(f"Average response time: {results['avg_response_time']:.3f}s")
```

---

This performance optimization guide provides comprehensive strategies for maximizing the performance and scalability of the Soodam backend system through database optimization, caching, API improvements, and infrastructure scaling.
