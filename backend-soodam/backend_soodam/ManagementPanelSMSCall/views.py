import json

from django.shortcuts import render

# Create your views here.
import requests

import urllib.request
try:
    import json
except ImportError:
    import simplejson as json

from fastapi import HTTPException
from config.utils import xml2json

import logging
logger = logging.getLogger(__name__)



class APIException(Exception):
    pass

class ManagementPanelKavenegar():
    def __init__(self):
        self.version = 'v1'
        self.host = 'api.kavenegar.com'
        self.apikey = '666B693367446638655A5769685950786244646634595A704F33533647397545316339636A644D637467733D'
        self.headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
            'charset': 'utf-8'
        }
        # self.api = KavenegarAPI('666B693367446638655A5769685950786244646634595A704F33533647397545316339636A644D637467733D')
        self.params = {'sender': '9982003474', 'receptor': '', 'message': ''}
    def _request(self, action, method, params=None):
        if params is None:
            params = {}
        url = 'https://' + self.host + '/' + self.version + '/' + self.apikey + '/' + action + '/' + method + '.json'
        try:
            content = requests.post(url , headers=self.headers,auth=None,data=params).content
            try:
                response = json.loads(content.decode("utf-8"))
                if response['return']['status']==200:
                    response=response['entries']
                else:
                    raise APIException((u'APIException[%s] %s' % (response['return']['status'],response['return']['message'])).encode('utf-8'))
            except ValueError as e:
                logger.error(f"Kavenegar exception : {e}")
                raise HTTPException(status_code=500, detail=f"Kavenegar Api Exception {e.encode().decode('UTF-8')}")
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Kavenegar exception : {e}")
            raise HTTPException(status_code=500, detail=f"Kavenegar Api Exception {e.encode().decode('UTF-8')}")

    async def sms_send(self, params=None):
        return self._request('sms', 'send',params)
    async def send_otp(self,receptor,otp_code):
        # try:
        self.params['receptor'] = receptor
        self.params['message'] =('اپلیکیشن سودم \n'
                                 'Code: {}'
                                 'را جهت ورود به پنل سودم وارد نمایید.').format(otp_code)
        res =await self.sms_send(self.params)
        logger.info(f"SMS Panel  Action receptor: {receptor} otp_code:{otp_code} status: {res}")
        return res
        # except APIException as e:
        #     logger.error(f"Kavenegar exception : {e}")
        #     raise HTTPException(status_code=500, detail=f"Kavenegar Api Exception {e.encode().decode('UTF-8')}")
        #
        # except HTTPException as e:
        #     logger.error(f"HTTP exception : {e}")
        #     raise HTTPException(status_code=500, detail=str(e))
        # except Exception as e:
        #     logger.error(f"Unknown exception : {e}")
        #     raise HTTPException(status_code=500, detail=str(e))

def singleton_object():
    return ManagementPanelKavenegar()


def GenerateTTS(phone_number, speaker, text, serverid):
    headers = {'content-type': 'application/x-www-form-urlencoded', }
    data = {'userName': '',
            'password': '',
            'text': text.__str__(),
            'speaker': speaker.__str__(),
            'title': phone_number.__str__(),
            'CallFromMobile': '',
            'serverid': serverid.__str__()}
    url = 'https://portal.avanak.ir/webservice3.asmx/GenerateTTS'
    # api = 'QuickSendWithTTS'
    data = urllib.parse.urlencode(data)
    data = data.encode('ascii')
    req = urllib.request.Request(url, data, headers)
    with urllib.request.urlopen(req) as response:
        detail_response = response.read()
    return output_respone(xml2json.main(detail_response))


def QuickSendWithTTS(phone_number, verify_code, serverid):
    headers = {'content-type': 'application/x-www-form-urlencoded', }
    data = {'userName': '',
            'password': '',
            'text': 'به سدام خوشْآمَدْیدْ.  کُدِ تأییدِ سدام' + verify_code.__str__() + 'میباشدْ ',
            'number': phone_number.__str__(),
            'vote': 'false',
            'serverid': serverid.__str__()}
    url = 'https://portal.avanak.ir/webservice3.asmx/QuickSendWithTTS'
    # api = 'QuickSendWithTTS'
    data = urllib.parse.urlencode(data)
    with urllib.request.urlopen(url, data, headers) as response:
        the_page = response.read()
    return the_page


def SendOTP(phone_number, text, length, serverid):
    headers = {'content-type': 'application/x-www-form-urlencoded', }

    data = {'userName': '',
            'password': '',
            'text': text.__str__(),
            'number': phone_number.__str__(),
            'Length': length,
            'serverid': serverid}
    url = 'https://portal.avanak.ir/webservice3.asmx/SendOTP'
    data = urllib.parse.urlencode(data)
    with urllib.request.urlopen(url, data, headers) as response:
        the_page = response.read()
    return the_page


def QuickSend(phone_number, message_id, serverid):
    headers = {'content-type': 'application/x-www-form-urlencoded', }
    data = {'userName': '',
            'password': '',
            'messageId': message_id.__str__(),
            'number': phone_number.__str__(),
            'vote': 'false',
            'serverid': serverid.__str__()}
    url = 'https://portal.avanak.ir/webservice3.asmx/QuickSend'
    data = urllib.parse.urlencode(data)
    data = data.encode('ascii')
    req = urllib.request.Request(url, data, headers)
    with urllib.request.urlopen(req) as response:
        detail_response = response.read()
    return output_respone(xml2json.main(detail_response))


def output_respone(string_example):
    response_dictionry = json.loads(string_example)
    res = {}
    for i in response_dictionry.keys():
        res[i] = response_dictionry[i]
    return res


class GenerateTextForTTS():
    def __init__(self, text='123456'):
        self.txt_input = text
        self.first_verify_code = None
        self.second_verify_code = None
        self.text_of_school_verfication_code = None
        self.text_of_soodam_verfication_code = ''
        # self.text_of__verfication_code =''
        # self.text_of_school_verfication_code =''

    def split_verify_code(self):
        string = ''
        for i in range(len(self.txt_input)):
            if i == len(self.txt_input) - 1:
                string = string + self.txt_input[i]
            else:
                string = string + self.txt_input[i] + ','
        self.first_verify_code = string
        string_2 = ''
        for i in range(0, len(self.txt_input), 2):
            if i == len(self.txt_input) - 2:
                string_2 = string_2 + self.txt_input[i:i + 2]
            else:
                string_2 = string_2 + self.txt_input[i:i + 2] + ','
        self.second_verify_code = string_2

    def get_text_of_soodam_verfication_code(self):
        return 'با سلام . کدِ تأییدِ  سِرویسِ  سدام ' + self.first_verify_code + ' .تکرار میشود .' + self.second_verify_code


class HandlerError():
    def __init__(self):
        self.generateTTS = {-1: 'authentication', -2: 'upload failed', -3: 'low credit', -4: 'connection failed to TTS',
                            -5: 'most character of 1000', -6: 'out of time to send ', }
        self.quicksend = {-1: 'authentication', -2: 'phone_number is incorrect', -3: 'low credit',
                          -6: 'out of time to send ', }

    def handler_generateTTS_error(self, error_code):
        if error_code is self.generateTTS.keys():
            return False
        else:
            return True

    def handler_quicksend_error(self, error_code):
        if error_code is self.quicksend.keys():
            return False
        else:
            return True


class HandlerRequest:
    def __init__(self, phone_number, verify_text):
        self.verify_code = verify_text
        self.phone_number = phone_number
        self.handler_error_object = HandlerError()

    def call_verify_soodam(self):
        GTFTT = GenerateTextForTTS(self.verify_code)
        GTFTT.split_verify_code()
        # text_to_send = GTFTT.get_text_of_school_verfication_code()
        response_gtts = GenerateTTS(self.phone_number, 'famale', GTFTT.get_text_of_soodam_verfication_code(), '15')
        if self.handler_error_object.handler_generateTTS_error(response_gtts[i] for i in response_gtts.keys()):
            voice_id = 0
            for i in response_gtts.keys():
                voice_id = response_gtts[i]
            response_quicksend = QuickSend(self.phone_number, voice_id, '15')
            if self.handler_error_object.handler_quicksend_error(
                    response_quicksend[i] for i in response_quicksend.keys()):
                return 200
        else:
            return 203
