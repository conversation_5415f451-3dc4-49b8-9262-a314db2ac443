from kavenegar import *
from urllib.error import HTT<PERSON><PERSON><PERSON><PERSON>


def send_sms_with_template(receptor, tokens: dict, template):
    """
        sending sms that needs template
    """
    try:
        api = KavenegarAPI(
            '4A324E4A5044455A6F42486D30656754597A6C70695A694C664C69566A7352343232644A752F704D4962383D'
        )
        params = {
            'receptor': receptor,
            'template': template,
        }
        for key, value in tokens.items():
            params[key] = value

        response = api.verify_lookup(params)
        print(response)
        return True
    except APIException as e:
        print(e)
        return False
    except HTTPError as e:
        print(e)
        return False


def send_sms_normal(receptor, message):
    try:
        api = KavenegarAPI(
            '4A324E4A5044455A6F42486D30656754597A6C70695A694C664C69566A7352343232644A752F704D4962383D')
        params_buyer = {
            'receptor': receptor,
            'message': message,
            'sender': '10005000505077'
        }
        response = api.sms_send(params_buyer)
        print(response)
    except APIException as e:
        print(e)
    except HTTPError as e:
        print(e)
