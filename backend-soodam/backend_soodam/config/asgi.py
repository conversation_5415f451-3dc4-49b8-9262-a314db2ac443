"""ASGI config for config project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/howto/deployment/asgi/
"""

import os
import asyncio
import logging
from django.core.asgi import get_asgi_application
from django.conf import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

"""
Settings
"""
env_state = os.getenv("ENV_STATE", "production")
if env_state == "production":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")
elif env_state == "staging":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.staging")
else:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")

"""
Django settings
"""
django_app = get_asgi_application()

"""
FastAPI settings
"""
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from app.routers import auth_router, blogs_router, gis_geolocation_router, users_router, home_router, contact_router
from app.routers.admin import admin_router, admin_versioned_router
from app.routers.advertisement import advertisement_router
from app.routers.health import health_router
from app.routers.security import security_router
from app.routers.graphql import graphql_router
from app.routers.websocket import websocket_router
from app.routers.notification import notification_router
from app.routers.chat import chat_router
from app.routers.image import image_router
from app.routers.search import search_router
from app.routers.autocomplete import autocomplete_router
from app.routers.recommendations import recommendations_router
from app.routers.analytics import analytics_router
from app.middleware.pagination import PaginationMiddleware
from app.middleware.monitoring import setup_monitoring
from app.middleware.analytics import setup_analytics
from app.core.logging import setup_logging
from app.core.cache import setup_caching
from app.core.rate_limit import setup_rate_limiting
from app.core.openapi import setup_openapi
from app.core.i18n import setup_i18n
from app.core.prometheus import setup_prometheus
from app.core.db_pool import setup_connection_pool
from app.core.elasticsearch import setup_elasticsearch
from app.core.autocomplete import setup_autocomplete
from app.middleware.security import SecurityMiddleware


origins = [
    '*',
    # "https://soodam.com",
    # "http://**************",
    # "http://localhost",
    "http://localhost:8000",
    "http://localhost:3000",
]
# version_api = APIRouter()

fastapi_app = FastAPI(
    title="Soodam API",
    description="""
    # Soodam Backend API

    This is the API documentation for the Soodam application backend.

    ## Features
    * **User Management**: Registration, authentication, and profile management
    * **Advertisement Management**: Create, read, update, and delete advertisements
    * **Blog Management**: Create, read, update, and delete blog posts
    * **Admin Management**: Admin dashboard, user management, content moderation
    * **Geolocation Services**: Location-based services and search

    ## Authentication

    Most endpoints require authentication using JWT tokens. To authenticate:

    1. Use the `/api/auth/login` endpoint to obtain a token
    2. Include the token in the `Authorization` header as `Bearer {token}`

    ## API Versioning

    API versioning is supported through the `Accept` header:

    ```
    Accept: application/json; version=1.0
    ```

    Available versions: 1.0, 2.0
    """,
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    swagger_ui_parameters={
        "persistAuthorization": True,
        "displayRequestDuration": True,
        "filter": True,
        "syntaxHighlight.theme": "monokai"
    }
)

# Set up CORS middleware
fastapi_app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Add middleware...

# Include routers...

# Debug static files paths
static_dir = os.path.abspath("static")
media_dir = os.path.abspath("media")
# logger.info(f"Static directory: {static_dir}")
# logger.info(f"Media directory: {media_dir}")

# Check if directories exist
if not os.path.exists(static_dir):
    # logger.warning(f"Static directory does not exist: {static_dir}")
    os.makedirs(static_dir, exist_ok=True)
    # logger.info(f"Created static directory: {static_dir}")

if not os.path.exists(media_dir):
    # logger.warning(f"Media directory does not exist: {media_dir}")
    os.makedirs(media_dir, exist_ok=True)
    # logger.info(f"Created media directory: {media_dir}")

# Mount static files with error handling for FastAPI
try:
    fastapi_app.mount("/static", StaticFiles(directory=static_dir), name="static")
    # logger.info("Successfully mounted static files for FastAPI")
except Exception as e:
    logger.error(f"Failed to mount static files for FastAPI: {str(e)}")

# Mount media files with error handling for FastAPI
try:
    fastapi_app.mount("/media", StaticFiles(directory=media_dir), name="media")
    # logger.info("Successfully mounted media files for FastAPI")
except Exception as e:
    logger.error(f"Failed to mount media files for FastAPI: {str(e)}")


# Add the security middleware (add this before other middleware)
fastapi_app.add_middleware(SecurityMiddleware)

# Set up pagination middleware
fastapi_app.add_middleware(PaginationMiddleware)

# Set up logging
setup_logging(fastapi_app, exclude_paths=["/api/health", "/metrics", "/api/docs", "/api/redoc"])

# Set up monitoring
setup_monitoring(fastapi_app, exclude_paths=["/api/health", "/metrics", "/api/docs", "/api/redoc"])

# Set up analytics
setup_analytics(fastapi_app)

# Set up caching
# setup_caching(
#     fastapi_app,
#     cache_paths=[],
#     # cache_paths=["/api/geolocation", "/api/advertisements", "/api/blogs"],
#     exclude_paths=["/api/health", "/metrics", "/api/docs", "/api/redoc", "/api/admin"],
#     timeout=300  # 5 minutes
# )

# Set up rate limiting with stricter settings
if os.getenv("ENV_STATE") != "local":  # Only enable rate limiting in non-local environments
    setup_rate_limiting(
        fastapi_app,
        limit=50,  # 50 requests per minute by default (reduced from 100)
        window=60,  # 1 minute window
        groups={
            "default": {"limit": 100, "window": 60},
            "auth": {"limit": 100, "window": 60},  # 5 requests per minute for auth endpoints (reduced from 10)
            "admin": {"limit": 100, "window": 60},  # 100 requests per minute for admin endpoints (reduced from 200)
            "sensitive": {"limit": 100, "window": 60}  # Very strict for sensitive endpoints
        },
        path_groups={
            "/api/auth": "auth",
            "/api/admin": "admin",
            "/api/user/edit_user_info": "sensitive"
        },
        exclude_paths=["/api/health", "/metrics", "/api/docs", "/api/redoc"],
        block_threshold=100  # Block after 5 violations
    )

# Set up custom OpenAPI documentation
setup_openapi(fastapi_app)

# Set up internationalization
setup_i18n(fastapi_app)

# Set up Prometheus metrics
setup_prometheus(fastapi_app, exclude_paths=["/metrics", "/api/health", "/api/docs", "/api/redoc"])

# Set up database connection pooling
setup_connection_pool(min_connections=5, max_connections=20)

# Set up Elasticsearch
setup_elasticsearch()

# Set up autocomplete
setup_autocomplete()

# These are async functions, so we need to create and run a task for them
# async def setup_async_services():
#     await setup_elasticsearch()
#     await setup_autocomplete()
#
# # Create a task for the async setup
# loop = asyncio.get_event_loop()
# loop.create_task(setup_async_services())

# routers
fastapi_app.include_router(home_router, tags=["home"], prefix="")

fastapi_app.include_router(users_router, tags=["users"], prefix="/api/user")
fastapi_app.include_router(auth_router, tags=["auth"], prefix="/api/auth")
# fastapi_app.include_router(health_router)
fastapi_app.include_router(gis_geolocation_router, tags=["gis_meta"], prefix="/api/geolocation")
# Include the consolidated advertisement router
fastapi_app.include_router(advertisement_router, tags=["advertisements"], prefix="/api/advertisements")
fastapi_app.include_router(blogs_router, tags=["blogs"], prefix="/api/blogs")
# fastapi_app.include_router(notification_router, tags=["notification"], prefix="/notification")
# fastapi_app.include_router(blogs_router, tags=["notes"], prefix="/blogs")

# Include both the original admin router (for backward compatibility) and the versioned router
fastapi_app.include_router(admin_router, tags=["admin"], prefix="/api/admin")

# Include the versioned admin router
admin_versioned_router.include_router(fastapi_app)

# Include the health router
fastapi_app.include_router(health_router, prefix="/api")

# Include the security router
fastapi_app.include_router(security_router, tags=["security"], prefix="/api/security")

# Include the GraphQL router
fastapi_app.include_router(graphql_router, prefix="/api/graphql")

# Include the notification router
fastapi_app.include_router(notification_router, prefix="/api/notifications")

# Include the chat router
fastapi_app.include_router(chat_router, prefix="/api/chat")

# Include the image router
fastapi_app.include_router(image_router, prefix="/api/images")

# Include the search router
fastapi_app.include_router(search_router, prefix="/api/search")

# Include the autocomplete router
fastapi_app.include_router(autocomplete_router, prefix="/api/autocomplete")

# Include the recommendations router
fastapi_app.include_router(recommendations_router, prefix="/api/recommendations")

# Include the analytics router
fastapi_app.include_router(analytics_router, prefix="/api/analytics")

# Include the WebSocket router
fastapi_app.include_router(websocket_router)

# Include the contact router
fastapi_app.include_router(contact_router,prefix="/api/contact")

# to mount Django
# fastapi_app.mount("/django", django_app)
# fastapi_app.mount("/", StaticFiles(directory="templates"), name="static")
fastapi_app.mount("/static", StaticFiles(directory="static"), name="static")
fastapi_app.mount("/media", StaticFiles(directory="media"), name="media")
