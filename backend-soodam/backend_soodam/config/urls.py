
from django.conf import settings
from django.contrib import admin
from django.contrib.staticfiles.urls import static, staticfiles_urlpatterns
from django.urls import path, re_path
from django.views.static import serve

urlpatterns = [
    path("admin/", admin.site.urls),
]

# Add static files URLs
urlpatterns += staticfiles_urlpatterns()

# Explicitly serve static files in all environments
urlpatterns += [
    re_path(r'^static/(?P<path>.*)$', serve, {
        'document_root': settings.STATIC_ROOT,
    }),
]

# Explicitly serve media files in all environments
urlpatterns += [
    re_path(r'^media/(?P<path>.*)$', serve, {
        'document_root': settings.MEDIA_ROOT,
    }),
]

# Add media files URLs if in debug mode (for development)
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
