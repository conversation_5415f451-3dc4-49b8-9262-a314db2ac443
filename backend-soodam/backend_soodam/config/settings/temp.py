import os
from typing import List

from .base import *  # noqa

# Override the INSTALLED_APPS to remove GIS
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 'django.contrib.gis',  # Removed GIS
    "django_jalali",
    "app.apps.AppConfig",
    "ManagementPanelSMSCall.apps.AppConfig"
]

DEBUG = True

ALLOWED_HOSTS: List[str] = ["*"]

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",  # Use SQLite instead of PostGIS
        "NAME": os.path.join(BASE_DIR, "db.sqlite3"),
    }
}
