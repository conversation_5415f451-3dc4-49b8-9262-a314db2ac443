"""
Database migrations module for the <PERSON>dam backend.

This module provides utilities for managing database migrations.
"""

import logging
import os
import sys
from typing import List, Optional, Tuple

from django.core.management import call_command
from django.db import connection
from django.db.migrations.executor import MigrationExecutor
from django.db.migrations.recorder import MigrationRecorder

logger = logging.getLogger(__name__)

def get_applied_migrations() -> List[Tuple[str, str]]:
    """
    Get a list of applied migrations.
    
    Returns:
        List[Tuple[str, str]]: A list of (app_label, migration_name) tuples
    """
    recorder = MigrationRecorder(connection)
    return list(recorder.migration_qs.values_list('app', 'name'))

def get_unapplied_migrations() -> List[Tuple[str, str]]:
    """
    Get a list of unapplied migrations.
    
    Returns:
        List[Tuple[str, str]]: A list of (app_label, migration_name) tuples
    """
    executor = MigrationExecutor(connection)
    targets = executor.loader.graph.leaf_nodes()
    plan = executor.migration_plan(targets)
    return [(migration.app_label, migration.name) for migration, _ in plan]

def apply_migrations(app_label: Optional[str] = None, interactive: bool = False) -> None:
    """
    Apply database migrations.
    
    Args:
        app_label: The app label to migrate (None for all apps)
        interactive: Whether to prompt for input
    """
    logger.info(f"Applying migrations for {'all apps' if app_label is None else app_label}")
    
    try:
        call_command('migrate', app_label=app_label, interactive=interactive, verbosity=1)
        logger.info("Migrations applied successfully")
    except Exception as e:
        logger.error(f"Error applying migrations: {str(e)}")
        raise

def create_migration(app_label: str, name: str, empty: bool = False) -> None:
    """
    Create a new migration.
    
    Args:
        app_label: The app label to create a migration for
        name: The name of the migration
        empty: Whether to create an empty migration
    """
    logger.info(f"Creating migration {name} for {app_label}")
    
    try:
        call_command('makemigrations', app_label, name=name, empty=empty, verbosity=1)
        logger.info(f"Migration {name} created successfully for {app_label}")
    except Exception as e:
        logger.error(f"Error creating migration: {str(e)}")
        raise

def rollback_migration(app_label: str, migration_name: Optional[str] = None) -> None:
    """
    Rollback a migration.
    
    Args:
        app_label: The app label to rollback
        migration_name: The name of the migration to rollback to (None for zero)
    """
    target = migration_name or 'zero'
    logger.info(f"Rolling back migrations for {app_label} to {target}")
    
    try:
        call_command('migrate', app_label, target, verbosity=1)
        logger.info(f"Migrations for {app_label} rolled back to {target}")
    except Exception as e:
        logger.error(f"Error rolling back migrations: {str(e)}")
        raise

def check_migrations() -> bool:
    """
    Check if there are unapplied migrations.
    
    Returns:
        bool: True if there are unapplied migrations, False otherwise
    """
    unapplied = get_unapplied_migrations()
    if unapplied:
        logger.warning(f"There are {len(unapplied)} unapplied migrations:")
        for app_label, name in unapplied:
            logger.warning(f"  {app_label}.{name}")
        return True
    else:
        logger.info("No unapplied migrations")
        return False

def create_data_migration(app_label: str, name: str, forwards_code: str, backwards_code: str = "pass") -> None:
    """
    Create a data migration.
    
    Args:
        app_label: The app label to create a migration for
        name: The name of the migration
        forwards_code: The code to run when applying the migration
        backwards_code: The code to run when rolling back the migration
    """
    logger.info(f"Creating data migration {name} for {app_label}")
    
    try:
        # Create an empty migration
        create_migration(app_label, name, empty=True)
        
        # Find the migration file
        migrations_dir = os.path.join('backend_soodam', app_label, 'migrations')
        migration_files = [f for f in os.listdir(migrations_dir) if f.endswith(f"_{name}.py")]
        
        if not migration_files:
            raise FileNotFoundError(f"Migration file for {app_label}.{name} not found")
        
        migration_file = os.path.join(migrations_dir, migration_files[0])
        
        # Update the migration file with the data migration code
        with open(migration_file, 'r') as f:
            content = f.read()
        
        # Replace the operations list
        operations_start = content.find('operations = [')
        operations_end = content.find(']', operations_start)
        
        new_operations = f"""operations = [
    migrations.RunPython(
        code={forwards_code},
        reverse_code={backwards_code}
    ),
]"""
        
        new_content = content[:operations_start] + new_operations + content[operations_end + 1:]
        
        with open(migration_file, 'w') as f:
            f.write(new_content)
        
        logger.info(f"Data migration {name} created successfully for {app_label}")
    except Exception as e:
        logger.error(f"Error creating data migration: {str(e)}")
        raise

def squash_migrations(app_label: str, start_migration: Optional[str] = None, end_migration: Optional[str] = None) -> None:
    """
    Squash migrations.
    
    Args:
        app_label: The app label to squash migrations for
        start_migration: The first migration to include (None for first)
        end_migration: The last migration to include (None for last)
    """
    logger.info(f"Squashing migrations for {app_label}")
    
    migration_name = f"{start_migration or 'initial'}_to_{end_migration or 'latest'}"
    
    try:
        call_command(
            'squashmigrations',
            app_label,
            start_migration or '0001',
            end_migration,
            verbosity=1
        )
        logger.info(f"Migrations for {app_label} squashed successfully")
    except Exception as e:
        logger.error(f"Error squashing migrations: {str(e)}")
        raise
