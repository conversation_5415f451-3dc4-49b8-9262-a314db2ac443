"""
Caching module for the <PERSON><PERSON> backend.

This module provides a standardized approach to caching API responses and database queries.
"""

import hashlib
import json
import logging
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from django.core.cache import cache
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Configure the logger
logger = logging.getLogger("soodam_cache")

# Default cache timeout (in seconds)
DEFAULT_CACHE_TIMEOUT = 60 * 5  # 5 minutes


def generate_cache_key(prefix: str, *args, **kwargs) -> str:
    """
    Generate a cache key from a prefix and arguments.
    
    Args:
        prefix: The prefix for the cache key
        *args: Positional arguments to include in the key
        **kwargs: Keyword arguments to include in the key
        
    Returns:
        str: The generated cache key
    """
    # Convert args and kwargs to a string representation
    key_parts = [prefix]
    
    if args:
        key_parts.append(str(args))
    
    if kwargs:
        # Sort kwargs by key to ensure consistent ordering
        sorted_kwargs = sorted(kwargs.items())
        key_parts.append(str(sorted_kwargs))
    
    # Join key parts and hash them
    key_str = ":".join(key_parts)
    hashed_key = hashlib.md5(key_str.encode()).hexdigest()
    
    return f"{prefix}:{hashed_key}"


def cache_response(timeout: int = DEFAULT_CACHE_TIMEOUT, prefix: str = "api"):
    """
    Decorator for caching API responses.
    
    Args:
        timeout: Cache timeout in seconds
        prefix: Cache key prefix
        
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = generate_cache_key(prefix, func.__name__, *args, **kwargs)
            
            # Try to get cached response
            cached_response = cache.get(cache_key)
            
            if cached_response is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_response
            
            # Cache miss, call the function
            logger.debug(f"Cache miss for key: {cache_key}")
            response = await func(*args, **kwargs)
            
            # Cache the response
            cache.set(cache_key, response, timeout)
            
            return response
        
        return wrapper
    
    return decorator


class CacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware for caching API responses.
    
    This middleware caches GET requests to specified paths and returns cached responses
    when available.
    """
    
    def __init__(
        self,
        app: ASGIApp,
        cache_paths: List[str] = None,
        exclude_paths: List[str] = None,
        timeout: int = DEFAULT_CACHE_TIMEOUT
    ):
        super().__init__(app)
        self.cache_paths = cache_paths or []
        self.exclude_paths = exclude_paths or []
        self.timeout = timeout
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Only cache GET requests
        if request.method != "GET":
            return await call_next(request)
        
        # Skip caching for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Only cache specified paths
        if self.cache_paths and not any(request.url.path.startswith(path) for path in self.cache_paths):
            return await call_next(request)
        
        # Generate cache key
        cache_key = generate_cache_key(
            "response",
            request.url.path,
            str(request.query_params),
            headers=str(request.headers.get("accept", "")),
        )
        
        # Try to get cached response
        cached_response = cache.get(cache_key)
        
        if cached_response is not None:
            logger.debug(f"Cache hit for key: {cache_key}")
            return Response(
                content=cached_response["content"],
                status_code=cached_response["status_code"],
                headers=cached_response["headers"],
                media_type=cached_response["media_type"]
            )
        
        # Cache miss, process the request
        logger.debug(f"Cache miss for key: {cache_key}")
        response = await call_next(request)
        
        # Only cache successful responses
        if 200 <= response.status_code < 400:
            # Get response content
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            # Cache the response
            cache.set(
                cache_key,
                {
                    "content": response_body,
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "media_type": response.media_type
                },
                self.timeout
            )
            
            # Return a new response with the same content
            return Response(
                content=response_body,
                status_code=response.status_code,
                headers=response.headers,
                media_type=response.media_type
            )
        
        return response


def setup_caching(
    app: FastAPI,
    cache_paths: List[str] = None,
    exclude_paths: List[str] = None,
    timeout: int = DEFAULT_CACHE_TIMEOUT
) -> None:
    """
    Set up caching for the FastAPI application.
    
    Args:
        app: The FastAPI application
        cache_paths: List of paths to cache
        exclude_paths: List of paths to exclude from caching
        timeout: Cache timeout in seconds
    """
    # Add the cache middleware
    app.add_middleware(
        CacheMiddleware,
        cache_paths=cache_paths or ["/api/"],
        exclude_paths=exclude_paths or [
            "/api/health",
            "/metrics",
            "/api/docs",
            "/api/redoc",
            "/api/admin"  # Don't cache admin endpoints
        ],
        timeout=timeout
    )
    
    # Log caching setup
    logger.info(f"Caching set up with timeout: {timeout} seconds")
    if cache_paths:
        logger.info(f"Caching paths: {cache_paths}")
    if exclude_paths:
        logger.info(f"Excluding paths from caching: {exclude_paths}")


def invalidate_cache(prefix: str, *args, **kwargs) -> None:
    """
    Invalidate a cache entry.
    
    Args:
        prefix: The prefix for the cache key
        *args: Positional arguments to include in the key
        **kwargs: Keyword arguments to include in the key
    """
    # Generate cache key
    cache_key = generate_cache_key(prefix, *args, **kwargs)
    
    # Delete the cache entry
    cache.delete(cache_key)
    
    logger.debug(f"Cache invalidated for key: {cache_key}")


def invalidate_cache_pattern(pattern: str) -> None:
    """
    Invalidate all cache entries matching a pattern.
    
    Args:
        pattern: The pattern to match
    """
    # Get all keys matching the pattern
    keys = cache.keys(pattern)
    
    # Delete all matching keys
    for key in keys:
        cache.delete(key)
    
    logger.debug(f"Cache invalidated for pattern: {pattern}, {len(keys)} keys deleted")
