"""
Benchmarking module for the Soodam backend.

This module provides utilities for benchmarking API performance.
"""

import time
import statistics
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
import asyncio
import logging
import json
from datetime import datetime
from functools import wraps

# Configure the logger
logger = logging.getLogger("soodam_benchmark")

class BenchmarkResult:
    """
    Class to store benchmark results.
    """
    
    def __init__(
        self,
        name: str,
        execution_times: List[float],
        start_time: float,
        end_time: float,
        success: bool = True,
        error: Optional[Exception] = None
    ):
        """
        Initialize the benchmark result.
        
        Args:
            name: The name of the benchmark
            execution_times: List of execution times in seconds
            start_time: The start time of the benchmark
            end_time: The end time of the benchmark
            success: Whether the benchmark was successful
            error: The error that occurred during the benchmark
        """
        self.name = name
        self.execution_times = execution_times
        self.start_time = start_time
        self.end_time = end_time
        self.success = success
        self.error = error
    
    @property
    def total_time(self) -> float:
        """Get the total time of the benchmark in seconds."""
        return self.end_time - self.start_time
    
    @property
    def average_time(self) -> float:
        """Get the average execution time in seconds."""
        return statistics.mean(self.execution_times) if self.execution_times else 0
    
    @property
    def median_time(self) -> float:
        """Get the median execution time in seconds."""
        return statistics.median(self.execution_times) if self.execution_times else 0
    
    @property
    def min_time(self) -> float:
        """Get the minimum execution time in seconds."""
        return min(self.execution_times) if self.execution_times else 0
    
    @property
    def max_time(self) -> float:
        """Get the maximum execution time in seconds."""
        return max(self.execution_times) if self.execution_times else 0
    
    @property
    def stdev_time(self) -> float:
        """Get the standard deviation of execution times in seconds."""
        return statistics.stdev(self.execution_times) if len(self.execution_times) > 1 else 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the benchmark result to a dictionary."""
        return {
            "name": self.name,
            "success": self.success,
            "error": str(self.error) if self.error else None,
            "total_time": self.total_time,
            "average_time": self.average_time,
            "median_time": self.median_time,
            "min_time": self.min_time,
            "max_time": self.max_time,
            "stdev_time": self.stdev_time,
            "execution_count": len(self.execution_times),
            "timestamp": datetime.now().isoformat()
        }
    
    def __str__(self) -> str:
        """Get a string representation of the benchmark result."""
        return json.dumps(self.to_dict(), indent=2)


def benchmark(name: Optional[str] = None, iterations: int = 1):
    """
    Decorator for benchmarking functions.
    
    Args:
        name: The name of the benchmark
        iterations: The number of iterations to run
        
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Tuple[Any, BenchmarkResult]:
            benchmark_name = name or func.__name__
            execution_times = []
            start_time = time.time()
            result = None
            success = True
            error = None
            
            try:
                for _ in range(iterations):
                    iteration_start = time.time()
                    result = await func(*args, **kwargs)
                    iteration_end = time.time()
                    execution_times.append(iteration_end - iteration_start)
            except Exception as e:
                success = False
                error = e
                logger.error(f"Benchmark error in {benchmark_name}: {str(e)}")
                raise
            finally:
                end_time = time.time()
                benchmark_result = BenchmarkResult(
                    name=benchmark_name,
                    execution_times=execution_times,
                    start_time=start_time,
                    end_time=end_time,
                    success=success,
                    error=error
                )
                
                # Log benchmark result
                logger.info(f"Benchmark result: {benchmark_result}")
                
                return result, benchmark_result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Tuple[Any, BenchmarkResult]:
            benchmark_name = name or func.__name__
            execution_times = []
            start_time = time.time()
            result = None
            success = True
            error = None
            
            try:
                for _ in range(iterations):
                    iteration_start = time.time()
                    result = func(*args, **kwargs)
                    iteration_end = time.time()
                    execution_times.append(iteration_end - iteration_start)
            except Exception as e:
                success = False
                error = e
                logger.error(f"Benchmark error in {benchmark_name}: {str(e)}")
                raise
            finally:
                end_time = time.time()
                benchmark_result = BenchmarkResult(
                    name=benchmark_name,
                    execution_times=execution_times,
                    start_time=start_time,
                    end_time=end_time,
                    success=success,
                    error=error
                )
                
                # Log benchmark result
                logger.info(f"Benchmark result: {benchmark_result}")
                
                return result, benchmark_result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


async def benchmark_endpoint(
    client,
    url: str,
    method: str = "GET",
    headers: Optional[Dict[str, str]] = None,
    data: Optional[Dict[str, Any]] = None,
    iterations: int = 10,
    name: Optional[str] = None
) -> BenchmarkResult:
    """
    Benchmark an API endpoint.
    
    Args:
        client: The test client
        url: The URL to benchmark
        method: The HTTP method to use
        headers: The headers to include
        data: The data to send
        iterations: The number of iterations to run
        name: The name of the benchmark
        
    Returns:
        BenchmarkResult: The benchmark result
    """
    benchmark_name = name or f"{method} {url}"
    execution_times = []
    start_time = time.time()
    success = True
    error = None
    
    try:
        for _ in range(iterations):
            iteration_start = time.time()
            
            if method == "GET":
                response = await client.get(url, headers=headers)
            elif method == "POST":
                response = await client.post(url, headers=headers, json=data)
            elif method == "PUT":
                response = await client.put(url, headers=headers, json=data)
            elif method == "DELETE":
                response = await client.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            iteration_end = time.time()
            execution_times.append(iteration_end - iteration_start)
            
            # Check if the response was successful
            if response.status_code >= 400:
                success = False
                error = Exception(f"HTTP {response.status_code}: {response.text}")
                break
    except Exception as e:
        success = False
        error = e
        logger.error(f"Benchmark error in {benchmark_name}: {str(e)}")
    finally:
        end_time = time.time()
        benchmark_result = BenchmarkResult(
            name=benchmark_name,
            execution_times=execution_times,
            start_time=start_time,
            end_time=end_time,
            success=success,
            error=error
        )
        
        # Log benchmark result
        logger.info(f"Endpoint benchmark result: {benchmark_result}")
        
        return benchmark_result
