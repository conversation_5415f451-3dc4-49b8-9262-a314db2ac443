"""
Elasticsearch integration for the Soodam backend.

This module provides utilities for integrating with Elasticsearch.
"""

import json
import logging
import os
from typing import Dict, List, Optional, Union

from elasticsearch import AsyncElasticsearch, NotFoundError
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)

# Elasticsearch client
es_client = AsyncElasticsearch(
    hosts=[os.environ.get('ELASTICSEARCH_URL', 'http://localhost:9200')],
    basic_auth=(
        os.environ.get('ELASTICSEARCH_USERNAME', ''),
        os.environ.get('ELASTICSEARCH_PASSWORD', '')
    ),
    verify_certs=os.environ.get('ELASTICSEARCH_VERIFY_CERTS', 'true').lower() == 'true'
)

# Elasticsearch indices
ADVERTISEMENT_INDEX = 'advertisements'
USER_INDEX = 'users'
BLOG_INDEX = 'blogs'


async def setup_elasticsearch():
    """
    Set up Elasticsearch indices.
    """
    try:
        # Create advertisement index
        if not await es_client.indices.exists(index=ADVERTISEMENT_INDEX):
            await es_client.indices.create(
                index=ADVERTISEMENT_INDEX,
                body={
                    'settings': {
                        'number_of_shards': 1,
                        'number_of_replicas': 0,
                        'analysis': {
                            'analyzer': {
                                'persian': {
                                    'tokenizer': 'standard',
                                    'filter': ['lowercase', 'arabic_normalization', 'persian_normalization']
                                }
                            }
                        }
                    },
                    'mappings': {
                        'properties': {
                            'id': {'type': 'integer'},
                            'title': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'description': {
                                'type': 'text',
                                'analyzer': 'persian'
                            },
                            'category_id': {'type': 'integer'},
                            'category_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_id': {'type': 'integer'},
                            'location_city': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_state': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_country': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_point': {'type': 'geo_point'},
                            'price': {'type': 'float'},
                            'currency': {'type': 'keyword'},
                            'is_negotiable': {'type': 'boolean'},
                            'user_id': {'type': 'integer'},
                            'user_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'status': {'type': 'keyword'},
                            'is_featured': {'type': 'boolean'},
                            'tags': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'created_at': {'type': 'date'},
                            'updated_at': {'type': 'date'},
                            'expiry_date': {'type': 'date'},
                            'views': {'type': 'integer'},
                            'favorites': {'type': 'integer'},
                            'inquiries': {'type': 'integer'},
                            'image_url': {'type': 'keyword'},
                            'attributes': {
                                'type': 'nested',
                                'properties': {
                                    'name': {
                                        'type': 'text',
                                        'analyzer': 'persian',
                                        'fields': {
                                            'keyword': {'type': 'keyword'}
                                        }
                                    },
                                    'value': {
                                        'type': 'text',
                                        'analyzer': 'persian',
                                        'fields': {
                                            'keyword': {'type': 'keyword'}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            )
            logger.info(f"Created Elasticsearch index: {ADVERTISEMENT_INDEX}")

        # Create user index
        if not await es_client.indices.exists(index=USER_INDEX):
            await es_client.indices.create(
                index=USER_INDEX,
                body={
                    'settings': {
                        'number_of_shards': 1,
                        'number_of_replicas': 0,
                        'analysis': {
                            'analyzer': {
                                'persian': {
                                    'tokenizer': 'standard',
                                    'filter': ['lowercase', 'arabic_normalization', 'persian_normalization']
                                }
                            }
                        }
                    },
                    'mappings': {
                        'properties': {
                            'id': {'type': 'integer'},
                            'username': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'email': {'type': 'keyword'},
                            'first_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'last_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'full_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'is_active': {'type': 'boolean'},
                            'is_staff': {'type': 'boolean'},
                            'is_superuser': {'type': 'boolean'},
                            'date_joined': {'type': 'date'},
                            'last_login': {'type': 'date'},
                            'avatar_url': {'type': 'keyword'},
                            'phone_number': {'type': 'keyword'},
                            'location_id': {'type': 'integer'},
                            'location_city': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_state': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'location_country': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            }
                        }
                    }
                }
            )
            logger.info(f"Created Elasticsearch index: {USER_INDEX}")

        # Create blog index
        if not await es_client.indices.exists(index=BLOG_INDEX):
            await es_client.indices.create(
                index=BLOG_INDEX,
                body={
                    'settings': {
                        'number_of_shards': 1,
                        'number_of_replicas': 0,
                        'analysis': {
                            'analyzer': {
                                'persian': {
                                    'tokenizer': 'standard',
                                    'filter': ['lowercase', 'arabic_normalization', 'persian_normalization']
                                }
                            }
                        }
                    },
                    'mappings': {
                        'properties': {
                            'id': {'type': 'integer'},
                            'title': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'content': {
                                'type': 'text',
                                'analyzer': 'persian'
                            },
                            'excerpt': {
                                'type': 'text',
                                'analyzer': 'persian'
                            },
                            'category_id': {'type': 'integer'},
                            'category_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'author_id': {'type': 'integer'},
                            'author_name': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'status': {'type': 'keyword'},
                            'tags': {
                                'type': 'text',
                                'analyzer': 'persian',
                                'fields': {
                                    'keyword': {'type': 'keyword'}
                                }
                            },
                            'created_at': {'type': 'date'},
                            'updated_at': {'type': 'date'},
                            'published_at': {'type': 'date'},
                            'views': {'type': 'integer'},
                            'likes': {'type': 'integer'},
                            'comments': {'type': 'integer'},
                            'image_url': {'type': 'keyword'}
                        }
                    }
                }
            )
            logger.info(f"Created Elasticsearch index: {BLOG_INDEX}")
    except Exception as e:
        logger.error(f"Error setting up Elasticsearch: {str(e)}")


async def index_advertisement(advertisement: Dict) -> Dict:
    """
    Index an advertisement in Elasticsearch.

    Args:
        advertisement: The advertisement data

    Returns:
        Dict: The indexing result
    """
    try:
        # Index the advertisement
        result = await es_client.index(
            index=ADVERTISEMENT_INDEX,
            id=advertisement['id'],
            document=advertisement,
            refresh=True
        )

        logger.info(f"Indexed advertisement {advertisement['id']} in Elasticsearch")

        return result
    except Exception as e:
        logger.error(f"Error indexing advertisement {advertisement.get('id')}: {str(e)}")
        raise


async def delete_advertisement(advertisement_id: int) -> Dict:
    """
    Delete an advertisement from Elasticsearch.

    Args:
        advertisement_id: The advertisement ID

    Returns:
        Dict: The deletion result
    """
    try:
        # Delete the advertisement
        result = await es_client.delete(
            index=ADVERTISEMENT_INDEX,
            id=advertisement_id,
            refresh=True
        )

        logger.info(f"Deleted advertisement {advertisement_id} from Elasticsearch")

        return result
    except NotFoundError:
        logger.warning(f"Advertisement {advertisement_id} not found in Elasticsearch")
        return {'result': 'not_found'}
    except Exception as e:
        logger.error(f"Error deleting advertisement {advertisement_id}: {str(e)}")
        raise


async def search_advertisements(
    query: str,
    category_id: Optional[int] = None,
    location_id: Optional[int] = None,
    price_min: Optional[float] = None,
    price_max: Optional[float] = None,
    is_featured: Optional[bool] = None,
    status: Optional[str] = None,
    sort_by: str = 'created_at',
    sort_order: str = 'desc',
    page: int = 1,
    limit: int = 10,
    facets: bool = True,
) -> Dict:
    """
    Search for advertisements in Elasticsearch.

    Args:
        query: The search query
        category_id: Filter by category ID
        location_id: Filter by location ID
        price_min: Filter by minimum price
        price_max: Filter by maximum price
        is_featured: Filter by featured status
        status: Filter by status
        sort_by: Sort field
        sort_order: Sort order
        page: Page number
        limit: Page size

    Returns:
        Dict: The search results
    """
    try:
        # Build the query
        must_clauses = []
        filter_clauses = []

        # Add the search query
        if query:
            must_clauses.append({
                'multi_match': {
                    'query': query,
                    'fields': ['title^3', 'description^2', 'category_name', 'location_city', 'location_state', 'tags', 'attributes.name', 'attributes.value'],
                    'type': 'best_fields',
                    'fuzziness': 'AUTO'
                }
            })

        # Add filters
        if category_id:
            filter_clauses.append({'term': {'category_id': category_id}})

        if location_id:
            filter_clauses.append({'term': {'location_id': location_id}})

        if price_min is not None:
            filter_clauses.append({'range': {'price': {'gte': price_min}}})

        if price_max is not None:
            filter_clauses.append({'range': {'price': {'lte': price_max}}})

        if is_featured is not None:
            filter_clauses.append({'term': {'is_featured': is_featured}})

        if status:
            filter_clauses.append({'term': {'status': status}})
        else:
            # Default to approved advertisements
            filter_clauses.append({'term': {'status': 'APPROVED'}})

        # Build the full query
        body = {
            'query': {
                'bool': {
                    'must': must_clauses,
                    'filter': filter_clauses
                }
            },
            'sort': [
                {sort_by: {'order': sort_order}}
            ],
            'from': (page - 1) * limit,
            'size': limit,
            'highlight': {
                'fields': {
                    'title': {},
                    'description': {}
                },
                'pre_tags': ['<em>'],
                'post_tags': ['</em>']
            }
        }

        # Add facets (aggregations) if requested
        if facets:
            body['aggs'] = {
                'categories': {
                    'terms': {
                        'field': 'category_id',
                        'size': 20
                    },
                    'aggs': {
                        'category_name': {
                            'terms': {
                                'field': 'category_name.keyword',
                                'size': 1
                            }
                        }
                    }
                },
                'locations': {
                    'terms': {
                        'field': 'location_city.keyword',
                        'size': 20
                    }
                },
                'price_ranges': {
                    'range': {
                        'field': 'price',
                        'ranges': [
                            {'to': 100},
                            {'from': 100, 'to': 500},
                            {'from': 500, 'to': 1000},
                            {'from': 1000, 'to': 5000},
                            {'from': 5000}
                        ]
                    }
                },
                'is_featured': {
                    'terms': {
                        'field': 'is_featured'
                    }
                },
                'status': {
                    'terms': {
                        'field': 'status'
                    }
                },
                'tags': {
                    'terms': {
                        'field': 'tags.keyword',
                        'size': 20
                    }
                },
                'price_stats': {
                    'stats': {
                        'field': 'price'
                    }
                }
            }

        # Execute the search
        result = await es_client.search(
            index=ADVERTISEMENT_INDEX,
            body=body
        )

        # Process the results
        hits = result['hits']['hits']
        total = result['hits']['total']['value']

        # Extract the advertisements
        advertisements = []
        for hit in hits:
            advertisement = hit['_source']

            # Add the score
            advertisement['score'] = hit['_score']

            # Add highlights
            if 'highlight' in hit:
                advertisement['highlights'] = hit['highlight']

            advertisements.append(advertisement)

        # Process facets if requested
        facet_results = {}
        if facets and 'aggregations' in result:
            aggs = result['aggregations']

            # Process category facets
            if 'categories' in aggs:
                categories = []
                for bucket in aggs['categories']['buckets']:
                    category_id = bucket['key']
                    category_name = None

                    # Get the category name from the nested aggregation
                    if 'category_name' in bucket and bucket['category_name']['buckets']:
                        category_name = bucket['category_name']['buckets'][0]['key']

                    categories.append({
                        'id': category_id,
                        'name': category_name,
                        'count': bucket['doc_count']
                    })

                facet_results['categories'] = categories

            # Process location facets
            if 'locations' in aggs:
                locations = []
                for bucket in aggs['locations']['buckets']:
                    locations.append({
                        'city': bucket['key'],
                        'count': bucket['doc_count']
                    })

                facet_results['locations'] = locations

            # Process price range facets
            if 'price_ranges' in aggs:
                price_ranges = []
                for bucket in aggs['price_ranges']['buckets']:
                    price_range = {
                        'count': bucket['doc_count']
                    }

                    if 'from' in bucket:
                        price_range['from'] = bucket['from']

                    if 'to' in bucket:
                        price_range['to'] = bucket['to']

                    price_ranges.append(price_range)

                facet_results['price_ranges'] = price_ranges

            # Process featured facets
            if 'is_featured' in aggs:
                featured = []
                for bucket in aggs['is_featured']['buckets']:
                    featured.append({
                        'value': bucket['key'],
                        'count': bucket['doc_count']
                    })

                facet_results['is_featured'] = featured

            # Process status facets
            if 'status' in aggs:
                statuses = []
                for bucket in aggs['status']['buckets']:
                    statuses.append({
                        'value': bucket['key'],
                        'count': bucket['doc_count']
                    })

                facet_results['status'] = statuses

            # Process tag facets
            if 'tags' in aggs:
                tags = []
                for bucket in aggs['tags']['buckets']:
                    tags.append({
                        'name': bucket['key'],
                        'count': bucket['doc_count']
                    })

                facet_results['tags'] = tags

            # Process price stats
            if 'price_stats' in aggs:
                facet_results['price_stats'] = aggs['price_stats']

        # Return the results
        return {
            'items': advertisements,
            'total': total,
            'page': page,
            'limit': limit,
            'pages': (total + limit - 1) // limit,
            'facets': facet_results if facets else None
        }
    except Exception as e:
        logger.error(f"Error searching advertisements: {str(e)}")
        raise
