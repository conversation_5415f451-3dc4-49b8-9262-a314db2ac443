"""
Database query profiler for the <PERSON><PERSON> backend.

This module provides utilities for profiling and optimizing database queries.
"""

import functools
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from django.db import connection, reset_queries
from django.db.models import Model, QuerySet
from django.db.models.query import QuerySet

from .prometheus import track_db_query

logger = logging.getLogger(__name__)


def query_debugger(func: Callable) -> Callable:
    """
    Decorator for debugging database queries.
    
    This decorator logs the number of queries executed by a function and the time taken.
    
    Args:
        func: The function to debug
        
    Returns:
        Callable: The decorated function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        reset_queries()
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        query_count = len(connection.queries)
        query_time = end_time - start_time
        
        logger.debug(f"Function: {func.__name__}")
        logger.debug(f"Number of queries: {query_count}")
        logger.debug(f"Execution time: {query_time:.4f}s")
        
        if query_count > 10:
            logger.warning(f"Function {func.__name__} executed {query_count} queries!")
        
        return result
    
    return wrapper


def profile_query(query_type: str = "select") -> Callable:
    """
    Decorator for profiling database queries.
    
    This decorator tracks the time taken by a database query and reports it to Prometheus.
    
    Args:
        query_type: The type of query (e.g., "select", "insert", "update", "delete")
        
    Returns:
        Callable: The decorator
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            duration = end_time - start_time
            track_db_query(query_type, duration)
            
            return result
        
        return wrapper
    
    return decorator


def optimize_queryset(queryset: QuerySet, select_related: List[str] = None, prefetch_related: List[str] = None) -> QuerySet:
    """
    Optimize a queryset by adding select_related and prefetch_related.
    
    Args:
        queryset: The queryset to optimize
        select_related: Fields to select_related
        prefetch_related: Fields to prefetch_related
        
    Returns:
        QuerySet: The optimized queryset
    """
    if select_related:
        queryset = queryset.select_related(*select_related)
    
    if prefetch_related:
        queryset = queryset.prefetch_related(*prefetch_related)
    
    return queryset


def analyze_query(queryset: QuerySet) -> Dict[str, Any]:
    """
    Analyze a queryset and return information about it.
    
    Args:
        queryset: The queryset to analyze
        
    Returns:
        Dict[str, Any]: Information about the queryset
    """
    # Get the query
    query = queryset.query
    
    # Get the SQL
    sql, params = queryset.query.sql_with_params()
    
    # Get the tables
    tables = set(query.tables)
    
    # Get the where clause
    where = query.where
    
    # Get the order by
    order_by = query.order_by
    
    # Get the limit and offset
    limit = query.high_mark
    offset = query.low_mark
    
    # Get the select related
    select_related = query.select_related
    
    # Get the prefetch related
    prefetch_related = getattr(queryset, "_prefetch_related_lookups", [])
    
    return {
        "sql": sql,
        "params": params,
        "tables": tables,
        "where": where,
        "order_by": order_by,
        "limit": limit,
        "offset": offset,
        "select_related": select_related,
        "prefetch_related": prefetch_related
    }


def get_query_execution_plan(queryset: QuerySet) -> Dict[str, Any]:
    """
    Get the execution plan for a queryset.
    
    Args:
        queryset: The queryset to analyze
        
    Returns:
        Dict[str, Any]: The execution plan
    """
    # Get the SQL
    sql, params = queryset.query.sql_with_params()
    
    # Execute EXPLAIN
    with connection.cursor() as cursor:
        cursor.execute(f"EXPLAIN ANALYZE {sql}", params)
        rows = cursor.fetchall()
    
    # Parse the execution plan
    plan = []
    for row in rows:
        plan.append(row[0])
    
    return {
        "sql": sql,
        "plan": plan
    }


def get_slow_queries(threshold: float = 1.0) -> List[Dict[str, Any]]:
    """
    Get slow queries from the database.
    
    Args:
        threshold: The threshold in seconds
        
    Returns:
        List[Dict[str, Any]]: The slow queries
    """
    # Execute query to get slow queries
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT query, calls, total_time, min_time, max_time, mean_time, stddev_time
            FROM pg_stat_statements
            WHERE total_time > %s
            ORDER BY total_time DESC
            LIMIT 10
        """, [threshold * 1000])  # Convert to milliseconds
        
        columns = [col[0] for col in cursor.description]
        rows = cursor.fetchall()
    
    # Convert to dictionaries
    queries = []
    for row in rows:
        query = dict(zip(columns, row))
        queries.append(query)
    
    return queries


def add_index_recommendations(model: Model) -> List[Dict[str, Any]]:
    """
    Get index recommendations for a model.
    
    Args:
        model: The model to analyze
        
    Returns:
        List[Dict[str, Any]]: The index recommendations
    """
    # Get the table name
    table_name = model._meta.db_table
    
    # Execute query to get index recommendations
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT
                relname AS table_name,
                seq_scan,
                idx_scan,
                seq_scan - idx_scan AS diff,
                n_live_tup AS rows
            FROM pg_stat_user_tables
            WHERE relname = %s
            ORDER BY diff DESC
        """, [table_name])
        
        columns = [col[0] for col in cursor.description]
        rows = cursor.fetchall()
    
    # Convert to dictionaries
    recommendations = []
    for row in rows:
        recommendation = dict(zip(columns, row))
        
        # Add recommendation if seq_scan > idx_scan
        if recommendation.get("diff", 0) > 0:
            recommendation["recommendation"] = "Consider adding indexes to this table"
            recommendations.append(recommendation)
    
    return recommendations
