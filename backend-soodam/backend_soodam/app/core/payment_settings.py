"""
Payment gateway settings for the Soodam backend.

This module provides settings for integrating with Iranian payment gateways.
"""

import os
from pydantic import BaseSettings


class PaymentSettings(BaseSettings):
    """Settings for payment gateways"""
    
    # General payment settings
    PAYMENT_SANDBOX_MODE: bool = os.getenv("PAYMENT_SANDBOX_MODE", "True") == "True"
    PAYMENT_CALLBACK_URL: str = os.getenv("PAYMENT_CALLBACK_URL", "https://api.soodam.com/api/payments/callback")
    
    # Zarinpal settings
    ZARINPAL_MERCHANT_ID: str = os.getenv("ZARINPAL_MERCHANT_ID", "your-merchant-id")
    ZARINPAL_CALLBACK_URL: str = os.getenv("ZARINPAL_CALLBACK_URL", "https://api.soodam.com/api/payments/callback")
    
    # <PERSON><PERSON> settings
    MELLAT_MERCHANT_ID: str = os.getenv("MELLAT_MERCHANT_ID", "your-merchant-id")
    MELLAT_TERMINAL_ID: str = os.getenv("MELLAT_TERMINAL_ID", "your-terminal-id")
    MELLAT_USERNAME: str = os.getenv("MELLAT_USERNAME", "your-username")
    MELLAT_PASSWORD: str = os.getenv("MELLAT_PASSWORD", "your-password")
    MELLAT_CALLBACK_URL: str = os.getenv("MELLAT_CALLBACK_URL", "https://api.soodam.com/api/payments/callback")
    
    # Saman settings
    SAMAN_MERCHANT_ID: str = os.getenv("SAMAN_MERCHANT_ID", "your-merchant-id")
    SAMAN_CALLBACK_URL: str = os.getenv("SAMAN_CALLBACK_URL", "https://api.soodam.com/api/payments/callback")
    
    # Parsian settings
    PARSIAN_PIN: str = os.getenv("PARSIAN_PIN", "your-pin")
    PARSIAN_CALLBACK_URL: str = os.getenv("PARSIAN_CALLBACK_URL", "https://api.soodam.com/api/payments/callback")
    
    class Config:
        env_file = "fastapi.env.dev"
        case_sensitive = True


# Create settings instance
payment_settings = PaymentSettings()
