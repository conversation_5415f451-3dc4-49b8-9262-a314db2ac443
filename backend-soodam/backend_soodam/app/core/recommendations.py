"""
Recommendation engine for the Soodam backend.

This module provides utilities for generating personalized recommendations.
"""

import logging
import math
import random
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple

import numpy as np
from django.db.models import Count, F, Q, Value
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from ..models import CustomUserModel
from ..models.advertisement import (
    AdvertisementFavoriteModel,
    AdvertisementTagModel,
    AdvertisementViewModel,
    AdvertisementModel,
    MainCategoryModel as AdvertisementCategoryModel,
)

logger = logging.getLogger(__name__)


class RecommendationEngine:
    """
    Recommendation engine for generating personalized recommendations.
    
    This class provides methods for generating recommendations based on
    user behavior, content similarity, and collaborative filtering.
    """
    
    @staticmethod
    async def get_user_recommendations(
        user_id: int,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get personalized recommendations for a user.
        
        Args:
            user_id: The user ID
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
        """
        try:
            # Get the user
            user = await CustomUserModel.objects.filter(id=user_id).afirst()
            
            if not user:
                logger.warning(f"User {user_id} not found")
                return []
            
            # Initialize recommendations
            recommendations = []
            
            # Get recommendations based on user behavior
            behavior_recs = await RecommendationEngine.get_behavior_based_recommendations(
                user_id, limit=limit, exclude_ids=exclude_ids
            )
            
            # Get recommendations based on content similarity
            content_recs = await RecommendationEngine.get_content_based_recommendations(
                user_id, limit=limit, exclude_ids=exclude_ids
            )
            
            # Get recommendations based on collaborative filtering
            collab_recs = await RecommendationEngine.get_collaborative_recommendations(
                user_id, limit=limit, exclude_ids=exclude_ids
            )
            
            # Get trending advertisements
            trending_recs = await RecommendationEngine.get_trending_advertisements(
                limit=limit, exclude_ids=exclude_ids
            )
            
            # Combine recommendations with weights
            # 40% behavior-based, 30% content-based, 20% collaborative, 10% trending
            behavior_count = int(limit * 0.4)
            content_count = int(limit * 0.3)
            collab_count = int(limit * 0.2)
            trending_count = limit - behavior_count - content_count - collab_count
            
            # Add behavior-based recommendations
            recommendations.extend(behavior_recs[:behavior_count])
            
            # Add content-based recommendations, excluding duplicates
            existing_ids = {rec['id'] for rec in recommendations}
            for rec in content_recs:
                if rec['id'] not in existing_ids and len(recommendations) < behavior_count + content_count:
                    recommendations.append(rec)
                    existing_ids.add(rec['id'])
            
            # Add collaborative recommendations, excluding duplicates
            for rec in collab_recs:
                if rec['id'] not in existing_ids and len(recommendations) < behavior_count + content_count + collab_count:
                    recommendations.append(rec)
                    existing_ids.add(rec['id'])
            
            # Add trending recommendations, excluding duplicates
            for rec in trending_recs:
                if rec['id'] not in existing_ids and len(recommendations) < limit:
                    recommendations.append(rec)
                    existing_ids.add(rec['id'])
            
            # If we still don't have enough recommendations, add random ones
            if len(recommendations) < limit:
                random_recs = await RecommendationEngine.get_random_advertisements(
                    limit=limit - len(recommendations),
                    exclude_ids=list(existing_ids) + (exclude_ids or [])
                )
                recommendations.extend(random_recs)
            
            # Shuffle the recommendations to mix different types
            random.shuffle(recommendations)
            
            # Add recommendation source for debugging
            for rec in recommendations:
                if 'source' not in rec:
                    rec['source'] = 'mixed'
            
            return recommendations[:limit]
        except Exception as e:
            logger.error(f"Error getting user recommendations: {str(e)}")
            return []
    
    @staticmethod
    async def get_behavior_based_recommendations(
        user_id: int,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on user behavior.
        
        Args:
            user_id: The user ID
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
        """
        try:
            # Initialize exclude IDs
            exclude_ids = exclude_ids or []
            
            # Get the user's viewed advertisements
            views = await AdvertisementViewModel.objects.filter(
                user_id=user_id
            ).values_list('advertisement_id', flat=True)
            
            # Get the user's favorited advertisements
            favorites = await AdvertisementFavoriteModel.objects.filter(
                user_id=user_id
            ).values_list('advertisement_id', flat=True)
            
            # Combine viewed and favorited advertisements with weights
            # Favorites have higher weight than views
            weighted_ads = Counter()
            for ad_id in views:
                weighted_ads[ad_id] += 1
            
            for ad_id in favorites:
                weighted_ads[ad_id] += 3
            
            # Get the categories and tags of the user's interacted advertisements
            categories = set()
            tags = set()
            
            # Get advertisements the user has interacted with
            interacted_ads = await AdvertisementModel.objects.filter(
                id__in=list(weighted_ads.keys())
            ).prefetch_related('category', 'tags')
            
            # Extract categories and tags
            for ad in interacted_ads:
                categories.add(ad.category.id)
                for tag in await ad.tags.all():
                    tags.add(tag.id)
            
            # Find similar advertisements based on categories and tags
            similar_ads = await AdvertisementModel.objects.filter(
                Q(category_id__in=categories) | Q(tags__id__in=tags),
                status='APPROVED',
                is_active=True
            ).exclude(
                id__in=exclude_ids + list(weighted_ads.keys())
            ).distinct().values(
                'id', 'title', 'description', 'price', 'category_id', 'user_id',
                'created_at', 'updated_at', 'is_featured'
            )
            
            # Score the similar advertisements based on category and tag matches
            scored_ads = []
            for ad in similar_ads:
                score = 0
                
                # Check if the advertisement's category matches user's interests
                if ad['category_id'] in categories:
                    score += 2
                
                # Check if the advertisement has tags that match user's interests
                ad_tags = await AdvertisementTagModel.objects.filter(
                    advertisement_id=ad['id']
                ).values_list('tag_id', flat=True)
                
                for tag_id in ad_tags:
                    if tag_id in tags:
                        score += 1
                
                # Add the advertisement with its score
                scored_ads.append({
                    **ad,
                    'score': score,
                    'source': 'behavior'
                })
            
            # Sort by score (descending) and return top N
            scored_ads.sort(key=lambda x: x['score'], reverse=True)
            
            return scored_ads[:limit]
        except Exception as e:
            logger.error(f"Error getting behavior-based recommendations: {str(e)}")
            return []
    
    @staticmethod
    async def get_content_based_recommendations(
        user_id: int,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on content similarity.
        
        Args:
            user_id: The user ID
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
        """
        try:
            # Initialize exclude IDs
            exclude_ids = exclude_ids or []
            
            # Get the user's viewed and favorited advertisements
            viewed_ids = await AdvertisementViewModel.objects.filter(
                user_id=user_id
            ).values_list('advertisement_id', flat=True)
            
            favorited_ids = await AdvertisementFavoriteModel.objects.filter(
                user_id=user_id
            ).values_list('advertisement_id', flat=True)
            
            # Combine viewed and favorited IDs
            interacted_ids = list(set(list(viewed_ids) + list(favorited_ids)))
            
            if not interacted_ids:
                return []
            
            # Get the user's interacted advertisements
            interacted_ads = await AdvertisementModel.objects.filter(
                id__in=interacted_ids
            ).values('id', 'title', 'description', 'category_id')
            
            # Get candidate advertisements (excluding interacted and excluded)
            candidate_ads = await AdvertisementModel.objects.filter(
                status='APPROVED',
                is_active=True
            ).exclude(
                id__in=interacted_ids + exclude_ids
            ).values('id', 'title', 'description', 'price', 'category_id', 'user_id',
                    'created_at', 'updated_at', 'is_featured')
            
            if not candidate_ads:
                return []
            
            # Prepare text data for TF-IDF
            interacted_texts = [f"{ad['title']} {ad['description']}" for ad in interacted_ads]
            candidate_texts = [f"{ad['title']} {ad['description']}" for ad in candidate_ads]
            all_texts = interacted_texts + candidate_texts
            
            # Create TF-IDF vectors
            vectorizer = TfidfVectorizer(stop_words='english', min_df=1)
            tfidf_matrix = vectorizer.fit_transform(all_texts)
            
            # Calculate similarity between interacted and candidate advertisements
            interacted_vectors = tfidf_matrix[:len(interacted_texts)]
            candidate_vectors = tfidf_matrix[len(interacted_texts):]
            
            # Calculate cosine similarity
            similarities = cosine_similarity(candidate_vectors, interacted_vectors)
            
            # Get the maximum similarity for each candidate
            max_similarities = np.max(similarities, axis=1)
            
            # Add similarity scores to candidate advertisements
            for i, ad in enumerate(candidate_ads):
                ad['score'] = float(max_similarities[i])
                ad['source'] = 'content'
            
            # Sort by similarity score (descending) and return top N
            sorted_ads = sorted(candidate_ads, key=lambda x: x['score'], reverse=True)
            
            return list(sorted_ads[:limit])
        except Exception as e:
            logger.error(f"Error getting content-based recommendations: {str(e)}")
            return []
    
    @staticmethod
    async def get_collaborative_recommendations(
        user_id: int,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on collaborative filtering.
        
        Args:
            user_id: The user ID
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
        """
        try:
            # Initialize exclude IDs
            exclude_ids = exclude_ids or []
            
            # Get the user's favorited advertisements
            user_favorites = await AdvertisementFavoriteModel.objects.filter(
                user_id=user_id
            ).values_list('advertisement_id', flat=True)
            
            if not user_favorites:
                return []
            
            # Find users who favorited the same advertisements
            similar_users = await AdvertisementFavoriteModel.objects.filter(
                advertisement_id__in=user_favorites
            ).exclude(
                user_id=user_id
            ).values_list('user_id', flat=True)
            
            if not similar_users:
                return []
            
            # Count how many times each user appears (shared favorites)
            user_counts = Counter(similar_users)
            
            # Get advertisements favorited by similar users but not by the target user
            similar_user_favorites = await AdvertisementFavoriteModel.objects.filter(
                user_id__in=user_counts.keys()
            ).exclude(
                advertisement_id__in=list(user_favorites) + exclude_ids
            ).values('advertisement_id', 'user_id')
            
            # Score advertisements based on user similarity
            ad_scores = defaultdict(float)
            for fav in similar_user_favorites:
                ad_scores[fav['advertisement_id']] += user_counts[fav['user_id']]
            
            if not ad_scores:
                return []
            
            # Get the top-scored advertisements
            top_ad_ids = [ad_id for ad_id, _ in sorted(ad_scores.items(), key=lambda x: x[1], reverse=True)[:limit]]
            
            # Get the advertisement details
            top_ads = await AdvertisementModel.objects.filter(
                id__in=top_ad_ids,
                status='APPROVED',
                is_active=True
            ).values('id', 'title', 'description', 'price', 'category_id', 'user_id',
                    'created_at', 'updated_at', 'is_featured')
            
            # Add scores and sort
            scored_ads = []
            for ad in top_ads:
                scored_ads.append({
                    **ad,
                    'score': ad_scores[ad['id']],
                    'source': 'collaborative'
                })
            
            # Sort by score (descending)
            scored_ads.sort(key=lambda x: x['score'], reverse=True)
            
            return scored_ads[:limit]
        except Exception as e:
            logger.error(f"Error getting collaborative recommendations: {str(e)}")
            return []
    
    @staticmethod
    async def get_trending_advertisements(
        limit: int = 10,
        days: int = 7,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get trending advertisements based on recent views and favorites.
        
        Args:
            limit: The maximum number of advertisements to return
            days: The number of days to consider for trending
            exclude_ids: Advertisement IDs to exclude
            
        Returns:
            List[Dict]: The trending advertisements
        """
        try:
            # Initialize exclude IDs
            exclude_ids = exclude_ids or []
            
            # Calculate the date threshold
            threshold_date = datetime.now() - timedelta(days=days)
            
            # Get recent views
            recent_views = await AdvertisementViewModel.objects.filter(
                created_at__gte=threshold_date
            ).values('advertisement_id').annotate(
                view_count=Count('id')
            )
            
            # Get recent favorites
            recent_favorites = await AdvertisementFavoriteModel.objects.filter(
                created_at__gte=threshold_date
            ).values('advertisement_id').annotate(
                favorite_count=Count('id')
            )
            
            # Create a dictionary of advertisement IDs to scores
            ad_scores = defaultdict(float)
            
            # Add view scores (1 point per view)
            for view in recent_views:
                ad_scores[view['advertisement_id']] += view['view_count']
            
            # Add favorite scores (3 points per favorite)
            for favorite in recent_favorites:
                ad_scores[favorite['advertisement_id']] += favorite['favorite_count'] * 3
            
            # Get the top-scored advertisements
            top_ad_ids = [ad_id for ad_id, _ in sorted(ad_scores.items(), key=lambda x: x[1], reverse=True)
                         if ad_id not in exclude_ids][:limit]
            
            # Get the advertisement details
            top_ads = await AdvertisementModel.objects.filter(
                id__in=top_ad_ids,
                status='APPROVED',
                is_active=True
            ).values('id', 'title', 'description', 'price', 'category_id', 'user_id',
                    'created_at', 'updated_at', 'is_featured')
            
            # Add scores and sort
            scored_ads = []
            for ad in top_ads:
                scored_ads.append({
                    **ad,
                    'score': ad_scores[ad['id']],
                    'source': 'trending'
                })
            
            # Sort by score (descending)
            scored_ads.sort(key=lambda x: x['score'], reverse=True)
            
            return scored_ads[:limit]
        except Exception as e:
            logger.error(f"Error getting trending advertisements: {str(e)}")
            return []
    
    @staticmethod
    async def get_random_advertisements(
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get random advertisements.
        
        Args:
            limit: The maximum number of advertisements to return
            exclude_ids: Advertisement IDs to exclude
            
        Returns:
            List[Dict]: The random advertisements
        """
        try:
            # Initialize exclude IDs
            exclude_ids = exclude_ids or []
            
            # Get random advertisements
            random_ads = await AdvertisementModel.objects.filter(
                status='APPROVED',
                is_active=True
            ).exclude(
                id__in=exclude_ids
            ).order_by('?').values(
                'id', 'title', 'description', 'price', 'category_id', 'user_id',
                'created_at', 'updated_at', 'is_featured'
            )[:limit]
            
            # Add source
            for ad in random_ads:
                ad['source'] = 'random'
                ad['score'] = 0
            
            return list(random_ads)
        except Exception as e:
            logger.error(f"Error getting random advertisements: {str(e)}")
            return []
