"""
Prometheus integration for the Soodam backend.

This module provides utilities for exposing metrics to Prometheus.
"""

import time
from typing import Callable, Dict, List, Optional, Tuple

from fastapi import FastAP<PERSON>, Request, Response
from prometheus_client import (
    REGISTRY,
    Counter,
    Gauge,
    Histogram,
    Summary,
    generate_latest,
    multiprocess,
)
from prometheus_client.exposition import CONTENT_TYPE_LATEST
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Define metrics
REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total number of HTTP requests",
    ["method", "endpoint", "status_code"]
)

REQUEST_TIME = Histogram(
    "http_request_duration_seconds",
    "HTTP request duration in seconds",
    ["method", "endpoint"],
    buckets=(0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0, float("inf"))
)

REQUEST_SIZE = Histogram(
    "http_request_size_bytes",
    "HTTP request size in bytes",
    ["method", "endpoint"],
    buckets=(100, 1_000, 10_000, 100_000, 1_000_000, float("inf"))
)

RESPONSE_SIZE = Histogram(
    "http_response_size_bytes",
    "HTTP response size in bytes",
    ["method", "endpoint"],
    buckets=(100, 1_000, 10_000, 100_000, 1_000_000, float("inf"))
)

REQUESTS_IN_PROGRESS = Gauge(
    "http_requests_in_progress",
    "Number of HTTP requests in progress",
    ["method", "endpoint"]
)

DB_QUERY_TIME = Histogram(
    "db_query_duration_seconds",
    "Database query duration in seconds",
    ["query_type"],
    buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, float("inf"))
)

DB_QUERY_COUNT = Counter(
    "db_query_total",
    "Total number of database queries",
    ["query_type"]
)

CACHE_HIT_COUNT = Counter(
    "cache_hit_total",
    "Total number of cache hits",
    ["cache_type"]
)

CACHE_MISS_COUNT = Counter(
    "cache_miss_total",
    "Total number of cache misses",
    ["cache_type"]
)

API_KEY_USAGE = Counter(
    "api_key_usage_total",
    "Total number of API key usages",
    ["api_key_id"]
)

LOGIN_ATTEMPT_COUNT = Counter(
    "login_attempt_total",
    "Total number of login attempts",
    ["success"]
)

TWO_FACTOR_AUTH_COUNT = Counter(
    "two_factor_auth_total",
    "Total number of two-factor authentication attempts",
    ["success"]
)

ACTIVE_USERS = Gauge(
    "active_users",
    "Number of active users"
)

PENDING_ADVERTISEMENTS = Gauge(
    "pending_advertisements",
    "Number of pending advertisements"
)

APPROVED_ADVERTISEMENTS = Gauge(
    "approved_advertisements",
    "Number of approved advertisements"
)

REJECTED_ADVERTISEMENTS = Gauge(
    "rejected_advertisements",
    "Number of rejected advertisements"
)


class PrometheusMiddleware(BaseHTTPMiddleware):
    """
    Middleware for collecting Prometheus metrics.

    This middleware collects metrics for HTTP requests, including:
    - Request count
    - Request duration
    - Request size
    - Response size
    - Requests in progress
    """

    def __init__(self, app: ASGIApp, exclude_paths: List[str] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or []

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip metrics collection for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Get endpoint for metrics
        endpoint = request.url.path
        method = request.method

        # Track requests in progress
        REQUESTS_IN_PROGRESS.labels(method=method, endpoint=endpoint).inc()

        # Record start time
        start_time = time.time()

        # Process the request
        try:
            response = await call_next(request)

            # Record request metrics
            status_code = response.status_code
            REQUEST_COUNT.labels(method=method, endpoint=endpoint, status_code=status_code).inc()

            # Record request duration
            REQUEST_TIME.labels(method=method, endpoint=endpoint).observe(time.time() - start_time)

            # Record request and response size
            # Don't try to read the request body as it may have been consumed already
            # Instead, use a default value or skip this metric
            REQUEST_SIZE.labels(method=method, endpoint=endpoint).observe(0)

            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk

            RESPONSE_SIZE.labels(method=method, endpoint=endpoint).observe(len(response_body))

            # Create a new response with the same content
            return Response(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
        finally:
            # Track requests in progress
            REQUESTS_IN_PROGRESS.labels(method=method, endpoint=endpoint).dec()


async def metrics_endpoint(request: Request) -> Response:
    """
    Endpoint for exposing Prometheus metrics.

    Args:
        request: The request

    Returns:
        Response: The response with Prometheus metrics
    """
    return Response(
        content=generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )


def setup_prometheus(app: FastAPI, exclude_paths: List[str] = None) -> None:
    """
    Set up Prometheus metrics collection for the FastAPI application.

    Args:
        app: The FastAPI application
        exclude_paths: Paths to exclude from metrics collection
    """
    # Add the Prometheus middleware
    app.add_middleware(
        PrometheusMiddleware,
        exclude_paths=exclude_paths or ["/metrics", "/api/health", "/api/docs", "/api/redoc"]
    )

    # Add the metrics endpoint
    app.add_route("/metrics", metrics_endpoint)


def track_db_query(query_type: str, duration: float) -> None:
    """
    Track a database query.

    Args:
        query_type: The type of query (e.g., "select", "insert", "update", "delete")
        duration: The query duration in seconds
    """
    DB_QUERY_TIME.labels(query_type=query_type).observe(duration)
    DB_QUERY_COUNT.labels(query_type=query_type).inc()


def track_cache_hit(cache_type: str) -> None:
    """
    Track a cache hit.

    Args:
        cache_type: The type of cache (e.g., "api", "db", "response")
    """
    CACHE_HIT_COUNT.labels(cache_type=cache_type).inc()


def track_cache_miss(cache_type: str) -> None:
    """
    Track a cache miss.

    Args:
        cache_type: The type of cache (e.g., "api", "db", "response")
    """
    CACHE_MISS_COUNT.labels(cache_type=cache_type).inc()


def track_api_key_usage(api_key_id: str) -> None:
    """
    Track an API key usage.

    Args:
        api_key_id: The API key ID
    """
    API_KEY_USAGE.labels(api_key_id=api_key_id).inc()


def track_login_attempt(success: bool) -> None:
    """
    Track a login attempt.

    Args:
        success: Whether the login attempt was successful
    """
    LOGIN_ATTEMPT_COUNT.labels(success=str(success).lower()).inc()


def track_two_factor_auth(success: bool) -> None:
    """
    Track a two-factor authentication attempt.

    Args:
        success: Whether the two-factor authentication attempt was successful
    """
    TWO_FACTOR_AUTH_COUNT.labels(success=str(success).lower()).inc()


def update_active_users(count: int) -> None:
    """
    Update the number of active users.

    Args:
        count: The number of active users
    """
    ACTIVE_USERS.set(count)


def update_advertisement_counts(pending: int, approved: int, rejected: int) -> None:
    """
    Update the advertisement counts.

    Args:
        pending: The number of pending advertisements
        approved: The number of approved advertisements
        rejected: The number of rejected advertisements
    """
    PENDING_ADVERTISEMENTS.set(pending)
    APPROVED_ADVERTISEMENTS.set(approved)
    REJECTED_ADVERTISEMENTS.set(rejected)
