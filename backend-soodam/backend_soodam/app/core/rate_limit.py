"""
Rate limiting module for the <PERSON><PERSON> backend.

This module provides a standardized approach to rate limiting API requests.
"""

import os
import time
from typing import Callable, Dict, List, Optional, Tuple, Union

from django.core.cache import cache
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Response, status
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Default rate limit (requests per minute)
DEFAULT_RATE_LIMIT = 60

# Default block threshold (requests per minute)
DEFAULT_BLOCK_THRESHOLD = 1


# Default rate limit window (in seconds)
DEFAULT_RATE_LIMIT_WINDOW = 60

# Default rate limit groups
DEFAULT_RATE_LIMIT_GROUPS = {
    "default": {
        "limit": DEFAULT_RATE_LIMIT,
        "window": DEFAULT_RATE_LIMIT_WINDOW
    },
    "auth": {
        "limit": 10,
        "window": 60
    },
    "admin": {
        "limit": 100,
        "window": 60
    }
}


class RateLimitExceeded(HTTPException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, limit: int, window: int, retry_after: int):
        """
        Initialize the exception.
        
        Args:
            limit: The rate limit
            window: The rate limit window in seconds
            retry_after: The number of seconds to wait before retrying
        """
        detail = f"Rate limit exceeded: {limit} requests per {window} seconds. Retry after {retry_after} seconds."
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            headers={"Retry-After": str(retry_after)}
        )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware for rate limiting API requests.
    
    This middleware limits the number of requests a client can make to the API
    within a specified time window.
    """
    
    def __init__(
        self,
        app: ASGIApp,
        limit: int = DEFAULT_RATE_LIMIT,
        window: int = DEFAULT_RATE_LIMIT_WINDOW,
        groups: Dict[str, Dict[str, int]] = None,
        path_groups: Dict[str, str] = None,
        exclude_paths: List[str] = None,
        block_threshold: int = DEFAULT_BLOCK_THRESHOLD,
    ):
        """
        Initialize the middleware.
        
        Args:
            app: The ASGI application
            limit: The default rate limit (requests per window)
            window: The default rate limit window in seconds
            groups: Rate limit groups (e.g., {"auth": {"limit": 10, "window": 60}})
            path_groups: Path to group mappings (e.g., {"/api/auth": "auth"})
            exclude_paths: Paths to exclude from rate limiting
            block_threshold: Number of violations before blocking an IP
        """
        super().__init__(app)
        self.limit = limit
        self.window = window
        self.groups = groups or DEFAULT_RATE_LIMIT_GROUPS
        self.path_groups = path_groups or {}
        self.exclude_paths = exclude_paths or []
        self.block_threshold = block_threshold
        
        # Initialize Redis connection for IP blocking
        redis_host = os.getenv("REDIS_HOST", "redis")
        redis_port = int(os.getenv("REDIS_PORT", "6379"))
        try:
            import redis
            self.redis = redis.Redis(host=redis_host, port=redis_port, db=0)
        except ImportError:
            import logging
            logging.getLogger("rate_limit").warning("Redis not available, IP blocking disabled")
            self.redis = None
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Check if IP is blocked
        if self.redis and self._is_ip_blocked(client_ip):
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={"detail": "Access denied due to excessive rate limit violations."}
            )
        
        # Get rate limit group for the path
        group = "default"
        for path, path_group in self.path_groups.items():
            if request.url.path.startswith(path):
                group = path_group
                break
        
        # Get rate limit and window for the group
        group_config = self.groups.get(group, {"limit": self.limit, "window": self.window})
        limit = group_config["limit"]
        window = group_config["window"]
        
        # Generate cache key for rate limiting
        cache_key = f"rate_limit:{client_ip}:{group}"
        
        # Get current rate limit data
        rate_limit_data = cache.get(cache_key)
        
        if rate_limit_data is None:
            # First request, initialize rate limit data
            rate_limit_data = {
                "count": 1,
                "start_time": time.time()
            }
            cache.set(cache_key, rate_limit_data, window)
        else:
            # Check if window has expired
            elapsed = time.time() - rate_limit_data["start_time"]
            
            if elapsed > window:
                # Window expired, reset rate limit data
                rate_limit_data = {
                    "count": 1,
                    "start_time": time.time()
                }
                cache.set(cache_key, rate_limit_data, window)
            else:
                # Window not expired, increment count
                rate_limit_data["count"] += 1
                
                # Check if rate limit exceeded
                if rate_limit_data["count"] > limit:
                    # Increment violation count
                    if self.redis:
                        self._increment_violation_count(client_ip)
                    
                    # Calculate retry after
                    retry_after = int(window - elapsed)
                    
                    # Raise rate limit exceeded exception
                    raise RateLimitExceeded(limit, window, retry_after)
                
                # Update rate limit data
                cache.set(cache_key, rate_limit_data, window)
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(max(0, limit - rate_limit_data["count"]))
        response.headers["X-RateLimit-Reset"] = str(int(rate_limit_data["start_time"] + window))
        
        return response

    def _is_ip_blocked(self, ip: str) -> bool:
        """Check if an IP is blocked"""
        if not self.redis:
            return False
        return bool(self.redis.get(f"blocked_ip:{ip}"))

    def _increment_violation_count(self, ip: str) -> None:
        """Increment violation count for an IP and block if threshold exceeded"""
        if not self.redis:
            return
        
        key = f"rate_limit_violation:{ip}"
        count = self.redis.incr(key)
        # Set expiry if new key
        if count == 1:
            self.redis.expire(key, 3600)  # 1 hour expiry
        
        # Block IP if threshold exceeded
        if count >= self.block_threshold:
            self._block_ip(ip)

    def _block_ip(self, ip: str) -> None:
        """Block an IP address"""
        if not self.redis:
            return
        
        # Add to Redis with 24-hour expiry
        self.redis.setex(f"blocked_ip:{ip}", 86400, 1)  # 24 hours
        
        # Log the blocked IP
        import logging
        logger = logging.getLogger("rate_limit")
        logger.warning(f"Blocked IP address {ip} due to excessive rate limit violations")


def setup_rate_limiting(
    app: FastAPI,
    limit: int = DEFAULT_RATE_LIMIT,
    window: int = DEFAULT_RATE_LIMIT_WINDOW,
    groups: Dict[str, Dict[str, int]] = None,
    path_groups: Dict[str, str] = None,
    exclude_paths: List[str] = None,
    block_threshold=10  # Number of violations before blocking
) -> None:

    """
    Set up rate limiting for the application.
    
    Args:
        app: The FastAPI application
        limit: The default rate limit (requests per window)
        window: The default rate limit window in seconds
        groups: Rate limit groups (e.g., {"auth": {"limit": 10, "window": 60}})
        path_groups: Path to group mappings (e.g., {"/api/auth": "auth"})
        exclude_paths: Paths to exclude from rate limiting
        block_threshold: Number of violations before blocking an IP
    """
    # Add the rate limit middleware with IP blocking
    app.add_middleware(
        RateLimitMiddleware,
        limit=limit,
        window=window,
        groups=groups or {
            "default": {"limit": 100, "window": 60},
            "auth": {"limit": 5, "window": 60},  # Stricter limits for auth
            "admin": {"limit": 50, "window": 60},
            "sensitive": {"limit": 3, "window": 60}  # Very strict for sensitive endpoints
        },
        path_groups=path_groups or {
            "/api/auth": "auth",
            "/api/admin": "admin",
            "/api/user/edit_user_info": "sensitive"
        },
        exclude_paths=exclude_paths or [
            "/static",
            "/media",
            "/api/health",
            "/metrics",
            "/api/docs",
            "/api/redoc"
        ],
        block_threshold=block_threshold
    )
