"""
Enhanced API versioning module for the Soodam backend.

This module provides a standardized approach to API versioning using FastAPI's
dependency injection system and path operations.
"""

from enum import Enum
from typing import Callable, Dict, Optional, Tuple, TypeVar, Union, Any
from fastapi import APIRouter, Depends, FastAPI, Header, HTTPException, Request, status
import re

# Type variable for callable functions
CallableT = TypeVar('CallableT', bound=Callable[..., Any])

# Default API version
DEFAULT_API_VERSION = (1, 0)

class ApiVersion(str, Enum):
    """API version enum for type safety and validation"""
    V1_0 = "1.0"
    V1_1 = "1.1"
    V2_0 = "2.0"
    
    @classmethod
    def from_string(cls, version_str: str) -> "ApiVersion":
        """Convert a string version to an ApiVersion enum value"""
        try:
            return cls(version_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid API version: {version_str}. Supported versions: {[v.value for v in cls]}"
            )
    
    @classmethod
    def from_tuple(cls, version_tuple: Tuple[int, int]) -> "ApiVersion":
        """Convert a tuple version (major, minor) to an ApiVersion enum value"""
        version_str = f"{version_tuple[0]}.{version_tuple[1]}"
        return cls.from_string(version_str)
    
    @property
    def as_tuple(self) -> Tuple[int, int]:
        """Convert the enum value to a tuple (major, minor)"""
        major, minor = self.value.split(".")
        return (int(major), int(minor))


def get_version_from_accept_header(accept_header: Optional[str] = None) -> ApiVersion:
    """
    Extract API version from the Accept header.
    
    Example Accept header: "application/json; version=1.0"
    
    Returns the default version if no version is specified.
    """
    if not accept_header:
        return ApiVersion.V1_0
    
    # Extract version from Accept header using regex
    version_match = re.search(r'version=(\d+\.\d+)', accept_header)
    if version_match:
        version_str = version_match.group(1)
        try:
            return ApiVersion.from_string(version_str)
        except ValueError:
            return ApiVersion.V1_0
    
    return ApiVersion.V1_0


async def get_api_version(
    accept: Optional[str] = Header(None, description="Accept header with optional version parameter")
) -> ApiVersion:
    """
    FastAPI dependency to extract the API version from the Accept header.
    
    Usage:
    ```
    @app.get("/endpoint")
    async def my_endpoint(api_version: ApiVersion = Depends(get_api_version)):
        if api_version == ApiVersion.V1_0:
            # Handle v1.0
            pass
        elif api_version == ApiVersion.V2_0:
            # Handle v2.0
            pass
    ```
    """
    return get_version_from_accept_header(accept)


def version(major: int, minor: int = 0) -> Callable[[CallableT], CallableT]:
    """
    Decorator for versioning FastAPI router endpoints with version (major, minor)
    
    Usage:
    ```
    @router.get('/endpoint')
    @version(1, 0)
    def endpoint_v1():
        return {"version": "1.0"}
        
    @router.get('/endpoint')
    @version(2, 0)
    def endpoint_v2():
        return {"version": "2.0"}
    ```
    """
    def decorator(func: CallableT) -> CallableT:
        func._api_version = (major, minor)
        return func

    return decorator


class VersionedAPIRouter:
    """
    A class to manage versioned API routers.
    
    This class allows you to register multiple versions of the same endpoint
    and automatically route requests to the appropriate version based on the
    Accept header.
    
    Usage:
    ```
    versioned_router = VersionedAPIRouter()
    
    @versioned_router.get('/endpoint', version=ApiVersion.V1_0)
    def endpoint_v1():
        return {"version": "1.0"}
        
    @versioned_router.get('/endpoint', version=ApiVersion.V2_0)
    def endpoint_v2():
        return {"version": "2.0"}
        
    # Register with FastAPI
    app = FastAPI()
    versioned_router.include_router(app)
    ```
    """
    
    def __init__(self, prefix: str = "", tags: Optional[list] = None):
        self.prefix = prefix
        self.tags = tags or []
        self.routers: Dict[ApiVersion, APIRouter] = {}
        
        # Create a router for each API version
        for version in ApiVersion:
            self.routers[version] = APIRouter(prefix=prefix, tags=tags)
    
    def get(self, path: str, version: ApiVersion, **kwargs):
        """Register a GET endpoint for a specific API version"""
        def decorator(func: Callable):
            self.routers[version].get(path, **kwargs)(func)
            return func
        return decorator
    
    def post(self, path: str, version: ApiVersion, **kwargs):
        """Register a POST endpoint for a specific API version"""
        def decorator(func: Callable):
            self.routers[version].post(path, **kwargs)(func)
            return func
        return decorator
    
    def put(self, path: str, version: ApiVersion, **kwargs):
        """Register a PUT endpoint for a specific API version"""
        def decorator(func: Callable):
            self.routers[version].put(path, **kwargs)(func)
            return func
        return decorator
    
    def delete(self, path: str, version: ApiVersion, **kwargs):
        """Register a DELETE endpoint for a specific API version"""
        def decorator(func: Callable):
            self.routers[version].delete(path, **kwargs)(func)
            return func
        return decorator
    
    def patch(self, path: str, version: ApiVersion, **kwargs):
        """Register a PATCH endpoint for a specific API version"""
        def decorator(func: Callable):
            self.routers[version].patch(path, **kwargs)(func)
            return func
        return decorator
    
    def include_router(self, app: FastAPI):
        """Include all version-specific routers in the FastAPI app"""
        for version, router in self.routers.items():
            major, minor = version.as_tuple
            app.include_router(
                router,
                prefix=f"/api/v{major}",
                tags=self.tags
            )
