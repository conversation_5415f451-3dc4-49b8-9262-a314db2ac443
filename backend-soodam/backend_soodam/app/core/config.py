# Add these settings to your config.py file
import os

# Iranian Bank Gateways Configuration
ZARIN<PERSON>L_MERCHANT_ID = os.getenv("ZARINPAL_MERCHANT_ID", "your-merchant-id")
ZARINPAL_CALLBACK_URL = os.getenv("ZARINPAL_CALLBACK_URL", "https://your-domain.com/api/payments/callback")
ZARINPAL_SANDBOX = os.getenv("ZARINPAL_SANDBOX", "True") == "True"

MELLAT_TERMINAL_ID = os.getenv("MELLAT_TERMINAL_ID", "your-terminal-id")
MELLAT_USERNAME = os.getenv("MELLAT_USERNAME", "your-username")
MELLAT_PASSWORD = os.getenv("MELLAT_PASSWORD", "your-password")
MELLAT_CALLBACK_URL = os.getenv("MELLAT_CALLBACK_URL", "https://your-domain.com/api/payments/callback")

SAMAN_MERCHANT_ID = os.getenv("SAMAN_MERCHANT_ID", "your-merchant-id")
SAMAN_CALLBACK_URL = os.getenv("SAMAN_CALLBACK_URL", "https://your-domain.com/api/payments/callback")

PARSIAN_PIN = os.getenv("PARSIAN_PIN", "your-pin")
PARSIAN_CALLBACK_URL = os.getenv("PARSIAN_CALLBACK_URL", "https://your-domain.com/api/payments/callback")