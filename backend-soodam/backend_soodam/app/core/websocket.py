"""
WebSocket support for the <PERSON><PERSON> backend.

This module provides utilities for WebSocket connections and real-time updates.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Set, Union

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class WebSocketMessage(BaseModel):
    """WebSocket message model."""
    
    type: str = Field(..., description="Message type")
    data: dict = Field(..., description="Message data")
    channel: Optional[str] = Field(None, description="Message channel")


class ConnectionManager:
    """
    WebSocket connection manager.
    
    This class manages WebSocket connections and broadcasts messages to connected clients.
    """
    
    def __init__(self):
        """Initialize the connection manager."""
        # All active connections
        self.active_connections: List[WebSocket] = []
        
        # Connections by user ID
        self.user_connections: Dict[int, List[WebSocket]] = {}
        
        # Connections by channel
        self.channel_connections: Dict[str, List[WebSocket]] = {}
        
        # User subscriptions to channels
        self.user_subscriptions: Dict[int, Set[str]] = {}
        
        # Connection to user ID mapping
        self.connection_user_map: Dict[WebSocket, Optional[int]] = {}
        
        # Connection to channels mapping
        self.connection_channels_map: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: Optional[int] = None) -> None:
        """
        Connect a WebSocket client.
        
        Args:
            websocket: The WebSocket connection
            user_id: The user ID (optional)
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_user_map[websocket] = user_id
        self.connection_channels_map[websocket] = set()
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(websocket)
            
            # Subscribe to user's channels
            if user_id in self.user_subscriptions:
                for channel in self.user_subscriptions[user_id]:
                    await self.subscribe(websocket, channel)
        
        logger.info(f"Client connected: {user_id or 'anonymous'}")
    
    async def disconnect(self, websocket: WebSocket) -> None:
        """
        Disconnect a WebSocket client.
        
        Args:
            websocket: The WebSocket connection
        """
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # Remove from user connections
        user_id = self.connection_user_map.get(websocket)
        if user_id and user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # Remove from channel connections
        channels = self.connection_channels_map.get(websocket, set())
        for channel in channels:
            if channel in self.channel_connections and websocket in self.channel_connections[channel]:
                self.channel_connections[channel].remove(websocket)
                if not self.channel_connections[channel]:
                    del self.channel_connections[channel]
        
        # Remove from mappings
        if websocket in self.connection_user_map:
            del self.connection_user_map[websocket]
        
        if websocket in self.connection_channels_map:
            del self.connection_channels_map[websocket]
        
        logger.info(f"Client disconnected: {user_id or 'anonymous'}")
    
    async def subscribe(self, websocket: WebSocket, channel: str) -> None:
        """
        Subscribe a WebSocket client to a channel.
        
        Args:
            websocket: The WebSocket connection
            channel: The channel to subscribe to
        """
        if channel not in self.channel_connections:
            self.channel_connections[channel] = []
        
        if websocket not in self.channel_connections[channel]:
            self.channel_connections[channel].append(websocket)
        
        # Add to connection channels map
        if websocket in self.connection_channels_map:
            self.connection_channels_map[websocket].add(channel)
        
        # Add to user subscriptions
        user_id = self.connection_user_map.get(websocket)
        if user_id:
            if user_id not in self.user_subscriptions:
                self.user_subscriptions[user_id] = set()
            self.user_subscriptions[user_id].add(channel)
        
        logger.info(f"Client subscribed to channel: {channel}")
        
        # Send confirmation message
        await self.send_personal_message(
            WebSocketMessage(
                type="subscription",
                data={"channel": channel, "status": "subscribed"},
                channel=channel
            ),
            websocket
        )
    
    async def unsubscribe(self, websocket: WebSocket, channel: str) -> None:
        """
        Unsubscribe a WebSocket client from a channel.
        
        Args:
            websocket: The WebSocket connection
            channel: The channel to unsubscribe from
        """
        if channel in self.channel_connections and websocket in self.channel_connections[channel]:
            self.channel_connections[channel].remove(websocket)
            if not self.channel_connections[channel]:
                del self.channel_connections[channel]
        
        # Remove from connection channels map
        if websocket in self.connection_channels_map and channel in self.connection_channels_map[websocket]:
            self.connection_channels_map[websocket].remove(channel)
        
        # Remove from user subscriptions
        user_id = self.connection_user_map.get(websocket)
        if user_id and user_id in self.user_subscriptions and channel in self.user_subscriptions[user_id]:
            self.user_subscriptions[user_id].remove(channel)
            if not self.user_subscriptions[user_id]:
                del self.user_subscriptions[user_id]
        
        logger.info(f"Client unsubscribed from channel: {channel}")
        
        # Send confirmation message
        await self.send_personal_message(
            WebSocketMessage(
                type="subscription",
                data={"channel": channel, "status": "unsubscribed"},
                channel=channel
            ),
            websocket
        )
    
    async def send_personal_message(self, message: WebSocketMessage, websocket: WebSocket) -> None:
        """
        Send a message to a specific client.
        
        Args:
            message: The message to send
            websocket: The WebSocket connection
        """
        try:
            await websocket.send_text(message.json())
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
            await self.disconnect(websocket)
    
    async def broadcast(self, message: WebSocketMessage) -> None:
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: The message to send
        """
        disconnected = []
        
        for websocket in self.active_connections:
            try:
                await websocket.send_text(message.json())
            except Exception as e:
                logger.error(f"Error broadcasting message: {str(e)}")
                disconnected.append(websocket)
        
        # Disconnect clients that had errors
        for websocket in disconnected:
            await self.disconnect(websocket)
    
    async def broadcast_to_channel(self, message: WebSocketMessage, channel: str) -> None:
        """
        Broadcast a message to all clients subscribed to a channel.
        
        Args:
            message: The message to send
            channel: The channel to broadcast to
        """
        if channel not in self.channel_connections:
            return
        
        disconnected = []
        
        for websocket in self.channel_connections[channel]:
            try:
                await websocket.send_text(message.json())
            except Exception as e:
                logger.error(f"Error broadcasting to channel: {str(e)}")
                disconnected.append(websocket)
        
        # Disconnect clients that had errors
        for websocket in disconnected:
            await self.disconnect(websocket)
    
    async def broadcast_to_user(self, message: WebSocketMessage, user_id: int) -> None:
        """
        Broadcast a message to all connections of a specific user.
        
        Args:
            message: The message to send
            user_id: The user ID
        """
        if user_id not in self.user_connections:
            return
        
        disconnected = []
        
        for websocket in self.user_connections[user_id]:
            try:
                await websocket.send_text(message.json())
            except Exception as e:
                logger.error(f"Error broadcasting to user: {str(e)}")
                disconnected.append(websocket)
        
        # Disconnect clients that had errors
        for websocket in disconnected:
            await self.disconnect(websocket)


# Create a global connection manager
manager = ConnectionManager()
