"""
Autocomplete functionality for the <PERSON><PERSON> backend.

This module provides utilities for autocomplete suggestions.
"""

import logging
import re
from typing import Dict, List, Optional, Set, Tuple

from elasticsearch import AsyncElasticsearch, NotFoundError

from .elasticsearch import es_client

logger = logging.getLogger(__name__)

# Autocomplete indices
AUTOCOMPLETE_INDEX = 'autocomplete'


async def setup_autocomplete():
    """
    Set up autocomplete index in Elasticsearch.
    """
    try:
        # Create autocomplete index
        if not await es_client.indices.exists(index=AUTOCOMPLETE_INDEX):
            await es_client.indices.create(
                index=AUTOCOMPLETE_INDEX,
                body={
                    'settings': {
                        'number_of_shards': 1,
                        'number_of_replicas': 0,
                        'analysis': {
                            'analyzer': {
                                'autocomplete': {
                                    'tokenizer': 'autocomplete',
                                    'filter': ['lowercase']
                                },
                                'autocomplete_search': {
                                    'tokenizer': 'lowercase'
                                },
                                'persian': {
                                    'tokenizer': 'standard',
                                    'filter': ['lowercase', 'arabic_normalization', 'persian_normalization']
                                }
                            },
                            'tokenizer': {
                                'autocomplete': {
                                    'type': 'edge_ngram',
                                    'min_gram': 2,
                                    'max_gram': 20,
                                    'token_chars': ['letter', 'digit']
                                }
                            }
                        }
                    },
                    'mappings': {
                        'properties': {
                            'text': {
                                'type': 'text',
                                'analyzer': 'autocomplete',
                                'search_analyzer': 'autocomplete_search',
                                'fields': {
                                    'persian': {
                                        'type': 'text',
                                        'analyzer': 'persian'
                                    }
                                }
                            },
                            'type': {
                                'type': 'keyword'
                            },
                            'weight': {
                                'type': 'integer'
                            },
                            'metadata': {
                                'type': 'object',
                                'enabled': True
                            }
                        }
                    }
                }
            )
            logger.info(f"Created Elasticsearch index: {AUTOCOMPLETE_INDEX}")
    except Exception as e:
        logger.error(f"Error setting up autocomplete: {str(e)}")


async def index_suggestion(
    text: str,
    type: str,
    weight: int = 1,
    metadata: Optional[Dict] = None
) -> Dict:
    """
    Index a suggestion in the autocomplete index.
    
    Args:
        text: The suggestion text
        type: The suggestion type (e.g., 'advertisement', 'category', 'location')
        weight: The suggestion weight (higher values appear first)
        metadata: Additional metadata for the suggestion
        
    Returns:
        Dict: The indexing result
    """
    try:
        # Generate a document ID based on text and type
        doc_id = f"{type}_{text.lower().replace(' ', '_')}"
        
        # Index the suggestion
        result = await es_client.index(
            index=AUTOCOMPLETE_INDEX,
            id=doc_id,
            document={
                'text': text,
                'type': type,
                'weight': weight,
                'metadata': metadata or {}
            },
            refresh=True
        )
        
        logger.info(f"Indexed suggestion '{text}' of type '{type}' in autocomplete index")
        
        return result
    except Exception as e:
        logger.error(f"Error indexing suggestion '{text}': {str(e)}")
        raise


async def delete_suggestion(
    text: str,
    type: str
) -> Dict:
    """
    Delete a suggestion from the autocomplete index.
    
    Args:
        text: The suggestion text
        type: The suggestion type
        
    Returns:
        Dict: The deletion result
    """
    try:
        # Generate a document ID based on text and type
        doc_id = f"{type}_{text.lower().replace(' ', '_')}"
        
        # Delete the suggestion
        result = await es_client.delete(
            index=AUTOCOMPLETE_INDEX,
            id=doc_id,
            refresh=True
        )
        
        logger.info(f"Deleted suggestion '{text}' of type '{type}' from autocomplete index")
        
        return result
    except NotFoundError:
        logger.warning(f"Suggestion '{text}' of type '{type}' not found in autocomplete index")
        return {'result': 'not_found'}
    except Exception as e:
        logger.error(f"Error deleting suggestion '{text}': {str(e)}")
        raise


async def get_suggestions(
    query: str,
    types: Optional[List[str]] = None,
    limit: int = 10
) -> List[Dict]:
    """
    Get autocomplete suggestions for a query.
    
    Args:
        query: The query to get suggestions for
        types: The suggestion types to include (e.g., ['advertisement', 'category', 'location'])
        limit: The maximum number of suggestions to return
        
    Returns:
        List[Dict]: The suggestions
    """
    try:
        # Build the query
        must_clauses = []
        filter_clauses = []
        
        # Add the search query
        if query:
            must_clauses.append({
                'multi_match': {
                    'query': query,
                    'fields': ['text', 'text.persian'],
                    'type': 'bool_prefix',
                    'operator': 'and'
                }
            })
        
        # Add type filter
        if types:
            filter_clauses.append({
                'terms': {
                    'type': types
                }
            })
        
        # Build the full query
        body = {
            'query': {
                'bool': {
                    'must': must_clauses,
                    'filter': filter_clauses
                }
            },
            'sort': [
                {'weight': {'order': 'desc'}},
                {'_score': {'order': 'desc'}}
            ],
            'size': limit
        }
        
        # Execute the search
        result = await es_client.search(
            index=AUTOCOMPLETE_INDEX,
            body=body
        )
        
        # Process the results
        hits = result['hits']['hits']
        
        # Extract the suggestions
        suggestions = []
        for hit in hits:
            source = hit['_source']
            
            suggestions.append({
                'text': source['text'],
                'type': source['type'],
                'weight': source['weight'],
                'metadata': source.get('metadata', {}),
                'score': hit['_score']
            })
        
        return suggestions
    except Exception as e:
        logger.error(f"Error getting suggestions for '{query}': {str(e)}")
        raise


async def index_popular_searches(searches: List[Dict]) -> Dict:
    """
    Index popular searches in the autocomplete index.
    
    Args:
        searches: List of popular searches with text, count, and metadata
        
    Returns:
        Dict: The indexing results
    """
    try:
        results = []
        
        for search in searches:
            text = search['text']
            count = search.get('count', 1)
            metadata = search.get('metadata', {})
            
            # Index the suggestion with weight based on count
            result = await index_suggestion(
                text=text,
                type='search',
                weight=count,
                metadata=metadata
            )
            
            results.append(result)
        
        return {
            'success': True,
            'count': len(results),
            'results': results
        }
    except Exception as e:
        logger.error(f"Error indexing popular searches: {str(e)}")
        raise


async def extract_keywords_from_text(text: str) -> List[str]:
    """
    Extract keywords from text for autocomplete suggestions.
    
    Args:
        text: The text to extract keywords from
        
    Returns:
        List[str]: The extracted keywords
    """
    # Remove special characters and convert to lowercase
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    
    # Split into words
    words = text.split()
    
    # Remove duplicates and short words
    keywords = set()
    for word in words:
        if len(word) >= 3:
            keywords.add(word)
    
    # Extract phrases (2-3 words)
    phrases = []
    for i in range(len(words) - 1):
        if len(words[i]) >= 3 and len(words[i+1]) >= 3:
            phrases.append(f"{words[i]} {words[i+1]}")
    
    for i in range(len(words) - 2):
        if len(words[i]) >= 3 and len(words[i+1]) >= 3 and len(words[i+2]) >= 3:
            phrases.append(f"{words[i]} {words[i+1]} {words[i+2]}")
    
    # Combine keywords and phrases
    all_keywords = list(keywords) + phrases
    
    # Sort by length (longer first)
    all_keywords.sort(key=len, reverse=True)
    
    # Limit to top 10
    return all_keywords[:10]
