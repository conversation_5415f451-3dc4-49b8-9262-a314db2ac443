"""
OpenAPI documentation enhancement module for the Soodam backend.

This module provides utilities for enhancing the OpenAPI documentation
generated by FastAPI.
"""

from typing import Any, Dict, List, Optional, Union
from fastapi import FastAP<PERSON>, APIRouter, Depends
from fastapi.openapi.utils import get_openapi

def custom_openapi(app: FastAPI) -> Dict[str, Any]:
    """
    Generate a custom OpenAPI schema for the FastAPI application.
    
    This function enhances the default OpenAPI schema with:
    - Better descriptions
    - More examples
    - Authentication information
    - Response examples
    
    Args:
        app: The FastAPI application
        
    Returns:
        Dict[str, Any]: The enhanced OpenAPI schema
    """
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT token in the format: Bearer {token}"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add API versioning information
    openapi_schema["info"]["x-api-versioning"] = {
        "supported_versions": ["1.0", "2.0"],
        "current_version": "1.0",
        "versioning_method": "Accept header",
        "example": "Accept: application/json; version=1.0"
    }
    
    # Add contact information
    openapi_schema["info"]["contact"] = {
        "name": "Soodam Support",
        "url": "https://soodam.com/support",
        "email": "<EMAIL>"
    }
    
    # Add license information
    openapi_schema["info"]["license"] = {
        "name": "Proprietary",
        "url": "https://soodam.com/license"
    }
    
    # Add server information
    openapi_schema["servers"] = [
        {
            "url": "/",
            "description": "Current server"
        },
        {
            "url": "https://api.soodam.com",
            "description": "Production server"
        },
        {
            "url": "https://staging-api.soodam.com",
            "description": "Staging server"
        }
    ]
    
    # Add tags with descriptions
    openapi_schema["tags"] = [
        {
            "name": "auth",
            "description": "Authentication operations",
            "externalDocs": {
                "description": "Authentication documentation",
                "url": "https://soodam.com/docs/auth"
            }
        },
        {
            "name": "users",
            "description": "User management operations",
            "externalDocs": {
                "description": "User management documentation",
                "url": "https://soodam.com/docs/users"
            }
        },
        {
            "name": "advertisements",
            "description": "Advertisement operations",
            "externalDocs": {
                "description": "Advertisement documentation",
                "url": "https://soodam.com/docs/advertisements"
            }
        },
        {
            "name": "blogs",
            "description": "Blog operations",
            "externalDocs": {
                "description": "Blog documentation",
                "url": "https://soodam.com/docs/blogs"
            }
        },
        {
            "name": "admin",
            "description": "Admin operations",
            "externalDocs": {
                "description": "Admin documentation",
                "url": "https://soodam.com/docs/admin"
            }
        },
        {
            "name": "Health",
            "description": "Health check operations",
            "externalDocs": {
                "description": "Health check documentation",
                "url": "https://soodam.com/docs/health"
            }
        },
        {
            "name": "Monitoring",
            "description": "Monitoring operations",
            "externalDocs": {
                "description": "Monitoring documentation",
                "url": "https://soodam.com/docs/monitoring"
            }
        }
    ]
    
    # Add common response examples
    openapi_schema["components"]["examples"] = {
        "ValidationError": {
            "summary": "Validation Error",
            "value": {
                "detail": [
                    {
                        "loc": ["body", "email"],
                        "msg": "field required",
                        "type": "value_error.missing"
                    }
                ]
            }
        },
        "AuthenticationError": {
            "summary": "Authentication Error",
            "value": {
                "detail": "Could not validate credentials"
            }
        },
        "PermissionError": {
            "summary": "Permission Error",
            "value": {
                "detail": "Not enough permissions"
            }
        },
        "NotFoundError": {
            "summary": "Not Found Error",
            "value": {
                "detail": "Item not found"
            }
        },
        "RateLimitError": {
            "summary": "Rate Limit Error",
            "value": {
                "detail": "Rate limit exceeded: 100 requests per 60 seconds. Retry after 30 seconds."
            }
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


def setup_openapi(app: FastAPI) -> None:
    """
    Set up custom OpenAPI documentation for the FastAPI application.
    
    Args:
        app: The FastAPI application
    """
    app.openapi = lambda: custom_openapi(app)
