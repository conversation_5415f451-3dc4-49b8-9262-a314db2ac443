"""
Database connection pooling for the <PERSON><PERSON> backend.

This module provides utilities for database connection pooling.
"""

import logging
import os
from typing import Dict, List, Optional

import psycopg2
from psycopg2 import pool
from django.conf import settings
from django.db import connections

logger = logging.getLogger(__name__)

# Connection pool
connection_pool = None


def setup_connection_pool(
    min_connections: int = 5,
    max_connections: int = 20,
    database: Optional[str] = None,
    user: Optional[str] = None,
    password: Optional[str] = None,
    host: Optional[str] = None,
    port: Optional[int] = None,
) -> None:
    """
    Set up the database connection pool.

    Args:
        min_connections: Minimum number of connections
        max_connections: Maximum number of connections
        database: Database name
        user: Database user
        password: Database password
        host: Database host
        port: Database port
    """
    global connection_pool

    # Get database settings from Django if not provided
    if not database:
        database = settings.DATABASES["default"]["NAME"]

    if not user:
        user = settings.DATABASES["default"]["USER"]

    if not password:
        password = settings.DATABASES["default"]["PASSWORD"]

    if not host:
        host = settings.DATABASES["default"]["HOST"]

    if not port:
        port = settings.DATABASES["default"]["PORT"]

    # Create connection pool
    try:
        # Convert port to integer if it's a string
        if port and isinstance(port, str):
            try:
                port = int(port)
            except ValueError:
                logger.warning(f"Invalid port value: {port}. Using default port.")
                port = 5432

        # Log connection details (without password)
        logger.info(f"Creating database connection pool to {host}:{port}/{database} as {user}")
        connection_pool = psycopg2.pool.ThreadedConnectionPool(
            minconn=min_connections,
            maxconn=max_connections,
            database=database,
            user=user,
            password=password,
            host=host,
            port=port,
        )

        logger.info(f"Database connection pool created with {min_connections} to {max_connections} connections")
    except Exception as e:
        logger.error(f"Error creating database connection pool: {str(e)}")
        logger.error(f"Connection details: host={host}, port={port}, database={database}, user={user}")
        # Try with default port if port conversion failed
        if isinstance(port, str):
            try:
                logger.info("Retrying with default port 5432")
                connection_pool = psycopg2.pool.ThreadedConnectionPool(
                    minconn=min_connections,
                    maxconn=max_connections,
                    database=database,
                    user=user,
                    password=password,
                    host=host,
                    port=5432,
                )
                logger.info("Connection successful with default port")
                return
            except Exception as retry_error:
                logger.error(f"Retry failed: {str(retry_error)}")
        raise


def get_connection():
    """
    Get a connection from the pool.

    Returns:
        Connection: A database connection

    Raises:
        Exception: If the connection pool is not set up
    """
    global connection_pool

    if connection_pool is None:
        raise Exception("Database connection pool not set up")

    return connection_pool.getconn()


def release_connection(connection):
    """
    Release a connection back to the pool.

    Args:
        connection: The connection to release

    Raises:
        Exception: If the connection pool is not set up
    """
    global connection_pool

    if connection_pool is None:
        raise Exception("Database connection pool not set up")

    connection_pool.putconn(connection)


def close_all_connections():
    """
    Close all connections in the pool.

    Raises:
        Exception: If the connection pool is not set up
    """
    global connection_pool

    if connection_pool is None:
        raise Exception("Database connection pool not set up")

    connection_pool.closeall()
    logger.info("All database connections closed")


class PooledConnection:
    """
    Context manager for pooled database connections.

    This context manager gets a connection from the pool and releases it when done.
    """

    def __init__(self):
        self.connection = None

    def __enter__(self):
        self.connection = get_connection()
        return self.connection

    def __exit__(self, exc_type, exc_val, exc_tb):
        release_connection(self.connection)


def execute_query(query: str, params: Optional[List] = None, fetch: bool = True):
    """
    Execute a SQL query using a pooled connection.

    Args:
        query: The SQL query
        params: The query parameters
        fetch: Whether to fetch results

    Returns:
        List: The query results

    Raises:
        Exception: If an error occurs
    """
    with PooledConnection() as connection:
        with connection.cursor() as cursor:
            cursor.execute(query, params or [])

            if fetch:
                return cursor.fetchall()
            else:
                return None


def get_connection_info() -> Dict:
    """
    Get information about database connections.

    Returns:
        Dict: Information about database connections
    """
    # Get information about Django connections
    django_connections = []
    for alias, conn in connections.items():
        django_connections.append({
            "alias": alias,
            "vendor": conn.vendor,
            "database": conn.settings_dict["NAME"],
            "user": conn.settings_dict["USER"],
            "host": conn.settings_dict["HOST"],
            "port": conn.settings_dict["PORT"],
        })

    # Get information about the connection pool
    pool_info = {}
    if connection_pool:
        pool_info = {
            "min_connections": connection_pool.minconn,
            "max_connections": connection_pool.maxconn,
        }

    return {
        "django_connections": django_connections,
        "connection_pool": pool_info,
    }
