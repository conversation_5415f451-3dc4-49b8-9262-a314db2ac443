"""
Security module for the <PERSON><PERSON> backend.

This module provides utilities for enhancing API security.
"""

import base64
import hashlib
import hmac
import logging
import os
import secrets
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

from jose import jwt
import pyotp
from fastapi import Depends, FastAPI, HTTPException, Request, Security, status
from fastapi.security import API<PERSON>eyHeader, APIKeyQuery, HTTPAuthorizationCredentials, HTTPBearer

from django.conf import settings
from django.contrib.auth.hashers import check_password, make_password

logger = logging.getLogger(__name__)

# API key header
API_KEY_HEADER = APIKeyHeader(name="X-API-Key", auto_error=False)

# API key query parameter
API_KEY_QUERY = APIKeyQuery(name="api_key", auto_error=False)

# Bearer token
bearer_scheme = HTTPBearer(auto_error=False)

# JWT settings
JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "your-secret-key")
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 30
JWT_REFRESH_TOKEN_EXPIRE_DAYS = 7

# API key settings
API_KEY_LENGTH = 32
API_KEY_PREFIX = "sk_"

# 2FA settings
TOTP_ISSUER = "Soodam"
TOTP_DIGITS = 6
TOTP_INTERVAL = 30


def generate_api_key() -> str:
    """
    Generate a new API key.
    
    Returns:
        str: The generated API key
    """
    key = secrets.token_hex(API_KEY_LENGTH)
    return f"{API_KEY_PREFIX}{key}"


def hash_api_key(api_key: str) -> str:
    """
    Hash an API key for storage.
    
    Args:
        api_key: The API key to hash
        
    Returns:
        str: The hashed API key
    """
    return make_password(api_key)


def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """
    Verify an API key against a hashed key.
    
    Args:
        api_key: The API key to verify
        hashed_key: The hashed API key
        
    Returns:
        bool: True if the API key is valid, False otherwise
    """
    return check_password(api_key, hashed_key)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: The data to encode in the token
        expires_delta: The token expiration time
        
    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT refresh token.
    
    Args:
        data: The data to encode in the token
        expires_delta: The token expiration time
        
    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=JWT_REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    return encoded_jwt


def decode_token(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token.
    
    Args:
        token: The JWT token to decode
        
    Returns:
        Dict[str, Any]: The decoded token data
        
    Raises:
        HTTPException: If the token is invalid
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_api_key(
    api_key_header: str = Depends(API_KEY_HEADER),
    api_key_query: str = Depends(API_KEY_QUERY),
) -> str:
    """
    Get the API key from the request.
    
    Args:
        api_key_header: The API key from the header
        api_key_query: The API key from the query parameter
        
    Returns:
        str: The API key
        
    Raises:
        HTTPException: If no API key is provided
    """
    if api_key_header:
        return api_key_header
    if api_key_query:
        return api_key_query
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="API key required",
    )


async def get_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(bearer_scheme),
) -> str:
    """
    Get the JWT token from the request.
    
    Args:
        credentials: The HTTP authorization credentials
        
    Returns:
        str: The JWT token
        
    Raises:
        HTTPException: If no token is provided
    """
    if credentials:
        return credentials.credentials
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Token required",
        headers={"WWW-Authenticate": "Bearer"},
    )


def generate_totp_secret() -> str:
    """
    Generate a new TOTP secret.
    
    Returns:
        str: The generated TOTP secret
    """
    return pyotp.random_base32()


def get_totp_uri(secret: str, username: str) -> str:
    """
    Get the TOTP URI for QR code generation.
    
    Args:
        secret: The TOTP secret
        username: The username
        
    Returns:
        str: The TOTP URI
    """
    totp = pyotp.TOTP(secret, digits=TOTP_DIGITS, interval=TOTP_INTERVAL)
    return totp.provisioning_uri(username, issuer_name=TOTP_ISSUER)


def verify_totp(secret: str, token: str) -> bool:
    """
    Verify a TOTP token.
    
    Args:
        secret: The TOTP secret
        token: The TOTP token
        
    Returns:
        bool: True if the token is valid, False otherwise
    """
    totp = pyotp.TOTP(secret, digits=TOTP_DIGITS, interval=TOTP_INTERVAL)
    return totp.verify(token)
