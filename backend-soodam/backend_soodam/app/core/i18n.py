"""
Internationalization module for the <PERSON>dam backend.

This module provides utilities for internationalizing the API.
"""

import gettext
import logging
import os
from functools import lru_cache
from typing import Any, Callable, Dict, List, Optional, Set, Union

from django.conf import settings
from django.utils.translation import gettext as _
from django.utils.translation import gettext_lazy as _lazy
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)

# Default language
DEFAULT_LANGUAGE = "en"

# Available languages
AVAILABLE_LANGUAGES = {
    "en": "English",
    "fa": "Persian",
    "ar": "Arabic",
    "tr": "Turkish",
}

class TranslationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for handling translations.
    
    This middleware sets the language for the current request based on the
    Accept-Language header or a language query parameter.
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get language from query parameter
        lang_param = request.query_params.get("lang")
        
        # Get language from Accept-Language header
        accept_language = request.headers.get("Accept-Language", "")
        
        # Determine language
        language = DEFAULT_LANGUAGE
        
        if lang_param and lang_param in AVAILABLE_LANGUAGES:
            language = lang_param
        elif accept_language:
            # Parse Accept-Language header
            languages = []
            for item in accept_language.split(","):
                lang = item.strip().split(";")[0]
                if "-" in lang:
                    lang = lang.split("-")[0]
                languages.append(lang)
            
            # Find first available language
            for lang in languages:
                if lang in AVAILABLE_LANGUAGES:
                    language = lang
                    break
        
        # Set language in request state
        request.state.language = language
        
        # Process request
        response = await call_next(request)
        
        # Add Content-Language header
        response.headers["Content-Language"] = language
        
        return response


@lru_cache(maxsize=128)
def get_translator(language: str) -> gettext.NullTranslations:
    """
    Get a translator for the specified language.
    
    Args:
        language: The language code
        
    Returns:
        gettext.NullTranslations: The translator
    """
    try:
        locale_dir = os.path.join(settings.BASE_DIR, "locale")
        translator = gettext.translation("messages", locale_dir, languages=[language])
        return translator
    except FileNotFoundError:
        logger.warning(f"Translation file for language {language} not found")
        return gettext.NullTranslations()


def translate(text: str, language: Optional[str] = None) -> str:
    """
    Translate text to the specified language.
    
    Args:
        text: The text to translate
        language: The language code (None for default)
        
    Returns:
        str: The translated text
    """
    if language is None:
        language = DEFAULT_LANGUAGE
    
    translator = get_translator(language)
    return translator.gettext(text)


def setup_i18n(app: FastAPI) -> None:
    """
    Set up internationalization for the FastAPI application.
    
    Args:
        app: The FastAPI application
    """
    # Add translation middleware
    app.add_middleware(TranslationMiddleware)
    
    # Add language dependency
    @app.middleware("http")
    async def add_language_header(request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        language = getattr(request.state, "language", DEFAULT_LANGUAGE)
        response.headers["Content-Language"] = language
        return response


def get_language_from_request(request: Request) -> str:
    """
    Get the language from the request.
    
    Args:
        request: The request
        
    Returns:
        str: The language code
    """
    return getattr(request.state, "language", DEFAULT_LANGUAGE)


def translate_response(response_data: Any, language: str) -> Any:
    """
    Translate a response to the specified language.
    
    Args:
        response_data: The response data
        language: The language code
        
    Returns:
        Any: The translated response data
    """
    if isinstance(response_data, dict):
        return {k: translate_response(v, language) for k, v in response_data.items()}
    elif isinstance(response_data, list):
        return [translate_response(item, language) for item in response_data]
    elif isinstance(response_data, str):
        return translate(response_data, language)
    else:
        return response_data


def translate_error(error: Dict[str, Any], language: str) -> Dict[str, Any]:
    """
    Translate an error response to the specified language.
    
    Args:
        error: The error response
        language: The language code
        
    Returns:
        Dict[str, Any]: The translated error response
    """
    if "detail" in error:
        error["detail"] = translate(error["detail"], language)
    return error
