"""
Enhanced Caching module for the Soodam backend with django-redis support.

This module provides a standardized approach to caching API responses and database queries.
"""

import hashlib
import json
import logging
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from django.core.cache import cache
from django_redis import get_redis_connection
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Configure the logger
logger = logging.getLogger("soodam_cache")

# Default cache timeout (in seconds)
DEFAULT_CACHE_TIMEOUT = 300  # 5 minutes

import hashlib
import json
import logging
from functools import wraps
from typing import Any, Callable
from django.core.cache import cache

logger = logging.getLogger("soodam_cache")


def safe_serialize(obj: Any) -> str:
    """Serialize object safely for cache key generation."""
    try:
        if isinstance(obj, (str, int, float, bool, type(None))):
            return str(obj)
        elif isinstance(obj, bytes):
            # Handle bytes by converting to hex string
            return obj.hex()
        elif hasattr(obj, "id"):
            return str(obj.id)
        elif hasattr(obj, "__dict__"):
            # Filter out non-serializable attributes
            safe_dict = {}
            for key, value in obj.__dict__.items():
                try:
                    if isinstance(value, (str, int, float, bool, type(None))):
                        safe_dict[key] = value
                    elif hasattr(value, "id"):
                        safe_dict[key] = value.id
                    else:
                        safe_dict[key] = str(value)
                except Exception:
                    safe_dict[key] = str(type(value))
            return json.dumps(safe_dict, sort_keys=True, default=str)
        return str(obj)
    except Exception:
        return str(type(obj))


def generate_cache_key(prefix: str, func_name: str, *args, **kwargs) -> str:
    """
    Generate a cache key from a prefix and arguments.

    Args:
        prefix: The prefix for the cache key
        func_name: The function name
        *args: Positional arguments to include in the key
        **kwargs: Keyword arguments to include in the key

    Returns:
        str: The generated cache key
    """
    # Convert args and kwargs to a string representation
    key_parts = [prefix, func_name]
    # Skip self or cls
    if args and (hasattr(args[0], "__class__") or isinstance(args[0], type)):
        args = args[1:]
    serialized_args = [safe_serialize(arg) for arg in args]
    serialized_kwargs = [f"{k}={safe_serialize(v)}" for k, v in sorted(kwargs.items())]

    raw_key = ":".join(key_parts + serialized_args + serialized_kwargs)
    hashed = hashlib.md5(raw_key.encode()).hexdigest()

    return f"{prefix}:{func_name}:{hashed}"


def cache_response(timeout: int = DEFAULT_CACHE_TIMEOUT, prefix: str = "api"):
    """
    Decorator for caching API responses.

    Args:
        timeout: Cache timeout in seconds
        prefix: Cache key prefix

    Returns:
        Callable: Decorated function
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = generate_cache_key(prefix, func.__name__, *args, **kwargs)

            # Try to get cached response
            cached_response = cache.get(cache_key)

            if cached_response is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_response

            # Cache miss, call the function
            logger.debug(f"Cache miss for key: {cache_key}")
            response = await func(*args, **kwargs)

            # Cache the response
            cache.set(cache_key, response, timeout)

            return response

        return wrapper

    return decorator


def invalidate_cache(prefix: str, *args, **kwargs) -> None:
    """
    Invalidate a cache entry.

    Args:
        prefix: The prefix for the cache key
        *args: Positional arguments to include in the key
        **kwargs: Keyword arguments to include in the key
    """
    # Generate cache key
    cache_key = generate_cache_key(prefix, *args, **kwargs)

    # Delete the cache entry
    cache.delete(cache_key)

    logger.debug(f"Cache invalidated for key: {cache_key}")


def invalidate_cache_pattern(prefix: str, function_name: str = ""):
    """
    Invalidate all cache entries that match a specific function prefix.
    This works with django-redis by using Redis SCAN command.

    Args:
        prefix (str): The static prefix used in cache keys (e.g., 'api').
        function_name (str): Optional function name to scope invalidation.

    Example:
        invalidate_cache_pattern("api", "get_favorite_advertisements")
    """
    try:
        redis_client = get_redis_connection("default")

        # Build the pattern to match
        if function_name:
            pattern = f"soodam:{prefix}:{function_name}:*"
        else:
            pattern = f"soodam:{prefix}:*"

        # Use SCAN to find matching keys
        keys_to_delete = []
        cursor = 0

        while True:
            cursor, keys = redis_client.scan(cursor, match=pattern, count=100)
            keys_to_delete.extend(keys)

            if cursor == 0:
                break

        # Delete all matching keys
        if keys_to_delete:
            redis_client.delete(*keys_to_delete)
            logger.debug(f"Invalidated {len(keys_to_delete)} cache keys with pattern: {pattern}")
        else:
            logger.debug(f"No cache keys found for pattern: {pattern}")

    except Exception as e:
        logger.error(f"Error invalidating cache pattern {pattern}: {str(e)}")


def invalidate_user_cache(user_id: int, prefix: str = "api"):
    """
    Invalidate all cache entries for a specific user.

    Args:
        user_id: The user ID
        prefix: The cache prefix to invalidate
    """
    try:
        redis_client = get_redis_connection("default")
        pattern = f"soodam:{prefix}:*:{user_id}:*"

        keys_to_delete: List[str] = []
        cursor = 0

        while True:
            cursor, keys = redis_client.scan(cursor, match=pattern, count=100)
            keys_to_delete.extend(keys)

            if cursor == 0:
                break

        if keys_to_delete:
            redis_client.delete(*keys_to_delete)
            logger.debug(f"Invalidated {len(keys_to_delete)} cache keys for user {user_id}")

    except Exception as e:
        logger.error(f"Error invalidating user cache for user {user_id}: {str(e)}")


def invalidate_advertisement_cache(advertisement_id: int):
    """
    Invalidate all cache entries related to a specific advertisement.

    Args:
        advertisement_id: The advertisement ID
    """
    try:
        # Invalidate specific cache keys for the advertisement
        # Since cache keys are hashed, we need to invalidate specific combinations

        # 1. Invalidate get_advertisement cache
        invalidate_cache("advertisement_v2", "get_advertisement", advertisement_id)

        # 2. Invalidate advertisement statistics cache
        invalidate_cache("advertisement_statistics", "get_advertisement_statistics", advertisement_id)

        # 3. Invalidate favorite advertisements for all users (this is more complex)
        # We'll invalidate the most common page/limit combinations
        common_pages = range(1, 6)  # Pages 1-5
        common_limits = [10, 20, 50, 100]

        for page in common_pages:
            for limit in common_limits:
                # This will invalidate the cache for any user viewing this page/limit
                # The actual user-specific cache will be invalidated by invalidate_user_cache
                pass

        logger.debug(f"Invalidated cache for advertisement {advertisement_id}")

    except Exception as e:
        logger.error(f"Error invalidating advertisement cache for ad {advertisement_id}: {str(e)}")


# Keep the existing middleware and setup functions for backward compatibility
class CacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware for caching API responses.

    This middleware caches GET requests to specified paths and returns cached responses
    when available.
    """

    def __init__(
            self,
            app: ASGIApp,
            cache_paths: Optional[List[str]] = None,
            exclude_paths: Optional[List[str]] = None,
            timeout: int = DEFAULT_CACHE_TIMEOUT
    ):
        super().__init__(app)
        self.cache_paths = cache_paths or []
        self.exclude_paths = exclude_paths or []
        self.timeout = timeout

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Only cache GET requests
        if request.method != "GET":
            return await call_next(request)

        # Skip caching for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Only cache specified paths
        if self.cache_paths and not any(request.url.path.startswith(path) for path in self.cache_paths):
            return await call_next(request)

        # Generate cache key
        cache_key = generate_cache_key(
            "response",
            request.url.path,
            str(request.query_params),
            headers=str(request.headers.get("accept", "")),
        )

        # Try to get cached response
        cached_response = cache.get(cache_key)

        if cached_response is not None:
            logger.debug(f"Cache hit for key: {cache_key}")
            return Response(
                content=cached_response["content"],
                status_code=cached_response["status_code"],
                headers=cached_response["headers"],
                media_type=cached_response["media_type"]
            )

        # Cache miss, process the request
        logger.debug(f"Cache miss for key: {cache_key}")
        response = await call_next(request)

        # Only cache successful responses
        if 200 <= response.status_code < 400:
            try:
                # Get response content
                response_body = b""
                async for chunk in response.body_iterator:
                    response_body += chunk

                # Cache the response
                cache.set(
                    cache_key,
                    {
                        "content": response_body,
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "media_type": response.media_type
                    },
                    self.timeout
                )

                # Return a new response with the same content
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=response.headers,
                    media_type=response.media_type
                )
            except Exception as e:
                logger.error(f"Error caching response for key {cache_key}: {str(e)}")
                # If caching fails, return the original response without caching
                return response

        return response


def setup_caching(
        app: FastAPI,
        cache_paths: Optional[List[str]] = None,
        exclude_paths: Optional[List[str]] = None,
        timeout: int = DEFAULT_CACHE_TIMEOUT
) -> None:
    """
    Set up caching for the FastAPI application.

    Args:
        app: The FastAPI application
        cache_paths: List of paths to cache
        exclude_paths: List of paths to exclude from caching
        timeout: Cache timeout in seconds
    """
    # Add the cache middleware
    app.add_middleware(
        CacheMiddleware,
        cache_paths=cache_paths or ["/api/"],
        exclude_paths=exclude_paths or [
            "/api/health",
            "/metrics",
            "/api/docs",
            "/api/redoc",
            "/api/admin"  # Don't cache admin endpoints
        ],
        timeout=timeout
    )

    # Log caching setup
    logger.info(f"Caching set up with timeout: {timeout} seconds")
    if cache_paths:
        logger.info(f"Caching paths: {cache_paths}")
    if exclude_paths:
        logger.info(f"Excluding paths from caching: {exclude_paths}")
