"""
Enhanced logging module for the Soodam backend.

This module provides a standardized approach to logging API requests, responses,
errors, and performance metrics.
"""

import json
import logging
import time
import uuid
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, Optional, Union

from fastapi import Fast<PERSON>I, Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Configure the logger
logger = logging.getLogger("soodam_api")

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging all API requests and responses.
    
    This middleware logs:
    - Request method, path, headers, and body
    - Response status code, headers, and body
    - Request processing time
    - Any exceptions that occur during request processing
    """
    
    def __init__(self, app: ASGIApp, exclude_paths: list = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Generate a unique request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to request state for use in other parts of the application
        request.state.request_id = request_id
        
        # Log request details
        await self._log_request(request, request_id)
        
        # Record start time
        start_time = time.time()
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response details
            await self._log_response(request, response, process_time, request_id)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
        except Exception as e:
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log the exception
            self._log_exception(request, e, process_time, request_id)
            
            # Re-raise the exception
            raise
    
    async def _log_request(self, request: Request, request_id: str) -> None:
        """Log request details"""
        # Get request body
        body = None
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body_bytes = await request.body()
                if body_bytes:
                    body = body_bytes.decode()
            except Exception:
                body = "<Could not read body>"
        
        # Log request details
        log_data = {
            "request_id": request_id,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "request",
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "headers": dict(request.headers),
            "body": body
        }
        
        logger.info(f"API Request: {json.dumps(log_data)}")
    
    async def _log_response(self, request: Request, response: Response, process_time: float, request_id: str) -> None:
        """Log response details"""
        # Log response details
        log_data = {
            "request_id": request_id,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "response",
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time_ms": round(process_time * 1000, 2),
            "headers": dict(response.headers)
        }
        
        # Log at appropriate level based on status code
        if response.status_code >= 500:
            logger.error(f"API Response: {json.dumps(log_data)}")
        elif response.status_code >= 400:
            logger.warning(f"API Response: {json.dumps(log_data)}")
        else:
            logger.info(f"API Response: {json.dumps(log_data)}")
    
    def _log_exception(self, request: Request, exception: Exception, process_time: float, request_id: str) -> None:
        """Log exception details"""
        # Log exception details
        log_data = {
            "request_id": request_id,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "exception",
            "method": request.method,
            "path": request.url.path,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "process_time_ms": round(process_time * 1000, 2)
        }
        
        logger.error(f"API Exception: {json.dumps(log_data)}", exc_info=True)


def log_function_call(func: Callable) -> Callable:
    """
    Decorator for logging function calls, arguments, return values, and exceptions.
    
    Usage:
    ```
    @log_function_call
    def my_function(arg1, arg2):
        return arg1 + arg2
    ```
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Generate a unique call ID
        call_id = str(uuid.uuid4())
        
        # Log function call
        log_data = {
            "call_id": call_id,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "function_call",
            "function": func.__name__,
            "module": func.__module__,
            "args": str(args),
            "kwargs": str(kwargs)
        }
        
        logger.debug(f"Function Call: {json.dumps(log_data)}")
        
        # Record start time
        start_time = time.time()
        
        try:
            # Call the function
            result = await func(*args, **kwargs)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log function return
            log_data = {
                "call_id": call_id,
                "timestamp": datetime.utcnow().isoformat(),
                "type": "function_return",
                "function": func.__name__,
                "module": func.__module__,
                "process_time_ms": round(process_time * 1000, 2),
                "result_type": type(result).__name__
            }
            
            logger.debug(f"Function Return: {json.dumps(log_data)}")
            
            return result
        except Exception as e:
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log function exception
            log_data = {
                "call_id": call_id,
                "timestamp": datetime.utcnow().isoformat(),
                "type": "function_exception",
                "function": func.__name__,
                "module": func.__module__,
                "process_time_ms": round(process_time * 1000, 2),
                "exception_type": type(e).__name__,
                "exception_message": str(e)
            }
            
            logger.error(f"Function Exception: {json.dumps(log_data)}", exc_info=True)
            
            # Re-raise the exception
            raise
    
    return wrapper


def setup_logging(app: FastAPI, exclude_paths: list = None) -> None:
    """
    Set up logging for the FastAPI application.
    
    Args:
        app: The FastAPI application
        exclude_paths: List of paths to exclude from logging
    """
    # Add the request logging middleware
    app.add_middleware(
        RequestLoggingMiddleware,
        exclude_paths=exclude_paths or ["/api/health", "/metrics"]
    )
    
    # Log application startup
    @app.on_event("startup")
    async def startup_event():
        logger.info("Application startup")
    
    # Log application shutdown
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Application shutdown")
