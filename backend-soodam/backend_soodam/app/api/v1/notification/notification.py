"""
Notification API for the Soodam backend.

This module provides API functions for notification operations.
"""

import logging
from typing import Dict, List, Optional

from fastapi import HTTPException, Request, status

from app.models import CustomUserModel
from app.models.notification import (
    NotificationDeviceModel,
    NotificationModel,
    NotificationPreferenceModel,
    NotificationType,
)
from app.schemas.notification import (
    NotificationDeviceCreateSchema,
    NotificationDeviceSchema,
    NotificationPreferenceSchema,
    NotificationSchema,
    NotificationUpdateSchema,
)
from app.services.notification import NotificationService
from app.utils.pagination import paginate_queryset

logger = logging.getLogger(__name__)


class NotificationAPI:
    """Notification API for the Soodam backend."""
    
    @staticmethod
    async def get_notifications(
        request: Request,
        user: CustomUserModel,
        page: int = 1,
        limit: int = 10,
        is_read: Optional[bool] = None,
        notification_type: Optional[str] = None,
    ) -> Dict:
        """
        Get notifications for a user.
        
        Args:
            request: The request
            user: The user
            page: The page number
            limit: The page size
            is_read: Filter by read status
            notification_type: Filter by notification type
            
        Returns:
            Dict: The notifications
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Build the queryset
            queryset = NotificationModel.objects.filter(user=user)
            
            # Apply filters
            if is_read is not None:
                queryset = queryset.filter(is_read=is_read)
            
            if notification_type:
                queryset = queryset.filter(type=notification_type)
            
            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)
            
            # Convert to schema
            items = []
            async for notification in paginated["items"]:
                items.append(NotificationSchema(
                    id=notification.id,
                    type=notification.type,
                    title=notification.title,
                    message=notification.message,
                    data=notification.data,
                    is_read=notification.is_read,
                    created_at=notification.created_at
                ))
            
            return {
                "items": items,
                "total": paginated["total"],
                "page": page,
                "limit": limit,
                "pages": (paginated["total"] + limit - 1) // limit,
                "unread_count": await NotificationModel.objects.filter(user=user, is_read=False).acount()
            }
        except Exception as e:
            logger.error(f"Error getting notifications: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting notifications"
            )
    
    @staticmethod
    async def get_notification(
        request: Request,
        notification_id: int,
        user: CustomUserModel,
    ) -> NotificationSchema:
        """
        Get a notification.
        
        Args:
            request: The request
            notification_id: The notification ID
            user: The user
            
        Returns:
            NotificationSchema: The notification
            
        Raises:
            HTTPException: If the notification is not found
        """
        try:
            # Get the notification
            notification = await NotificationModel.objects.filter(
                id=notification_id,
                user=user
            ).afirst()
            
            if not notification:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Notification not found"
                )
            
            return NotificationSchema(
                id=notification.id,
                type=notification.type,
                title=notification.title,
                message=notification.message,
                data=notification.data,
                is_read=notification.is_read,
                created_at=notification.created_at
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting notification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting notification"
            )
    
    @staticmethod
    async def mark_notification_as_read(
        request: Request,
        notification_id: int,
        user: CustomUserModel,
    ) -> NotificationSchema:
        """
        Mark a notification as read.
        
        Args:
            request: The request
            notification_id: The notification ID
            user: The user
            
        Returns:
            NotificationSchema: The updated notification
            
        Raises:
            HTTPException: If the notification is not found
        """
        try:
            # Mark the notification as read
            success = await NotificationService.mark_notification_as_read(notification_id, user.id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Notification not found"
                )
            
            # Get the updated notification
            return await NotificationAPI.get_notification(request, notification_id, user)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error marking notification as read"
            )
    
    @staticmethod
    async def mark_all_notifications_as_read(
        request: Request,
        user: CustomUserModel,
    ) -> Dict:
        """
        Mark all notifications as read.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            Dict: The result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Mark all notifications as read
            count = await NotificationService.mark_all_notifications_as_read(user.id)
            
            return {
                "success": True,
                "count": count
            }
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error marking all notifications as read"
            )
    
    @staticmethod
    async def delete_notification(
        request: Request,
        notification_id: int,
        user: CustomUserModel,
    ) -> Dict:
        """
        Delete a notification.
        
        Args:
            request: The request
            notification_id: The notification ID
            user: The user
            
        Returns:
            Dict: The result
            
        Raises:
            HTTPException: If the notification is not found
        """
        try:
            # Delete the notification
            success = await NotificationService.delete_notification(notification_id, user.id)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Notification not found"
                )
            
            return {
                "success": True
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting notification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting notification"
            )
    
    @staticmethod
    async def delete_all_notifications(
        request: Request,
        user: CustomUserModel,
    ) -> Dict:
        """
        Delete all notifications.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            Dict: The result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Delete all notifications
            count = await NotificationService.delete_all_notifications(user.id)
            
            return {
                "success": True,
                "count": count
            }
        except Exception as e:
            logger.error(f"Error deleting all notifications: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting all notifications"
            )
    
    @staticmethod
    async def get_notification_preferences(
        request: Request,
        user: CustomUserModel,
    ) -> NotificationPreferenceSchema:
        """
        Get notification preferences.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            NotificationPreferenceSchema: The notification preferences
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the notification preferences
            preferences = await NotificationPreferenceModel.objects.filter(
                user=user
            ).afirst()
            
            # If no preferences, create default preferences
            if not preferences:
                preferences = await NotificationPreferenceModel.objects.acreate(
                    user=user
                )
            
            return NotificationPreferenceSchema(
                email_enabled=preferences.email_enabled,
                push_enabled=preferences.push_enabled,
                sms_enabled=preferences.sms_enabled,
                advertisement_created=preferences.advertisement_created,
                advertisement_updated=preferences.advertisement_updated,
                advertisement_deleted=preferences.advertisement_deleted,
                advertisement_approved=preferences.advertisement_approved,
                advertisement_rejected=preferences.advertisement_rejected,
                advertisement_expired=preferences.advertisement_expired,
                advertisement_viewed=preferences.advertisement_viewed,
                advertisement_favorited=preferences.advertisement_favorited,
                advertisement_inquiry=preferences.advertisement_inquiry,
                advertisement_comment=preferences.advertisement_comment,
                advertisement_reply=preferences.advertisement_reply,
                advertisement_match=preferences.advertisement_match,
                user_followed=preferences.user_followed,
                user_mentioned=preferences.user_mentioned,
                user_message=preferences.user_message,
                system_announcement=preferences.system_announcement
            )
        except Exception as e:
            logger.error(f"Error getting notification preferences: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting notification preferences"
            )
    
    @staticmethod
    async def update_notification_preferences(
        request: Request,
        schema: NotificationPreferenceSchema,
        user: CustomUserModel,
    ) -> NotificationPreferenceSchema:
        """
        Update notification preferences.
        
        Args:
            request: The request
            schema: The notification preference schema
            user: The user
            
        Returns:
            NotificationPreferenceSchema: The updated notification preferences
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the notification preferences
            preferences = await NotificationPreferenceModel.objects.filter(
                user=user
            ).afirst()
            
            # If no preferences, create default preferences
            if not preferences:
                preferences = await NotificationPreferenceModel.objects.acreate(
                    user=user
                )
            
            # Update the preferences
            for field, value in schema.dict().items():
                if hasattr(preferences, field) and value is not None:
                    setattr(preferences, field, value)
            
            # Save the preferences
            await preferences.asave()
            
            return await NotificationAPI.get_notification_preferences(request, user)
        except Exception as e:
            logger.error(f"Error updating notification preferences: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating notification preferences"
            )
    
    @staticmethod
    async def get_notification_devices(
        request: Request,
        user: CustomUserModel,
    ) -> List[NotificationDeviceSchema]:
        """
        Get notification devices.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            List[NotificationDeviceSchema]: The notification devices
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the notification devices
            devices = await NotificationDeviceModel.objects.filter(
                user=user
            ).values("id", "device_token", "device_type", "is_active", "created_at", "last_used_at")
            
            return [NotificationDeviceSchema(
                id=device["id"],
                device_token=device["device_token"],
                device_type=device["device_type"],
                is_active=device["is_active"],
                created_at=device["created_at"],
                last_used_at=device["last_used_at"]
            ) for device in devices]
        except Exception as e:
            logger.error(f"Error getting notification devices: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting notification devices"
            )
    
    @staticmethod
    async def create_notification_device(
        request: Request,
        schema: NotificationDeviceCreateSchema,
        user: CustomUserModel,
    ) -> NotificationDeviceSchema:
        """
        Create a notification device.
        
        Args:
            request: The request
            schema: The notification device schema
            user: The user
            
        Returns:
            NotificationDeviceSchema: The created notification device
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check if the device already exists
            device = await NotificationDeviceModel.objects.filter(
                user=user,
                device_token=schema.device_token
            ).afirst()
            
            if device:
                # Update the device
                device.device_type = schema.device_type
                device.is_active = True
                device.last_used_at = None
                await device.asave()
            else:
                # Create the device
                device = await NotificationDeviceModel.objects.acreate(
                    user=user,
                    device_token=schema.device_token,
                    device_type=schema.device_type,
                    is_active=True
                )
            
            return NotificationDeviceSchema(
                id=device.id,
                device_token=device.device_token,
                device_type=device.device_type,
                is_active=device.is_active,
                created_at=device.created_at,
                last_used_at=device.last_used_at
            )
        except Exception as e:
            logger.error(f"Error creating notification device: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating notification device"
            )
    
    @staticmethod
    async def delete_notification_device(
        request: Request,
        device_id: int,
        user: CustomUserModel,
    ) -> Dict:
        """
        Delete a notification device.
        
        Args:
            request: The request
            device_id: The device ID
            user: The user
            
        Returns:
            Dict: The result
            
        Raises:
            HTTPException: If the device is not found
        """
        try:
            # Get the device
            device = await NotificationDeviceModel.objects.filter(
                id=device_id,
                user=user
            ).afirst()
            
            if not device:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Device not found"
                )
            
            # Delete the device
            await device.adelete()
            
            return {
                "success": True
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting notification device: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting notification device"
            )
