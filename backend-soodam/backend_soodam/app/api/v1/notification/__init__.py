from app.api.v1.notification.notification import NotificationAPI


class NotificationsAPI:
    NotificationAPI=NotificationAPI
    @classmethod
    async def get_all_notifications(cls, request, current_user, page, limit, is_read, notification_type):
        return await cls.NotificationAPI.get_notifications(request, current_user, page, limit, is_read, notification_type)

    @classmethod
    async def get_notification(cls,request, notification_id, current_user):
        return await cls.NotificationAPI.get_notification(request, notification_id, current_user)
    @classmethod
    async def update_mark_notification (cls,request, notification_id, current_user):
        return await cls.NotificationAPI.mark_notification_as_read(request, notification_id, current_user)
    @classmethod
    async def read_mark_notification(cls,request, notification_id, current_user):
        return await cls.NotificationAPI.mark_notification_as_read(request, notification_id, current_user)
    @classmethod
    async def read_mark_all_notifications(cls,request, current_user):
        return await cls.NotificationAPI.mark_all_notifications_as_read(request, current_user)
    @classmethod
    async def delete_notification(cls,request, notification_id, current_user):
        return await cls.NotificationAPI.delete_notification(request, notification_id, current_user)
    @classmethod
    async def delete_all_notifications(cls,request, current_user):
        return await cls.NotificationAPI.delete_all_notifications(request, current_user)
    @classmethod
    async def get_notification_preferences(cls,request, current_user):
        return await cls.NotificationAPI.get_notification_preferences(request, current_user)
    @classmethod
    async def update_notification_preferences(cls,request, schema, current_user):
        return await cls.NotificationAPI.update_notification_preferences(request, schema, current_user)
    @classmethod
    async def get_notification_devices(cls,request, current_user):
        return await cls.NotificationAPI.get_notification_devices(request, current_user)
    @classmethod
    async def create_notification_device(cls,request, schema, current_user):
        return await cls.NotificationAPI.create_notification_device(request, schema, current_user)
    @classmethod
    async def delete_notification_device(cls,request, device_id, current_user):
        return await cls.NotificationAPI.delete_notification_device(request, device_id, current_user)
