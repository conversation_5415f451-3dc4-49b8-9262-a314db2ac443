"""
CRUD API for advertisements.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional

import math
from asgiref.sync import sync_to_async
from django.contrib.gis.geos import Point
from django.db.models import Q
from fastapi import Request, HTTPException, status
from starlette.responses import JSONResponse

from app.enums.status import AdvertisementStatus
from app.models import ProvinceModel, CityModel
from app.models.advertisement import (
    BooleanAttributeModel,
    ChoiceAttributeModel,
    ChoiceOptionModel,
    SubCategoryModel,
    TextAttributeModel,
    AdvertisementLocationModel,
    AdvertisementPriceModel,
    AdvertisementImagesModel,
    AdvertisementVideoModel,
    AdvertisementStatisticsModel, AdvertisementTextValueModel, AdvertisementBooleanValueModel, HighlightAttributeModel,
)
from app.schemas.advertisement import (
    AdvertisementAttributeSchema,
    AdvertisementCategorySchema,
    AdvertisementImageSchema,
    AdvertisementListItemSchemaV2,
    AdvertisementLocationSchema,
    AdvertisementPriceSchema,
    AdvertisementStatisticsSchema,
    AdvertisementUserSchema,
)
from app.utils.pagination import paginate_queryset
from app.core.cache import cache_response, invalidate_cache
from app.core.db_profiler import optimize_queryset, profile_query
from app.models import CustomUserModel
from app.models.advertisement import AdvertisementModel
from app.schemas.advertisement import (
    AdvertisementCreateSchemaV2,
    AdvertisementDetailSchemaV2,
    AdvertisementListSchemaV2,
    AdvertisementUpdateSchemaV2,
)

logger = logging.getLogger(__name__)

class CRUDAPI:
    @classmethod
    # @cache_response(timeout=300, prefix="advertisements_v2")
    @profile_query("select")
    async def get_advertisements(cls,
        request: Request,
        page: int = 1,
        limit: int = 10,
        category_id: Optional[int] = None,
        search: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        location: Optional[str] = None,
        price_min: Optional[float] = None,
        price_max: Optional[float] = None,
        featured: Optional[bool] = None,
    ) -> AdvertisementListSchemaV2:
        """
        Get a list of advertisements with enhanced filtering.

        Args:
            request: The request
            page: The page number
            limit: The page size
            category_id: Filter by category ID
            search: Search term
            sort_by: Sort field
            sort_order: Sort order (asc or desc)
            location: Filter by location
            price_min: Minimum price
            price_max: Maximum price
            featured: Filter by featured status

        Returns:
            AdvertisementListSchemaV2: The advertisement list

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Build the queryset
            queryset = AdvertisementModel.objects.filter(status=AdvertisementStatus.APPROVED)

            # Apply filters
            if category_id:
                queryset = queryset.filter(sub_category_id=category_id)

            if search:
                queryset = queryset.filter(
                    Q(title__icontains=search) |
                    Q(description__icontains=search) |
                    Q(tags__name__icontains=search)
                ).distinct()

            if location:
                queryset = queryset.filter(
                    Q(address__city__icontains=location) |
                    Q(address__province__icontains=location) |
                    Q(address__address__icontains=location)
                )

            if price_min is not None:
                queryset = queryset.filter(price__amount__gte=price_min)

            if price_max is not None:
                queryset = queryset.filter(price__amount__lte=price_max)

            # if featured is not None:
            #     queryset = queryset.filter(is_featured=featured)

            # Apply sorting
            order_prefix = "-" if sort_order.lower() == "desc" else ""
            queryset = queryset.order_by(f"{order_prefix}{sort_by}")

            # Optimize the queryset
            queryset = optimize_queryset(
                queryset,
                select_related=[
                    "sub_category",
                    "sub_category__main_category",
                    "user",
                ],
                prefetch_related=[
                    "price",
                    "address",
                    "address__province",
                    "address__city",
                    "images",
                    "videos",
                    "tags",
                    "choice_attributes",
                    "advertisement_boolean_values",
                    "advertisement_text_values",
                ]
            )

            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)

            # Build the response
            items = []
            async for advertisement in paginated["items"]:
                # Get the primary image
                primary_image = None
                images = await sync_to_async(list)(advertisement.images.all())
                for image in images:
                    if image.is_primary:
                        primary_image = image.url
                        break

                # Build the location schema
                address = await sync_to_async(lambda: advertisement.address)()
                location_schema = AdvertisementLocationSchema(
                    id=address.id if address else None,
                    city=address.city if address else None,
                    province=address.province if address else None,
                    latitude=address.latitude if address else None,
                    longitude=address.longitude if address else None,
                    address=address.address if address else "",
                    zip_code=address.zip_code if address else "",
                    geolocation=address.geolocation.__str__() if address and address.geolocation else None
                )

                # Build the category schema
                sub_category = await sync_to_async(lambda: advertisement.sub_category)()
                category = AdvertisementCategorySchema(
                    id=sub_category.id,
                    name=sub_category.name,
                    key=sub_category.key,
                    main_category=sub_category.main_category,
                    icon=sub_category.icon.__str__() if sub_category.icon else None
                )

                # Build the price schema
                price_obj = await sync_to_async(lambda: advertisement.price)()
                price = AdvertisementPriceSchema(
                    amount=price_obj.amount if price_obj else 0,
                    currency=price_obj.currency if price_obj else "",
                    is_negotiable=price_obj.is_negotiable if price_obj else False,
                    discount_amount=price_obj.discount_amount if price_obj else 0,
                    original_amount=price_obj.original_amount if price_obj else 0,
                    price_per_unit=price_obj.price_per_unit if price_obj else 0,
                    unit=price_obj.unit if price_obj else ""
                )
                # Build the attributes schema
                attributes = []
                highlight_attributes = []
                # Try to fetch highlight features for the sub_category
                find_highlight_feature = await HighlightAttributeModel.objects.filter(
                    sub_category=advertisement.sub_category,
                ).afirst()
                # Prepare sets of attribute IDs (not value IDs!) for highlight matching
                highlight_index_feature = {
                    "choice": set(),
                    "bool": set(),
                    "text": set(),
                }
                if find_highlight_feature:
                    # Get highlighted choice attribute IDs
                    async for choice_attr in find_highlight_feature.choice_attributes.all():
                        highlight_index_feature["choice"].add(str(choice_attr.id))
                    # Get highlighted boolean attribute IDs
                    async for bool_attr in find_highlight_feature.boolean_attributes.all():
                        highlight_index_feature["bool"].add(str(bool_attr.id))
                    # Get highlighted text attribute IDs
                    async for text_attr in find_highlight_feature.text_attributes.all():
                        highlight_index_feature["text"].add(str(text_attr.id))
                # For each attribute value, check if its attribute.id is in the highlight set
                async for choice_value in advertisement.choice_attributes.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(choice_value.attribute.id),
                            name=choice_value.attribute.name,
                            key=choice_value.attribute.key,
                            type='choice',
                            value={
                                "id": str(choice_value.id),
                                "value": choice_value.value
                            },
                            # unit=None,
                            icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        # Compare attribute.id, not value.id
                        if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

                async for bool_value in advertisement.advertisement_boolean_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(bool_value.attribute.id),
                            name=bool_value.attribute.name,
                            key=bool_value.attribute.key,
                            type='bool',
                            value=bool_value.value,
                            icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

                async for text_value in advertisement.advertisement_text_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(text_value.attribute.id),
                            name=text_value.attribute.name,
                            key=text_value.attribute.key,
                            type='text',
                            value=text_value.value,
                            icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(text_value.attribute.id) in highlight_index_feature["text"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

                # Build the user schema
                user = await sync_to_async(lambda: advertisement.user)()
                user = AdvertisementUserSchema(
                    id=user.id,
                    username=user.username if user.username else "",
                    full_name=f"{user.first_name} {user.last_name}",
                    avatar=user.avatar.__str__() if user.avatar else None,
                    rating=getattr(user, 'rating', 0),
                    is_verified=user.is_verified,
                    created_at=user.created_at
                )

                # Build the list item schema
                item = AdvertisementListItemSchemaV2(
                    id=advertisement.id,
                    title=advertisement.title,
                    price=price,
                    full_address=location_schema,
                    category=category,
                    highlight_attributes=highlight_attributes,
                    primary_image=primary_image,
                    created_at=advertisement.created_at.__str__(),
                    updated_at=advertisement.updated_at,
                    status=advertisement.status,
                    user=user
                )

                items.append(item)

            # Build the filters
            filters = {
                "category_id": category_id,
                "search": search,
                "location": location,
                "price_min": price_min,
                "price_max": price_max,
                "featured": featured
            }

            # Build the sort
            sort = {
                "field": sort_by,
                "order": sort_order
            }

            # Calculate pagination
            total_pages = math.ceil(paginated["total"] / limit)

            return AdvertisementListSchemaV2(
                items=items,
                total=paginated["total"],
                page=page,
                limit=limit,
                pages=total_pages,
                has_next=page < total_pages,
                has_prev=page > 1,
                filters=filters,
                sort=sort
            )
        except Exception as e:
            logger.error(f"Error getting advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisements"
            )

    @classmethod
    # @cache_response(timeout=300, prefix="advertisement_v2")
    @profile_query("select")
    async def get_advertisement(cls,
            request: Request,
            advertisement_id: int,
    ) -> AdvertisementDetailSchemaV2:
        """
        Get a specific advertisement with enhanced details.

        Args:
            request: The request
            advertisement_id: The advertisement ID

        Returns:
            AdvertisementDetailSchemaV2: The advertisement details

        Raises:
            HTTPException: If the advertisement is not found
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.select_related(
                "sub_category", "sub_category__main_category", "user", "statistics"
            ).prefetch_related(
                "address", "address__province", "address__city", "price", "images", "videos", "tags",
                "user__rating", "advertisement_boolean_values",
            ).filter(id=advertisement_id).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )

            # Increment view count
            if advertisement.statistics:
                advertisement.statistics.views += 1
                await advertisement.statistics.asave()
            # Build the location schema
            address = await advertisement.address.select_related("province", "city").afirst()

            if not address:
                # Handle the case where there's no address
                full_address = AdvertisementLocationSchema(
                    id=None,
                    city=None,
                    province=None,
                    address="",
                    zip_code="",
                    latitude=None,
                    longitude=None,
                    geolocation=None
                )
            else:
                # Build the location schema using the first address
                full_address = AdvertisementLocationSchema(
                    id=address.id,
                    province=address.province,
                    city=address.city,
                    address=address.address,
                    zip_code=address.zip_code,
                    latitude=address.latitude,
                    longitude=address.longitude,
                    geolocation=address.geolocation.__str__()
                )
            # Build the category schema
            # sub_category = await advertisement.sub_category.afirst()
            category = AdvertisementCategorySchema(
                id=advertisement.sub_category.id,
                name=advertisement.sub_category.name,
                key=advertisement.sub_category.key,
                main_category=advertisement.sub_category.main_category,
                icon=advertisement.sub_category.icon.__str__()
            )

            # Build the price schema
            price = await advertisement.price.afirst()
            price = AdvertisementPriceSchema(
                id=price.id,
                deposit=price.deposit,
                rent=price.rent,
                amount=price.amount,
                currency=price.currency,
                is_negotiable=price.is_negotiable,
                discount_amount=price.discount_amount,
                original_amount=price.original_amount,
                price_per_unit=price.price_per_unit,
                unit=price.unit
            )

            # Build the user schema
            user = AdvertisementUserSchema(
                id=advertisement.user.id,
                username=advertisement.user.username if advertisement.user.username else "",
                full_name=f"{advertisement.user.first_name} {advertisement.user.last_name}",
                avatar=advertisement.user.avatar.__str__() if advertisement.user.avatar else "",
                rating=getattr(advertisement.user, 'get_rating', 0),  # Safely get rating
                is_verified=advertisement.user.is_verified,
                created_at=advertisement.user.created_at.__str__() if advertisement.user.created_at else "",
            )

            # Build the images schema
            images = []
            for image in advertisement.images.all():
                images.append(AdvertisementImageSchema(
                    id=image.id,
                    url=image.url,
                    is_primary=image.is_primary,
                    order=image.order,
                    width=image.width,
                    height=image.height,
                    alt_text=image.alt_text
                ))

            # Build the attributes schema
            attributes = []
            highlight_attributes = []
            # Try to fetch highlight features for the sub_category
            find_highlight_feature = await HighlightAttributeModel.objects.filter(
                sub_category=advertisement.sub_category,
            ).afirst()
            # Prepare sets of attribute IDs (not value IDs!) for highlight matching
            highlight_index_feature = {
                "choice": set(),
                "bool": set(),
                "text": set(),
            }
            if find_highlight_feature:
                # Get highlighted choice attribute IDs
                async for choice_attr in find_highlight_feature.choice_attributes.all():
                    highlight_index_feature["choice"].add(str(choice_attr.id))
                # Get highlighted boolean attribute IDs
                async for bool_attr in find_highlight_feature.boolean_attributes.all():
                    highlight_index_feature["bool"].add(str(bool_attr.id))
                # Get highlighted text attribute IDs
                async for text_attr in find_highlight_feature.text_attributes.all():
                    highlight_index_feature["text"].add(str(text_attr.id))
            # For each attribute value, check if its attribute.id is in the highlight set
            async for choice_value in advertisement.choice_attributes.select_related('attribute').all():
                try:
                    attribute = AdvertisementAttributeSchema(
                        id=str(choice_value.attribute.id),
                        name=choice_value.attribute.name,
                        key=choice_value.attribute.key,
                        type='choice',
                        value={
                            "id": str(choice_value.id),
                            "value": choice_value.value
                        },
                        # unit=None,
                        icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                    )
                    attributes.append(attribute)
                    # Compare attribute.id, not value.id
                    if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                        highlight_attributes.append(attribute)
                except Exception as e:
                    logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

            async for bool_value in advertisement.advertisement_boolean_values.select_related('attribute').all():
                try:
                    attribute = AdvertisementAttributeSchema(
                        id=str(bool_value.attribute.id),
                        name=bool_value.attribute.name,
                        key=bool_value.attribute.key,
                        type='bool',
                        value=bool_value.value,
                        icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                    )
                    attributes.append(attribute)
                    if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                        highlight_attributes.append(attribute)
                except Exception as e:
                    logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

            async for text_value in advertisement.advertisement_text_values.select_related('attribute').all():
                try:
                    attribute = AdvertisementAttributeSchema(
                        id=str(text_value.attribute.id),
                        name=text_value.attribute.name,
                        key=text_value.attribute.key,
                        type='text',
                        value=text_value.value,
                        icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                    )
                    attributes.append(attribute)
                    if str(text_value.attribute.id) in highlight_index_feature["text"]:
                        highlight_attributes.append(attribute)
                except Exception as e:
                    logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

            # Build the contact schema
            # contact = AdvertisementContactSchema(
            #     phone=advertisement.contact.phone if hasattr(advertisement, "contact") else None,
            #     email=advertisement.contact.email if hasattr(advertisement, "contact") else None,
            #     website=advertisement.contact.website if hasattr(advertisement, "contact") else None,
            #     whatsapp=advertisement.contact.whatsapp if hasattr(advertisement, "contact") else None,
            #     telegram=advertisement.contact.telegram if hasattr(advertisement, "contact") else None,
            #     show_phone=advertisement.contact.show_phone if hasattr(advertisement, "contact") else True,
            #     show_email=advertisement.contact.show_email if hasattr(advertisement, "contact") else True
            # )

            # Build the statistics schema
            statistics = AdvertisementStatisticsSchema(
                views=advertisement.statistics.views if hasattr(advertisement, "statistics") else 0,
                favorites=advertisement.statistics.favorites if hasattr(advertisement, "statistics") else 0,
                inquiries=advertisement.statistics.inquiries if hasattr(advertisement, "statistics") else 0,
                shares=advertisement.statistics.shares if hasattr(advertisement, "statistics") else 0,
                last_viewed_at=advertisement.statistics.last_viewed_at if hasattr(advertisement,
                                                                                  "statistics") else None
            )

            # Get tags
            tags = [tag.name for tag in advertisement.tags.all()]

            # Get similar advertisements asynchronously
            # similar_ads = await AdvertisementModel.objects.filter(
            #     sub_category=advertisement.sub_category,
            #     status=AdvertisementStatus.APPROVED
            # ).exclude(id=advertisement.id).values_list("id", flat=True)[:5]
            #
            # # Convert to list using async methods
            # similar_ads_list = [ad_id async for ad_id in similar_ads]
            return AdvertisementDetailSchemaV2(
                id=advertisement.id,
                title=advertisement.title,
                description=advertisement.description,
                security_code_owner_building=advertisement.security_code_owner_building,
                phone_number_owner_building=advertisement.phone_number_owner_building,
                category=category,
                price=price,
                full_address=full_address,
                attributes=attributes,
                highlight_attributes=highlight_attributes,
                images=images,
                # contact=contact,
                user=user,
                status=advertisement.status,
                tags=tags,
                statistics=statistics,
                created_at=advertisement.created_at.__str__(),
                updated_at=advertisement.updated_at.__str__(),
                expiry_date=advertisement.expiry_date.__str__(),
                # slug=advertisement.slug,
                # similar_ads=list(similar_ads_list)
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting advertisement: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisement"
            )

    @classmethod
    @profile_query("insert")
    async def create_advertisement(cls,
            request: Request,
            schema: AdvertisementCreateSchemaV2,
            current_user: CustomUserModel,
    ) -> AdvertisementDetailSchemaV2:
        """
        Create a new advertisement.

        Args:
            request: The request
            schema: The advertisement creation schema
            current_user: The current user

        Returns:
            AdvertisementDetailSchemaV2: The created advertisement

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Validate the category exists
            category = await SubCategoryModel.objects.filter(id=schema.category_id).select_related(
                'main_category').afirst()
            if not category:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Category not found"
                )

            # Create the advertisement
            advertisement = AdvertisementModel(
                title=schema.title,
                description=schema.description,
                security_code_owner_building=schema.security_code_owner_building,
                phone_number_owner_building=schema.phone_number_owner_building,
                main_category=category.main_category,
                sub_category=category,
                user=current_user,
                status=AdvertisementStatus.PENDING,
                # slug=slugify(schema.title)
            )
            await advertisement.asave()

            # Create or update the price
            price_data = {
                "advertisement": advertisement,
                "deposit": schema.price.deposit,
                "rent": schema.price.rent,
                "amount": schema.price.amount,
                "currency": schema.price.currency,
                "is_negotiable": schema.price.is_negotiable,
                "discount_amount": schema.price.discount_amount,
                "original_amount": schema.price.original_amount,
                "price_per_unit": schema.price.price_per_unit,
                "unit": schema.price.unit
            }
            price = AdvertisementPriceModel(**price_data)
            await price.asave()

            # Get or create the location
            address = AdvertisementLocationModel(
                advertisement=advertisement,
                province=await ProvinceModel.objects.filter(id=schema.full_address.province_id).afirst(),
                city=await CityModel.objects.filter(id=schema.full_address.city_id).afirst(),
                # country=schema.country,
                address=schema.full_address.address if schema.full_address.address else "",
                street=schema.full_address.street if schema.full_address.street else "",
                zip_code=schema.full_address.zip_code,
                longitude=schema.full_address.longitude,
                latitude=schema.full_address.latitude,
                geolocation=Point(x=schema.full_address.longitude, y=schema.full_address.latitude, srid=4326),
            )

            await address.asave()
            # Associate the location and price  with the advertisement
            # advertisement.address = address
            # await address.set(advertisement=advertisement)
            # await price.set(advertisement=advertisement)
            await advertisement.asave()

            # Process attributes if provided
            if schema.attributes:
                for feature in schema.attributes:
                    if feature["type"] == 'choice':
                        choice_feature_object = await ChoiceAttributeModel.objects.filter(id=feature["id"]).afirst()
                        choice_feature_value_object = await ChoiceOptionModel.objects.filter(
                            id=feature["value"]["id"]).afirst()
                        if not choice_feature_object or not choice_feature_value_object:
                            raise HTTPException(status_code=404,
                                                detail="Choice feature or value not found with " + str(
                                                    feature["id"]) + str(
                                                    feature["name"]) + str(feature["value"]["id"]))
                        await sync_to_async(advertisement.choice_attributes.add)(choice_feature_value_object)
                    if feature["type"] == 'text':
                        text_feature_object = await TextAttributeModel.objects.filter(id=feature["id"]).afirst()
                        if not text_feature_object:
                            raise HTTPException(status_code=404,
                                                detail="Text feature  or value not found with " + str(
                                                    feature["id"]) + str(
                                                    feature["name"]) + str(feature["value"]["id"]))
                        await AdvertisementTextValueModel.objects.acreate(**{
                            "advertisement": advertisement,
                            "attribute": text_feature_object,
                            "value": feature["value"]
                        })
                    if feature["type"] == 'bool':
                        bool_feature_object = await BooleanAttributeModel.objects.filter(id=feature["id"]).afirst()
                        if not bool_feature_object:
                            raise HTTPException(status_code=404,
                                                detail="Boolean feature  or value not found with " + str(
                                                    feature["id"]) + str(
                                                    feature["name"]) + str(feature["value"]["id"]))
                        await AdvertisementBooleanValueModel.objects.acreate(**{
                            "advertisement": advertisement,
                            "attribute": bool_feature_object,
                            "value": feature["value"]
                        })
                    await advertisement.asave()
            # Process images if provided
            if schema.images:
                for idx, image_data in enumerate(schema.images):
                    # Create image
                    image_data = dict(image_data)
                    image = await AdvertisementImagesModel.objects.acreate(
                        advertisement=advertisement,
                        url=image_data.get("url", ""),
                        is_primary=image_data.get("is_primary", idx == 0),
                        order=image_data.get("order", idx),
                        width=image_data.get("width", 0),
                        height=image_data.get("height", 0),
                        alt_text=image_data.get("alt_text", "")
                    )
                    await image.asave()

            # Process videos if provided
            if schema.videos:
                for idx, video_data in enumerate(schema.videos):
                    video_data = dict(video_data)
                    # Create video
                    video = await AdvertisementVideoModel.objects.acreate(
                        advertisement=advertisement,
                        url=video_data.get("url", ""),
                        thumbnail_url=video_data.get("thumbnail_url", ""),
                        is_primary=video_data.get("is_primary", idx == 0),
                        order=video_data.get("order", idx),
                        duration=video_data.get("duration", 0),
                        title=video_data.get("title", ""),
                        description=video_data.get("description", "")
                    )
                    await video.asave()

            # Process contact information if provided
            # if schema.contact:
            #     contact = await AdvertisementContactModel.objects.acreate(
            #         advertisement=advertisement,
            #         phone=schema.contact.phone,
            #         email=schema.contact.email,
            #         website=schema.contact.website,
            #         whatsapp=schema.contact.whatsapp,
            #         telegram=schema.contact.telegram,
            #         show_phone=schema.contact.show_phone,
            #         show_email=schema.contact.show_email
            #     )
            #      await cantact.asave()

            # Create statistics
            statistics = await AdvertisementStatisticsModel.objects.acreate(
                advertisement=advertisement,
                views=0,
                favorites=0,
                inquiries=0,
                shares=0
            )
            await statistics.asave()
            # Set expiry date (default to 30 days from now)
            advertisement.expiry_date = datetime.now() + timedelta(days=30)
            # advertisement.is_featured = schema.is_featured
            await advertisement.asave()

            # Invalidate cache
            # await invalidate_cache(prefix="advertisements_v2")
            # Return the created advertisement
            return await cls.get_advertisement(request, advertisement.id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating advertisement: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating advertisement: {str(e)}"
            )

    @classmethod
    @profile_query("update")
    async def update_advertisement(cls,
            request: Request,
            advertisement_id: int,
            schema: AdvertisementUpdateSchemaV2,
            current_user: CustomUserModel,
    ) -> AdvertisementDetailSchemaV2:
        """
        Update an existing advertisement.

        Args:
            request: The request
            advertisement_id: The advertisement ID
            schema: The advertisement update schema
            current_user: The current user

        Returns:
            AdvertisementDetailSchemaV2: The updated advertisement

        Raises:
            HTTPException: If the advertisement is not found or user is not authorized
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.select_related(
                "sub_category", "sub_category__main_category", "user"
            ).filter(id=advertisement_id).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )

            # Check if user is authorized to update
            if advertisement.user.id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to update this advertisement"
                )

            # Update basic fields
            advertisement.status=AdvertisementStatus.PENDING
            if schema.title:
                advertisement.title = schema.title
            if schema.description:
                advertisement.description = schema.description
            if schema.security_code_owner_building:
                advertisement.security_code_owner_building = schema.security_code_owner_building
            if schema.phone_number_owner_building:
                advertisement.phone_number_owner_building = schema.phone_number_owner_building
            # Update category if provided
            if schema.category_id:
                category = await SubCategoryModel.objects.filter(id=schema.category_id).select_related(
                    'main_category').afirst()
                if not category:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Category not found"
                    )
                advertisement.main_category = category.main_category
                advertisement.sub_category = category

            # Update price if provided
            if schema.price:
                price = await advertisement.price.afirst()
                if price:
                    for field, value in schema.price.dict(exclude_unset=True).items():
                        setattr(price, field, value)
                    await price.asave()

            # Update location if provided
            if schema.full_address:
                address = await advertisement.address.afirst()
                if address:
                    for field, value in schema.full_address.dict(exclude_unset=True).items():
                        if field == 'province_id':
                            address.province = await ProvinceModel.objects.filter(id=value).afirst()
                        elif field == 'city_id':
                            address.city = await CityModel.objects.filter(id=value).afirst()
                        elif field in ['longitude', 'latitude'] and value is not None:
                            address.longitude = schema.full_address.longitude
                            address.latitude = schema.full_address.latitude
                            address.geolocation = Point(x=schema.full_address.longitude,
                                                      y=schema.full_address.latitude, 
                                                      srid=4326)
                        else:
                            setattr(address, field, value)
                    await address.asave()

            # Update attributes if provided
            if schema.attributes:
                # Clear existing attributes
                await advertisement.choice_attributes.aclear()
                await AdvertisementTextValueModel.objects.filter(advertisement=advertisement).adelete()
                await AdvertisementBooleanValueModel.objects.filter(advertisement=advertisement).adelete()

                # Add new attributes
                for feature in schema.attributes:
                    if feature.type == 'choice':
                        choice_feature = await ChoiceAttributeModel.objects.filter(id=feature.id).afirst()
                        choice_value = await ChoiceOptionModel.objects.filter(id=feature.value.id).afirst()
                        if not choice_feature or not choice_value:
                            raise HTTPException(status_code=404, detail="Choice feature or value not found")
                        await sync_to_async(advertisement.choice_attributes.add)(choice_value)

                    elif feature.type == 'text':
                        text_feature = await TextAttributeModel.objects.filter(id=feature.id).afirst()
                        if not text_feature:
                            raise HTTPException(status_code=404, detail="Text feature not found")
                        await AdvertisementTextValueModel.objects.acreate(
                            advertisement=advertisement,
                            attribute=text_feature,
                            value=feature.value
                        )

                    elif feature.type == 'bool':
                        bool_feature = await BooleanAttributeModel.objects.filter(id=feature.id).afirst()
                        if not bool_feature:
                            raise HTTPException(status_code=404, detail="Boolean feature not found")
                        await AdvertisementBooleanValueModel.objects.acreate(
                            advertisement=advertisement,
                            attribute=bool_feature,
                            value=feature.value
                        )

            # Update images if provided
            if schema.images:
                # Delete existing images
                await AdvertisementImagesModel.objects.filter(advertisement=advertisement).adelete()
                
                # Add new images
                for idx, image_data in enumerate(schema.images):
                    image_data = dict(image_data)
                    await AdvertisementImagesModel.objects.acreate(
                        advertisement=advertisement,
                        url=image_data.get("url", ""),
                        is_primary=image_data.get("is_primary", idx == 0),
                        order=image_data.get("order", idx),
                        width=image_data.get("width", 0),
                        height=image_data.get("height", 0),
                        alt_text=image_data.get("alt_text", "")
                    )

            # Update videos if provided
            if schema.videos:
                # Delete existing videos
                await AdvertisementVideoModel.objects.filter(advertisement=advertisement).adelete()
                
                # Add new videos
                for idx, video_data in enumerate(schema.videos):
                    video_data = dict(video_data)
                    await AdvertisementVideoModel.objects.acreate(
                        advertisement=advertisement,
                        url=video_data.get("url", ""),
                        thumbnail_url=video_data.get("thumbnail_url", ""),
                        is_primary=video_data.get("is_primary", idx == 0),
                        order=video_data.get("order", idx),
                        duration=video_data.get("duration", 0),
                        title=video_data.get("title", ""),
                        description=video_data.get("description", "")
                    )

            # Save the advertisement
            await advertisement.asave()
            
            # Invalidate cache for the specific advertisement
            invalidate_cache(
                "advertisement_v2",
                "get_advertisement",
                advertisement_id
            )

            # Return the updated advertisement
            return await cls.get_advertisement(request, advertisement.id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating advertisement: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating advertisement: {str(e)}"
            )

    @classmethod
    @profile_query("delete_advertisement")
    async def delete_advertisement(
            cls,
            request: Request,
            advertisement_id: int,
            current_user: CustomUserModel,
    ) -> None:
        """
        Delete an advertisement.

        Args:
            request: The request
            advertisement_id: The advertisement ID
            current_user: The current user

        Returns:
            None

        Raises:
            HTTPException: If the advertisement is not found or user is not authorized
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.select_related(
                "sub_category", "sub_category__main_category", "user"
            ).filter(id=advertisement_id).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )
            
            # Check if user is authorized to delete
            if advertisement.user.id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to delete this advertisement"
                )
            await AdvertisementModel.delete_with_relations(advertisement_id)
            # Delete the advertisement
            await advertisement.adelete()

            # Invalidate cache for the deleted advertisement
            invalidate_cache(
                "advertisement_v2",
                "get_advertisement",
                advertisement_id
            )

            return JSONResponse(content={"message": "Advertisement deleted successfully"}, status_code=status.HTTP_204_NO_CONTENT)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting advertisement: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting advertisement: {str(e)}"
            )

