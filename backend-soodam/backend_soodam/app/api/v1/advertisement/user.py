"""
User-specific advertisement API.
"""

import logging


import math
from asgiref.sync import sync_to_async

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status


# from app.core.cache import cache_response
from app.core.db_profiler import optimize_queryset, profile_query
from app.enums.status import AdvertisementStatus
from app.models import CustomUserModel
from app.models.advertisement import (
    AdvertisementModel,
    AdvertisementStatisticsModel,
    AdvertisementFavoriteModel, HighlightAttributeModel,
)
from app.schemas import AdvertisementImageSchema, AdvertisementAttributeSchema, AdvertisementStatisticsSchema, \
    AdvertisementDetailSchemaV2
from app.schemas.advertisement import (
    AdvertisementCategorySchema,
    AdvertisementListItemSchemaV2,
    AdvertisementLocationSchema,
    AdvertisementPriceSchema,
    AdvertisementUserSchema,
)

logger = logging.getLogger(__name__)

class UserAdvertisementAPI:
    @classmethod
    # @cache_response(timeout=300, prefix="advertisement_v2")
    @profile_query("select")
    async def get_my_advertisement(cls, request: Request, current_user: CustomUserModel):
        """Get advertisements created by the current user."""
        try:
            # Get all advertisements for the current user
            queryset = AdvertisementModel.objects.filter(
                user=current_user
            ).select_related(
                "sub_category", "sub_category__main_category", "user", "statistics"
            ).prefetch_related(
                "address", "address__province", "address__city", "price", "images", 
                "videos", "tags", "advertisement_boolean_values", "advertisement_text_values"
            )

            # Optimize the queryset
            queryset = optimize_queryset(
                queryset,
                select_related=["sub_category", "sub_category__main_category", "user", "statistics"],
                prefetch_related=["address", "price", "images", "tags"]
            )

            user = AdvertisementUserSchema(
                id=current_user.id,
                username=current_user.username if current_user.username else "",
                full_name=f"{current_user.first_name} {current_user.last_name}",
                avatar=current_user.avatar.__str__() if current_user.avatar else "",
                rating=getattr(current_user, "get_rating", 0),
                is_verified=current_user.is_verified,
                created_at=current_user.created_at.__str__()
            )
            # Get all advertisements for the user
            advertisements = []
            async for advertisement in queryset:
                # Get the primary image
                primary_image = None
                for image in advertisement.images.all():
                    if image.is_primary:
                        primary_image = image.url
                        break

                # Build the location schema
                address = await advertisement.address.select_related("province", "city").afirst()
                if not address:
                    continue

                # Build location schema
                full_address = AdvertisementLocationSchema(
                    id=address.id,
                    province=address.province if address.province else "",
                    city=address.city if address.city else "",
                    address=address.address or "",
                    zip_code=address.zip_code or "",
                    latitude=address.latitude,
                    longitude=address.longitude,
                    geolocation=address.geolocation.__str__()
                )

                # Build the category schema
                category = AdvertisementCategorySchema(
                    id=advertisement.sub_category.id,
                    name=advertisement.sub_category.name,
                    key=advertisement.sub_category.key,
                    main_category=advertisement.sub_category.main_category,
                    icon=advertisement.sub_category.icon.__str__() if advertisement.sub_category.icon else ""
                )

                # Build the price schema
                price = await advertisement.price.afirst()
                if price:
                    price_schema = AdvertisementPriceSchema(
                        id=price.id,
                        deposit=price.deposit,
                        rent=price.rent,
                        amount=price.amount,
                        currency=price.currency,
                        is_negotiable=price.is_negotiable,
                        discount_amount=price.discount_amount,
                        original_amount=price.original_amount,
                        price_per_unit=price.price_per_unit,
                        unit=price.unit
                    )
                else:
                    price_schema = None
                # Build the attributes schema
                attributes = []
                highlight_attributes = []
                # Try to fetch highlight features for the sub_category
                find_highlight_feature = await HighlightAttributeModel.objects.filter(
                    sub_category=advertisement.sub_category,
                ).afirst()
                # Prepare sets of attribute IDs (not value IDs!) for highlight matching
                highlight_index_feature = {
                    "choice": set(),
                    "bool": set(),
                    "text": set(),
                }
                if find_highlight_feature:
                    # Get highlighted choice attribute IDs
                    async for choice_attr in find_highlight_feature.choice_attributes.all():
                        highlight_index_feature["choice"].add(str(choice_attr.id))
                    # Get highlighted boolean attribute IDs
                    async for bool_attr in find_highlight_feature.boolean_attributes.all():
                        highlight_index_feature["bool"].add(str(bool_attr.id))
                    # Get highlighted text attribute IDs
                    async for text_attr in find_highlight_feature.text_attributes.all():
                        highlight_index_feature["text"].add(str(text_attr.id))
                # For each attribute value, check if its attribute.id is in the highlight set
                async for choice_value in advertisement.choice_attributes.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(choice_value.attribute.id),
                            name=choice_value.attribute.name,
                            key=choice_value.attribute.key,
                            type='choice',
                            value={
                                "id": str(choice_value.id),
                                "value": choice_value.value
                            },
                            # unit=None,
                            icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        # Compare attribute.id, not value.id
                        if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

                async for bool_value in advertisement.advertisement_boolean_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(bool_value.attribute.id),
                            name=bool_value.attribute.name,
                            key=bool_value.attribute.key,
                            type='bool',
                            value=bool_value.value,
                            icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

                async for text_value in advertisement.advertisement_text_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(text_value.attribute.id),
                            name=text_value.attribute.name,
                            key=text_value.attribute.key,
                            type='text',
                            value=text_value.value,
                            icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(text_value.attribute.id) in highlight_index_feature["text"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

                # Build the list item schema
                item = AdvertisementListItemSchemaV2(
                    id=advertisement.id,
                    title=advertisement.title,
                    price=price_schema,
                    full_address=full_address,
                    category=category,
                    highlight_attributes=highlight_attributes,
                    primary_image=primary_image,
                    created_at=advertisement.created_at.__str__(),
                    updated_at=advertisement.updated_at.__str__(),
                    status=advertisement.status,
                    user=user
                )
                advertisements.append(item)

            # Return the list of advertisements
            return {
                "items": advertisements,
                "total": len(advertisements),
                "user": user
            }
        except Exception as e:
            logger.error(f"Error getting user advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting user advertisements: {str(e)}"
            )

    @classmethod
    async def toggle_favorite(cls, request: Request, advertisement_id: int, current_user: CustomUserModel):
        """Toggle favorite status for an advertisement."""
        try:
            # Check if advertisement exists
            advertisement = await AdvertisementModel.objects.filter(id=advertisement_id).afirst()
            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )
            # Check if already favorited
            favorite = await AdvertisementFavoriteModel.objects.filter(
                user=current_user,
                advertisement=advertisement
            ).afirst()
            if favorite:
                # Remove from favorites
                await favorite.adelete()
                # Update statistics
                stats = await AdvertisementStatisticsModel.objects.filter(advertisement=advertisement).afirst()
                if stats and stats.favorites > 0:
                    stats.favorites -= 1
                    await stats.asave()
                status_action = "removed"
            else:
                # Add to favorites
                favorite = AdvertisementFavoriteModel(
                    user=current_user,
                    advertisement=advertisement
                )
                await favorite.asave()
                
                # Update statistics
                stats = await AdvertisementStatisticsModel.objects.filter(advertisement=advertisement).afirst()
                if stats:
                    stats.favorites += 1
                    await stats.asave()
                else:
                    stats = AdvertisementStatisticsModel(
                        advertisement=advertisement,
                        favorites=1
                    )
                    await stats.asave()
                status_action = "added"
            
            # Invalidate all related caches comprehensively
            # Use the new comprehensive cache invalidation function
            # Invalidate all advertisement-related caches
            return {"status": status_action, "message": f"Advertisement {status_action} to favorites"}
        except Exception as e:
            logger.error(f"Error toggling favorite status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error toggling favorite status: {str(e)}"
            )

    @classmethod
    # @cache_response(timeout=300, prefix="api")
    async def get_favorite_advertisements(cls, request: Request, current_user: CustomUserModel, page: int = 1, limit: int = 10):
        """Get favorite advertisements for the current user."""
        try:
            # Calculate offset
            offset = (page - 1) * limit
            # Get favorite advertisement IDs
            favorite_ids = AdvertisementFavoriteModel.objects.filter(
                user=current_user
            ).values_list('advertisement_id', flat=True)
            # Get advertisements
            queryset = AdvertisementModel.objects.filter(
                id__in=favorite_ids,
                # status=AdvertisementStatus.APPROVED
            ).select_related(
                "sub_category", "sub_category__main_category", "user", "statistics"
            ).prefetch_related(
                "address", "address__province", "address__city", "price", "images"
            )
            # Get total count
            total = await queryset.acount()
            # Apply pagination
            queryset = queryset[offset:offset + limit]
            # Build response
            advertisements = []
            async for advertisement in queryset:
                # Get the primary image
                primary_image = None
                for image in advertisement.images.all():
                    if image.is_primary:
                        primary_image = image.url
                        break
                # Build the location schema
                address = await advertisement.address.select_related("province", "city").afirst()
                if not address:
                    continue  # Skip advertisements without addresses
                full_address = AdvertisementLocationSchema(
                    id=address.id,
                    province=address.province if address.province else None,
                    city=address.city if address.city else None,
                    address=address.address or "",
                    zip_code=address.zip_code or "",
                    latitude=address.latitude,
                    longitude=address.longitude,
                    geolocation=address.geolocation.__str__()
                )
                # Build the category schema
                category = AdvertisementCategorySchema(
                    id=advertisement.sub_category.id,
                    name=advertisement.sub_category.name,
                    key=advertisement.sub_category.key,
                    main_category=advertisement.sub_category.main_category,
                    icon=advertisement.sub_category.icon.__str__() if advertisement.sub_category.icon else ""
                )
                # Build the price schema
                price = await advertisement.price.afirst()
                if price:
                    price_schema = AdvertisementPriceSchema(
                        id=price.id,
                        deposit=price.deposit,
                        rent=price.rent,
                        amount=price.amount,
                        currency=price.currency,
                        is_negotiable=price.is_negotiable,
                        discount_amount=price.discount_amount,
                        original_amount=price.original_amount,
                        price_per_unit=price.price_per_unit,
                        unit=price.unit
                    )
                else:
                    price_schema = None
                # Get user data safely in async context
                user = await sync_to_async(lambda: advertisement.user)()
                user_schema = AdvertisementUserSchema(
                    id=user.id,
                    username=user.username if user.username else "",
                    full_name=f"{user.first_name} {user.last_name}",
                    avatar=user.avatar.__str__() if user.avatar else "",
                    rating=await sync_to_async(lambda: getattr(user, 'rating', 0))(),  # Safely get rating in async context
                    is_verified=user.is_verified,
                    created_at=user.created_at.__str__()
                )
                # Build the attributes schema
                attributes = []
                highlight_attributes = []
                # Try to fetch highlight features for the sub_category
                find_highlight_feature = await HighlightAttributeModel.objects.filter(
                    sub_category=advertisement.sub_category,
                ).afirst()
                # Prepare sets of attribute IDs (not value IDs!) for highlight matching
                highlight_index_feature = {
                    "choice": set(),
                    "bool": set(),
                    "text": set(),
                }
                if find_highlight_feature:
                    # Get highlighted choice attribute IDs
                    async for choice_attr in find_highlight_feature.choice_attributes.all():
                        highlight_index_feature["choice"].add(str(choice_attr.id))
                    # Get highlighted boolean attribute IDs
                    async for bool_attr in find_highlight_feature.boolean_attributes.all():
                        highlight_index_feature["bool"].add(str(bool_attr.id))
                    # Get highlighted text attribute IDs
                    async for text_attr in find_highlight_feature.text_attributes.all():
                        highlight_index_feature["text"].add(str(text_attr.id))
                # For each attribute value, check if its attribute.id is in the highlight set
                async for choice_value in advertisement.choice_attributes.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(choice_value.attribute.id),
                            name=choice_value.attribute.name,
                            key=choice_value.attribute.key,
                            type='choice',
                            value={
                                "id": str(choice_value.id),
                                "value": choice_value.value
                            },
                            # unit=None,
                            icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        # Compare attribute.id, not value.id
                        if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

                async for bool_value in advertisement.advertisement_boolean_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(bool_value.attribute.id),
                            name=bool_value.attribute.name,
                            key=bool_value.attribute.key,
                            type='bool',
                            value=bool_value.value,
                            icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

                async for text_value in advertisement.advertisement_text_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(text_value.attribute.id),
                            name=text_value.attribute.name,
                            key=text_value.attribute.key,
                            type='text',
                            value=text_value.value,
                            icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(text_value.attribute.id) in highlight_index_feature["text"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

                # Build the list item schema
                item = AdvertisementListItemSchemaV2(
                    id=advertisement.id,
                    title=advertisement.title,
                    price=price_schema,
                    full_address=full_address,
                    category=category,
                    primary_image=primary_image,
                    highlight_attributes=highlight_attributes,
                    created_at=advertisement.created_at.__str__(),
                    updated_at=advertisement.updated_at.__str__(),
                    status=advertisement.status,
                    user=user_schema  # No need to include user info
                )
                advertisements.append(item)
             # Calculate pagination info
            total_pages = math.ceil(total / limit)
            has_next = page < total_pages
            has_prev = page > 1
            # Return the list of advertisements with all required fields
            return {
                "items": advertisements,
                "total": total,
                "page": page,
                "limit": limit,
                "pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev,
                "filters": {},  # No filters applied
                "sort": {"field": "created_at", "order": "desc"}  # Default sorting
            }
        except Exception as e:
            logger.error(f"Error getting favorite advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting favorite advertisements: {str(e)}"
            )