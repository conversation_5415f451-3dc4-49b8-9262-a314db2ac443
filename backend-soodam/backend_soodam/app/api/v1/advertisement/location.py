


import json
import logging
import os
import uuid


import math
from typing import Optional

from asgiref.sync import sync_to_async
from fastapi import HTTPException, Request, status


from app.core.cache import cache_response
from app.core.db_profiler import optimize_queryset, profile_query
from app.enums.status import AdvertisementStatus
from app.models.advertisement import (
    AdvertisementModel, HighlightAttributeModel,
)
from app.schemas import AdvertisementAttributeSchema
from app.schemas.advertisement import (
    AdvertisementCategorySchema,
    AdvertisementListItemSchemaV2,
    AdvertisementListSchemaV2,
    AdvertisementLocationSchema,
    AdvertisementPriceSchema,
    AdvertisementUserSchema,
)
from app.utils.pagination import paginate_queryset


logger = logging.getLogger(__name__)

class LocationAPI:
    @classmethod
    # @cache_response(timeout=300, prefix="advertisements_nearby")
    @profile_query("select")
    async def get_advertisements_nearby(cls,
            request: Request,
            latitude: float,
            longitude: float,
            radius: float = 10.0,  # in kilometers
            page: int = 1,
            limit: int = 10,
            category_id: Optional[int] = None,
    ) -> AdvertisementListSchemaV2:
        """
        Get advertisements near a specific location.

        Args:
            request: The request
            latitude: The latitude
            longitude: The longitude
            radius: The radius in kilometers
            page: The page number
            limit: The page size
            category_id: Filter by category ID

        Returns:
            AdvertisementListSchemaV2: The advertisement list

        Raises:
            HTTPException: If an error occurs
        """
        try:
            from django.contrib.gis.geos import Point
            from django.contrib.gis.measure import D
            from django.contrib.gis.db.models.functions import Distance
            # Validate coordinates
            if not (-90 <= latitude <= 90):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Latitude must be between -90 and 90"
                )
            if not (-180 <= longitude <= 180):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Longitude must be between -180 and 180"
                )
            # Debug: Check if there are any advertisements with locations
            total_ads = await sync_to_async(AdvertisementModel.objects.count)()
            ads_with_locations = await sync_to_async(AdvertisementModel.objects.filter(address__isnull=False).count)()
            logger.debug(f"Total advertisements: {total_ads}")
            logger.debug(f"Advertisements with locations: {ads_with_locations}")

            # Create a point from the provided coordinates
            point = Point(longitude, latitude, srid=4326)

            # Debug: Check the initial queryset
            initial_queryset = AdvertisementModel.objects.filter(
                status=AdvertisementStatus.APPROVED,
                address__isnull=False  # First check if address exists
            )

            initial_count = await sync_to_async(initial_queryset.count)()
            logger.debug(f"Approved ads with addresses: {initial_count}")

            # Then apply the distance filter
            queryset = initial_queryset.filter(
                address__geolocation__distance_lte=(point, D(km=radius))
            ).annotate(
                distance=Distance('address__geolocation', point)
            ).order_by('distance')

            # Log the final count
            final_count = await sync_to_async(queryset.count)()
            logger.debug(f"Ads within {radius}km: {final_count}")

            # Apply category filter if provided
            if category_id:
                queryset = await sync_to_async(queryset.filter)(sub_category__id=category_id)
                count = await sync_to_async(queryset.count)()
                logger.debug(f"After category filter: {count} advertisements")

            # Optimize the queryset
            queryset = optimize_queryset(
                queryset,
                select_related=["sub_category", "sub_category__main_category", "user"],
                prefetch_related=["address", "address__province", "address__city", "price", "images", "tags"]
            )

            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)

            # Convert to schema (similar to get_advertisements method)
            items = []
            async for advertisement in paginated["items"]:
                # Get the primary image
                primary_image = None
                for image in advertisement.images.all():
                    if image.is_primary:
                        primary_image = image.url
                        break

                # Build the location schema
                address = await advertisement.address.select_related("province", "city").afirst()
                full_address = AdvertisementLocationSchema(
                    id=address.id,
                    province=address.province,
                    city=address.city,
                    # country=advertisement.location.country,
                    latitude=address.latitude,
                    longitude=address.longitude,
                    address=address.address,
                    zip_code=address.zip_code,
                    geolocation=address.geolocation.__str__()

                )

                # Build the category schema
                category = AdvertisementCategorySchema(
                    id=advertisement.sub_category.id,
                    name=advertisement.sub_category.name,
                    key=advertisement.sub_category.key,
                    main_category=advertisement.sub_category.main_category,
                    icon=advertisement.sub_category.icon.__str__()
                )
                # Build the price schema
                price = await advertisement.price.afirst()
                price = AdvertisementPriceSchema(
                    id=price.id,
                    deposit=price.deposit,
                    rent=price.rent,
                    amount=price.amount,
                    currency=price.currency,
                    is_negotiable=price.is_negotiable,
                    discount_amount=price.discount_amount,
                    original_amount=price.original_amount,
                    price_per_unit=price.price_per_unit,
                    unit=price.unit
                )
                # Build the user schema
                user = AdvertisementUserSchema(
                    id=advertisement.user.id,
                    username=advertisement.user.username if advertisement.user.username else "",
                    full_name=f"{advertisement.user.first_name} {advertisement.user.last_name}",
                    avatar=advertisement.user.avatar.__str__() if advertisement.user.avatar else "",
                    rating=getattr(advertisement.user, 'get_rating', 0),  # Safely get rating
                    is_verified=advertisement.user.is_verified,
                    created_at=advertisement.user.created_at.__str__() if advertisement.user.created_at else "",
                )
                # user = AdvertisementUserSchema(
                #     id=advertisement.user.id,
                #     username=advertisement.user.username,
                #     full_name=f"{advertisement.user.first_name} {advertisement.user.last_name}",
                #     avatar=advertisement.user.avatar.__str__(),
                #     rating=advertisement.user.rating,
                #     is_verified=advertisement.user.is_verified,
                #     member_since=advertisement.user.date_joined
                # )
                # Build the attributes schema
                attributes = []
                highlight_attributes = []
                # Try to fetch highlight features for the sub_category
                find_highlight_feature = await HighlightAttributeModel.objects.filter(
                    sub_category=advertisement.sub_category,
                ).afirst()
                # Prepare sets of attribute IDs (not value IDs!) for highlight matching
                highlight_index_feature = {
                    "choice": set(),
                    "bool": set(),
                    "text": set(),
                }
                if find_highlight_feature:
                    # Get highlighted choice attribute IDs
                    async for choice_attr in find_highlight_feature.choice_attributes.all():
                        highlight_index_feature["choice"].add(str(choice_attr.id))
                    # Get highlighted boolean attribute IDs
                    async for bool_attr in find_highlight_feature.boolean_attributes.all():
                        highlight_index_feature["bool"].add(str(bool_attr.id))
                    # Get highlighted text attribute IDs
                    async for text_attr in find_highlight_feature.text_attributes.all():
                        highlight_index_feature["text"].add(str(text_attr.id))
                # For each attribute value, check if its attribute.id is in the highlight set
                async for choice_value in advertisement.choice_attributes.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(choice_value.attribute.id),
                            name=choice_value.attribute.name,
                            key=choice_value.attribute.key,
                            type='choice',
                            value={
                                "id": str(choice_value.id),
                                "value": choice_value.value
                            },
                            # unit=None,
                            icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        # Compare attribute.id, not value.id
                        if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

                async for bool_value in advertisement.advertisement_boolean_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(bool_value.attribute.id),
                            name=bool_value.attribute.name,
                            key=bool_value.attribute.key,
                            type='bool',
                            value=bool_value.value,
                            icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

                async for text_value in advertisement.advertisement_text_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(text_value.attribute.id),
                            name=text_value.attribute.name,
                            key=text_value.attribute.key,
                            type='text',
                            value=text_value.value,
                            icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(text_value.attribute.id) in highlight_index_feature["text"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

                # Build the list item schema
                item = AdvertisementListItemSchemaV2(
                    id=advertisement.id,
                    title=advertisement.title,
                    price=price,
                    full_address=full_address,
                    category=category,
                    primary_image=primary_image,
                    highlight_attributes=highlight_attributes,
                    created_at=advertisement.created_at.__str__(),
                    updated_at=advertisement.updated_at.__str__(),
                    status=advertisement.status,
                    user=user
                )
                items.append(item)
            # Build the filters
            filters = {
                "category_id": category_id,
                "latitude": latitude,
                "longitude": longitude,
                "radius": radius
            }


            # Build the sort
            sort = {
                "field": "distance",
                "order": "asc"
            }

            # Calculate pagination
            total_pages = math.ceil(paginated["total"] / limit)

            return AdvertisementListSchemaV2(
                items=items,
                total=paginated["total"],
                page=page,
                limit=limit,
                pages=total_pages,
                has_next=page < total_pages,
                has_prev=page > 1,
                filters=filters,
                sort=sort
            )

        except Exception as e:
            logger.error(f"Error getting nearby advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting nearby advertisements"
            )
