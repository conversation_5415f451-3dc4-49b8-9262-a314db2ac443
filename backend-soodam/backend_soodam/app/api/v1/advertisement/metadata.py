"""
Metadata API for advertisements.
"""

from typing import Any
from fastapi import Request, HTTPException, status
from app.schemas.advertisement import ReadFeatureByCategorySchema
from app.models.advertisement import (
    MainCategoryModel,
    SubCategoryModel,
    SubCategoryLevelTwoModel,
    BooleanAttributeModel,
    ChoiceAttributeModel,
    ChoiceOptionModel,
    TextAttributeModel,
    HighlightAttributeModel,
    PropertyModel
)
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class MetadataAPI:
        # ... existing get_meta_data code ...
    """Metadata related operations."""
    @classmethod
    async def get_meta_data(cls, request: Request) -> Any:
        """
        Get metadata for advertisements including categories and features.
        Args:
            request: The request object
        Returns:
            Dict containing main_categories and features
        """
        try:
            data = {
                "main_categories": [],
                "features": []
            }

            # Get all main categories with prefetched sub-categories
            main_categories = []
            async for main_cate in MainCategoryModel.objects.all():
                # Handle potential None values safely
                main_category = {
                    "id": main_cate.id,
                    "name": main_cate.name or "",
                    "description": main_cate.description or "",
                    "status": main_cate.star or 0,
                    "key": main_cate.key or "",
                    "review": main_cate.review or 0,
                    "total_adv": main_cate.total_adv or 0,
                    "icon": main_cate.icon.__str__() if main_cate.icon else "",
                    "sub_categories": [],
                }

                # Get sub-categories for this main category
                async for sub_category in SubCategoryModel.objects.filter(main_category=main_cate.id).all():
                    sub_categories_level_two = []

                    # Get level two sub-categories
                    async for sub_sub_category in SubCategoryLevelTwoModel.objects.filter(
                            sub_category=sub_category.id).all():
                        try:
                            sub_categories_level_two.append({
                                "id": sub_sub_category.id,
                                "sub_category": sub_category.id,
                                "name": sub_sub_category.name or "",
                                "description": sub_sub_category.description or "",
                                "star": sub_sub_category.star or 0,
                                "key": sub_sub_category.key or "",
                                "review": sub_sub_category.review or 0,
                                "total_adv": sub_sub_category.total_adv or 0,
                                "icon": sub_sub_category.icon.__str__() if sub_sub_category.icon else "",
                            })
                        except Exception as e:
                            # Log error but continue processing other categories
                            logger.error(f"Error processing sub-sub-category {sub_sub_category.id}: {str(e)}")

                    # Add sub-category with its level two categories
                    try:
                        main_category["sub_categories"].append({
                            "id": sub_category.id,
                            "main_category": main_cate.id,
                            "name": sub_category.name or "",
                            "description": sub_category.description or "",
                            "star": sub_category.star or 0,
                            "key": sub_category.key or "",
                            "review": sub_category.review or 0,
                            "total_adv": sub_category.total_adv or 0,
                            "sub_sub_category": sub_categories_level_two,
                            "icon": sub_category.icon.__str__() if sub_category.icon else "",
                        })
                    except Exception as e:
                        # Log error but continue processing other categories
                        logger.error(f"Error processing sub-category {sub_category.id}: {str(e)}")

                main_categories.append(main_category)

            # Get all features
            features = []

            # Get boolean features
            async for boolean_attribute in BooleanAttributeModel.objects.all():
                try:
                    boolean_feature = {
                        "id": boolean_attribute.id,
                        "name": boolean_attribute.name or "",
                        "key": boolean_attribute.key or "",
                        "type": 'bool',
                        "value": False,
                        "icon": boolean_attribute.icon.__str__() if boolean_attribute.icon else "",
                    }
                    features.append(boolean_feature)
                except Exception as e:
                    # Log error but continue processing other features
                    logger.error(f"Error processing boolean attribute {boolean_attribute.id}: {str(e)}")

            # Get choice features
            async for choice_attribute in ChoiceAttributeModel.objects.all():
                try:
                    choices_feature = {
                        "id": choice_attribute.id,
                        "name": choice_attribute.name or "",
                        "key": choice_attribute.key or "",
                        "type": 'choice',
                        "value": [],
                        "icon": choice_attribute.icon.__str__() if choice_attribute.icon else "",
                    }

                    # Get options for this choice attribute
                    async for choice_value in ChoiceOptionModel.objects.filter(attribute=choice_attribute.id):
                        try:
                            choices_feature["value"].append({
                                "id": choice_value.id,
                                "value": choice_value.value or "",
                            })
                        except Exception as e:
                            # Log error but continue processing other options
                            logger.error(f"Error processing choice option {choice_value.id}: {str(e)}")

                    features.append(choices_feature)
                except Exception as e:
                    # Log error but continue processing other features
                    logger.error(f"Error processing choice attribute {choice_attribute.id}: {str(e)}")

            # Get text features
            async for text_attribute in TextAttributeModel.objects.all():
                try:
                    text_feature = {
                        "id": text_attribute.id,
                        "name": text_attribute.name or "",
                        "key": text_attribute.key or "",
                        "type": 'text',
                        "value": "",
                        "icon": text_attribute.icon.__str__() if text_attribute.icon else "",
                    }
                    features.append(text_feature)
                except Exception as e:
                    # Log error but continue processing other features
                    logger.error(f"Error processing text attribute {text_attribute.id}: {str(e)}")

            # Assign data
            data["main_categories"] = main_categories
            data["features"] = features

            return data
        except Exception as e:
            # Log the error and return a meaningful error message
            logger.error(f"Error in get_meta_data: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error retrieving metadata: {str(e)}"
            )

    @classmethod
    async def get_feature_by_category(cls, request: Request, schema: ReadFeatureByCategorySchema) -> Any:
            """
            Get features by category.

            Args:
                request: The request object
                schema: The schema containing category information

            Returns:
                JSON response with features
            """
            try:
                features = []
                highlight_feature = []

                # Validate the subcategory exists
                sub_category = await SubCategoryModel.objects.filter(id=schema.sub_category_id).afirst()
                if not sub_category:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Subcategory not found"
                    )

                # Get properties for this subcategory
                async for feature in PropertyModel.objects.filter(
                    sub_category=schema.sub_category_id
                ).all():
                    try:
                        # Get highlight features
                        find_highlight_feature = await HighlightAttributeModel.objects.filter(
                            sub_category__id=schema.sub_category_id
                        ).afirst()

                        highlight_index_feature = {
                            "choice": [],
                            "bool": [],
                            "text": [],
                        }

                        if find_highlight_feature:
                            # Get highlighted choice attributes
                            async for choice in find_highlight_feature.choice_attributes.all():
                                highlight_index_feature["choice"].append(choice.id)

                            # Get highlighted boolean attributes
                            async for choice in find_highlight_feature.boolean_attributes.all():
                                highlight_index_feature["bool"].append(choice.id)

                            # Get highlighted text attributes
                            async for choice in find_highlight_feature.text_attributes.all():
                                highlight_index_feature["text"].append(choice.id)

                        # Process choice attributes
                        async for choice in feature.choice_attributes.all():
                            try:
                                # Get choice options
                                choice_options = []
                                async for choice_value in ChoiceOptionModel.objects.filter(attribute=choice.id):
                                    try:
                                        choice_options.append({
                                            "id": str(choice_value.id),  # Convert UUID to string
                                            "value": choice_value.value or ""
                                        })
                                    except Exception as e:
                                        logger.error(f"Error processing choice option {choice_value.id}: {str(e)}")

                                # Create feature object
                                feature_obj = {
                                    "id": str(choice.id),  # Convert UUID to string
                                    "name": choice.name or "",
                                    "key": choice.key or "",
                                    "type": 'choice',
                                    "icon": choice.icon.__str__() if choice.icon else "",
                                    "value": choice_options
                                }

                                # Add to features list
                                features.append(feature_obj)

                                # Add to highlight features if applicable
                                if str(choice.id) in highlight_index_feature["choice"]:  # Convert UUID to string for comparison
                                    highlight_feature.append(feature_obj)
                            except Exception as e:
                                logger.error(f"Error processing choice attribute {choice.id}: {str(e)}")

                        # Process text attributes
                        async for text_feature_value in feature.text_attributes.all():
                            try:
                                # Create feature object
                                feature_obj = {
                                    "id": str(text_feature_value.id),  # Convert UUID to string
                                    "name": text_feature_value.name or "",
                                    "key": text_feature_value.key or "",
                                    "placeholder": text_feature_value.placeholder if hasattr(text_feature_value, "placeholder") else "",
                                    "type": 'text',
                                    "image": text_feature_value.icon.__str__() if text_feature_value.icon else "",
                                }

                                # Add to features list
                                features.append(feature_obj)

                                # Add to highlight features if applicable
                                if str(text_feature_value.id) in highlight_index_feature["text"]:  # Convert UUID to string for comparison
                                    highlight_feature.append(feature_obj)
                            except Exception as e:
                                logger.error(f"Error processing text attribute {text_feature_value.id}: {str(e)}")

                        # Process boolean attributes
                        async for bool_feature_value in feature.boolean_attributes.all():
                            try:
                                # Create feature object
                                feature_obj = {
                                    "id": str(bool_feature_value.id),  # Convert UUID to string
                                    "name": bool_feature_value.name or "",
                                    "key": bool_feature_value.key or "",
                                    "type": 'bool',
                                    "icon": bool_feature_value.icon.__str__() if bool_feature_value.icon else "",
                                }

                                # Add to features list
                                features.append(feature_obj)

                                # Add to highlight features if applicable
                                if str(bool_feature_value.id) in highlight_index_feature["bool"]:  # Convert UUID to string for comparison
                                    highlight_feature.append(feature_obj)
                            except Exception as e:
                                logger.error(f"Error processing boolean attribute {bool_feature_value.id}: {str(e)}")
                    except Exception as e:
                        logger.error(f"Error processing feature {feature.id}: {str(e)}")

                # Return the features as a JSON response
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content={
                        "features": features,
                        "highlight_features": highlight_feature
                    }
                )
            except HTTPException as e:
                # Re-raise HTTP exceptions
                raise
            except Exception as e:
                # Log the error and return a meaningful error message
                logger.error(f"Error in get_feature_by_category: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error retrieving features: {str(e)}"
                )
