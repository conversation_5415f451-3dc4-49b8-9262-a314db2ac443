"""
Media API for advertisements.
"""
import logging
import os
import uuid

from typing import Any, List
from fastapi import HTTPException, Request, status, UploadFile
from django.conf import settings as django_settings

from app.models import CustomUserModel
from app.models.advertisement import (
    AdvertisementModel,
    AdvertisementImagesModel,
    AdvertisementVideoModel,
)

from PIL import Image
import io
from pillow_heif import register_heif_opener

logger = logging.getLogger(__name__)

class MediaAPI:
    """Media management operations."""
    @classmethod
    async def upload_advertisement_media(cls, request: Request, files: List[UploadFile], current_user: CustomUserModel) -> Any:
        images=[]
        # Define allowed image formats including iOS formats
        ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.heic', '.heif'}
        ALLOWED_CONTENT_TYPES = {
            "image/jpeg", 
            "image/png", 
            "image/gif",
            "image/heic",
            "image/heif"
        }
        
        for file in files:
            try:
                # Check file size
                file.file.seek(0, 2)
                file_size = file.file.tell()
                await file.seek(0)  # Reset file position

                # Validate file size (2MB limit)
                if file_size > 3 * 1024 * 1024:
                    raise HTTPException(status_code=400, detail="File too large (max 3MB)")

                # Validate content type
                content_type = file.content_type
                if content_type not in ALLOWED_CONTENT_TYPES:
                    raise HTTPException(status_code=400, detail=f"Invalid file type. Allowed types: {', '.join(ALLOWED_CONTENT_TYPES)}")

                # Get file extension and generate unique filename
                ext = os.path.splitext(file.filename)[1].lower()
                if ext not in ALLOWED_EXTENSIONS:
                    raise HTTPException(status_code=400, detail=f"Invalid file extension. Allowed extensions: {', '.join(ALLOWED_EXTENSIONS)}")

                # Create proper path without double slashes
                image_dir = os.path.join(django_settings.MEDIA_ROOT, 'documents', 'advertisement',"images")

                # Create directory if it doesn't exist
                if not os.path.exists(image_dir):
                    os.makedirs(image_dir)

                # Read file content
                file_content = await file.read()
                
                # Convert HEIC/HEIF to JPEG if needed
                if ext in ['.heic', '.heif']:
                    try:
                        # Register HEIF opener
                        register_heif_opener()
                        # Open image from bytes
                        img = Image.open(io.BytesIO(file_content))
                        # Convert to RGB if needed (in case of RGBA)
                        if img.mode in ('RGBA', 'LA'):
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[-1])
                            img = background
                        elif img.mode != 'RGB':
                            img = img.convert('RGB')
                        
                        # Save as JPEG
                        output = io.BytesIO()
                        img.save(output, format='JPEG', quality=85)
                        file_content = output.getvalue()
                        ext = '.jpg'  # Change extension to .jpg
                    except Exception as e:
                        logger.error(f"Error converting HEIC/HEIF image: {str(e)}")
                        raise HTTPException(
                            status_code=400,
                            detail="Error processing HEIC/HEIF image. Please try converting it to JPEG first."
                        )

                # Generate unique filename with appropriate extension
                file_name = f"{str(uuid.uuid4())}{ext}"

                # Full path to save the file
                file_path = os.path.join(image_dir, file_name)

                # Save the file
                with open(file_path, 'wb+') as destination:
                    destination.write(file_content)

                # Update user's avatar in the database
                relative_path = os.path.join(django_settings.MEDIA_URL,'documents', 'advertisement' ,'images', file_name)
                images.append({
                    "url":relative_path,
                })

            except HTTPException:
                # Re-raise HTTP exceptions
                raise
            except Exception as e:
                # Log the error properly instead of using print
                logger.error(f"Error uploading avatar: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error uploading avatar: {str(e)}")
            finally:
                # Always close the file
                await file.close()
        return images

    @classmethod
    async def delete_advertisement_media(cls, request: Request, advertisement_id: int, media_id: int, media_type: str, current_user: CustomUserModel) -> dict:
        """
        Delete a media file (image or video) from an advertisement.

        Args:
            request: The request object
            advertisement_id: The ID of the advertisement
            media_id: The ID of the media to delete
            media_type: Type of media ('image' or 'video')
            current_user: The current user

        Returns:
            dict: Status message

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check if advertisement exists and belongs to the current user
            advertisement = await AdvertisementModel.objects.filter(
                id=advertisement_id,
                user=current_user
            ).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found or you don't have permission to delete its media"
                )

            # Handle image deletion
            if media_type.lower() == 'image':
                image = await AdvertisementImagesModel.objects.filter(
                    id=media_id,
                    advertisement=advertisement
                ).afirst()

                if not image:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Image not found"
                    )

                # Delete the physical file
                if image.url:
                    try:
                        file_path = os.path.join(django_settings.MEDIA_ROOT, image.url.lstrip('/'))
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        logger.error(f"Error deleting image file: {str(e)}")

                # Delete the database record
                await image.adelete()

            # Handle video deletion
            elif media_type.lower() == 'video':
                video = await AdvertisementVideoModel.objects.filter(
                    id=media_id,
                    advertisement=advertisement
                ).afirst()

                if not video:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Video not found"
                    )

                # Delete the physical files (video and thumbnail)
                if video.url:
                    try:
                        video_path = os.path.join(django_settings.MEDIA_ROOT, video.url.lstrip('/'))
                        if os.path.exists(video_path):
                            os.remove(video_path)
                    except Exception as e:
                        logger.error(f"Error deleting video file: {str(e)}")

                if video.thumbnail_url:
                    try:
                        thumbnail_path = os.path.join(django_settings.MEDIA_ROOT, video.thumbnail_url.lstrip('/'))
                        if os.path.exists(thumbnail_path):
                            os.remove(thumbnail_path)
                    except Exception as e:
                        logger.error(f"Error deleting video thumbnail: {str(e)}")

                # Delete the database record
                await video.adelete()

            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid media type. Must be 'image' or 'video'"
                )

            return {
                "status": "success",
                "message": f"{media_type.capitalize()} deleted successfully"
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting advertisement media: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting advertisement media: {str(e)}"
            )

    @classmethod
    async def delete_temporary_media(cls, request: Request, media_url: str, current_user: CustomUserModel) -> dict:
            """
            Delete a temporary media file that was uploaded but not yet associated with an advertisement.

            Args:
                request: The request object
                media_url: The URL of the media file to delete
                current_user: The current user

            Returns:
                dict: Status message

            Raises:
                HTTPException: If an error occurs
            """
            try:
                # Validate the media URL
                if not media_url:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Media URL is required"
                    )

                # Check if the file exists in the media directory
                file_path = os.path.join(django_settings.MEDIA_ROOT, media_url.lstrip('/'))
                
                if not os.path.exists(file_path):
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Media file not found"
                    )

                # Verify the file is in the correct directory (documents/advertisement/images)
                if not file_path.startswith(os.path.join(django_settings.MEDIA_ROOT, 'documents', 'advertisement', 'images')):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Invalid media file path"
                    )

                # Delete the file
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.error(f"Error deleting temporary media file: {str(e)}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Error deleting media file"
                    )

                return {
                    "status": "success",
                    "message": "Media file deleted successfully"
                }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error deleting temporary media: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error deleting temporary media: {str(e)}"
                )
