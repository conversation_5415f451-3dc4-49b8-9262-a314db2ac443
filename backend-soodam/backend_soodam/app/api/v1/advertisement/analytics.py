import logging

from datetime import datetime, timedelta
from typing import Optional
from django.db.models import Count
from fastapi import HTT<PERSON>Exception, Request, status
from starlette.responses import JSONResponse
from app.core.cache import cache_response
from app.core.db_profiler import profile_query
from app.models import CustomUserModel
from app.models.advertisement import (
    AdvertisementModel,
    AdvertisementViewModel,
    AdvertisementStatisticsModel
)
from app.schemas.v1.advertisement import AdvertisementSchema

logger = logging.getLogger(__name__)
class AnalyticsAPI:
    @classmethod
    @profile_query("insert")
    async def record_advertisement_view(cls,
            request: Request,
            advertisement_id: int,
            user_id: Optional[int] = None,
    ) -> JSONResponse:
        """
        Record an advertisement view.

        Args:
            request: The request
            advertisement_id: The advertisement ID
            user_id: The user ID (optional)

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.filter(id=advertisement_id).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )

            # Get the user if provided
            user = None
            if user_id:
                user = await CustomUserModel.objects.filter(id=user_id).afirst()

            # Create view data using schema
            view_data = AdvertisementSchema.AnalyticsSchema.AdvertisementViewCreateSchema(
                advertisement_id=advertisement_id,
                user_id=user.id if user else None,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent", ""),
                referrer=request.headers.get("referer", "") if request.headers.get("referer") else None
            )
            # Create the view record
            view = AdvertisementViewModel(
                advertisement=advertisement,
                user=user,
                ip_address=str(view_data.ip_address) if view_data.ip_address else None,
                user_agent=view_data.user_agent,
                referrer=str(view_data.referrer) if view_data.referrer else None
            )
            await view.asave()
            # Update advertisement statistics
            try:
                stats = await AdvertisementStatisticsModel.objects.filter(advertisement=advertisement).afirst()
                if stats:
                    await stats.aincrement_views()
                else:
                    # Create new statistics if none exist
                    stats = AdvertisementStatisticsModel(
                        advertisement=advertisement,
                        views=1,
                        favorites=0,
                        inquiries=0,
                        shares=0
                    )
                    await stats.asave()

            except Exception as e:
                logger.error(f"Error updating advertisement statistics: {str(e)}")
                # Continue execution even if statistics update fails
                pass
            return JSONResponse(content={"message": "View recorded successfully"}, status_code=status.HTTP_200_OK)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error recording advertisement view: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error recording view: {str(e)}"
            )
            # Update the advertisement statistics
            # if hasattr(advertisement, "statistics"):
            #     advertisement.statistics.views += 1
            #     advertisement.statistics.last_viewed_at = datetime.now()
            #     await advertisement.statistics.asave()

    @classmethod
    # @cache_response(timeout=300, prefix="advertisement_statistics")
    async def get_advertisement_statistics(cls,
                                           request: Request,
                                           advertisement_id: int,
                                           current_user: CustomUserModel
    ):
        """Get statistics for a specific advertisement."""
        try:
            # Check if advertisement exists and belongs to the current user
            advertisement = await AdvertisementModel.objects.filter(
                id=advertisement_id,
                user=current_user
            ).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found or you don't have permission to view its statistics"
                )

            # Get statistics
            stats = await AdvertisementStatisticsModel.objects.filter(
                advertisement=advertisement
            ).afirst()

            if not stats:
                # Create empty statistics if none exist
                stats = AdvertisementStatisticsModel(
                    advertisement=advertisement,
                    views=0,
                    favorites=0,
                    inquiries=0,
                    shares=0
                )
                await stats.asave()

            # Get view history (last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            views_by_day = await AdvertisementViewModel.objects.filter(
                advertisement=advertisement,
                created_at__gte=thirty_days_ago
            ).values('created_at__date').annotate(
                count=Count('id')
            ).order_by('created_at__date')

            # Format view history
            view_history = []
            async for day_data in views_by_day:
                view_history.append({
                    "date": day_data['created_at__date'].strftime('%Y-%m-%d'),
                    "count": day_data['count']
                })

            # Return statistics
            return {
                "advertisement_id": advertisement.id,
                "title": advertisement.title,
                "status": advertisement.status,
                "created_at": advertisement.created_at.__str__(),
                "statistics": {
                    "views": stats.views,
                    "favorites": stats.favorites,
                    "inquiries": stats.inquiries,
                    "shares": getattr(stats, 'shares', 0),
                },
                "view_history": view_history
            }

        except Exception as e:
            logger.error(f"Error getting advertisement statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting advertisement statistics: {str(e)}"
            )
