"""
Admin API for managing advertisement edits.
"""

import logging
from typing import Optional

from asgiref.sync import sync_to_async
from fastapi import Request, HTTPException, status
from starlette.responses import JSONResponse

from app.enums.status import AdvertisementStatus
from app.models import CustomUserModel
from app.models.advertisement import (
    AdvertisementEditModel,
    AdvertisementModel,
)
from app.schemas.advertisement.v2.advertisement import (
    AdvertisementEditDetailSchemaV2,
    AdvertisementEditListSchemaV2,
    AdvertisementEditApprovalSchemaV2,
)
from app.utils.pagination import paginate_queryset
from app.core.db_profiler import profile_query
import jdatetime

logger = logging.getLogger(__name__)


class AdminAdvertisementEditAPI:
    @classmethod
    @profile_query("select")
    async def get_pending_edits(cls,
        request: Request,
        page: int = 1,
        limit: int = 10,
        status_filter: Optional[str] = None,
    ) -> AdvertisementEditListSchemaV2:
        """
        Get a list of advertisement edits for admin review.

        Args:
            request: The request
            page: The page number
            limit: The page size
            status_filter: Filter by edit status (pending, approved, rejected)

        Returns:
            AdvertisementEditListSchemaV2: The advertisement edits list

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Build the queryset
            queryset = AdvertisementEditModel.objects.select_related(
                "original_advertisement",
                "original_advertisement__user",
                "main_category",
                "sub_category",
                "reviewed_by"
            ).prefetch_related(
                "price",
                "address",
                "text_values",
                "boolean_values",
                "choice_attributes"
            )

            # Apply status filter
            if status_filter:
                if status_filter.lower() == "pending":
                    queryset = queryset.filter(edit_status=AdvertisementStatus.PENDING)
                elif status_filter.lower() == "approved":
                    queryset = queryset.filter(edit_status=AdvertisementStatus.APPROVED)
                elif status_filter.lower() == "rejected":
                    queryset = queryset.filter(edit_status=AdvertisementStatus.REJECTED)

            # Order by creation date (newest first)
            queryset = queryset.order_by("-created_at")

            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)

            # Build the response
            items = []
            async for edit in paginated["items"]:
                # Build the edit detail schema
                item = AdvertisementEditDetailSchemaV2(
                    id=edit.id,
                    original_advertisement_id=edit.original_advertisement.id,
                    edit_status=edit.edit_status,
                    title=edit.title,
                    description=edit.description,
                    security_code_owner_building=edit.security_code_owner_building,
                    phone_number_owner_building=edit.phone_number_owner_building,
                    admin_notes=edit.admin_notes,
                    reviewed_by=None,  # TODO: Build user schema if reviewed_by exists
                    reviewed_at=edit.reviewed_at.__str__() if edit.reviewed_at else None,
                    created_at=edit.created_at.__str__(),
                    updated_at=edit.updated_at.__str__(),
                )
                items.append(item)

            # Calculate pagination
            import math
            total_pages = math.ceil(paginated["total"] / limit)

            return AdvertisementEditListSchemaV2(
                items=items,
                total=paginated["total"],
                page=page,
                limit=limit,
                pages=total_pages,
                has_next=page < total_pages,
                has_prev=page > 1,
            )
        except Exception as e:
            logger.error(f"Error getting advertisement edits: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisement edits"
            )

    @classmethod
    @profile_query("select")
    async def get_edit_detail(cls,
        request: Request,
        edit_id: int,
    ) -> AdvertisementEditDetailSchemaV2:
        """
        Get detailed information about a specific advertisement edit.

        Args:
            request: The request
            edit_id: The edit ID

        Returns:
            AdvertisementEditDetailSchemaV2: The edit details

        Raises:
            HTTPException: If the edit is not found
        """
        try:
            # Get the edit
            edit = await AdvertisementEditModel.objects.select_related(
                "original_advertisement",
                "original_advertisement__user",
                "main_category",
                "sub_category",
                "reviewed_by"
            ).prefetch_related(
                "price",
                "address",
                "text_values",
                "boolean_values",
                "choice_attributes"
            ).filter(id=edit_id).afirst()

            if not edit:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement edit not found"
                )

            # Build the detailed response
            # TODO: Build complete schemas for category, price, location, attributes, etc.
            return AdvertisementEditDetailSchemaV2(
                id=edit.id,
                original_advertisement_id=edit.original_advertisement.id,
                edit_status=edit.edit_status,
                title=edit.title,
                description=edit.description,
                security_code_owner_building=edit.security_code_owner_building,
                phone_number_owner_building=edit.phone_number_owner_building,
                admin_notes=edit.admin_notes,
                reviewed_by=None,  # TODO: Build user schema if reviewed_by exists
                reviewed_at=edit.reviewed_at.__str__() if edit.reviewed_at else None,
                created_at=edit.created_at.__str__(),
                updated_at=edit.updated_at.__str__(),
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting advertisement edit detail: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisement edit detail"
            )

    @classmethod
    @profile_query("update")
    async def approve_or_reject_edit(cls,
        request: Request,
        edit_id: int,
        schema: AdvertisementEditApprovalSchemaV2,
        admin_user: CustomUserModel,
    ) -> JSONResponse:
        """
        Approve or reject an advertisement edit.

        Args:
            request: The request
            edit_id: The edit ID
            schema: The approval schema
            admin_user: The admin user

        Returns:
            JSONResponse: Success message

        Raises:
            HTTPException: If the edit is not found or action is invalid
        """
        try:
            # Get the edit
            edit = await AdvertisementEditModel.objects.select_related(
                "original_advertisement"
            ).filter(id=edit_id).afirst()

            if not edit:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement edit not found"
                )

            # Check if edit is still pending
            if edit.edit_status != AdvertisementStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Edit has already been reviewed"
                )

            # Validate action
            if schema.action.lower() not in ["approve", "reject"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Action must be 'approve' or 'reject'"
                )

            # Update edit record
            edit.admin_notes = schema.admin_notes
            edit.reviewed_by = admin_user
            edit.reviewed_at = jdatetime.datetime.now()

            if schema.action.lower() == "approve":
                # Apply the edit to the original advertisement
                await sync_to_async(edit.apply_to_original)()
                message = "Advertisement edit approved and applied successfully"
            else:
                # Reject the edit
                edit.edit_status = AdvertisementStatus.REJECTED
                await edit.asave()
                message = "Advertisement edit rejected successfully"

            return JSONResponse(
                content={"message": message},
                status_code=status.HTTP_200_OK
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing advertisement edit approval: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing advertisement edit approval: {str(e)}"
            )
