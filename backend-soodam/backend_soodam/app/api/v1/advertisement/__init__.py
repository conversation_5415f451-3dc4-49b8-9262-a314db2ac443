"""
Advertisement API Interface.

This module provides a unified interface for all advertisement-related operations.
"""

from app.api.v1.advertisement.metadata import MetadataAPI
from app.api.v1.advertisement.media import MediaAPI
from app.api.v1.advertisement.crud import CRUDAPI
from app.api.v1.advertisement.user import UserAdvertisementAPI
from app.api.v1.advertisement.location import LocationAPI
from app.api.v1.advertisement.analytics import AnalyticsAPI
from app.api.v1.advertisement.moderation import ModerationAPI


class AdvertisementAPI:
    """Consolidated Advertisement API for the Soodam backend."""

    MetadataAPI = MetadataAPI
    MediaAPI = MediaAPI
    CRUDAPI = CRUDAPI
    UserAdvertisementAPI = UserAdvertisementAPI
    LocationAPI = LocationAPI
    AnalyticsAPI = AnalyticsAPI
    ModerationAPI = ModerationAPI

    # Backward compatibility methods
    @classmethod
    async def get_meta_data(cls, request):
        return await cls.MetadataAPI.get_meta_data(request)

    @classmethod
    async def get_feature_by_category(cls, request, schema):
        return await cls.MetadataAPI.get_feature_by_category(request, schema)

    @classmethod
    async def upload_advertisement_media(cls, request, files, current_user):
        return await cls.MediaAPI.upload_advertisement_media(request, files, current_user)

    @classmethod
    async def delete_advertisement_media(cls, request, advertisement_id, media_id, media_type, current_user):
        return await cls.MediaAPI.delete_advertisement_media(request, advertisement_id, media_id, media_type,
                                                             current_user)

    @classmethod
    async def delete_temporary_media(cls, request, media_url, current_user):
        return await cls.MediaAPI.delete_temporary_media(request, media_url, current_user)

    @classmethod
    async def get_advertisements(cls, request, page=1, limit=10, category_id=None, search=None, sort_by="created_at",
                                 sort_order="desc", location=None, price_min=None, price_max=None, featured=None):
        return await cls.CRUDAPI.get_advertisements(request, page, limit, category_id, search, sort_by, sort_order,
                                                    location, price_min, price_max, featured)

    @classmethod
    async def get_advertisement(cls, request, advertisement_id):
        return await cls.CRUDAPI.get_advertisement(request, advertisement_id)

    @classmethod
    async def create_advertisement(cls, request, schema, current_user):
        return await cls.CRUDAPI.create_advertisement(request, schema, current_user)

    @classmethod
    async def update_advertisement(cls, request, advertisement_id, schema, current_user):
        return await cls.CRUDAPI.update_advertisement(request, advertisement_id, schema, current_user)

    @classmethod
    async def delete_advertisement(cls, request, advertisement_id, current_user):
        return await cls.CRUDAPI.delete_advertisement(request, advertisement_id, current_user)

    @classmethod
    async def get_my_advertisement(cls, request, current_user):
        return await cls.UserAdvertisementAPI.get_my_advertisement(request, current_user)

    @classmethod
    async def toggle_favorite(cls, request, advertisement_id, current_user):
        return await cls.UserAdvertisementAPI.toggle_favorite(request, advertisement_id, current_user)

    @classmethod
    async def get_favorite_advertisements(cls, request, current_user, page=1, limit=10):
        return await cls.UserAdvertisementAPI.get_favorite_advertisements(request, current_user, page, limit)

    @classmethod
    async def get_advertisements_nearby(cls, request, latitude, longitude, radius=10.0, page=1, limit=10,
                                        category_id=None):
        return await cls.LocationAPI.get_advertisements_nearby(request, latitude, longitude, radius, page, limit,
                                                               category_id)

    @classmethod
    async def record_advertisement_view(cls, request, advertisement_id, user_id=None):
        return await cls.AnalyticsAPI.record_advertisement_view(request, advertisement_id, user_id)

    @classmethod
    async def get_advertisement_statistics(cls, request, advertisement_id, current_user):
        return await cls.AnalyticsAPI.get_advertisement_statistics(request, advertisement_id, current_user)

    @classmethod
    async def flag_advertisement(cls, request, advertisement_id, reason, current_user):
        return await cls.ModerationAPI.flag_advertisement(request, advertisement_id, reason, current_user)