
from app.models.contact import Contact
from app.schemas.v1.contact.contact import <PERSON><PERSON><PERSON>, ContactResponse
from fastapi import <PERSON>TT<PERSON><PERSON><PERSON><PERSON>, status
from django.db import IntegrityError
from fastapi import Request, HTTPException, status

class ContactsAPI:
    @classmethod
    async def create_contact_message(cls,request:Request,contact_data: <PERSON><PERSON><PERSON>) -> ContactResponse:
        try:
            contact =await Contact.objects.acreate(
                name=contact_data.name,
                email=contact_data.email,
                phone_number=contact_data.phone_number,
                address=contact_data.address,
                message=contact_data.message
            )
            return ContactResponse(
                id=contact.id,
                name=contact.name,
                email=contact.email,
                phone_number=contact.phone_number,
                address=contact.address,
                message=contact.message,
                created_at=contact.created_at
            )
        except IntegrityError:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create contact message.")