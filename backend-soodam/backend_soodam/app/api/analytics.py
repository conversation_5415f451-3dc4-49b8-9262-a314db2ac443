"""
Analytics API for the Soodam backend.

This module provides API functions for analytics operations.
"""

import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

from django.db.models import Avg, Count, F, Q, Sum
from fastapi import HTTPException, Request, status

from ..models.analytics import (
    APIRequestLogModel,
    CategoryStatisticsModel,
    DailyStatisticsModel,
    LocationStatisticsModel,
    SearchQueryLogModel,
    UserActivityLogModel,
)
from ..services.analytics import AnalyticsService

logger = logging.getLogger(__name__)


class AnalyticsAPI:
    """Analytics API for the Soodam backend."""
    
    @staticmethod
    async def get_daily_statistics(
        request: Request,
        start_date: date,
        end_date: date,
    ) -> List[Dict]:
        """
        Get daily statistics.
        
        Args:
            request: The request
            start_date: The start date
            end_date: The end date
            
        Returns:
            List[Dict]: The daily statistics
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Get daily statistics
            stats = await DailyStatisticsModel.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            ).order_by('date').values()
            
            return list(stats)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting daily statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting daily statistics: {str(e)}"
            )
    
    @staticmethod
    async def get_category_statistics(
        request: Request,
        start_date: date,
        end_date: date,
        category_id: Optional[int] = None,
    ) -> List[Dict]:
        """
        Get category statistics.
        
        Args:
            request: The request
            start_date: The start date
            end_date: The end date
            category_id: The category ID (optional)
            
        Returns:
            List[Dict]: The category statistics
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Build the query
            query = Q(date__gte=start_date, date__lte=end_date)
            
            if category_id is not None:
                query &= Q(category_id=category_id)
            
            # Get category statistics
            stats = await CategoryStatisticsModel.objects.filter(query).order_by(
                'date', 'category_name'
            ).values()
            
            return list(stats)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting category statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting category statistics: {str(e)}"
            )
    
    @staticmethod
    async def get_location_statistics(
        request: Request,
        start_date: date,
        end_date: date,
        location_id: Optional[int] = None,
    ) -> List[Dict]:
        """
        Get location statistics.
        
        Args:
            request: The request
            start_date: The start date
            end_date: The end date
            location_id: The location ID (optional)
            
        Returns:
            List[Dict]: The location statistics
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Build the query
            query = Q(date__gte=start_date, date__lte=end_date)
            
            if location_id is not None:
                query &= Q(location_id=location_id)
            
            # Get location statistics
            stats = await LocationStatisticsModel.objects.filter(query).order_by(
                'date', 'city'
            ).values()
            
            return list(stats)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting location statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting location statistics: {str(e)}"
            )
    
    @staticmethod
    async def get_popular_searches(
        request: Request,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 10,
    ) -> List[Dict]:
        """
        Get popular searches.
        
        Args:
            request: The request
            start_date: The start date (optional)
            end_date: The end date (optional)
            limit: The maximum number of results to return
            
        Returns:
            List[Dict]: The popular searches
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Set default date range if not provided
            if start_date is None:
                start_date = date.today() - timedelta(days=30)
            
            if end_date is None:
                end_date = date.today()
            
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            # Get popular searches
            searches = await SearchQueryLogModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).values('query').annotate(
                count=Count('id')
            ).order_by('-count')[:limit]
            
            return list(searches)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting popular searches: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting popular searches: {str(e)}"
            )
    
    @staticmethod
    async def get_user_activity(
        request: Request,
        user_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        action_type: Optional[str] = None,
        limit: int = 100,
    ) -> List[Dict]:
        """
        Get user activity.
        
        Args:
            request: The request
            user_id: The user ID
            start_date: The start date (optional)
            end_date: The end date (optional)
            action_type: The action type (optional)
            limit: The maximum number of results to return
            
        Returns:
            List[Dict]: The user activity
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Set default date range if not provided
            if start_date is None:
                start_date = date.today() - timedelta(days=30)
            
            if end_date is None:
                end_date = date.today()
            
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            # Build the query
            query = Q(
                user_id=user_id,
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            )
            
            if action_type:
                query &= Q(action_type=action_type)
            
            # Get user activity
            activity = await UserActivityLogModel.objects.filter(query).order_by(
                '-created_at'
            ).values()[:limit]
            
            return list(activity)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting user activity: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting user activity: {str(e)}"
            )
    
    @staticmethod
    async def get_api_requests(
        request: Request,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        status_code: Optional[int] = None,
        limit: int = 100,
    ) -> List[Dict]:
        """
        Get API requests.
        
        Args:
            request: The request
            start_date: The start date (optional)
            end_date: The end date (optional)
            endpoint: The endpoint (optional)
            method: The HTTP method (optional)
            status_code: The status code (optional)
            limit: The maximum number of results to return
            
        Returns:
            List[Dict]: The API requests
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Set default date range if not provided
            if start_date is None:
                start_date = date.today() - timedelta(days=1)
            
            if end_date is None:
                end_date = date.today()
            
            # Check date range
            if end_date < start_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="End date must be greater than or equal to start date"
                )
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            # Build the query
            query = Q(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            )
            
            if endpoint:
                query &= Q(endpoint__contains=endpoint)
            
            if method:
                query &= Q(method=method)
            
            if status_code:
                query &= Q(status_code=status_code)
            
            # Get API requests
            requests = await APIRequestLogModel.objects.filter(query).order_by(
                '-created_at'
            ).values()[:limit]
            
            return list(requests)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting API requests: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting API requests: {str(e)}"
            )
    
    @staticmethod
    async def generate_statistics(
        request: Request,
        target_date: Optional[date] = None,
    ) -> Dict:
        """
        Generate statistics for a specific date.
        
        Args:
            request: The request
            target_date: The target date (defaults to yesterday)
            
        Returns:
            Dict: The generated statistics
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Set the target date to yesterday if not provided
            if target_date is None:
                target_date = date.today() - timedelta(days=1)
            
            # Generate daily statistics
            daily_stats = await AnalyticsService.generate_daily_statistics(target_date)
            
            # Generate category statistics
            category_stats = await AnalyticsService.generate_category_statistics(target_date)
            
            # Generate location statistics
            location_stats = await AnalyticsService.generate_location_statistics(target_date)
            
            return {
                'success': True,
                'date': target_date.isoformat(),
                'daily_statistics': daily_stats.id if daily_stats else None,
                'category_statistics_count': len(category_stats),
                'location_statistics_count': len(location_stats)
            }
        except Exception as e:
            logger.error(f"Error generating statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating statistics: {str(e)}"
            )
