"""
Security API for the Soodam backend.
"""

import logging
from datetime import datetime
from typing import List, Optional

from django.db.models import Q
from fastapi import HTTPException, Request, status

from ..core.security import (
    generate_api_key,
    generate_totp_secret,
    get_totp_uri,
    hash_api_key,
    verify_api_key,
    verify_totp,
)
from ..models import CustomUserModel
from ..models.security import APIKeyModel, LoginAttemptModel, TwoFactorAuthModel
from ..schemas.security import (
    APIKeyCreate,
    APIKeyInfo,
    APIKeyList,
    APIKeyResponse,
    LoginAttemptInfo,
    LoginAttemptList,
    TwoFactorAuthSetup,
    TwoFactorAuthStatus,
)
from ..utils.pagination import paginate_queryset

logger = logging.getLogger(__name__)


class SecurityAPI:
    """Security API for the Soodam backend."""
    
    @staticmethod
    async def create_api_key(request: Request, schema: APIKeyCreate, user: CustomUserModel) -> APIKeyResponse:
        """
        Create a new API key.
        
        Args:
            request: The request
            schema: The API key creation schema
            user: The user
            
        Returns:
            APIKeyResponse: The created API key
            
        Raises:
            HTTPException: If the API key could not be created
        """
        # Generate API key
        api_key = generate_api_key()
        
        # Hash API key
        key_hash = hash_api_key(api_key)
        
        # Create API key
        api_key_model = await APIKeyModel.objects.acreate(
            name=schema.name,
            key_hash=key_hash,
            user=user,
            expires_at=schema.expires_at,
        )
        
        logger.info(f"API key created for user {user.email}")
        
        return APIKeyResponse(
            id=api_key_model.id,
            name=api_key_model.name,
            key=api_key,
            is_active=api_key_model.is_active,
            created_at=api_key_model.created_at,
            expires_at=api_key_model.expires_at,
        )
    
    @staticmethod
    async def get_api_keys(request: Request, user: CustomUserModel, page: int = 1, limit: int = 10) -> APIKeyList:
        """
        Get API keys for a user.
        
        Args:
            request: The request
            user: The user
            page: The page number
            limit: The page size
            
        Returns:
            APIKeyList: The API keys
        """
        # Get API keys
        queryset = APIKeyModel.objects.filter(user=user)
        
        # Paginate
        paginated = await paginate_queryset(queryset, page, limit)
        
        # Convert to schema
        items = []
        for api_key in paginated["items"]:
            items.append(
                APIKeyInfo(
                    id=api_key.id,
                    name=api_key.name,
                    is_active=api_key.is_active,
                    created_at=api_key.created_at,
                    last_used_at=api_key.last_used_at,
                    expires_at=api_key.expires_at,
                )
            )
        
        return APIKeyList(
            items=items,
            total=paginated["total"],
        )
    
    @staticmethod
    async def revoke_api_key(request: Request, api_key_id: int, user: CustomUserModel) -> None:
        """
        Revoke an API key.
        
        Args:
            request: The request
            api_key_id: The API key ID
            user: The user
            
        Raises:
            HTTPException: If the API key could not be found or revoked
        """
        # Get API key
        api_key = await APIKeyModel.objects.filter(id=api_key_id, user=user).afirst()
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found",
            )
        
        # Revoke API key
        api_key.is_active = False
        await api_key.asave()
        
        logger.info(f"API key {api_key_id} revoked for user {user.email}")
    
    @staticmethod
    async def setup_two_factor_auth(request: Request, user: CustomUserModel) -> TwoFactorAuthSetup:
        """
        Set up two-factor authentication.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            TwoFactorAuthSetup: The two-factor authentication setup
        """
        # Generate TOTP secret
        secret = generate_totp_secret()
        
        # Get TOTP URI
        uri = get_totp_uri(secret, user.email)
        
        # Create or update two-factor authentication
        two_factor_auth, created = await TwoFactorAuthModel.objects.aget_or_create(
            user=user,
            defaults={
                "secret": secret,
                "is_enabled": False,
            },
        )
        
        if not created:
            two_factor_auth.secret = secret
            two_factor_auth.is_enabled = False
            await two_factor_auth.asave()
        
        logger.info(f"Two-factor authentication set up for user {user.email}")
        
        return TwoFactorAuthSetup(
            secret=secret,
            uri=uri,
        )
    
    @staticmethod
    async def enable_two_factor_auth(request: Request, token: str, user: CustomUserModel) -> bool:
        """
        Enable two-factor authentication.
        
        Args:
            request: The request
            token: The TOTP token
            user: The user
            
        Returns:
            bool: True if two-factor authentication was enabled, False otherwise
            
        Raises:
            HTTPException: If two-factor authentication could not be enabled
        """
        # Get two-factor authentication
        two_factor_auth = await TwoFactorAuthModel.objects.filter(user=user).afirst()
        
        if not two_factor_auth:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Two-factor authentication not set up",
            )
        
        # Verify token
        if not verify_totp(two_factor_auth.secret, token):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token",
            )
        
        # Enable two-factor authentication
        two_factor_auth.is_enabled = True
        two_factor_auth.last_verified_at = datetime.now()
        await two_factor_auth.asave()
        
        logger.info(f"Two-factor authentication enabled for user {user.email}")
        
        return True
    
    @staticmethod
    async def disable_two_factor_auth(request: Request, user: CustomUserModel) -> bool:
        """
        Disable two-factor authentication.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            bool: True if two-factor authentication was disabled, False otherwise
            
        Raises:
            HTTPException: If two-factor authentication could not be disabled
        """
        # Get two-factor authentication
        two_factor_auth = await TwoFactorAuthModel.objects.filter(user=user).afirst()
        
        if not two_factor_auth:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Two-factor authentication not set up",
            )
        
        # Disable two-factor authentication
        two_factor_auth.is_enabled = False
        await two_factor_auth.asave()
        
        logger.info(f"Two-factor authentication disabled for user {user.email}")
        
        return True
    
    @staticmethod
    async def get_two_factor_auth_status(request: Request, user: CustomUserModel) -> TwoFactorAuthStatus:
        """
        Get two-factor authentication status.
        
        Args:
            request: The request
            user: The user
            
        Returns:
            TwoFactorAuthStatus: The two-factor authentication status
        """
        # Get two-factor authentication
        two_factor_auth = await TwoFactorAuthModel.objects.filter(user=user).afirst()
        
        if not two_factor_auth:
            return TwoFactorAuthStatus(
                is_enabled=False,
                created_at=None,
                last_verified_at=None,
            )
        
        return TwoFactorAuthStatus(
            is_enabled=two_factor_auth.is_enabled,
            created_at=two_factor_auth.created_at,
            last_verified_at=two_factor_auth.last_verified_at,
        )
    
    @staticmethod
    async def verify_two_factor_auth(request: Request, token: str, user: CustomUserModel) -> bool:
        """
        Verify two-factor authentication.
        
        Args:
            request: The request
            token: The TOTP token
            user: The user
            
        Returns:
            bool: True if the token is valid, False otherwise
        """
        # Get two-factor authentication
        two_factor_auth = await TwoFactorAuthModel.objects.filter(user=user).afirst()
        
        if not two_factor_auth:
            return False
        
        # Verify token
        if not verify_totp(two_factor_auth.secret, token):
            return False
        
        # Update last verified at
        two_factor_auth.last_verified_at = datetime.now()
        await two_factor_auth.asave()
        
        return True
    
    @staticmethod
    async def get_login_attempts(
        request: Request,
        user: CustomUserModel,
        page: int = 1,
        limit: int = 10,
        success: Optional[bool] = None,
    ) -> LoginAttemptList:
        """
        Get login attempts for a user.
        
        Args:
            request: The request
            user: The user
            page: The page number
            limit: The page size
            success: Filter by success
            
        Returns:
            LoginAttemptList: The login attempts
        """
        # Get login attempts
        queryset = LoginAttemptModel.objects.filter(user=user)
        
        if success is not None:
            queryset = queryset.filter(success=success)
        
        # Paginate
        paginated = await paginate_queryset(queryset, page, limit)
        
        # Convert to schema
        items = []
        for login_attempt in paginated["items"]:
            items.append(
                LoginAttemptInfo(
                    id=login_attempt.id,
                    ip_address=login_attempt.ip_address,
                    user_agent=login_attempt.user_agent,
                    success=login_attempt.success,
                    created_at=login_attempt.created_at,
                )
            )
        
        return LoginAttemptList(
            items=items,
            total=paginated["total"],
        )
