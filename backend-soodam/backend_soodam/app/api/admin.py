"""
Consolidated Admin API for the Soodam backend.

This module provides API functions for admin operations, combining
functionality from the original admin.py and admin_enhanced.py files.
"""
from asgiref.sync import sync_to_async
from fastapi import HTTPException, Request, status
from typing import List, Optional, Dict, Any

from app.core.elasticsearch import delete_advertisement
from app.models import (
    CustomUserModel, AdvertisementModel, BlogModel, AdminType,
    SpecialAdmin, CityModel, ProvinceModel, AdminActivity,
    AdminTransaction, UserRoleModel, RolePermissionModel,
    PermissionModel, RoleModel, HighlightAttributeModel
)
from app.models.advertisement import (
    AdvertisementEditModel, AdvertisementEditTextValueModel,
    AdvertisementEditBooleanValueModel, AdvertisementEditPriceModel,
    AdvertisementEditLocationModel
)
from django.db.models import Count, Q, Sum
import logging
from math import ceil
from datetime import datetime
import jdatetime

from ..enums.status import (
    AdvertisementStatus, BlogStatus, AdminStatus,
    ActionType, TransactionType
)
from app.schemas import AdvertisementUserSchema, AdvertisementLocationSchema, AdvertisementPriceSchema, \
    AdvertisementListItemSchemaV2, AdvertisementCategorySchema, AdvertisementAttributeSchema
from ..utils.pagination import paginate_request, paginate_queryset
from ..core.cache import cache_response, invalidate_cache

from app.schemas.admin import (
    SpecialAdminCreateSchema, SpecialAdminUpdateSchema, AdminTransactionCreateSchema,
    AdminTypeSchema, SpecialAdminResponseSchema, PaginatedSpecialAdminResponse,
    PaginatedAdminActivityResponse, AdminDashboardStatsResponse, PaginatedTransactionResponse,
    PaginatedAdvertisementResponse, AdvertisementResponse
)
from ..schemas.pagination import PaginationMetadata

logger = logging.getLogger(__name__)


class AdminAPI:
    """Consolidated Admin API for the Soodam backend."""

    #
    # Advertisement Management Methods
    #
    @staticmethod
    # @cache_response(timeout=60, prefix="admin_advertisements")
    async def get_all_advertisements(
        request: Request,
        current_admin: CustomUserModel,
        status_filter: Optional[int] = None,
        page: int = 1,
        limit: int = 1,
        search: Optional[str] = None
    ) -> PaginatedAdvertisementResponse:
        """
        Get all advertisements for admin review with pagination.

        Args:
            request: The request object
            current_admin: The current admin user
            status_filter: Optional filter by status
            page: Page number
            limit: Items per page
            search: Optional search term

        Returns:
            PaginatedAdvertisementResponse containing advertisements and pagination metadata

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the base queryset with limited fields
            query = await sync_to_async(AdvertisementModel.objects.select_related(
                "sub_category", "sub_category__main_category", "user", "statistics"
            ).prefetch_related(
                "address", "address__province", "address__city", "price", "images", "videos", "tags", "user__rating",
                "advertisement_boolean_values",
            ).all)()
            # Apply filters
            if status_filter is not None:
                query = query.filter(status=status_filter)
            if search:
                query = query.filter(
                    Q(title__icontains=search) |
                    Q(description__icontains=search)
                )
            # Get paginated response
            paginated_response = await paginate_request(
                request=request,
                queryset=query,
                page=page,
                limit=limit,
                order_by="-created_at"
            )
            # Convert items to AdvertisementResponse
            items = []
            for ad in paginated_response.items:
                user = AdvertisementUserSchema(
                    id=ad.user.id,
                    username=ad.user.username if ad.user.username else "",
                    full_name=f"{ad.user.first_name} {ad.user.last_name}",
                    avatar=ad.user.avatar.__str__(),
                    rating=getattr(ad.user, 'get_rating', 0),
                    is_verified=ad.user.is_verified,
                    created_at=ad.user.created_at_jalali.__str__()
                )
                # Safely get address data
                address_data = None
                price=None
                category=None
                if hasattr(ad, 'address') and ad.address:
                    address=await ad.address.select_related("province","city").afirst()
                    address_data=AdvertisementLocationSchema(
                        id=address.id,
                        province=address.province,
                        city=address.city,
                        # country=advertisement.location.country,
                        latitude=address.latitude,
                        longitude=address.longitude,
                        address=address.address,
                        zip_code=address.zip_code,
                        geolocation=address.geolocation.__str__()
                    )
                if hasattr(ad,'price') and ad.price:
                    price = await ad.price.afirst()
                    price = AdvertisementPriceSchema(
                        id=price.id,
                        deposit=price.deposit,
                        rent=price.rent,
                        amount=price.amount,
                        currency=price.currency,
                        is_negotiable=price.is_negotiable,
                        discount_amount=price.discount_amount,
                        original_amount=price.original_amount,
                        price_per_unit=price.price_per_unit,
                        unit=price.unit
                    )
                primary_image = None
                for image in ad.images.all():
                    if image.is_primary:
                        primary_image = image.url
                        break
                category = AdvertisementCategorySchema(
                    id=ad.sub_category.id,
                    name=ad.sub_category.name,
                    key=ad.sub_category.key,
                    main_category=ad.sub_category.main_category,
                    icon=ad.sub_category.icon.__str__()
                )
                # Build the attributes schema
                attributes = []
                highlight_attributes = []
                # Try to fetch highlight features for the sub_category
                find_highlight_feature = await HighlightAttributeModel.objects.filter(
                    sub_category=ad.sub_category,
                ).afirst()
                # Prepare sets of attribute IDs (not value IDs!) for highlight matching
                highlight_index_feature = {
                    "choice": set(),
                    "bool": set(),
                    "text": set(),
                }
                if find_highlight_feature:
                    # Get highlighted choice attribute IDs
                    async for choice_attr in find_highlight_feature.choice_attributes.all():
                        highlight_index_feature["choice"].add(str(choice_attr.id))
                    # Get highlighted boolean attribute IDs
                    async for bool_attr in find_highlight_feature.boolean_attributes.all():
                        highlight_index_feature["bool"].add(str(bool_attr.id))
                    # Get highlighted text attribute IDs
                    async for text_attr in find_highlight_feature.text_attributes.all():
                        highlight_index_feature["text"].add(str(text_attr.id))
                # For each attribute value, check if its attribute.id is in the highlight set
                async for choice_value in ad.choice_attributes.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(choice_value.attribute.id),
                            name=choice_value.attribute.name,
                            key=choice_value.attribute.key,
                            type='choice',
                            value={
                                "id": str(choice_value.id),
                                "value": choice_value.value
                            },
                            # unit=None,
                            icon=choice_value.attribute.icon.__str__() if choice_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        # Compare attribute.id, not value.id
                        if str(choice_value.attribute.id) in highlight_index_feature["choice"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing choice attribute {choice_value.id}: {str(e)}")

                async for bool_value in ad.advertisement_boolean_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(bool_value.attribute.id),
                            name=bool_value.attribute.name,
                            key=bool_value.attribute.key,
                            type='bool',
                            value=bool_value.value,
                            icon=bool_value.attribute.icon.__str__() if bool_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(bool_value.attribute.id) in highlight_index_feature["bool"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing boolean attribute {bool_value.id}: {str(e)}")

                async for text_value in ad.advertisement_text_values.select_related('attribute').all():
                    try:
                        attribute = AdvertisementAttributeSchema(
                            id=str(text_value.attribute.id),
                            name=text_value.attribute.name,
                            key=text_value.attribute.key,
                            type='text',
                            value=text_value.value,
                            icon=text_value.attribute.icon.__str__() if text_value.attribute.icon else ""
                        )
                        attributes.append(attribute)
                        if str(text_value.attribute.id) in highlight_index_feature["text"]:
                            highlight_attributes.append(attribute)
                    except Exception as e:
                        logger.error(f"Error processing text attribute {text_value.id}: {str(e)}")

                ad_response = AdvertisementListItemSchemaV2(
                    id=ad.id,
                    title=ad.title,
                    status=ad.status,
                    user=user,
                    category=category,
                    full_address=address_data,
                    price=price,
                    highlight_attributes=highlight_attributes,
                    primary_image=primary_image,
                    created_at=ad.created_at.__str__(),
                    updated_at=ad.updated_at.__str__(),
                    # description=ad.description,
                    # main_category=category,
                    # images=[{"id": img.id, "url": img.url} for img in ad.images.all()] if hasattr(ad, 'images') else [],
                    # videos=[{"id": vid.id, "url": vid.url} for vid in ad.videos.all()] if hasattr(ad, 'videos') else [],
                    # tags=[{"id": tag.id, "name": tag.name} for tag in ad.tags.all()] if hasattr(ad, 'tags') else [],
                    # statistics={
                    #     "id": ad.statistics.id,
                    #     "views": ad.statistics.views,
                    #     "likes_count": ad.statistics.likes_count,
                    #     "dislikes_count": ad.statistics.dislikes_count,
                    #     "comments_count": ad.statistics.comments_count
                    # } if ad.statistics else None,
                    # user_rating={
                    #     "id": ad.user.rating.id,
                    #     "rating": getattr(ad.user.rating, 'get_rating', 0),
                    # } if hasattr(ad.user, 'rating') and ad.user.rating else None,
                    # advertisement_boolean_values={
                    #     k: v for k, v in ad.advertisement_boolean_values.items()
                    # } if hasattr(ad, 'advertisement_boolean_values') else None
                )
                items.append(ad_response)
            # Create the final response
            return PaginatedAdvertisementResponse(
                items=items,
                metadata=PaginationMetadata(
                    page=paginated_response.metadata.page,
                    limit=paginated_response.metadata.limit,
                    total_count=paginated_response.metadata.total_count,
                    total_pages=paginated_response.metadata.total_pages,
                    has_next=paginated_response.metadata.has_next,
                    has_prev=paginated_response.metadata.has_prev
                )
            )

        except Exception as e:
            logger.error(f"Error fetching advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch advertisements"
            )

    @staticmethod
    async def approve_advertisement(request: Request, adv_id: int, approve: bool, current_admin: CustomUserModel):
        """
        Approve or reject an advertisement.

        Args:
            request: The request object
            adv_id: Advertisement ID
            approve: Whether to approve or reject
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If advertisement not found or an error occurs
        """
        try:
            advertisement = await AdvertisementModel.objects.filter(id=adv_id).afirst()
            if not advertisement:
                raise HTTPException(status_code=404, detail="Advertisement not found")

            # Use enum values instead of hardcoded status values
            advertisement.status = (
                AdvertisementStatus.APPROVED if approve else AdvertisementStatus.REJECTED
            )
            await advertisement.asave()

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.UPDATE,
                    entity_type="advertisement",
                    entity_id=adv_id,
                    description=f"Advertisement {'approved' if approve else 'rejected'}"
                )

            # Invalidate related caches
            invalidate_cache("admin_advertisements")
            invalidate_cache("admin_dashboard")

            return {
                "id": advertisement.id,
                "status": advertisement.status,
                "message": f"Advertisement {'approved' if approve else 'rejected'} successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating advertisement status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update advertisement status"
            )

    @staticmethod
    async def delete_advertisement(request: Request, adv_id: int, current_admin: CustomUserModel):
        """
        Delete an advertisement and all its related data.

        Args:
            request: The request object
            adv_id: Advertisement ID
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If advertisement not found or an error occurs
        """
        try:
            # Use the custom method to delete advertisement with all relations
            success, message = await AdvertisementModel.delete_with_relations(adv_id)
            
            if not success:
                if "not found" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=500, detail=message)

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.DELETE,
                    entity_type="advertisement",
                    entity_id=adv_id,
                    description="Advertisement and all related data deleted"
                )

            # Invalidate related caches
            invalidate_cache("admin_advertisements")
            invalidate_cache("admin_dashboard")
            invalidate_cache(f"advertisement_{adv_id}")

            # Delete from Elasticsearch if it exists
            try:
                await delete_advertisement(adv_id)
            except Exception as es_error:
                logger.warning(f"Error deleting advertisement from Elasticsearch: {str(es_error)}")

            return {
                "id": adv_id,
                "status": "deleted",
                "message": "Advertisement and all related data deleted successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting advertisement: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete advertisement: {str(e)}"
            )

    #
    # Advertisement Edit Management Methods
    #

    @staticmethod
    async def get_pending_advertisement_edits(
        request: Request,
        current_admin: CustomUserModel,
        page: int = 1,
        limit: int = 10,
        status_filter: Optional[str] = None,
    ):
        """
        Get pending advertisement edits for admin review.

        Args:
            request: The request object
            current_admin: The current admin user
            page: Page number
            limit: Items per page
            status_filter: Filter by edit status (pending, approved, rejected)

        Returns:
            Paginated list of advertisement edits

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Build the queryset
            query = AdvertisementEditModel.objects.select_related(
                "original_advertisement",
                "original_advertisement__user",
                "main_category",
                "sub_category",
                "reviewed_by"
            ).prefetch_related(
                "price",
                "address",
                "text_values",
                "boolean_values",
                "choice_attributes"
            )

            # Apply status filter
            if status_filter:
                if status_filter.lower() == "pending":
                    query = query.filter(edit_status=AdvertisementStatus.PENDING)
                elif status_filter.lower() == "approved":
                    query = query.filter(edit_status=AdvertisementStatus.APPROVED)
                elif status_filter.lower() == "rejected":
                    query = query.filter(edit_status=AdvertisementStatus.REJECTED)

            # Order by creation date (newest first)
            query = query.order_by("-created_at")

            # Use the enhanced pagination utility
            return await paginate_request(
                request=request,
                queryset=query,
                page=page,
                limit=limit,
                order_by="-created_at"
            )

        except Exception as e:
            logger.error(f"Error getting advertisement edits: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisement edits"
            )

    @staticmethod
    async def get_advertisement_edit_detail(
        request: Request,
        edit_id: int,
        current_admin: CustomUserModel,
    ):
        """
        Get detailed information about a specific advertisement edit.

        Args:
            request: The request object
            edit_id: The edit ID
            current_admin: The current admin user

        Returns:
            Detailed edit information

        Raises:
            HTTPException: If the edit is not found
        """
        try:
            # Get the edit
            edit = await AdvertisementEditModel.objects.select_related(
                "original_advertisement",
                "original_advertisement__user",
                "main_category",
                "sub_category",
                "reviewed_by"
            ).prefetch_related(
                "price",
                "address",
                "text_values",
                "boolean_values",
                "choice_attributes"
            ).filter(id=edit_id).afirst()

            if not edit:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement edit not found"
                )

            return edit

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting advertisement edit detail: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting advertisement edit detail"
            )

    @staticmethod
    async def approve_or_reject_advertisement_edit(
        request: Request,
        edit_id: int,
        action: str,
        admin_notes: Optional[str] = None,
        current_admin: CustomUserModel = None,
    ):
        """
        Approve or reject an advertisement edit.

        Args:
            request: The request object
            edit_id: The edit ID
            action: Action to take ('approve' or 'reject')
            admin_notes: Optional admin notes
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If the edit is not found or action is invalid
        """
        try:
            # Get the edit
            edit = await AdvertisementEditModel.objects.select_related(
                "original_advertisement"
            ).filter(id=edit_id).afirst()

            if not edit:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement edit not found"
                )

            # Check if edit is still pending
            if edit.edit_status != AdvertisementStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Edit has already been reviewed"
                )

            # Validate action
            if action.lower() not in ["approve", "reject"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Action must be 'approve' or 'reject'"
                )

            # Update edit record
            edit.admin_notes = admin_notes
            edit.reviewed_by = current_admin
            edit.reviewed_at = jdatetime.datetime.now()

            if action.lower() == "approve":
                # Apply the edit to the original advertisement
                await sync_to_async(edit.apply_to_original)()
                message = "Advertisement edit approved and applied successfully"
            else:
                # Reject the edit
                edit.edit_status = AdvertisementStatus.REJECTED
                await edit.asave()
                message = "Advertisement edit rejected successfully"

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.UPDATE,
                    entity_type="advertisement_edit",
                    entity_id=edit_id,
                    description=f"Advertisement edit {'approved' if action.lower() == 'approve' else 'rejected'}"
                )

            # Invalidate related caches
            invalidate_cache("admin_advertisements")
            invalidate_cache("admin_dashboard")

            return {
                "id": edit.id,
                "status": edit.edit_status,
                "message": message
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing advertisement edit approval: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing advertisement edit approval: {str(e)}"
            )

    #
    # User Management Methods
    #

    @staticmethod
    # @cache_response(timeout=60, prefix="admin_users")
    async def get_all_users(
        request: Request,
        current_admin: CustomUserModel,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
        page: int = 1,
        limit: int = 10
    ):
        """
        Get all users for admin management with filtering and pagination.

        Args:
            request: The request object
            current_admin: The current admin user
            is_active: Optional filter by active status
            search: Optional search term
            page: Page number
            limit: Items per page

        Returns:
            List of users with pagination metadata

        Raises:
            HTTPException: If an error occurs
        """
        try:
            query =await  sync_to_async(CustomUserModel.objects.all)()

            # Apply filters
            if is_active is not None:
                query = query.filter(is_active=is_active)

            if search:
                query = query.filter(
                    Q(email__icontains=search) |
                    Q(first_name__icontains=search) |
                    Q(last_name__icontains=search) |
                    Q(phone_number__icontains=search)
                )

            # Use the enhanced pagination utility
            return await paginate_request(
                request=request,
                queryset=query,
                page=page,
                limit=limit,
                order_by="-date_joined"
            )
        except Exception as e:
            logger.error(f"Error fetching users: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch users"
            )

    @staticmethod
    async def ban_user(
        request: Request,
        user_id: int,
        ban: bool,
        reason: Optional[str] = None,
        current_admin: CustomUserModel = None
    ):
        """
        Ban or unban a user.

        Args:
            request: The request object
            user_id: User ID
            ban: Whether to ban or unban
            reason: Optional reason for banning
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If user not found or an error occurs
        """
        try:
            user = await CustomUserModel.objects.filter(id=user_id).afirst()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Don't allow banning yourself
            if user.id == current_admin.id:
                raise HTTPException(status_code=400, detail="Cannot ban yourself")

            user.is_active = not ban
            await user.asave()

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.UPDATE,
                    entity_type="user",
                    entity_id=user_id,
                    description=f"User {'banned' if ban else 'unbanned'}{f': {reason}' if reason else ''}"
                )

            # Invalidate related caches
            invalidate_cache("admin_users")
            invalidate_cache("admin_dashboard")

            return {
                "id": user.id,
                "status": "banned" if ban else "active",
                "message": f"User {'banned' if ban else 'unbanned'} successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user status"
            )

    #
    # Blog Management Methods
    #

    @staticmethod
    # @cache_response(timeout=60, prefix="admin_blogs")
    async def get_all_blogs(
        request: Request,
        current_admin: CustomUserModel,
        status_filter: Optional[str] = None,
        search: Optional[str] = None,
        page: int = 1,
        limit: int = 10
    ):
        """
        Get all blogs for admin management with filtering and pagination.

        Args:
            request: The request object
            current_admin: The current admin user
            status_filter: Optional filter by status
            search: Optional search term
            page: Page number
            limit: Items per page

        Returns:
            List of blogs with pagination metadata

        Raises:
            HTTPException: If an error occurs
        """
        try:
            query = BlogModel.objects.all()

            # Apply filters
            if status_filter:
                # Use enum values for blog status
                if status_filter == BlogStatus.DRAFT:
                    query = query.filter(post_status=BlogStatus.DRAFT)
                elif status_filter == BlogStatus.PUBLISHED:
                    query = query.filter(post_status=BlogStatus.PUBLISHED)
                elif status_filter == BlogStatus.REJECTED:
                    query = query.filter(post_status=BlogStatus.REJECTED)

            if search:
                query = query.filter(
                    Q(post_title__icontains=search) |
                    Q(post_content__icontains=search)
                )

            # Use the enhanced pagination utility
            return await paginate_request(
                request=request,
                queryset=query,
                page=page,
                limit=limit,
                order_by="-created_at"
            )
        except Exception as e:
            logger.error(f"Error fetching blogs: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch blogs"
            )

    @staticmethod
    async def approve_blog(request: Request, blog_id: int, approve: bool, current_admin: CustomUserModel):
        """
        Approve or reject a blog.

        Args:
            request: The request object
            blog_id: Blog ID
            approve: Whether to approve or reject
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If blog not found or an error occurs
        """
        try:
            blog = await BlogModel.objects.filter(id=blog_id).afirst()
            if not blog:
                raise HTTPException(status_code=404, detail="Blog not found")

            # Use enum values for blog status
            blog.post_status = BlogStatus.PUBLISHED if approve else BlogStatus.REJECTED
            await blog.asave()

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.UPDATE,
                    entity_type="blog",
                    entity_id=blog_id,
                    description=f"Blog {'approved' if approve else 'rejected'}"
                )

            # Invalidate related caches
            invalidate_cache("admin_blogs")

            return {
                "id": blog.id,
                "status": blog.post_status,
                "message": f"Blog {'approved' if approve else 'rejected'} successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating blog status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update blog status"
            )

    @staticmethod
    async def delete_blog(request: Request, blog_id: int, current_admin: CustomUserModel):
        """
        Delete a blog.

        Args:
            request: The request object
            blog_id: Blog ID
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If blog not found or an error occurs
        """
        try:
            blog = await BlogModel.objects.filter(id=blog_id).afirst()
            if not blog:
                raise HTTPException(status_code=404, detail="Blog not found")

            await blog.adelete()

            # Log admin action
            admin = await SpecialAdmin.objects.filter(user=current_admin).afirst()
            if admin:
                await AdminActivity.objects.acreate(
                    admin=admin,
                    action_type=ActionType.DELETE,
                    entity_type="blog",
                    entity_id=blog_id,
                    description="Blog deleted"
                )

            # Invalidate related caches
            invalidate_cache("admin_blogs")

            return {
                "id": blog_id,
                "status": "deleted",
                "message": "Blog deleted successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting blog: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete blog"
            )

    #
    # Dashboard Methods
    #

    @staticmethod
    # @cache_response(timeout=300, prefix="admin_dashboard")  # Cache for 5 minutes
    async def get_admin_dashboard_stats(request: Request, current_admin: CustomUserModel):
        """
        Get admin dashboard statistics.

        Args:
            request: The request object
            current_admin: The current admin user

        Returns:
            Dict with dashboard statistics

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get user stats
            total_users = await CustomUserModel.objects.acount()
            active_users = await CustomUserModel.objects.filter(is_active=True).acount()

            # Get advertisement stats
            total_advertisements = await AdvertisementModel.objects.acount()
            pending_advertisements = await AdvertisementModel.objects.filter(
                status=AdvertisementStatus.PENDING
            ).acount()
            approved_advertisements = await AdvertisementModel.objects.filter(
                status=AdvertisementStatus.APPROVED
            ).acount()
            rejected_advertisements = await AdvertisementModel.objects.filter(
                status=AdvertisementStatus.REJECTED
            ).acount()

            # Get advertisement edit stats
            pending_edits = await AdvertisementEditModel.objects.filter(
                edit_status=AdvertisementStatus.PENDING
            ).acount()
            approved_edits = await AdvertisementEditModel.objects.filter(
                edit_status=AdvertisementStatus.APPROVED
            ).acount()
            rejected_edits = await AdvertisementEditModel.objects.filter(
                edit_status=AdvertisementStatus.REJECTED
            ).acount()

            # Get advertisements by category
            adv_by_category_list=[]
            async for item in AdvertisementModel.objects.values(
                                                'main_category__name'
                                                ).annotate(
                                                    count=Count('id')
                                                ).order_by('-count')[:5]:
                # Convert to list of dicts for serialization
                adv_by_category_list.append(
                    {
                        "category": item["main_category__name"] or "Uncategorized",
                        "count": item["count"]
                    }
                )

            return {
                "total_users": total_users,
                "active_users": active_users,
                "total_advertisements": total_advertisements,
                "pending_advertisements": pending_advertisements,
                "approved_advertisements": approved_advertisements,
                "rejected_advertisements": rejected_advertisements,
                "pending_advertisement_edits": pending_edits,
                "approved_advertisement_edits": approved_edits,
                "rejected_advertisement_edits": rejected_edits,
                "advertisements_by_category": adv_by_category_list
            }
        except Exception as e:
                logger.error(f"Error getting dashboard stats: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get dashboard statistics"
                )

    #
    # Special Admin Management Methods
    #

    @staticmethod
    async def get_all_special_admins(
        request: Request,
        current_admin: CustomUserModel,
        admin_type_id: Optional[int] = None,
        status: Optional[int] = None,
        page: int = 1,
        limit: int = 10,
        search: Optional[str] = None
    ):
        """
        Get all special admins with filtering and pagination.

        Args:
            request: The request object
            current_admin: The current admin user
            admin_type_id: Optional filter by admin type
            status: Optional filter by status
            page: Page number
            limit: Items per page
            search: Optional search term

        Returns:
            List of special admins with pagination metadata

        Raises:
            HTTPException: If an error occurs or user doesn't have permission
        """
        try:
            # Ensure the current user is a super admin
            if not current_admin.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only super admins can view all special admins"
                )

            query = SpecialAdmin.objects.select_related('user', 'admin_type', 'created_by')

            # Apply filters
            if admin_type_id:
                query = query.filter(admin_type_id=admin_type_id)

            if status is not None:
                query = query.filter(status=status)

            if search:
                query = query.filter(
                    Q(user__first_name__icontains=search) |
                    Q(user__last_name__icontains=search) |
                    Q(user__email__icontains=search) |
                    Q(user__phone_number__icontains=search)
                )

            # Use the enhanced pagination utility
            result = await paginate_queryset(
                queryset=query,
                page=page,
                size=limit,
                order_by="-created_at"
            )

            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching special admins: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch special admins"
            )

    @staticmethod
    async def get_special_admin_detail(request: Request, admin_id: int, current_admin: CustomUserModel):
        """
        Get detailed information about a special admin.

        Args:
            request: The request object
            admin_id: Special admin ID
            current_admin: The current admin user

        Returns:
            Special admin details

        Raises:
            HTTPException: If special admin not found or user doesn't have permission
        """
        try:
            # Get the special admin with related data
            special_admin = await SpecialAdmin.objects.filter(id=admin_id).select_related(
                'user', 'admin_type', 'created_by'
            ).prefetch_related('cities', 'provinces').afirst()

            if not special_admin:
                raise HTTPException(status_code=404, detail="Special admin not found")

            # Check permissions - super admins can view any admin, others can only view themselves
            if not current_admin.is_superuser and special_admin.user.id != current_admin.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You don't have permission to view this admin"
                )

            return special_admin
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching special admin details: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch special admin details"
            )

    @staticmethod
    async def create_special_admin(request: Request, schema: SpecialAdminCreateSchema, current_admin: CustomUserModel):
        """
        Create a new special admin.

        Args:
            request: The request object
            schema: Special admin creation schema
            current_admin: The current admin user

        Returns:
            Created special admin

        Raises:
            HTTPException: If user not found, already a special admin, or an error occurs
        """
        try:
            # Check if user exists
            user = await CustomUserModel.objects.filter(id=schema.user_id).afirst()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Check if user is already a special admin
            existing_admin = await SpecialAdmin.objects.filter(user_id=schema.user_id).afirst()
            if existing_admin:
                raise HTTPException(status_code=400, detail="User is already a special admin")

            # Check if admin type exists
            admin_type = await AdminType.objects.filter(id=schema.admin_type_id).afirst()
            if not admin_type:
                raise HTTPException(status_code=404, detail="Admin type not found")

            # Create special admin with enum for status
            special_admin = await SpecialAdmin.objects.acreate(
                user_id=schema.user_id,
                admin_type_id=schema.admin_type_id,
                can_manage_users=schema.can_manage_users,
                can_manage_advertisements=schema.can_manage_advertisements,
                can_manage_blogs=schema.can_manage_blogs,
                can_manage_agents=schema.can_manage_agents,
                status=AdminStatus.ACTIVE,  # Default to active
                created_by=current_admin
            )

            # Add cities and provinces
            if schema.city_ids:
                cities = await CityModel.objects.filter(id__in=schema.city_ids).all()
                await special_admin.cities.aset(cities)

            if schema.province_ids:
                provinces = await ProvinceModel.objects.filter(id__in=schema.province_ids).all()
                await special_admin.provinces.aset(provinces)

            # Log admin action
            await AdminActivity.objects.acreate(
                admin=await SpecialAdmin.objects.filter(user=current_admin).afirst(),
                action_type=ActionType.CREATE,
                entity_type="special_admin",
                entity_id=special_admin.id,
                description=f"Created special admin for user {user.email}"
            )

            return special_admin
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating special admin: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create special admin"
            )

    @staticmethod
    async def update_special_admin(request: Request, admin_id: int, schema: SpecialAdminUpdateSchema, current_admin: CustomUserModel):
        """
        Update a special admin.

        Args:
            request: The request object
            admin_id: Special admin ID
            schema: Special admin update schema
            current_admin: The current admin user

        Returns:
            Updated special admin

        Raises:
            HTTPException: If special admin not found or an error occurs
        """
        try:
            # Get the special admin
            special_admin = await SpecialAdmin.objects.filter(id=admin_id).prefetch_related('cities', 'provinces').afirst()
            if not special_admin:
                raise HTTPException(status_code=404, detail="Special admin not found")

            # Check if admin type exists if provided
            if schema.admin_type_id:
                admin_type = await AdminType.objects.filter(id=schema.admin_type_id).afirst()
                if not admin_type:
                    raise HTTPException(status_code=404, detail="Admin type not found")
                special_admin.admin_type_id = schema.admin_type_id

            # Update fields
            if schema.can_manage_users is not None:
                special_admin.can_manage_users = schema.can_manage_users

            if schema.can_manage_advertisements is not None:
                special_admin.can_manage_advertisements = schema.can_manage_advertisements

            if schema.can_manage_blogs is not None:
                special_admin.can_manage_blogs = schema.can_manage_blogs

            if schema.can_manage_agents is not None:
                special_admin.can_manage_agents = schema.can_manage_agents

            if schema.status is not None:
                special_admin.status = schema.status

            # Save changes
            await special_admin.asave()

            # Update cities if provided
            if schema.city_ids is not None:
                cities = await CityModel.objects.filter(id__in=schema.city_ids).all()
                await special_admin.cities.aset(cities)

            # Update provinces if provided
            if schema.province_ids is not None:
                provinces = await ProvinceModel.objects.filter(id__in=schema.province_ids).all()
                await special_admin.provinces.aset(provinces)

            # Log admin action
            await AdminActivity.objects.acreate(
                admin=await SpecialAdmin.objects.filter(user=current_admin).afirst(),
                action_type=ActionType.UPDATE,
                entity_type="special_admin",
                entity_id=special_admin.id,
                description=f"Updated special admin {special_admin.id}"
            )

            return special_admin
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating special admin: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update special admin"
            )

    @staticmethod
    async def delete_special_admin(request: Request, admin_id: int, current_admin: CustomUserModel):
        """
        Delete a special admin.

        Args:
            request: The request object
            admin_id: Special admin ID
            current_admin: The current admin user

        Returns:
            Dict with status information

        Raises:
            HTTPException: If special admin not found or an error occurs
        """
        try:
            # Get the special admin
            special_admin = await SpecialAdmin.objects.filter(id=admin_id).afirst()
            if not special_admin:
                raise HTTPException(status_code=404, detail="Special admin not found")

            # Store user info for logging
            user_id = special_admin.user_id
            user_email = (await CustomUserModel.objects.filter(id=user_id).afirst()).email

            # Delete the special admin
            await special_admin.adelete()

            # Log admin action
            await AdminActivity.objects.acreate(
                admin=await SpecialAdmin.objects.filter(user=current_admin).afirst(),
                action_type=ActionType.DELETE,
                entity_type="special_admin",
                entity_id=admin_id,
                description=f"Deleted special admin for user {user_email}"
            )

            return {
                "id": admin_id,
                "status": "deleted",
                "message": "Special admin deleted successfully"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting special admin: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete special admin"
            )

    #
    # Admin Type Methods
    #

    @staticmethod
    async def get_admin_types(request: Request, current_admin: CustomUserModel):
        """
        Get all admin types.

        Args:
            request: The request object
            current_admin: The current admin user

        Returns:
            List of admin types

        Raises:
            HTTPException: If an error occurs
        """
        try:
            admin_types = await AdminType.objects.all()
            return admin_types
        except Exception as e:
            logger.error(f"Error fetching admin types: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch admin types"
            )

    @staticmethod
    async def create_admin_type(request: Request, schema: AdminTypeSchema, current_admin: CustomUserModel):
        """
        Create a new admin type.

        Args:
            request: The request object
            schema: Admin type schema
            current_admin: The current admin user

        Returns:
            Created admin type

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check if admin type with same name already exists
            existing = await AdminType.objects.filter(name=schema.name).afirst()
            if existing:
                raise HTTPException(status_code=400, detail="Admin type with this name already exists")

            # Create admin type
            admin_type = await AdminType.objects.acreate(
                name=schema.name,
                description=schema.description
            )

            # Log admin action
            await AdminActivity.objects.acreate(
                admin=await SpecialAdmin.objects.filter(user=current_admin).afirst(),
                action_type=ActionType.CREATE,
                entity_type="admin_type",
                entity_id=admin_type.id,
                description=f"Created admin type {schema.name}"
            )

            return admin_type
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating admin type: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create admin type"
            )
