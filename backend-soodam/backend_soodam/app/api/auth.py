import string
from logging import get<PERSON>og<PERSON>
from typing import Any
import random
import re

from fastapi import Request, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse

from ManagementPanelSMSCall.views import ManagementPanelKavenegar
from app.schemas import PhoneNumberRegisteredSchema, VerifyCodeSchema, LogAccessCreateSchema, LogAccessEditSchema, \
    CallSendVerifyCode, LogAccessSchema
from app.models import CustomUserModel, LogAccess
from asgiref.sync import sync_to_async
from config.exceptions import (
    InvalidCredentialsException,
    InvalidEmailOrPasswordException,
)
from config.jwt import create_access_token_response

logger = getLogger(__name__)


class AuthAPI:
    @classmethod
    async def see_ver_code(cls, request: Request, schema: PhoneNumberRegisteredSchema) -> Any:
        try:
            log_access_object = await LogAccess.objects.filter(phone_number=schema.phone_number).afirst()
            if not log_access_object:
                raise InvalidCredentialsException()
            return JSONResponse({"code": log_access_object.verify_code})
        except LogAccess.DoesNotExist:
            raise HTTPException(status_code=500,detail="Dose not exist")
    @classmethod
    async def get_ver_code(cls, request: Request, schema: PhoneNumberRegisteredSchema) -> Any:
        try:
            ip_address = request.get('REMOTE_ADDR')
            response = {'status': 'failed ',
                        'message': ''}
            check_phone_number = re.search(r"09([0-9][0-9])-?[0-9]{3}-?[0-9]{4}", schema.phone_number)
            verify_code = ''.join(random.choice(string.digits) for _ in range(6))
            # print(singleton_object().send_otp())
            # send_message = Send_Message(phone_number_request, verify_code)
            # send_result = send_message.start()
            # send_message = send_sms(phone_number_request, verify_code)
            if check_phone_number is not None:
                sms_manager = ManagementPanelKavenegar()
                sms_response = await sms_manager.send_otp(schema.phone_number, verify_code)
                logger.info(f"sms_response: {sms_response}")
                user_object = await CustomUserModel.objects.aupdate_or_create(phone_number=schema.phone_number)
                print(user_object)
                token = create_access_token_response(
                    {"sub": str(user_object[0].uuid)})['token']
                log_access_update_schema = LogAccessSchema(
                    user=user_object[0],
                    phone_number=schema.phone_number,
                    user_token=token.__str__(),
                    verify_code=verify_code.__str__(),
                    ip_address=ip_address.__str__(),
                    try_to_verify=0)
                if user_object[1]:
                    await LogAccess.objects.acreate(**log_access_update_schema.dict())
                else:
                    del log_access_update_schema.user
                    await LogAccess.objects.filter(user=user_object[0]).aupdate(**log_access_update_schema.dict())
                return JSONResponse({"status": 200, "detail": "success"})
        except Exception as e:
            logger.error("get_ver_code : "+e.__str__())
            raise HTTPException(status_code=400,
                                detail="شماره وارد شده نادرست میباشد لطفا شماره مورد نظر را بررسی کنید و دوباره تلاش کنید ")

    @classmethod
    async def verify(cls, request: Request, schema: VerifyCodeSchema) -> Any:
        try:
            ip_address = request.get('REMOTE_ADDR')
            check_phone_number = re.search(r"09([0-9][0-9])-?[0-9]{3}-?[0-9]{4}", schema.phone_number)
            log_access_check_try = await LogAccess.objects.filter(phone_number=schema.phone_number).afirst()
            if check_phone_number:
                if log_access_check_try.try_to_verify < 4:
                    log_access_object = await LogAccess.objects.filter(phone_number=schema.phone_number,
                                                                       verify_code=schema.verify_code).afirst()
                    if log_access_object:
                        response = {
                            'status': 200,
                            'token': log_access_object.user_token,
                            "token_type": "bearer"}
                        return JSONResponse(response)
                    else:
                        await LogAccess.objects.filter(phone_number=schema.phone_number).aupdate(**{
                            "try_to_verify": log_access_check_try.try_to_verify + 1
                        })
                        remain_try_to_verify = 3 - log_access_check_try.try_to_verify
                        raise HTTPException(status_code=400,
                                            detail="کد وارد شده معتبر نمی باشد شما {} تلاش دیگر دارید.".format(
                                                 remain_try_to_verify if remain_try_to_verify > 0 else 0))
                else:
                    raise HTTPException(status_code=400,detail="تعداد تلاش های شما از حد مجاز عبور کرده است در صورت دریافت نکردن کد از روش "
                                           "تماس استفاده کنید یا شماره مورد نظر را بررسی کنید و دوباره تلاش کنید ")
            else:
                raise HTTPException(status_code=400,
                                    detail="شماره وارد شده نادرست میباشد لطفا شماره مورد نظر را بررسی کنید و دوباره تلاش کنید ")
        except:
            logger.debug("verify_api : ")
            raise
            # raise HTTPException(status_code=400,detail="Internal Server Error")

    @classmethod
    async def call_send_verify_code(cls, request: Request, schema: CallSendVerifyCode) -> Any:
        ip_address = request.get('REMOTE_ADDR')
        response = {}
        check_phone_number = re.search(r"09([0-9][0-9])-?[0-9]{3}-?[0-9]{4}", schema.phone_number)
        log_access_check_try = await LogAccess.objects.filter(phone_number=schema.phone_number).afirst()
        if log_access_check_try.try_to_call < 4:
            if check_phone_number is not None:
                log_access_object = await LogAccess.objects.filter(phone_number=schema.phone_number).afirst()
                if log_access_object:
                    handler_request = HandlerRequest(schema.phone_number, log_access_object.verify_code.__str__())
                    # send_call(phone_number_request, obj[0].__getitem__('verify_code'))
                    return JSONResponse(
                        {"status": 200, "detail": "success", "call_value": handler_request.call_verify_soodam()})
                else:
                    raise HTTPException(status_code=400,
                                        detail="لطفا ابتدا از صفحه ورود اقدام کنید")
            else:
                raise HTTPException(status_code=400,
                                    detail="لطفا شماره مورد نظر رو بررسی کنید")
        else:
            raise HTTPException(status_code=400,
                                detail="لطفا چند ساعت دیگر تلاش کنید")

    @classmethod
    async def login(
            cls, request: Request, form_data: OAuth2PasswordRequestForm
    ) -> Any:
        credentials = {"login_field": form_data.username, "password": form_data.password}
        if all(credentials.values()):
            # Try to authenticate with the provided field as email, username, or phone_number
            user = await cls()._authenticate_user(**credentials)
        else:
            raise InvalidCredentialsException()
        return create_access_token_response(
                {"sub": str(user.uuid)})
            # {"sub": str(user.uuid)})
        # {"sub": str(user.uuid), "is_active": user.is_active, "user_group": user.user_group"is_active": user.is_active, "user_group": user.user_group})


    async def _authenticate_user(self, login_field: str, password: str) -> CustomUserModel:
        """
        Enhanced authentication with dual username support.

        Args:
            login_field: Email address or phone number
            password: User password

        Returns:
            CustomUserModel instance if authentication successful

        Raises:
            InvalidEmailOrPasswordException: If authentication fails

        """
        from app.dependencies.authentication import AuthenticationHelper
        # Use the enhanced authentication helper
        validation_result = await sync_to_async(AuthenticationHelper.validate_credentials)(
            login_field, password
        )

        if validation_result['valid'] and validation_result['user']:
            user = validation_result['user']
            logger.info(f"Successful authentication for user: {user.get_username_display()}")
            return user
        else:
            error_msg = validation_result.get('error', 'Invalid credentials')
            logger.warning(f"Failed authentication attempt for: {login_field} - {error_msg}")
            raise InvalidEmailOrPasswordException()