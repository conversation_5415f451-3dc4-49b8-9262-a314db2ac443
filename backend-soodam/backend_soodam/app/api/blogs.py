import os
from asgiref.sync import sync_to_async
import datetime

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, UploadFile, Form, File
from django.conf import settings as django_settings
from fastapi.responses import JSONResponse

from ..models import CustomUserModel, BlogModel, BlogPictureModel, BlogFileModel  # , NotificationModel

from ..schemas import CreateBlogSchema, DeleteBlogSchema, EditBlogSchema


class BlogsAPI:
    @classmethod
    async def get_blogs(cls, request: Request, current_user: CustomUserModel) -> list[BlogModel]:
        admin_object = await CustomUserModel.objects.aget(email=current_user.email)
        blogs = []
        # if admin_object.user_group >= 3:
        async for blog in (BlogModel.objects.filter(
                created_at__gte=datetime.datetime.today()
                        .replace(
                    day=1,
                    hour=0,
                    minute=0,
                    second=0,
                    microsecond=0),
        )
                .all().select_related('post_author')
                .order_by('-created_at')):  # .exclude(Q(quantity=0):
            blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blogs.append({
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_author": blog.post_author.first_name,
                "post_image": str(blogPicture.image),
                "blog_file": str(blogFile.blog_file) if blogFile else '',
                "created_at": blog.created_at,
            })
        return blogs

    @classmethod
    async def blog_by_id(cls, request: Request, blog_id: str | None, current_user: CustomUserModel) -> BlogModel:
        admin_object = await CustomUserModel.objects.aget(email=current_user.email)
        # if admin_object.user_group >= 3:
        async for blog in BlogModel.objects.filter(id=int(blog_id)).all().select_related(
                'post_author'):  # .exclude(Q(quantity=0):
            post_image = []
            blog_file = ''
            async for blogPicture in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                post_image.append(str(blogPicture.image))
            # blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            return {
                "id": blog.id,
                "post_title": blog.post_title,
                # "post_start_publication":  blog.post_start_publication,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_image": post_image,
                "blog_file": str(blogFile.blog_file) if blogFile else '',
                "post_status": True,
                "post_author": blog.post_author.first_name,
                "created_at": blog.created_at,
                # "count_like": "",
                # "count_dislike": "",
                # "comments":[]
            }

    @classmethod
    async def insert_blog(cls, request: Request, schema: CreateBlogSchema, current_user: CustomUserModel) -> list[
        BlogModel]:
        is_admin = await CustomUserModel.objects.values().filter(email=current_user.email).afirst()
        blogs = []
        blog = await BlogModel.objects.filter(post_title=schema.post_title).afirst()
        if blog:
            raise HTTPException(status_code=400, detail="blog already inserted")
        post_image = schema.post_image
        blog_file = schema.blog_file
        del schema.post_image
        del schema.blog_file
        schema.post_author = await CustomUserModel.objects.aget(email=current_user.email)
        await sync_to_async(BlogModel.objects.create)(**schema.dict())
        await sync_to_async(BlogPictureModel.objects.create)(**{
            "blog": await BlogModel.objects.aget(post_title=schema.post_title),
            "image": post_image,
        })
        await sync_to_async(BlogFileModel.objects.create)(**{
            "blog": await BlogModel.objects.aget(post_title=schema.post_title),
            "blog_file": blog_file,
        })
        async for blog in BlogModel.objects.filter(created_at__gte=datetime.datetime.today()
                .replace(
            day=1,
            hour=0,
            minute=0,
            second=0,
            microsecond=0),
        ).all().select_related('post_author').order_by('-created_at'):  # .exclude(Q(quantity=0):
            blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blogs.append({
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_author": blog.post_author.first_name,
                "post_image": str(blogPicture.image),
                "blog_file": str(blogFile.blog_file) if blogFile else '',
                "created_at": blog.created_at,
            })
        return blogs

    @classmethod
    async def upload_blog_picture(cls, request: Request, file: UploadFile, post_title: str,
                                  current_user: CustomUserModel):
        # print( {"file_sizes": [len(file.file.read()) for file in files]})
        # return
        ext = os.path.splitext(file.filename)[1].lower()
        valid_extensions = ['.png', '.jpg', '.jpeg']
        pass_name = ''
        if not post_title:
            pass_name = rand(1, 100)
        if ext in valid_extensions:
            filename = django_settings.MEDIA_ROOT + '/documents/blogs/' + post_title + '-' + pass_name + '_tmp.jpg'
            # print(filename)
            with open(filename, 'wb+') as destination:
                destination.write(file.file.read())
                destination.close()
                # destination.write(chunk)
            # CustomUser.objects.values().filter(phone_number=phone_number_request,
            #                                    ip_address=ip_address).update(
            #     picture_profile=filename)
        else:
            raise HTTPException(status_code=404, detail="you'r file format is incorrect")
        return JSONResponse(content={"filename": filename, "detail": "success"})

    @classmethod
    async def upload_blog_file(cls, request: Request, file: UploadFile, post_title: str, current_user: CustomUserModel):
        ext = os.path.splitext(file.filename)[1].lower()
        valid_extensions = ['.pdf', '.docx', ]
        print(file)
        pass_name = ''
        if not post_title:
            pass_name = rand(1, 100)
        if ext in valid_extensions:
            print(ext)
            filename = django_settings.MEDIA_ROOT + '/documents/blogs/doc/' + post_title + '-' + pass_name + '_tmp' + ext
            print(filename)
            with open(filename, 'wb+') as destination:
                destination.write(file.file.read())
                destination.close()
                # destination.write(chunk)
            # CustomUser.objects.values().filter(phone_number=phone_number_request,
            #                                    ip_address=ip_address).update(
            #     picture_profile=filename)
        else:
            raise HTTPException(status_code=404, detail="you'r file format is incorrect")
        return JSONResponse(content={"filename": filename, "detail": "success"})

    @classmethod
    async def edit_blog(cls, request: Request, schema: EditBlogSchema, current_user: CustomUserModel) -> list[
        BlogModel]:
        is_admin = await CustomUserModel.objects.values().filter(email=current_user.email).afirst()
        blog = await BlogModel.objects.filter(pk=int(schema.id)).afirst()
        if blog:
            blogs = []
            post_image = schema.post_image
            del schema.post_image
            schema.post_author = await CustomUserModel.objects.aget(email=current_user.email)
            # async for pic in BlogPictureModel.objects.values('image').filter(blog=blog).all():
            #     print(pic)
            # for image in post_image:
            #     if image==blog_picture.image:
            await BlogModel.objects.aupdate(**schema.dict())
            # post_image = schema.post_image
            # blog_file = schema.blog_file
            # await sync_to_async(BlogPictureModel.objects.create)(**{
            #     "blog": await BlogModel.objects.aget(post_title=schema.post_title),
            #     "image": post_image,
            #     "blog_file": blog_file
            # })
            # await sync_to_async(BlogPictureModel.objects.create)(**{
            #     "blog": await BlogModel.objects.aget(post_title=schema.id),
            #     "image": post_image
            # })
            async for blog in BlogModel.objects.filter(created_at__gte=datetime.datetime.today()
                    .replace(
                day=1,
                hour=0,
                minute=0,
                second=0,
                microsecond=0),
            ).all().select_related('post_author').order_by(
                '-created_at'):
                blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
                blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
                blogs.append({
                    "id": blog.id,
                    "post_title": blog.post_title,
                    "post_description": blog.post_description,
                    "post_content": blog.post_content,
                    "post_author": blog.post_author.first_name,
                    "post_image": str(blogPicture.image),
                    "blog_file": str(blogFile.blog_file),
                    "created_at": blog.created_at,
                })
            return blogs

        else:
            raise HTTPException(status_code=404, detail="Blog dose not exist !!!!")

    @classmethod
    async def delete_blog(cls, request: Request, schema: DeleteBlogSchema, current_user: CustomUserModel) -> list[
        BlogModel]:
        blog = await BlogModel.objects.filter(pk=int(schema.id)).afirst()
        if blog:
            await BlogModel.objects.filter(pk=int(schema.id)).adelete()
            blogs = []
            async for blog in BlogModel.objects.filter(created_at__gte=datetime.datetime.today()
                    .replace(
                day=1,
                hour=0,
                minute=0,
                second=0,
                microsecond=0),
            ).all().select_related('post_author').order_by(
                '-created_at'):  # .exclude(Q(quantity=0):
                blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
                blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
                blogs.append({
                    "id": blog.id,
                    "post_title": blog.post_title,
                    "post_description": blog.post_description,
                    "post_content": blog.post_content,
                    "post_author": blog.post_author.first_name,
                    "post_image": str(blogPicture.image),
                    "blog_file": str(blogFile.blog_file),
                    "created_at": blog.created_at,
                })
            return blogs
        else:
            raise HTTPException(status_code=404, detail="Blog dose not exist !!!!")
