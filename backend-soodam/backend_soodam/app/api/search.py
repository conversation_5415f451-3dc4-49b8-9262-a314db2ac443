"""
Search API for the Soodam backend.

This module provides API functions for search operations.
"""

import logging
from typing import Dict, List, Optional

from fastapi import HTT<PERSON>Exception, Request, status

from ..core.elasticsearch import search_advertisements
from ..models import CustomUserModel
from ..services.elasticsearch_sync import ElasticsearchSyncService

logger = logging.getLogger(__name__)


class SearchAPI:
    """Search API for the Soodam backend."""

    @staticmethod
    async def search_advertisements(
        request: Request,
        query: str,
        category_id: Optional[int] = None,
        location_id: Optional[int] = None,
        price_min: Optional[float] = None,
        price_max: Optional[float] = None,
        is_featured: Optional[bool] = None,
        status: Optional[str] = None,
        sort_by: str = 'created_at',
        sort_order: str = 'desc',
        page: int = 1,
        limit: int = 10,
        facets: bool = True,
    ) -> Dict:
        """
        Search for advertisements.

        Args:
            request: The request
            query: The search query
            category_id: Filter by category ID
            location_id: Filter by location ID
            price_min: Filter by minimum price
            price_max: Filter by maximum price
            is_featured: Filter by featured status
            status: Filter by status
            sort_by: Sort field
            sort_order: Sort order
            page: Page number
            limit: Page size

        Returns:
            Dict: The search results

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Search for advertisements
            results = await search_advertisements(
                query=query,
                category_id=category_id,
                location_id=location_id,
                price_min=price_min,
                price_max=price_max,
                is_featured=is_featured,
                status=status,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                limit=limit,
                facets=facets
            )

            return results
        except Exception as e:
            logger.error(f"Error searching advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching advertisements: {str(e)}"
            )

    @staticmethod
    async def sync_advertisement(
        request: Request,
        advertisement_id: int,
    ) -> Dict:
        """
        Sync an advertisement with Elasticsearch.

        Args:
            request: The request
            advertisement_id: The advertisement ID

        Returns:
            Dict: The sync result

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Sync the advertisement
            result = await ElasticsearchSyncService.sync_advertisement(advertisement_id)

            return result
        except Exception as e:
            logger.error(f"Error syncing advertisement {advertisement_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error syncing advertisement: {str(e)}"
            )

    @staticmethod
    async def sync_all_advertisements(
        request: Request,
    ) -> Dict:
        """
        Sync all advertisements with Elasticsearch.

        Args:
            request: The request

        Returns:
            Dict: The sync result

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Sync all advertisements
            result = await ElasticsearchSyncService.sync_all_advertisements()

            return result
        except Exception as e:
            logger.error(f"Error syncing all advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error syncing all advertisements: {str(e)}"
            )

    @staticmethod
    async def sync_user(
        request: Request,
        user_id: int,
    ) -> Dict:
        """
        Sync a user with Elasticsearch.

        Args:
            request: The request
            user_id: The user ID

        Returns:
            Dict: The sync result

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Sync the user
            result = await ElasticsearchSyncService.sync_user(user_id)

            return result
        except Exception as e:
            logger.error(f"Error syncing user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error syncing user: {str(e)}"
            )

    @staticmethod
    async def sync_blog(
        request: Request,
        blog_id: int,
    ) -> Dict:
        """
        Sync a blog with Elasticsearch.

        Args:
            request: The request
            blog_id: The blog ID

        Returns:
            Dict: The sync result

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Sync the blog
            result = await ElasticsearchSyncService.sync_blog(blog_id)

            return result
        except Exception as e:
            logger.error(f"Error syncing blog {blog_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error syncing blog: {str(e)}"
            )
