from pyexpat import features
from typing import Any
from fastapi import Request, HTTPException
from asgiref.sync import sync_to_async

from ..models import CustomUserModel, ProvinceModel, CityModel


class GisGeolocationAPI:
    @classmethod
    async def get_province(cls, request: Request) -> Any:
        provinces = []
        async for province in ProvinceModel.objects.all():
            provinces.append({
                "id": province.id,
                "name": province.name,
            })
        return provinces

    @classmethod
    async def get_cites_by_province_id(cls, request: Request, province_id: int) -> Any:
        cities = []
        try:
            async for city in CityModel.objects.filter(province_id=province_id).all():
                cities.append({
                    "id": city.id,
                    "name": city.name,
                    "province_id": city.province_id,
                })
        except CityModel.DoesNotExist:
            import logging
            logger=logging.getLogger(__name__)
            logger.info(f"{province_id} does not exist")
            raise HTTPException(status_code=404, detail="City not found")
        return cities
