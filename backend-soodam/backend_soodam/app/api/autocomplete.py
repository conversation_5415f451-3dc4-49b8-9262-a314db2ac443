"""
Autocomplete API for the Soodam backend.

This module provides API functions for autocomplete operations.
"""

import logging
from typing import Dict, List, Optional

from fastapi import HTTPException, Request, status

from ..core.autocomplete import (
    extract_keywords_from_text,
    get_suggestions,
    index_popular_searches,
    index_suggestion,
)
from ..models import CustomUserModel

logger = logging.getLogger(__name__)


class AutocompleteAPI:
    """Autocomplete API for the Soodam backend."""
    
    @staticmethod
    async def get_suggestions(
        request: Request,
        query: str,
        types: Optional[List[str]] = None,
        limit: int = 10,
    ) -> List[Dict]:
        """
        Get autocomplete suggestions.
        
        Args:
            request: The request
            query: The query to get suggestions for
            types: The suggestion types to include
            limit: The maximum number of suggestions to return
            
        Returns:
            List[Dict]: The suggestions
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get suggestions
            suggestions = await get_suggestions(query, types, limit)
            
            return suggestions
        except Exception as e:
            logger.error(f"Error getting suggestions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting suggestions: {str(e)}"
            )
    
    @staticmethod
    async def index_suggestion(
        request: Request,
        text: str,
        type: str,
        weight: int = 1,
        metadata: Optional[Dict] = None,
        user: Optional[CustomUserModel] = None,
    ) -> Dict:
        """
        Index a suggestion.
        
        Args:
            request: The request
            text: The suggestion text
            type: The suggestion type
            weight: The suggestion weight
            metadata: Additional metadata for the suggestion
            user: The user (optional)
            
        Returns:
            Dict: The indexing result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check if the user is authorized
            if user and not user.is_staff:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to index suggestions"
                )
            
            # Index the suggestion
            result = await index_suggestion(text, type, weight, metadata)
            
            return {
                'success': True,
                'text': text,
                'type': type,
                'weight': weight,
                'result': result
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error indexing suggestion: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error indexing suggestion: {str(e)}"
            )
    
    @staticmethod
    async def index_popular_searches(
        request: Request,
        searches: List[Dict],
        user: CustomUserModel,
    ) -> Dict:
        """
        Index popular searches.
        
        Args:
            request: The request
            searches: List of popular searches with text, count, and metadata
            user: The user
            
        Returns:
            Dict: The indexing results
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check if the user is authorized
            if not user.is_staff:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to index popular searches"
                )
            
            # Index the popular searches
            result = await index_popular_searches(searches)
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error indexing popular searches: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error indexing popular searches: {str(e)}"
            )
    
    @staticmethod
    async def extract_keywords(
        request: Request,
        text: str,
    ) -> List[str]:
        """
        Extract keywords from text.
        
        Args:
            request: The request
            text: The text to extract keywords from
            
        Returns:
            List[str]: The extracted keywords
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Extract keywords
            keywords = await extract_keywords_from_text(text)
            
            return keywords
        except Exception as e:
            logger.error(f"Error extracting keywords: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error extracting keywords: {str(e)}"
            )
