import os
import uuid
import logging
from typing import Any, Dict
import io
import re

from asgiref.sync import sync_to_async

from ..models import (
    CustomUserModel, UserPayment, UserWallet, SubScriptionModel,
    ProvinceModel, CityModel, UserRating, CustomUserLocationModel
)
from fastapi import HTTPEx<PERSON>, UploadFile, status
from django.conf import settings as django_settings
from django.db import transaction, IntegrityError
from django.core.exceptions import ValidationError

# Import PIL with fallback
try:
    from PIL import Image
except ImportError:
    Image = None
from app.schemas.gis_geolocation import (
    CustomUserLocationCreateSchema as CreateAddressSchema,
    CustomUserLocationEditSchema as EditAddressSchema,
)
from app.schemas.users.v1 import (
    EditUserInfoSchema
)

logger = logging.getLogger(__name__)

# Legacy API classes for backward compatibility



class UserProfileAPI:
    """Enhanced API for user profile management with comprehensive features."""

    @classmethod
    async def get_user_profile(cls, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Get comprehensive user profile information.

        Args:
            current_user: The authenticated user

        Returns:
            Dict containing user profile data

        Raises:
            HTTPException: If user not found or database error
        """
        try:
            # Use select_related and prefetch_related for optimal queries
            user_object = await CustomUserModel.objects.select_related(
                "province", "city"
            ).prefetch_related("address").aget(id=current_user.id)

            # Get related data efficiently
            user_addresses = [addr async for addr in user_object.address.all()]
            user_wallet = await UserWallet.objects.filter(user=user_object.id).afirst()
            subscription = await SubScriptionModel.objects.filter(user=user_object.id).afirst()
            user_rating = await UserRating.objects.filter(user=user_object.id).afirst()

            # Format birthday safely
            birthday_str = None
            if user_object.birthday:
                try:
                    birthday_str = user_object.birthday.strftime("%Y-%m-%d")
                except (AttributeError, ValueError):
                    logger.warning(f"Invalid birthday format for user {user_object.id}")

            # Build comprehensive user profile
            profile_data = {
                "id": user_object.id,
                "uuid": str(user_object.uuid) if hasattr(user_object, 'uuid') else None,
                "first_name": user_object.first_name or "",
                "last_name": user_object.last_name or "",
                "full_name": user_object.get_full_name(),
                "father_name": user_object.father_name or "",
                "security_number": user_object.security_number or "",
                "email": user_object.email or "",
                "phone_number": user_object.phone_number or "",
                "is_verified": user_object.is_verified,
                "is_active": user_object.is_active,
                "user_type": user_object.user_type,
                "user_group": user_object.user_group,
                "birthday": birthday_str,
                "gender": user_object.gender,
                "country_code": user_object.country_code or "",

                # Location information
                "province": {
                    "id": user_object.province.id,
                    "name": user_object.province.name,
                    "slug": user_object.province.slug
                } if user_object.province else None,

                "city": {
                    "id": user_object.city.id,
                    "name": user_object.city.name,
                    "slug": user_object.city.slug
                } if user_object.city else None,

                # Addresses
                "addresses": [
                    {
                        "id": addr.id,
                        "address": addr.address if hasattr(addr, 'address') else "",
                        "zip_code": addr.zip_code if hasattr(addr, 'zip_code') else "",
                        "latitude": float(addr.latitude) if hasattr(addr, 'latitude') and addr.latitude else None,
                        "longitude": float(addr.longitude) if hasattr(addr, 'longitude') and addr.longitude else None,
                    } for addr in user_addresses
                ],

                # Avatar
                "avatar": {
                    "url": user_object.avatar.url if user_object.avatar else None,
                    "path": str(user_object.avatar) if user_object.avatar else None
                },

                # Wallet information
                "wallet": {
                    "balance": float(user_wallet.amount) if user_wallet and user_wallet.amount else 0.0,
                    "currency": "IRR",
                    "last_updated": user_wallet.modified_at.isoformat() if user_wallet and hasattr(user_wallet, 'modified_at') else None
                },

                # Subscription information
                "subscription": {
                    "id": subscription.id,
                    "type": subscription.subscription_type,
                    "plan_count": subscription.subscription_plan_count,
                    "plan_month": subscription.subscription_plan_month,
                    "total_views": subscription.total_views,
                    "adv_views": subscription.adv_views,
                    "expire_at": subscription.expire_at_time.isoformat() if subscription.expire_at_time else None,
                    "is_active": subscription.active_plan,
                    "status": subscription.status
                } if subscription else None,

                # Rating information
                "rating": {
                    "positive": float(user_rating.positive_rating) if user_rating else 0.0,
                    "negative": float(user_rating.negative_rating) if user_rating else 0.0,
                    "total": user_rating.get_rating() if user_rating else 0.0
                },

                # Timestamps
                "date_joined": user_object.date_joined.isoformat() if user_object.date_joined else None,
                "last_login": user_object.last_login.isoformat() if user_object.last_login else None,
                "created_at": user_object.created_at.isoformat() if hasattr(user_object, 'created_at') and user_object.created_at else None,
                "updated_at": user_object.updated_at.isoformat() if hasattr(user_object, 'updated_at') and user_object.updated_at else None
            }

            return profile_data

        except CustomUserModel.DoesNotExist:
            logger.error(f"User with ID {current_user.id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="کاربر یافت نشد"
            )
        except Exception as e:
            logger.error(f"Error retrieving user profile for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در دریافت اطلاعات کاربر"
            )

    @classmethod
    async def update_user_profile(cls, schema: EditUserInfoSchema, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Update user profile information with comprehensive validation.

        Args:
            schema: User update data
            current_user: The authenticated user

        Returns:
            Dict containing success message and updated data

        Raises:
            HTTPException: If validation fails or database error
        """
        try:
            # Verify user ownership
            if schema.id != current_user.id:
                logger.warning(f"User {current_user.id} attempted to modify user {schema.id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="شما نمی‌توانید اطلاعات حساب شخص دیگری را تغییر دهید"
                )
            from django.db import transaction
            # Use database transaction for data consistency
            # Get user object
            user_object = await CustomUserModel.objects.select_related("province", "city").aget(
                id=current_user.id
            )

            # Update address if provided
            address_data=None
            if schema.full_address:
                # Verify province and city exist
                try:
                    province = await ProvinceModel.objects.aget(id=schema.full_address.province_id)
                    city = await CityModel.objects.aget(id=schema.full_address.city_id)

                    # Verify city belongs to province
                    if city.province_id != province.id:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="شهر انتخاب شده متعلق به استان انتخاب شده نیست"
                        )
                except (ProvinceModel.DoesNotExist, CityModel.DoesNotExist):
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="استان یا شهر یافت نشد"
                    )

                # Create or update address
                address_data = {
                    "province_id": schema.full_address.province_id,
                    "city_id": schema.full_address.city_id,
                    "address": schema.full_address.address,
                    "zip_code": schema.full_address.zip_code,
                    "latitude": schema.full_address.latitude,
                    "longitude": schema.full_address.longitude
                }

                if schema.full_address.id:
                    # Update existing address
                    address = await CustomUserLocationModel.objects.filter(
                        id=schema.full_address.id
                    ).afirst()
                    if not address:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail="آدرس یافت نشد"
                        )
                    # Update address
                    await CustomUserLocationModel.objects.filter(id=address.id).aupdate(**address_data)
                else:
                    # Create new address
                    address = await CustomUserLocationModel.objects.acreate(**address_data)
                    # Add to user's addresses
                    await user_object.address.aadd(address)

            # Prepare user update data
            user_update_data = {
                "first_name": schema.first_name if schema.first_name else None,
                "last_name": schema.last_name if schema.last_name else None,
                "father_name": schema.father_name if schema.father_name else None,
                "security_number": schema.security_number if schema.security_number else None,
                "email": schema.email if schema.email else None,
                "province_id": schema.province_id if schema.province_id else None,
                "city_id": schema.city_id if schema.city_id else None,
                "birthday": schema.birthday if schema.birthday else None,
                "gender": schema.gender if schema.gender else None,
                "avatar": schema.avatar if schema.avatar else None,
            }

            # Remove None values
            user_update_data = {k: v for k, v in user_update_data.items() if v is not None}

            # Validate security number if provided
            if "security_number" in user_update_data and schema.security_number:
                    if not re.match(r"^\d{10}$", str(user_update_data["security_number"])):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="فرمت کد ملی نامعتبر است"
                        )

            # Update user information
            # await CustomUserModel.objects.filter(id=current_user.id).aupdate(**user_update_data)
            # بسته‌بندی atomic در تابع sync
            await cls._update_user_transactional(current_user.id, schema, address_data, user_update_data)

            # Get updated user data
            updated_user = await CustomUserModel.objects.select_related(
                "province", "city"
            ).prefetch_related("address").aget(id=current_user.id)

            # Get updated addresses
            addresses = [addr async for addr in updated_user.address.select_related('province','city').all()]

            logger.info(f"User profile updated successfully for user {current_user.id}")

            return {
                "status": "success",
                "message": "اطلاعات کاربر با موفقیت به‌روزرسانی شد",
                "updated_fields": list(user_update_data.keys()),
                "user": {
                    "id": updated_user.id,
                    "first_name": updated_user.first_name,
                    "last_name": updated_user.last_name,
                    "email": updated_user.email,
                    "phone_number": updated_user.phone_number,
                    "province": {
                        "id":updated_user.province.id,
                        "name":updated_user.province.name,
                    } if updated_user.province else None,
                    "city":{
                        "id": updated_user.city.id,
                        "name": updated_user.city.name,
                    } if updated_user.city else None,
                    "addresses": [
                        {
                            "id": addr.id,
                            "province": addr.province.name if addr.province else None,
                            "city": addr.city.name if addr.city else None,
                            "address": addr.address,
                            "zip_code": addr.zip_code,
                            "latitude": float(addr.latitude) if addr.latitude else None,
                            "longitude": float(addr.longitude) if addr.longitude else None
                        } for addr in addresses
                    ]
                }
            }

        except ValidationError as e:
            logger.error(f"Validation error updating user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except Exception as e:
            raise
        except IntegrityError as e:
            logger.error(f"Integrity error updating user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="خطا در به‌روزرسانی اطلاعات - داده‌های تکراری"
            )
        except Exception as e:
            logger.error(f"Error updating user profile for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در به‌روزرسانی اطلاعات کاربر"
            )

    @staticmethod
    @sync_to_async
    def _update_user_transactional(user_id: int, schema, address_data, user_update_data):
        with transaction.atomic():
            if address_data:
                if schema.full_address.id:
                    address = CustomUserLocationModel.objects.filter(id=schema.full_address.id).first()
                    if not address:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail="آدرس یافت نشد"
                        )
                    CustomUserLocationModel.objects.filter(id=address.id).update(**address_data)
                else:
                    address = CustomUserLocationModel.objects.create(**address_data)
                    user = CustomUserModel.objects.get(id=user_id)
                    user.address.add(address)

            if user_update_data:
                CustomUserModel.objects.filter(id=user_id).update(**user_update_data)
    @classmethod
    async def get_user_transactions(cls, current_user: CustomUserModel, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """
        Get user transaction history with pagination.

        Args:
            current_user: The authenticated user
            page: Page number (default: 1)
            per_page: Items per page (default: 20)

        Returns:
            Dict containing paginated transaction data

        Raises:
            HTTPException: If user not found or database error
        """
        try:
            # Calculate offset
            offset = (page - 1) * per_page

            # Get total count
            total_count = await UserPayment.objects.filter(user=current_user).acount()

            # Get paginated transactions
            transactions = []
            async for payment in UserPayment.objects.filter(
                user=current_user
            ).select_related('user').order_by('-buy_datetime')[offset:offset + per_page]:
                transaction_data = {
                    "id": payment.id,
                    "payment_id": payment.payment_id,
                    "reference_number": payment.reference_number,
                    "sale_reference_id": payment.sale_reference_id,
                    "amount": float(payment.amount),
                    "bank_name": payment.bank_name,
                    "status_payment": payment.status_payment,
                    "payment_finished": payment.payment_finished,
                    "buy_datetime": payment.buy_datetime.isoformat() if payment.buy_datetime else None,
                    "created_at": payment.created_at.isoformat() if hasattr(payment, 'created_at') and payment.created_at else None
                }
                transactions.append(transaction_data)

            # Calculate pagination info
            total_pages = (total_count + per_page - 1) // per_page
            has_next = page < total_pages
            has_prev = page > 1

            return {
                "transactions": transactions,
                "pagination": {
                    "current_page": page,
                    "per_page": per_page,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev
                }
            }

        except Exception as e:
            logger.error(f"Error retrieving transactions for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در دریافت تراکنش‌ها"
            )

    @classmethod
    async def get_user_wallet(cls, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Get user wallet information with detailed balance and transaction summary.

        Args:
            current_user: The authenticated user

        Returns:
            Dict containing wallet information

        Raises:
            HTTPException: If user not found or database error
        """
        try:
            # Get wallet information
            user_wallet = await UserWallet.objects.filter(user=current_user.id).afirst()

            # Get transaction statistics
            total_transactions = await UserPayment.objects.filter(user=current_user).acount()
            successful_transactions = await UserPayment.objects.filter(
                user=current_user,
                status_payment=True
            ).acount()

            # Calculate total spent
            total_spent = 0
            async for payment in UserPayment.objects.filter(
                user=current_user,
                status_payment=True
            ).values_list('amount', flat=True):
                total_spent += float(payment)

            wallet_info = {
                "balance": float(user_wallet.amount) if user_wallet and user_wallet.amount else 0.0,
                "currency": "IRR",
                "currency_symbol": "ریال",
                "formatted_balance": f"{float(user_wallet.amount):,.0f} ریال" if user_wallet and user_wallet.amount else "0 ریال",
                "last_updated": user_wallet.modified_at.isoformat() if user_wallet and hasattr(user_wallet, 'modified_at') else None,
                "statistics": {
                    "total_transactions": total_transactions,
                    "successful_transactions": successful_transactions,
                    "failed_transactions": total_transactions - successful_transactions,
                    "total_spent": total_spent,
                    "formatted_total_spent": f"{total_spent:,.0f} ریال"
                }
            }

            return wallet_info

        except Exception as e:
            logger.error(f"Error retrieving wallet for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در دریافت اطلاعات کیف پول"
            )

    @classmethod
    async def upload_user_avatar(cls, file: UploadFile, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Upload and process user avatar with comprehensive validation and optimization.

        Args:
            file: The uploaded image file
            current_user: The authenticated user

        Returns:
            Dict containing avatar information

        Raises:
            HTTPException: If validation fails or upload error
        """
        try:
            # Validate file presence
            if not file or not file.filename:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="فایل آپلود نشده است"
                )

            # Check file size
            file.file.seek(0, 2)
            file_size = file.file.tell()
            await file.seek(0)  # Reset file position

            # Validate file size (5MB limit)
            max_size = 5 * 1024 * 1024  # 5MB
            if file_size > max_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"حجم فایل بیش از حد مجاز است (حداکثر {max_size // (1024*1024)}MB)"
                )

            # Validate content type
            allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
            if file.content_type not in allowed_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="فرمت فایل مجاز نیست (فقط JPEG, PNG, GIF, WebP)"
                )

            # Get file extension and validate
            ext = os.path.splitext(file.filename)[1].lower()
            allowed_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
            if ext not in allowed_extensions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="پسوند فایل مجاز نیست"
                )

            # Read file content for processing
            file_content = await file.read()

            # Validate image content using PIL
            try:
                image = Image.open(io.BytesIO(file_content))
                image.verify()  # Verify it's a valid image

                # Re-open for processing (verify() closes the image)
                image = Image.open(io.BytesIO(file_content))

                # Convert to RGB if necessary
                if image.mode in ('RGBA', 'LA', 'P'):
                    image = image.convert('RGB')
                    ext = '.jpg'  # Save as JPEG for compatibility

                # Resize image if too large (max 800x800)
                max_dimension = 800
                if image.width > max_dimension or image.height > max_dimension:
                    image.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)

            except Exception as e:
                logger.error(f"Invalid image file for user {current_user.id}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="فایل تصویر معتبر نیست"
                )

            # Generate unique filename
            file_name = f"user_{current_user.id}_{uuid.uuid4().hex[:8]}{ext}"

            # Create directory structure
            avatar_dir = os.path.join(django_settings.MEDIA_ROOT, 'documents', 'user', 'avatar')
            os.makedirs(avatar_dir, exist_ok=True)

            # Full path to save the file
            file_path = os.path.join(avatar_dir, file_name)

            # Save optimized image
            image.save(file_path, quality=85, optimize=True)

            # Delete old avatar if exists
            try:
                old_user = await CustomUserModel.objects.aget(id=current_user.id)
                if old_user.avatar and old_user.avatar.name:
                    old_path = os.path.join(django_settings.MEDIA_ROOT, old_user.avatar.name)
                    if os.path.exists(old_path) and 'user.jpg' not in old_path:  # Don't delete default avatar
                        os.remove(old_path)
            except Exception as e:
                logger.warning(f"Could not delete old avatar for user {current_user.id}: {str(e)}")

            # Update user's avatar in the database
            relative_path = os.path.join('documents', 'user', 'avatar', file_name)
            await CustomUserModel.objects.filter(id=current_user.id).aupdate(avatar=relative_path)

            # Generate avatar URL
            avatar_url = f"{django_settings.MEDIA_URL}{relative_path}"

            logger.info(f"Avatar uploaded successfully for user {current_user.id}")

            return {
                "status": "success",
                "message": "تصویر پروفایل با موفقیت آپلود شد",
                "avatar": {
                    "path": relative_path,
                    "url": avatar_url,
                    "filename": file_name,
                    "size": len(file_content),
                    "dimensions": f"{image.width}x{image.height}"
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading avatar for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در آپلود تصویر پروفایل"
            )
        finally:
            # Ensure file is closed
            if hasattr(file, 'file') and hasattr(file.file, 'close'):
                try:
                    file.file.close()
                except:
                    pass

    @classmethod
    async def delete_user_avatar(cls, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Delete user avatar and reset to default.

        Args:
            current_user: The authenticated user

        Returns:
            Dict containing success message

        Raises:
            HTTPException: If database error
        """
        try:
            # Get current user data
            user_object = await CustomUserModel.objects.aget(id=current_user.id)

            # Delete current avatar file if exists
            if user_object.avatar and user_object.avatar.name:
                avatar_path = os.path.join(django_settings.MEDIA_ROOT, user_object.avatar.name)
                if os.path.exists(avatar_path) and 'user.jpg' not in avatar_path:  # Don't delete default
                    try:
                        os.remove(avatar_path)
                        logger.info(f"Deleted avatar file: {avatar_path}")
                    except Exception as e:
                        logger.warning(f"Could not delete avatar file {avatar_path}: {str(e)}")

            # Reset to default avatar
            default_avatar = 'documents/user/avatar/user.jpg'
            await CustomUserModel.objects.filter(id=current_user.id).aupdate(avatar=default_avatar)

            logger.info(f"Avatar reset to default for user {current_user.id}")

            return {
                "status": "success",
                "message": "تصویر پروفایل حذف شد",
                "avatar": {
                    "path": default_avatar,
                    "url": f"{django_settings.MEDIA_URL}{default_avatar}"
                }
            }

        except Exception as e:
            logger.error(f"Error deleting avatar for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در حذف تصویر پروفایل"
            )

class NormalUserAPI(UserProfileAPI):
    """Legacy API class - use UserProfileAPI instead."""
    pass

class UserAddressAPI:
    """API for managing user addresses."""

    @classmethod
    async def create_address(cls, schema: CreateAddressSchema, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Create a new address for the user.

        Args:
            schema: Address creation data
            current_user: The authenticated user

        Returns:
            Dict containing created address information

        Raises:
            HTTPException: If validation fails or database error
        """
        try:
            # Validate province and city exist
            province = await ProvinceModel.objects.filter(id=schema.province_id).afirst()
            if not province:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="استان یافت نشد"
                )

            city = await CityModel.objects.filter(id=schema.city_id, province=province).afirst()
            if not city:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="شهر یافت نشد یا متعلق به استان انتخابی نیست"
                )

            # Create address
            address_data = {
                "province_id": schema.province_id,
                "city_id": schema.city_id,
                "address": schema.address,
                "zip_code": schema.zip_code,
                "latitude": schema.latitude,
                "longitude": schema.longitude,
            }

            address = await CustomUserLocationModel.objects.acreate(**address_data)
            # Add address to user's addresses
            await current_user.address.aadd(address)

            logger.info(f"Address created successfully for user {current_user.id}")
            return {
                "status": "success",
                "message": "آدرس با موفقیت ایجاد شد",
                "address": {
                    "id": address.id,
                    "province": province.name,
                    "city": city.name,
                    "address": address.address,
                    "zip_code": address.zip_code,
                    "latitude": float(address.latitude) if address.latitude else None,
                    "longitude": float(address.longitude) if address.longitude else None
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating address for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در ایجاد آدرس"
            )

    @classmethod
    async def get_user_addresses(cls, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Get all addresses for the user.

        Args:
            current_user: The authenticated user

        Returns:
            Dict containing user addresses

        Raises:
            HTTPException: If database error
        """
        try:
            addresses = []
            async for address in current_user.address.select_related('province', 'city').all():
                address_data = {
                    "id": address.id,
                    "province": {
                        "id": address.province.id,
                        "name": address.province.name,
                        "slug": address.province.slug
                    } if hasattr(address, 'province') and address.province else None,
                    "city": {
                        "id": address.city.id,
                        "name": address.city.name,
                        "slug": address.city.slug
                    } if hasattr(address, 'city') and address.city else None,
                    "address": address.address if hasattr(address, 'address') else "",
                    "zip_code": address.zip_code if hasattr(address, 'zip_code') else "",
                    "latitude": float(address.latitude) if hasattr(address, 'latitude') and address.latitude else None,
                    "longitude": float(address.longitude) if hasattr(address, 'longitude') and address.longitude else None,
                    "created_at": address.created_at.isoformat() if hasattr(address, 'created_at') and address.created_at else None
                }
                addresses.append(address_data)

            return {
                "addresses": addresses,
                "total_count": len(addresses)
            }

        except Exception as e:
            logger.error(f"Error retrieving addresses for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در دریافت آدرس‌ها"
            )

    @classmethod
    async def update_address(cls, address_id: int, schema: EditAddressSchema, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Update an existing address.

        Args:
            address_id: ID of the address to update
            schema: Address update data
            current_user: The authenticated user

        Returns:
            Dict containing updated address information

        Raises:
            HTTPException: If address not found or validation fails
        """
        try:
            # Check if address belongs to user
            address = await CustomUserLocationModel.objects.filter(
                id=address_id,
                # object_primary_key=current_user.id
            ).afirst()

            if not address:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="آدرس یافت نشد یا متعلق به شما نیست"
                )

            # Validate province and city
            province = await ProvinceModel.objects.filter(id=schema.province_id).afirst()
            if not province:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="استان یافت نشد"
                )

            city = await CityModel.objects.filter(id=schema.city_id, province=province).afirst()
            if not city:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="شهر یافت نشد یا متعلق به استان انتخابی نیست"
                )

            # Update address
            update_data = {
                "province_id": schema.province_id,
                "city_id": schema.city_id,
                "address": schema.address,
                "zip_code": schema.zip_code,
                "latitude": schema.latitude,
                "longitude": schema.longitude
            }
            await CustomUserLocationModel.objects.filter(id=address_id).aupdate(**update_data)

            logger.info(f"Address {address_id} updated successfully for user {current_user.id}")

            return {
                "status": "success",
                "message": "آدرس با موفقیت به‌روزرسانی شد",
                "address": {
                    "id": address_id,
                    "province": province.name,
                    "city": city.name,
                    "address": schema.address,
                    "zip_code": schema.zip_code,
                    "latitude": float(schema.latitude) if schema.latitude else None,
                    "longitude": float(schema.longitude) if schema.longitude else None
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating address {address_id} for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در به‌روزرسانی آدرس"
            )

    @classmethod
    async def delete_address(cls, address_id: int, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Delete an address.

        Args:
            address_id: ID of the address to delete
            current_user: The authenticated user

        Returns:
            Dict containing success message

        Raises:
            HTTPException: If address not found
        """
        try:
            # Check if address belongs to user
            address = await CustomUserLocationModel.objects.filter(
                id=address_id,
            ).afirst()

            if not address:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="آدرس یافت نشد یا متعلق به شما نیست"
                )

            # Remove from user's addresses and delete
            await current_user.address.aremove(address)
            await address.adelete()

            logger.info(f"Address {address_id} deleted successfully for user {current_user.id}")

            return {
                "status": "success",
                "message": "آدرس با موفقیت حذف شد"
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting address {address_id} for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در حذف آدرس"
            )

class RegisterUserAPI(UserProfileAPI):
    """Legacy API class - use UserProfileAPI instead."""
    pass


class UserSubscriptionAPI:
    """API for managing user subscriptions."""

    @classmethod
    async def get_user_subscription(cls, current_user: CustomUserModel) -> Dict[str, Any]:
        """
        Get user subscription information.

        Args:
            current_user: The authenticated user

        Returns:
            Dict containing subscription information

        Raises:
            HTTPException: If database error
        """
        try:
            subscription = await SubScriptionModel.objects.filter(user=current_user).afirst()

            if not subscription:
                return {
                    "subscription": None,
                    "message": "هیچ اشتراکی فعال نیست"
                }

            # Calculate remaining days
            remaining_days = 0
            if subscription.expire_at_time:
                from datetime import datetime
                now = datetime.now()
                if subscription.expire_at_time > now:
                    remaining_days = (subscription.expire_at_time - now).days

            subscription_data = {
                "id": subscription.id,
                "type": subscription.subscription_type,
                "type_display": dict(SubScriptionModel.SUBSCRIPTION_TYPE)[subscription.subscription_type],
                "plan_count": subscription.subscription_plan_count,
                "plan_count_display": dict(SubScriptionModel.SUBSCRIPTION_BY_ADV_COUNT)[subscription.subscription_plan_count],
                "plan_month": subscription.subscription_plan_month,
                "plan_month_display": dict(SubScriptionModel.SUBSCRIPTION_BY_MONTH)[subscription.subscription_plan_month],
                "total_views": subscription.total_views,
                "adv_views": subscription.adv_views,
                "remaining_views": max(0, subscription.total_views - subscription.adv_views),
                "expire_at": subscription.expire_at_time.isoformat() if subscription.expire_at_time else None,
                "remaining_days": remaining_days,
                "is_active": subscription.active_plan,
                "status": subscription.status,
                "created_at": subscription.created_at.isoformat() if subscription.created_at else None
            }

            return {
                "subscription": subscription_data,
                "is_expired": remaining_days <= 0 if subscription.expire_at_time else False,
                "needs_renewal": remaining_days <= 7 if subscription.expire_at_time else False
            }

        except Exception as e:
            logger.error(f"Error retrieving subscription for user {current_user.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="خطا در دریافت اطلاعات اشتراک"
            )

class AgentUserAPI:
    """API for agent users - to be implemented based on requirements."""
    pass
