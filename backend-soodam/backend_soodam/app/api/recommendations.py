"""
Recommendations API for the Soodam backend.

This module provides API functions for recommendation operations.
"""

import logging
from typing import Dict, List, Optional

from fastapi import HTT<PERSON>Exception, Request, status

from ..core.recommendations import RecommendationEngine
from ..models import CustomUserModel

logger = logging.getLogger(__name__)


class RecommendationsAPI:
    """Recommendations API for the Soodam backend."""
    
    @staticmethod
    async def get_user_recommendations(
        request: Request,
        user: CustomUserModel,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get personalized recommendations for a user.
        
        Args:
            request: The request
            user: The user
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get recommendations
            recommendations = await RecommendationEngine.get_user_recommendations(
                user.id, limit, exclude_ids
            )
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting user recommendations: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting user recommendations: {str(e)}"
            )
    
    @staticmethod
    async def get_behavior_based_recommendations(
        request: Request,
        user: CustomUserModel,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on user behavior.
        
        Args:
            request: The request
            user: The user
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get recommendations
            recommendations = await RecommendationEngine.get_behavior_based_recommendations(
                user.id, limit, exclude_ids
            )
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting behavior-based recommendations: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting behavior-based recommendations: {str(e)}"
            )
    
    @staticmethod
    async def get_content_based_recommendations(
        request: Request,
        user: CustomUserModel,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on content similarity.
        
        Args:
            request: The request
            user: The user
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get recommendations
            recommendations = await RecommendationEngine.get_content_based_recommendations(
                user.id, limit, exclude_ids
            )
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting content-based recommendations: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting content-based recommendations: {str(e)}"
            )
    
    @staticmethod
    async def get_collaborative_recommendations(
        request: Request,
        user: CustomUserModel,
        limit: int = 10,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get recommendations based on collaborative filtering.
        
        Args:
            request: The request
            user: The user
            limit: The maximum number of recommendations to return
            exclude_ids: Advertisement IDs to exclude from recommendations
            
        Returns:
            List[Dict]: The recommended advertisements
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get recommendations
            recommendations = await RecommendationEngine.get_collaborative_recommendations(
                user.id, limit, exclude_ids
            )
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting collaborative recommendations: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting collaborative recommendations: {str(e)}"
            )
    
    @staticmethod
    async def get_trending_advertisements(
        request: Request,
        limit: int = 10,
        days: int = 7,
        exclude_ids: Optional[List[int]] = None,
    ) -> List[Dict]:
        """
        Get trending advertisements.
        
        Args:
            request: The request
            limit: The maximum number of advertisements to return
            days: The number of days to consider for trending
            exclude_ids: Advertisement IDs to exclude
            
        Returns:
            List[Dict]: The trending advertisements
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get trending advertisements
            trending = await RecommendationEngine.get_trending_advertisements(
                limit, days, exclude_ids
            )
            
            return trending
        except Exception as e:
            logger.error(f"Error getting trending advertisements: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting trending advertisements: {str(e)}"
            )
