"""
Chat API for the Soodam backend.

This module provides API functions for chat operations.
"""

import logging
import math
from typing import Dict, List, Optional

from django.db.models import Count, F, Q, Value
from fastapi import HTTPException, Request, status

from ..core.websocket import WebSocketMessage, manager
from ..models import CustomUserModel
from ..models.advertisement import AdvertisementModel
from ..models.chat import Chat<PERSON>ttachmentModel, ChatMessageModel, ChatRoomModel
from ..schemas.chat import (
    ChatAdvertisementSchema,
    ChatAttachmentSchema,
    ChatMessageCreateSchema,
    ChatMessageListSchema,
    ChatMessageSchema,
    ChatRoomCreateSchema,
    ChatRoomListSchema,
    ChatRoomSchema,
    ChatUserSchema,
)
from ..services.notification import NotificationService
from ..utils.pagination import paginate_queryset

logger = logging.getLogger(__name__)


class ChatAPI:
    """Chat API for the Soodam backend."""

    @staticmethod
    async def get_chat_rooms(
        request: Request,
        user: CustomUserModel,
        page: int = 1,
        limit: int = 10,
        advertisement_id: Optional[int] = None,
    ) -> ChatRoomListSchema:
        """
        Get chat rooms for a user.

        Args:
            request: The request
            user: The user
            page: The page number
            limit: The page size
            advertisement_id: Filter by advertisement ID

        Returns:
            ChatRoomListSchema: The chat rooms

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Build the queryset
            queryset = ChatRoomModel.objects.filter(
                Q(sender=user) | Q(receiver=user),
                is_active=True
            )

            # Apply filters
            if advertisement_id:
                queryset = queryset.filter(advertisement_id=advertisement_id)

            # Order by updated_at
            queryset = queryset.order_by("-updated_at")

            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)

            # Convert to schema
            items = []
            for room in paginated["items"]:
                # Get the other user
                other_user = room.receiver if room.sender.id == user.id else room.sender

                # Get the last message
                last_message = await ChatMessageModel.objects.filter(room=room).order_by("-created_at").afirst()

                # Get the unread count
                unread_count = await ChatMessageModel.objects.filter(
                    room=room,
                    sender=other_user,
                    is_read=False
                ).acount()

                # Get the advertisement image
                advertisement_image = await room.advertisement.images.filter(is_primary=True).afirst()

                # Build the room schema
                items.append(ChatRoomSchema(
                    id=room.id,
                    advertisement=ChatAdvertisementSchema(
                        id=room.advertisement.id,
                        title=room.advertisement.title,
                        image_url=advertisement_image.url if advertisement_image else None
                    ),
                    sender=ChatUserSchema(
                        id=room.sender.id,
                        username=room.sender.username,
                        full_name=f"{room.sender.first_name} {room.sender.last_name}",
                        avatar_url=room.sender.avatar_url
                    ),
                    receiver=ChatUserSchema(
                        id=room.receiver.id,
                        username=room.receiver.username,
                        full_name=f"{room.receiver.first_name} {room.receiver.last_name}",
                        avatar_url=room.receiver.avatar_url
                    ),
                    is_active=room.is_active,
                    created_at=room.created_at,
                    updated_at=room.updated_at,
                    last_message=ChatMessageSchema(
                        id=last_message.id,
                        room_id=last_message.room.id,
                        sender_id=last_message.sender.id,
                        content=last_message.content,
                        is_read=last_message.is_read,
                        created_at=last_message.created_at,
                        attachments=[]
                    ) if last_message else None,
                    unread_count=unread_count
                ))

            # Calculate pagination
            total_pages = math.ceil(paginated["total"] / limit)

            return ChatRoomListSchema(
                items=items,
                total=paginated["total"],
                page=page,
                limit=limit,
                pages=total_pages
            )
        except Exception as e:
            logger.error(f"Error getting chat rooms: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting chat rooms"
            )

    @staticmethod
    async def get_chat_room(
        request: Request,
        room_id: int,
        user: CustomUserModel,
    ) -> ChatRoomSchema:
        """
        Get a chat room.

        Args:
            request: The request
            room_id: The room ID
            user: The user

        Returns:
            ChatRoomSchema: The chat room

        Raises:
            HTTPException: If the room is not found
        """
        try:
            # Get the room
            room = await ChatRoomModel.objects.filter(
                Q(sender=user) | Q(receiver=user),
                id=room_id,
                is_active=True
            ).afirst()

            if not room:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Chat room not found"
                )

            # Get the other user
            other_user = room.receiver if room.sender.id == user.id else room.sender

            # Get the last message
            last_message = await ChatMessageModel.objects.filter(room=room).order_by("-created_at").afirst()

            # Get the unread count
            unread_count = await ChatMessageModel.objects.filter(
                room=room,
                sender=other_user,
                is_read=False
            ).acount()

            # Get the advertisement image
            advertisement_image = await room.advertisement.images.filter(is_primary=True).afirst()

            # Build the room schema
            return ChatRoomSchema(
                id=room.id,
                advertisement=ChatAdvertisementSchema(
                    id=room.advertisement.id,
                    title=room.advertisement.title,
                    image_url=advertisement_image.url if advertisement_image else None
                ),
                sender=ChatUserSchema(
                    id=room.sender.id,
                    username=room.sender.username,
                    full_name=f"{room.sender.first_name} {room.sender.last_name}",
                    avatar_url=room.sender.avatar_url
                ),
                receiver=ChatUserSchema(
                    id=room.receiver.id,
                    username=room.receiver.username,
                    full_name=f"{room.receiver.first_name} {room.receiver.last_name}",
                    avatar_url=room.receiver.avatar_url
                ),
                is_active=room.is_active,
                created_at=room.created_at,
                updated_at=room.updated_at,
                last_message=ChatMessageSchema(
                    id=last_message.id,
                    room_id=last_message.room.id,
                    sender_id=last_message.sender.id,
                    content=last_message.content,
                    is_read=last_message.is_read,
                    created_at=last_message.created_at,
                    attachments=[]
                ) if last_message else None,
                unread_count=unread_count
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chat room: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting chat room"
            )

    @staticmethod
    async def create_chat_room(
        request: Request,
        schema: ChatRoomCreateSchema,
        user: CustomUserModel,
    ) -> ChatRoomSchema:
        """
        Create a chat room.

        Args:
            request: The request
            schema: The chat room creation schema
            user: The user

        Returns:
            ChatRoomSchema: The created chat room

        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.filter(id=schema.advertisement_id).afirst()

            if not advertisement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Advertisement not found"
                )

            # Get the receiver
            receiver = await CustomUserModel.objects.filter(id=schema.receiver_id).afirst()

            if not receiver:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Receiver not found"
                )

            # Check if the room already exists
            room = await ChatRoomModel.objects.filter(
                advertisement=advertisement,
                sender=user,
                receiver=receiver
            ).afirst()

            if room:
                # Room already exists, just create a new message
                message = await ChatMessageModel.objects.acreate(
                    room=room,
                    sender=user,
                    content=schema.message
                )

                # Update the room
                room.updated_at = message.created_at
                await room.asave()
            else:
                # Create the room
                room = await ChatRoomModel.objects.acreate(
                    advertisement=advertisement,
                    sender=user,
                    receiver=receiver
                )

                # Create the initial message
                message = await ChatMessageModel.objects.acreate(
                    room=room,
                    sender=user,
                    content=schema.message
                )

            # Send notification to the receiver
            await NotificationService.create_notification(
                user=receiver,
                notification_type="ADVERTISEMENT_INQUIRY",
                title="New message",
                message=f"You have a new message from {user.get_full_name()} about {advertisement.title}",
                data={
                    "room_id": room.id,
                    "advertisement_id": advertisement.id,
                    "sender_id": user.id
                }
            )

            # Send WebSocket message to the receiver
            await manager.broadcast_to_user(
                WebSocketMessage(
                    type="chat_message",
                    data={
                        "room_id": room.id,
                        "message": {
                            "id": message.id,
                            "room_id": room.id,
                            "sender_id": user.id,
                            "content": message.content,
                            "is_read": message.is_read,
                            "created_at": message.created_at.isoformat(),
                            "attachments": []
                        }
                    },
                    channel=f"user:{receiver.id}:chat"
                ),
                receiver.id
            )

            # Return the created room
            return await ChatAPI.get_chat_room(request, room.id, user)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating chat room: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating chat room"
            )

    @staticmethod
    async def get_chat_messages(
        request: Request,
        room_id: int,
        user: CustomUserModel,
        page: int = 1,
        limit: int = 20,
    ) -> ChatMessageListSchema:
        """
        Get chat messages for a room.

        Args:
            request: The request
            room_id: The room ID
            user: The user
            page: The page number
            limit: The page size

        Returns:
            ChatMessageListSchema: The chat messages

        Raises:
            HTTPException: If the room is not found
        """
        try:
            # Get the room
            room = await ChatRoomModel.objects.filter(
                Q(sender=user) | Q(receiver=user),
                id=room_id,
                is_active=True
            ).afirst()

            if not room:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Chat room not found"
                )

            # Get the other user
            other_user = room.receiver if room.sender.id == user.id else room.sender

            # Mark messages from the other user as read
            await ChatMessageModel.objects.filter(
                room=room,
                sender=other_user,
                is_read=False
            ).aupdate(is_read=True)

            # Build the queryset
            queryset = ChatMessageModel.objects.filter(room=room).order_by("-created_at")

            # Paginate the queryset
            paginated = await paginate_queryset(queryset, page, limit)

            # Convert to schema
            items = []
            for message in paginated["items"]:
                # Get the attachments
                # Get attachment fields as a list of dictionaries
                attachments = await ChatAttachmentModel.objects.filter(message=message).avalues(
                    "id", "file_url", "file_type", "file_name", "file_size", "created_at"
                )

                # Build the message schema
                items.append(ChatMessageSchema(
                    id=message.id,
                    room_id=message.room.id,
                    sender_id=message.sender.id,
                    content=message.content,
                    is_read=message.is_read,
                    created_at=message.created_at,
                    attachments=[ChatAttachmentSchema(
                        id=attachment["id"],
                        file_url=attachment["file_url"],
                        file_type=attachment["file_type"],
                        file_name=attachment["file_name"],
                        file_size=attachment["file_size"],
                        created_at=attachment["created_at"]
                    ) for attachment in attachments]
                ))

            # Calculate pagination
            total_pages = math.ceil(paginated["total"] / limit)

            return ChatMessageListSchema(
                items=items,
                total=paginated["total"],
                page=page,
                limit=limit,
                pages=total_pages
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chat messages: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error getting chat messages"
            )

    @staticmethod
    async def send_chat_message(
        request: Request,
        room_id: int,
        schema: ChatMessageCreateSchema,
        user: CustomUserModel,
    ) -> ChatMessageSchema:
        """
        Send a chat message.

        Args:
            request: The request
            room_id: The room ID
            schema: The chat message creation schema
            user: The user

        Returns:
            ChatMessageSchema: The created chat message

        Raises:
            HTTPException: If the room is not found
        """
        try:
            # Get the room
            room = await ChatRoomModel.objects.filter(
                Q(sender=user) | Q(receiver=user),
                id=room_id,
                is_active=True
            ).afirst()

            if not room:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Chat room not found"
                )

            # Get the other user
            other_user = room.receiver if room.sender.id == user.id else room.sender

            # Create the message
            message = await ChatMessageModel.objects.acreate(
                room=room,
                sender=user,
                content=schema.content
            )

            # Create attachments if provided
            attachments = []
            if schema.attachments:
                for attachment_data in schema.attachments:
                    attachment = await ChatAttachmentModel.objects.acreate(
                        message=message,
                        file_url=attachment_data["file_url"],
                        file_type=attachment_data["file_type"],
                        file_name=attachment_data["file_name"],
                        file_size=attachment_data["file_size"]
                    )

                    attachments.append(ChatAttachmentSchema(
                        id=attachment.id,
                        file_url=attachment.file_url,
                        file_type=attachment.file_type,
                        file_name=attachment.file_name,
                        file_size=attachment.file_size,
                        created_at=attachment.created_at
                    ))

            # Update the room
            room.updated_at = message.created_at
            await room.asave()

            # Send notification to the other user
            await NotificationService.create_notification(
                user=other_user,
                notification_type="USER_MESSAGE",
                title="New message",
                message=f"You have a new message from {user.get_full_name()}",
                data={
                    "room_id": room.id,
                    "message_id": message.id,
                    "sender_id": user.id
                }
            )

            # Send WebSocket message to the other user
            await manager.broadcast_to_user(
                WebSocketMessage(
                    type="chat_message",
                    data={
                        "room_id": room.id,
                        "message": {
                            "id": message.id,
                            "room_id": room.id,
                            "sender_id": user.id,
                            "content": message.content,
                            "is_read": message.is_read,
                            "created_at": message.created_at.isoformat(),
                            "attachments": [attachment.dict() for attachment in attachments]
                        }
                    },
                    channel=f"user:{other_user.id}:chat"
                ),
                other_user.id
            )

            # Return the created message
            return ChatMessageSchema(
                id=message.id,
                room_id=message.room.id,
                sender_id=message.sender.id,
                content=message.content,
                is_read=message.is_read,
                created_at=message.created_at,
                attachments=attachments
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error sending chat message: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error sending chat message"
            )
