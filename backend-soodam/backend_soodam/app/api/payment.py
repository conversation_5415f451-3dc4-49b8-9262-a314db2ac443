from typing import Any, Dict, Optional
import uuid
import logging
from datetime import datetime

from fastapi import Request, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import CustomUserModel, UserPayment
from app.deps.db import get_db
from app.core.payment_settings import payment_settings as settings
from app.services.iranian_payment import PaymentGatewayFactory

logger = logging.getLogger(__name__)

class PaymentAPI:
    """API for handling Iranian bank payment gateways"""

    @staticmethod
    async def initialize_payment(
        request: Request,
        amount: float,
        bank_name: str,
        description: str,
        current_user: CustomUserModel,
        db: AsyncSession = Depends(get_db),
        callback_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """Initialize a payment with an Iranian bank gateway"""
        try:
            # Generate unique reference number and tracking code
            reference_number = str(uuid.uuid4().hex)[:11]
            tracking_code = str(uuid.uuid4().hex)[:8]

            # Create payment record
            payment = UserPayment(
                payment_id=uuid.uuid4(),
                reference_number=reference_number,
                sale_reference_id="",  # Will be filled after successful payment
                status_payment=False,
                payment_finished=False,
                amount=amount,
                bank_name=bank_name,
                user=current_user
            )

            # Save to database
            db.add(payment)
            await db.commit()

            # Use the payment gateway factory to get the appropriate gateway
            try:
                # Use custom callback URL if provided
                custom_callback = callback_url or f"{settings.PAYMENT_CALLBACK_URL}?tracking_code={tracking_code}"

                # Get the payment gateway
                gateway = PaymentGatewayFactory.create_gateway(bank_name, custom_callback)

                # Request payment from the gateway
                payment_result = await gateway.request_payment(
                    amount=amount,
                    description=description,
                    mobile=current_user.phone_number
                )

                if payment_result["success"]:
                    # Return redirect URL and reference information
                    return {
                        "redirect_url": payment_result["redirect_url"],
                        "reference_number": reference_number,
                        "tracking_code": tracking_code
                    }
                else:
                    # Payment initialization failed
                    logger.error(f"Payment gateway error: {payment_result.get('error_message')}")
                    raise HTTPException(
                        status_code=400,
                        detail=f"Payment gateway error: {payment_result.get('error_message')}"
                    )

            except NotImplementedError:
                # Fallback to basic implementation for unsupported banks
                logger.warning(f"Using fallback implementation for bank: {bank_name}")

                # Determine bank gateway URL based on bank_name
                if bank_name.lower() == "zarinpal":
                    gateway_url = f"https://www.zarinpal.com/pg/StartPay/{reference_number}"
                elif bank_name.lower() == "mellat":
                    gateway_url = f"https://bpm.shaparak.ir/pgwchannel/startpay.mellat?RefId={reference_number}"
                elif bank_name.lower() == "saman":
                    gateway_url = f"https://sep.shaparak.ir/payment.aspx?MID={settings.SAMAN_MERCHANT_ID}&ResNum={reference_number}"
                elif bank_name.lower() == "parsian":
                    gateway_url = f"https://pec.shaparak.ir/NewIPG/?Token={reference_number}"
                else:
                    gateway_url = f"https://www.zarinpal.com/pg/StartPay/{reference_number}"

                # Return redirect URL and reference information
                return {
                    "redirect_url": gateway_url,
                    "reference_number": reference_number,
                    "tracking_code": tracking_code
                }

        except Exception as e:
            logger.error(f"Error initializing payment: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to initialize payment")

    @staticmethod
    async def verify_payment(
        request: Request,
        tracking_code: str,
        status: int,
        reference_id: Optional[str] = None,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """Verify a payment after bank callback"""
        try:
            # Find payment by reference number
            payment = await db.query(UserPayment).filter(
                UserPayment.reference_number == tracking_code
            ).first()

            if not payment:
                raise HTTPException(status_code=404, detail="Payment not found")

            # Try to use the payment gateway for verification
            try:
                gateway = PaymentGatewayFactory.create_gateway(payment.bank_name)

                # Verify payment with the gateway
                verify_result = await gateway.verify_payment(tracking_code, reference_id)

                is_success = verify_result["success"]
                message = verify_result.get("message", "پرداخت با موفقیت انجام شد" if is_success else "پرداخت ناموفق بود")
                bank_reference = verify_result.get("reference_id", reference_id or "")

            except (NotImplementedError, Exception) as e:
                # Fallback to basic implementation
                logger.warning(f"Using fallback verification for bank {payment.bank_name}: {str(e)}")
                is_success = status == 1
                message = "پرداخت با موفقیت انجام شد" if is_success else "پرداخت ناموفق بود"
                bank_reference = reference_id or str(uuid.uuid4().hex)[:11]

            # Update payment record
            payment.status_payment = is_success
            payment.payment_finished = True

            if bank_reference:
                payment.sale_reference_id = bank_reference

            # Save changes
            await db.commit()

            # Return verification result
            return {
                "is_success": is_success,
                "reference_number": payment.reference_number,
                "tracking_code": tracking_code,
                "bank_reference": payment.sale_reference_id,
                "message": message
            }

        except Exception as e:
            logger.error(f"Error verifying payment: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to verify payment")

    @staticmethod
    async def get_payment_history(
        request: Request,
        current_user: CustomUserModel,
        page: int = 1,
        page_size: int = 10,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """Get payment history for a user"""
        try:
            # Query payment records for the user
            total_count = await db.query(UserPayment).filter(
                UserPayment.user_id == current_user.id
            ).count()

            payments = await db.query(UserPayment).filter(
                UserPayment.user_id == current_user.id
            ).order_by(
                UserPayment.buy_datetime.desc()
            ).offset((page - 1) * page_size).limit(page_size).all()

            # Format results
            payment_list = []
            for payment in payments:
                payment_list.append({
                    "id": str(payment.payment_id),
                    "amount": float(payment.amount),
                    "bank_name": payment.bank_name,
                    "status": "موفق" if payment.status_payment else "ناموفق",
                    "date": payment.buy_datetime,
                    "reference_number": payment.reference_number,
                    "sale_reference_id": payment.sale_reference_id
                })

            # Return payment history
            return {
                "payments": payment_list,
                "total_count": total_count,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            logger.error(f"Error getting payment history: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to get payment history")