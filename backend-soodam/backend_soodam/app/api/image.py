"""
Image API for the Soodam backend.

This module provides API functions for image operations.
"""

import logging
from typing import Dict, List, Optional

from fastapi import HTT<PERSON>Exception, Request, UploadFile, status

from ..models import CustomUserModel
from ..services.image import ImageService

logger = logging.getLogger(__name__)


class ImageAPI:
    """Image API for the Soodam backend."""
    
    @staticmethod
    async def upload_image(
        request: Request,
        file: UploadFile,
        folder: str = 'uploads/images',
        generate_sizes: bool = True,
        user: Optional[CustomUserModel] = None,
    ) -> Dict:
        """
        Upload an image.
        
        Args:
            request: The request
            file: The uploaded file
            folder: The folder to upload to
            generate_sizes: Whether to generate different sizes
            user: The user (optional)
            
        Returns:
            Dict: The upload result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Upload the image
            result = await ImageService.upload_image(file, folder, generate_sizes)
            
            # Add user information if provided
            if user:
                result['user_id'] = user.id
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading image: {str(e)}"
            )
    
    @staticmethod
    async def upload_advertisement_image(
        request: Request,
        file: UploadFile,
        advertisement_id: int,
        is_primary: bool = False,
        user: CustomUserModel = None,
    ) -> Dict:
        """
        Upload an advertisement image.
        
        Args:
            request: The request
            file: The uploaded file
            advertisement_id: The advertisement ID
            is_primary: Whether the image is primary
            user: The user
            
        Returns:
            Dict: The upload result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Upload the image
            folder = f"uploads/advertisements/{advertisement_id}"
            result = await ImageService.upload_image(file, folder, True)
            
            # Add advertisement information
            result['advertisement_id'] = advertisement_id
            result['is_primary'] = is_primary
            
            # Add user information if provided
            if user:
                result['user_id'] = user.id
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading advertisement image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading advertisement image: {str(e)}"
            )
    
    @staticmethod
    async def upload_profile_image(
        request: Request,
        file: UploadFile,
        user: CustomUserModel,
    ) -> Dict:
        """
        Upload a profile image.
        
        Args:
            request: The request
            file: The uploaded file
            user: The user
            
        Returns:
            Dict: The upload result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Upload the image
            folder = f"uploads/profiles/{user.id}"
            result = await ImageService.upload_image(file, folder, True)
            
            # Add user information
            result['user_id'] = user.id
            
            # Update the user's avatar URL
            user.avatar_url = result['url']
            await user.asave()
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading profile image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading profile image: {str(e)}"
            )
    
    @staticmethod
    async def delete_image(
        request: Request,
        key: str,
        delete_sizes: bool = True,
        user: Optional[CustomUserModel] = None,
    ) -> Dict:
        """
        Delete an image.
        
        Args:
            request: The request
            key: The S3 key
            delete_sizes: Whether to delete different sizes
            user: The user (optional)
            
        Returns:
            Dict: The deletion result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Delete the image
            result = await ImageService.delete_image(key, delete_sizes)
            
            # Add user information if provided
            if user:
                result['user_id'] = user.id
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting image: {str(e)}"
            )
