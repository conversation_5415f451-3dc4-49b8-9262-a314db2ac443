"""
Management command for managing database migrations.
"""

import logging
from typing import Any, Optional

from django.core.management.base import BaseCommand, CommandError

from app.core.migrations import (
    apply_migrations,
    check_migrations,
    create_data_migration,
    create_migration,
    get_applied_migrations,
    get_unapplied_migrations,
    rollback_migration,
    squash_migrations,
)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Manage database migrations"
    
    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest="command", help="Command to run")
        
        # apply command
        apply_parser = subparsers.add_parser("apply", help="Apply migrations")
        apply_parser.add_argument("--app", help="App label to migrate")
        
        # check command
        check_parser = subparsers.add_parser("check", help="Check for unapplied migrations")
        
        # create command
        create_parser = subparsers.add_parser("create", help="Create a new migration")
        create_parser.add_argument("app", help="App label to create a migration for")
        create_parser.add_argument("name", help="Name of the migration")
        create_parser.add_argument("--empty", action="store_true", help="Create an empty migration")
        
        # rollback command
        rollback_parser = subparsers.add_parser("rollback", help="Rollback migrations")
        rollback_parser.add_argument("app", help="App label to rollback")
        rollback_parser.add_argument("--target", help="Migration to rollback to (default: zero)")
        
        # list command
        list_parser = subparsers.add_parser("list", help="List migrations")
        list_parser.add_argument("--applied", action="store_true", help="List applied migrations")
        list_parser.add_argument("--unapplied", action="store_true", help="List unapplied migrations")
        
        # data command
        data_parser = subparsers.add_parser("data", help="Create a data migration")
        data_parser.add_argument("app", help="App label to create a migration for")
        data_parser.add_argument("name", help="Name of the migration")
        data_parser.add_argument("--forwards", required=True, help="Python code to run when applying the migration")
        data_parser.add_argument("--backwards", default="pass", help="Python code to run when rolling back the migration")
        
        # squash command
        squash_parser = subparsers.add_parser("squash", help="Squash migrations")
        squash_parser.add_argument("app", help="App label to squash migrations for")
        squash_parser.add_argument("--start", help="First migration to include (default: first)")
        squash_parser.add_argument("--end", help="Last migration to include (default: last)")
    
    def handle(self, *args: Any, **options: Any) -> Optional[str]:
        command = options["command"]
        
        if command == "apply":
            app = options.get("app")
            self.stdout.write(f"Applying migrations for {'all apps' if app is None else app}")
            apply_migrations(app_label=app, interactive=True)
            self.stdout.write(self.style.SUCCESS("Migrations applied successfully"))
        
        elif command == "check":
            self.stdout.write("Checking for unapplied migrations")
            has_unapplied = check_migrations()
            if not has_unapplied:
                self.stdout.write(self.style.SUCCESS("No unapplied migrations"))
        
        elif command == "create":
            app = options["app"]
            name = options["name"]
            empty = options.get("empty", False)
            self.stdout.write(f"Creating {'empty ' if empty else ''}migration {name} for {app}")
            create_migration(app_label=app, name=name, empty=empty)
            self.stdout.write(self.style.SUCCESS(f"Migration {name} created successfully for {app}"))
        
        elif command == "rollback":
            app = options["app"]
            target = options.get("target")
            self.stdout.write(f"Rolling back migrations for {app} to {target or 'zero'}")
            rollback_migration(app_label=app, migration_name=target)
            self.stdout.write(self.style.SUCCESS(f"Migrations for {app} rolled back to {target or 'zero'}"))
        
        elif command == "list":
            applied = options.get("applied", False)
            unapplied = options.get("unapplied", False)
            
            if not applied and not unapplied:
                applied = unapplied = True
            
            if applied:
                self.stdout.write("Applied migrations:")
                for app, name in get_applied_migrations():
                    self.stdout.write(f"  {app}.{name}")
            
            if unapplied:
                self.stdout.write("Unapplied migrations:")
                for app, name in get_unapplied_migrations():
                    self.stdout.write(f"  {app}.{name}")
        
        elif command == "data":
            app = options["app"]
            name = options["name"]
            forwards = options["forwards"]
            backwards = options.get("backwards", "pass")
            self.stdout.write(f"Creating data migration {name} for {app}")
            create_data_migration(app_label=app, name=name, forwards_code=forwards, backwards_code=backwards)
            self.stdout.write(self.style.SUCCESS(f"Data migration {name} created successfully for {app}"))
        
        elif command == "squash":
            app = options["app"]
            start = options.get("start")
            end = options.get("end")
            self.stdout.write(f"Squashing migrations for {app}")
            squash_migrations(app_label=app, start_migration=start, end_migration=end)
            self.stdout.write(self.style.SUCCESS(f"Migrations for {app} squashed successfully"))
        
        else:
            raise CommandError("No command specified")
