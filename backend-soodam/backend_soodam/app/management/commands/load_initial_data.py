import csv
import os
from django.core.management.base import BaseCommand
from django.conf import settings

# Import your models
from app.models import (
    ProvinceModel, 
    CityModel, 
    MainCategoryModel, 
    SubCategoryModel,
    BooleanAttributeModel,
    TextAttributeModel,
    ChoiceAttributeModel
)


class Command(BaseCommand):
    help = "Load initial data from CSV files in the detail directory"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dir',
            type=str,
            default='detail',
            help='Directory containing CSV files (relative to project root)'
        )
        parser.add_argument(
            '--skip-existing',
            action='store_true',
            help='Skip loading data if the target model already has records'
        )

    def handle(self, *args, **options):
        base_dir = options['dir']
        skip_existing = options['skip_existing']
        
        # Get the absolute path to the detail directory
        detail_dir = os.path.join(settings.BASE_DIR, base_dir)
        
        # Load provinces
        self.load_provinces(detail_dir, skip_existing)
        
        # Load cities (depends on provinces)
        self.load_cities(detail_dir, skip_existing)
        
        # Load main categories
        self.load_main_categories(detail_dir, skip_existing)
        
        # Load sub categories (depends on main categories)
        self.load_sub_categories(detail_dir, skip_existing)
        
        # Load features if available
        self.load_features(detail_dir, skip_existing)
        
        self.stdout.write(self.style.SUCCESS("All initial data loaded successfully"))

    def load_provinces(self, detail_dir, skip_existing):
        """Load provinces from provinces.csv"""
        if skip_existing and ProvinceModel.objects.exists():
            self.stdout.write("Provinces already exist, skipping...")
            return
            
        provinces_csv = os.path.join(detail_dir, 'provinces.csv')
        
        try:
            with open(provinces_csv, "r", encoding="utf-8") as provinces_file:
                reader = csv.reader(provinces_file)
                # Skip the header row
                next(reader)
                # Create Province objects from the CSV data
                provinces = [
                    ProvinceModel(
                        province_id=province[0], 
                        name=province[1], 
                        slug=province[2], 
                        tel_prefix=province[3]
                    )
                    for province in reader
                ]
                # Bulk create all the Province objects in the database
                ProvinceModel.objects.bulk_create(provinces)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(provinces)} provinces"))
        except FileNotFoundError:
            self.stderr.write(f"File not found: {provinces_csv}")
        except Exception as e:
            self.stderr.write(f"Error loading provinces: {str(e)}")

    def load_cities(self, detail_dir, skip_existing):
        """Load cities from cities.csv"""
        if skip_existing and CityModel.objects.exists():
            self.stdout.write("Cities already exist, skipping...")
            return
            
        cities_csv = os.path.join(detail_dir, 'cities.csv')
        
        try:
            with open(cities_csv, "r", encoding="utf-8") as cities_file:
                reader = csv.reader(cities_file)
                # Skip the header row
                next(reader)
                # Create an empty list to hold the City objects
                cities = []
                for city in reader:
                    city_id = city[0]
                    city_name = city[1]
                    slug = city[2]
                    province_id = city[3]
                    
                    try:
                        # Get the corresponding Province object from the database
                        province = ProvinceModel.objects.get(province_id=province_id)
                        # Create a City object and add it to the list
                        cities.append(CityModel(
                            city_id=city_id,
                            name=city_name, 
                            province=province, 
                            slug=slug
                        ))
                    except ProvinceModel.DoesNotExist:
                        self.stderr.write(f"Province with ID {province_id} not found for city {city_name}")
                
                # Bulk create all the City objects in the database
                CityModel.objects.bulk_create(cities)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(cities)} cities"))
        except FileNotFoundError:
            self.stderr.write(f"File not found: {cities_csv}")
        except Exception as e:
            self.stderr.write(f"Error loading cities: {str(e)}")

    def load_main_categories(self, detail_dir, skip_existing):
        """Load main categories from main_category.csv"""
        if skip_existing and MainCategoryModel.objects.exists():
            self.stdout.write("Main categories already exist, skipping...")
            return
            
        main_category_csv = os.path.join(detail_dir, 'main_category.csv')
        
        try:
            with open(main_category_csv, "r", encoding="utf-8") as main_category_file:
                reader = csv.reader(main_category_file)
                # Skip the header row
                next(reader)
                # Create MainCategory objects from the CSV data
                main_categories = []
                for i, main_category in enumerate(reader, 1):
                    # Convert string 'True'/'False' to boolean
                    star = main_category[1].lower() == 'true'
                    
                    main_categories.append(MainCategoryModel(
                        id=i,  # Use row number as ID to match references in sub_category.csv
                        name=main_category[0],
                        star=star,
                        key=main_category[2],
                        description=main_category[3] if len(main_category) > 3 else "",
                        review=int(main_category[4]) if len(main_category) > 4 and main_category[4] else 0,
                        total_adv=int(main_category[5]) if len(main_category) > 5 and main_category[5] else 0
                    ))
                
                # Bulk create all the MainCategory objects in the database
                MainCategoryModel.objects.bulk_create(main_categories)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(main_categories)} main categories"))
        except FileNotFoundError:
            self.stderr.write(f"File not found: {main_category_csv}")
        except Exception as e:
            self.stderr.write(f"Error loading main categories: {str(e)}")

    def load_sub_categories(self, detail_dir, skip_existing):
        """Load sub categories from sub_category.csv"""
        if skip_existing and SubCategoryModel.objects.exists():
            self.stdout.write("Sub categories already exist, skipping...")
            return
            
        sub_category_csv = os.path.join(detail_dir, 'sub_category.csv')
        
        try:
            with open(sub_category_csv, "r", encoding="utf-8") as sub_category_file:
                reader = csv.reader(sub_category_file)
                # Skip the header row
                next(reader)
                # Create SubCategory objects from the CSV data
                sub_categories = []
                for sub_category in reader:
                    # Convert string 'True'/'False' to boolean
                    star = sub_category[1].lower() == 'true'
                    main_category_id = sub_category[6]
                    
                    try:
                        # Get the corresponding MainCategory object from the database
                        main_category = MainCategoryModel.objects.get(id=main_category_id)
                        # Create a SubCategory object and add it to the list
                        sub_categories.append(SubCategoryModel(
                            name=sub_category[0],
                            star=star,
                            key=sub_category[2],
                            description=sub_category[3] if len(sub_category) > 3 else "",
                            review=int(sub_category[4]) if len(sub_category) > 4 and sub_category[4] else 0,
                            total_adv=int(sub_category[5]) if len(sub_category) > 5 and sub_category[5] else 0,
                            main_category=main_category
                        ))
                    except MainCategoryModel.DoesNotExist:
                        self.stderr.write(f"Main category with ID {main_category_id} not found for sub category {sub_category[0]}")
                
                # Bulk create all the SubCategory objects in the database
                SubCategoryModel.objects.bulk_create(sub_categories)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(sub_categories)} sub categories"))
        except FileNotFoundError:
            self.stderr.write(f"File not found: {sub_category_csv}")
        except Exception as e:
            self.stderr.write(f"Error loading sub categories: {str(e)}")

    def load_features(self, detail_dir, skip_existing):
        """Load features from feature CSV files if they exist"""
        features_dir = os.path.join(detail_dir, 'features')
        
        # Check if features directory exists
        if not os.path.exists(features_dir):
            self.stdout.write("Features directory not found, skipping...")
            return
            
        # Load boolean attributes
        self.load_boolean_attributes(features_dir, skip_existing)
        
        # Load text attributes
        self.load_text_attributes(features_dir, skip_existing)
        
        # Load choice attributes
        self.load_choice_attributes(features_dir, skip_existing)
        
        # Load choice attribute values if available
        self.load_choice_attribute_values(features_dir, skip_existing)

    def load_boolean_attributes(self, features_dir, skip_existing):
        """Load boolean attributes from boolean_attribute.csv"""
        if skip_existing and BooleanAttributeModel.objects.exists():
            self.stdout.write("Boolean attributes already exist, skipping...")
            return
            
        boolean_attribute_csv = os.path.join(features_dir, 'boolean_attribute.csv')
        
        if not os.path.exists(boolean_attribute_csv):
            self.stdout.write("Boolean attributes file not found, skipping...")
            return
            
        try:
            with open(boolean_attribute_csv, "r", encoding="utf-8") as file:
                reader = csv.reader(file)
                # Skip the header row
                next(reader)
                # Create BooleanAttribute objects from the CSV data
                attributes = []
                for row in reader:
                    attributes.append(BooleanAttributeModel(
                        name=row[0],
                        key=row[1] if len(row) > 1 else "",
                        description=row[2] if len(row) > 2 else ""
                    ))
                
                # Bulk create all the BooleanAttribute objects in the database
                BooleanAttributeModel.objects.bulk_create(attributes)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(attributes)} boolean attributes"))
        except Exception as e:
            self.stderr.write(f"Error loading boolean attributes: {str(e)}")

    def load_text_attributes(self, features_dir, skip_existing):
        """Load text attributes from text_attribute.csv"""
        if skip_existing and TextAttributeModel.objects.exists():
            self.stdout.write("Text attributes already exist, skipping...")
            return
            
        text_attribute_csv = os.path.join(features_dir, 'text_attribute.csv')
        
        if not os.path.exists(text_attribute_csv):
            self.stdout.write("Text attributes file not found, skipping...")
            return
            
        try:
            with open(text_attribute_csv, "r", encoding="utf-8") as file:
                reader = csv.reader(file)
                # Skip the header row
                next(reader)
                # Create TextAttribute objects from the CSV data
                attributes = []
                for row in reader:
                    attributes.append(TextAttributeModel(
                        name=row[0],
                        key=row[1] if len(row) > 1 else "",
                        description=row[2] if len(row) > 2 else ""
                    ))
                
                # Bulk create all the TextAttribute objects in the database
                TextAttributeModel.objects.bulk_create(attributes)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(attributes)} text attributes"))
        except Exception as e:
            self.stderr.write(f"Error loading text attributes: {str(e)}")

    def load_choice_attributes(self, features_dir, skip_existing):
        """Load choice attributes from choice_attribute.csv"""
        if skip_existing and ChoiceAttributeModel.objects.exists():
            self.stdout.write("Choice attributes already exist, skipping...")
            return
            
        choice_attribute_csv = os.path.join(features_dir, 'choice_attribute.csv')
        
        if not os.path.exists(choice_attribute_csv):
            self.stdout.write("Choice attributes file not found, skipping...")
            return
            
        try:
            with open(choice_attribute_csv, "r", encoding="utf-8") as file:
                reader = csv.reader(file)
                # Skip the header row
                next(reader)
                # Create ChoiceAttribute objects from the CSV data
                attributes = []
                for row in reader:
                    attributes.append(ChoiceAttributeModel(
                        name=row[0],
                        key=row[1] if len(row) > 1 else "",
                        description=row[2] if len(row) > 2 else ""
                    ))
                
                # Bulk create all the ChoiceAttribute objects in the database
                ChoiceAttributeModel.objects.bulk_create(attributes)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(attributes)} choice attributes"))
        except Exception as e:
            self.stderr.write(f"Error loading choice attributes: {str(e)}")

    def load_choice_attribute_values(self, features_dir, skip_existing):
        """Load choice attribute values from values/choice_attribute_value.csv"""
        values_dir = os.path.join(features_dir, 'values')
        
        if not os.path.exists(values_dir):
            self.stdout.write("Choice attribute values directory not found, skipping...")
            return
            
        choice_attribute_value_csv = os.path.join(values_dir, 'choice_attribute_value.csv')
        
        if not os.path.exists(choice_attribute_value_csv):
            self.stdout.write("Choice attribute values file not found, skipping...")
            return
            
        try:
            # Import here to avoid circular imports
            from app.models import ChoiceAttributeValueModel
            
            if skip_existing and ChoiceAttributeValueModel.objects.exists():
                self.stdout.write("Choice attribute values already exist, skipping...")
                return
                
            with open(choice_attribute_value_csv, "r", encoding="utf-8") as file:
                reader = csv.reader(file)
                # Skip the header row
                next(reader)
                # Create ChoiceAttributeValue objects from the CSV data
                values = []
                for row in reader:
                    attribute_id = row[0]
                    value = row[1]
                    
                    try:
                        # Get the corresponding ChoiceAttribute object from the database
                        attribute = ChoiceAttributeModel.objects.get(id=attribute_id)
                        # Create a ChoiceAttributeValue object and add it to the list
                        values.append(ChoiceAttributeValueModel(
                            attribute=attribute,
                            value=value
                        ))
                    except ChoiceAttributeModel.DoesNotExist:
                        self.stderr.write(f"Choice attribute with ID {attribute_id} not found for value {value}")
                
                # Bulk create all the ChoiceAttributeValue objects in the database
                ChoiceAttributeValueModel.objects.bulk_create(values)
                self.stdout.write(self.style.SUCCESS(f"Imported {len(values)} choice attribute values"))
        except ImportError:
            self.stderr.write("ChoiceAttributeValueModel not found, skipping choice attribute values")
        except Exception as e:
            self.stderr.write(f"Error loading choice attribute values: {str(e)}")