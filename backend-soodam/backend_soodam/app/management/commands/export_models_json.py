from django.core.management.base import BaseCommand
from django.core import serializers
import os
import json
import datetime

class Command(BaseCommand):
    help = "Export specific models with relationships as JSON files"

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            default='fixtures',
            help='Directory to save JSON files'
        )

    def handle(self, *args, **options):
        output_dir = options['output_dir']

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # List of models to export
        models = [
            'app.MainCategoryModel',
            'app.SubCategoryModel',
            'app.SubCategoryLevelTwoModel',
            'app.ChoiceAttributeModel',
            'app.ChoiceOptionModel',
            'app.BooleanAttributeModel',
            'app.TextAttributeModel',
            'app.PropertyModel',
            'app.HighlightAttributeModel',
            'app.ProvinceModel',
            'app.CityModel'
        ]

        # Export each model
        for model_path in models:
            app_label, model_name = model_path.split('.')
            filename = f"{app_label}_{model_name.lower()}.json"
            filepath = os.path.join(output_dir, filename)
            
            self.stdout.write(f"Exporting {model_path} to {filepath}...")
            
            # Use dumpdata command to export with relationships
            from django.core.management import call_command
            from io import StringIO
            
            output = StringIO()
            call_command(
                'dumpdata', 
                model_path, 
                '--indent=2', 
                '--natural-foreign', 
                '--natural-primary',
                stdout=output
            )
            
            # Write to file
            with open(filepath, 'w') as f:
                f.write(output.getvalue())
            
            self.stdout.write(self.style.SUCCESS(f"Successfully exported {model_path}"))

        # Create a manifest file with metadata
        manifest = {
            'date': str(datetime.datetime.now()),
            'models': models,
            'files': [f"{model_path.split('.')[-2]}_{model_path.split('.')[-1].lower()}.json" for model_path in models]
        }
        
        with open(os.path.join(output_dir, 'manifest.json'), 'w') as f:
            json.dump(manifest, f, indent=2)

        self.stdout.write(self.style.SUCCESS(
            f"Successfully exported all models to {output_dir}"
        ))
