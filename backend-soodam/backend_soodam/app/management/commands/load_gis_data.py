import os
from django.core.management.base import BaseCommand
from django.conf import settings
from django.contrib.gis.utils import LayerMapping
from django.contrib.gis.gdal import DataSource


class Command(BaseCommand):
    help = "Load GIS data from shapefiles"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dir',
            type=str,
            default='gis_data',
            help='Directory containing GIS data files (relative to project root)'
        )
        parser.add_argument(
            '--skip-existing',
            action='store_true',
            help='Skip loading data if the target model already has records'
        )

    def handle(self, *args, **options):
        base_dir = options['dir']
        skip_existing = options['skip_existing']
        
        # Get the absolute path to the GIS data directory
        gis_data_dir = os.path.join(settings.BASE_DIR, base_dir)
        
        if not os.path.exists(gis_data_dir):
            self.stdout.write(f"GIS data directory {gis_data_dir} not found, skipping...")
            return
        
        # Load province boundaries if available
        self.load_province_boundaries(gis_data_dir, skip_existing)
        
        # Load city boundaries if available
        self.load_city_boundaries(gis_data_dir, skip_existing)
        
        # Load other GIS data if available
        self.load_other_gis_data(gis_data_dir, skip_existing)
        
        self.stdout.write(self.style.SUCCESS("GIS data loading completed"))

    def load_province_boundaries(self, gis_data_dir, skip_existing):
        """Load province boundaries from shapefile"""
        try:
            # Import here to avoid circular imports
            from app.models import ProvinceBoundaryModel
            
            if skip_existing and ProvinceBoundaryModel.objects.exists():
                self.stdout.write("Province boundaries already exist, skipping...")
                return
                
            province_shapefile = os.path.join(gis_data_dir, 'provinces', 'province_boundaries.shp')
            
            if not os.path.exists(province_shapefile):
                self.stdout.write("Province boundaries shapefile not found, skipping...")
                return
                
            # Define the mapping between shapefile fields and model fields
            mapping = {
                'province_id': 'PROVINCE_ID',
                'name': 'NAME',
                'geom': 'MULTIPOLYGON',
            }
            
            # Use LayerMapping to import the data
            lm = LayerMapping(ProvinceBoundaryModel, province_shapefile, mapping)
            lm.save(strict=True, verbose=True)
            
            self.stdout.write(self.style.SUCCESS("Province boundaries loaded successfully"))
        except ImportError:
            self.stderr.write("ProvinceBoundaryModel not found, skipping province boundaries")
        except Exception as e:
            self.stderr.write(f"Error loading province boundaries: {str(e)}")

    def load_city_boundaries(self, gis_data_dir, skip_existing):
        """Load city boundaries from shapefile"""
        try:
            # Import here to avoid circular imports
            from app.models import CityBoundaryModel
            
            if skip_existing and CityBoundaryModel.objects.exists():
                self.stdout.write("City boundaries already exist, skipping...")
                return
                
            city_shapefile = os.path.join(gis_data_dir, 'cities', 'city_boundaries.shp')
            
            if not os.path.exists(city_shapefile):
                self.stdout.write("City boundaries shapefile not found, skipping...")
                return
                
            # Define the mapping between shapefile fields and model fields
            mapping = {
                'city_id': 'CITY_ID',
                'name': 'NAME',
                'province_id': 'PROVINCE_ID',
                'geom': 'MULTIPOLYGON',
            }
            
            # Use LayerMapping to import the data
            lm = LayerMapping(CityBoundaryModel, city_shapefile, mapping)
            lm.save(strict=True, verbose=True)
            
            self.stdout.write(self.style.SUCCESS("City boundaries loaded successfully"))
        except ImportError:
            self.stderr.write("CityBoundaryModel not found, skipping city boundaries")
        except Exception as e:
            self.stderr.write(f"Error loading city boundaries: {str(e)}")

    def load_other_gis_data(self, gis_data_dir, skip_existing):
        """Load other GIS data from shapefiles"""
        # Add code to load other GIS data as needed
        pass