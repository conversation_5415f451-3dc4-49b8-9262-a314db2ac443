# Database Initialization Commands

This directory contains several Django management commands for initializing the database with data from CSV files and shapefiles.

## Available Commands

### `initialize_database`

A comprehensive command that runs migrations, creates fixtures, loads data from CSV files, and loads GIS data.

```bash
python manage.py initialize_database [options]
```

Options:
- `--skip-existing`: Skip loading data if the target model already has records
- `--skip-migrations`: Skip running migrations
- `--skip-fixtures`: Skip creating fixtures
- `--skip-gis`: Skip loading GIS data
- `--detail-dir`: Directory containing CSV files (default: 'detail')
- `--gis-dir`: Directory containing GIS data files (default: 'gis_data')
- `--fixtures-dir`: Directory to save fixture files (default: 'app/fixtures')

### `load_initial_data`

Loads data directly from CSV files into the database.

```bash
python manage.py load_initial_data [options]
```

Options:
- `--dir`: Directory containing CSV files (default: 'detail')
- `--skip-existing`: Skip loading data if the target model already has records

### `create_fixtures_from_csv`

Creates Django fixture files from CSV files.

```bash
python manage.py create_fixtures_from_csv [options]
```

Options:
- `--dir`: Directory containing CSV files (default: 'detail')
- `--output-dir`: Directory to save fixture files (default: 'app/fixtures')

### `load_gis_data`

Loads GIS data from shapefiles.

```bash
python manage.py load_gis_data [options]
```

Options:
- `--dir`: Directory containing GIS data files (default: 'gis_data')
- `--skip-existing`: Skip loading data if the target model already has records

## CSV File Structure

The commands expect CSV files with the following structure:

### `provinces.csv`
```
province_id,name,slug,tel_prefix
1,Tehran,tehran,021
2,Isfahan,isfahan,031
...
```

### `cities.csv`
```
city_id,name,slug,province_id
1,Tehran,tehran,1
2,Karaj,karaj,2
...
```

### `main_category.csv`
```
name,star,key,description,review,total_adv
Real Estate,true,real-estate,Properties for sale and rent,100,5000
Vehicles,true,vehicles,Cars and motorcycles,200,3000
...
```

### `sub_category.csv`
```
name,star,key,description,review,total_adv,main_category_id
Apartments,true,apartments,Apartments for sale and rent,50,2500,1
Houses,true,houses,Houses for sale and rent,30,1500,1
...
```

## Feature CSV Files

Feature CSV files should be placed in the `features` subdirectory:

### `boolean_attribute.csv`
```
name,key,description
Parking,parking,Has parking space
Elevator,elevator,Has elevator
...
```

### `text_attribute.csv`
```
name,key,description
Area,area,Property area in square meters
Year Built,year-built,Year the property was built
...
```

### `choice_attribute.csv`
```
name,key,description
Property Type,property-type,Type of property
Heating System,heating-system,Type of heating system
...
```

### `values/choice_attribute_value.csv`
```
attribute_id,value
1,Apartment
1,House
1,Villa
2,Central Heating
2,Radiator
2,Floor Heating
...
```

## GIS Data Structure

GIS data should be organized in the following structure:

```
gis_data/
├── provinces/
│   └── province_boundaries.shp
│   └── province_boundaries.shx
│   └── province_boundaries.dbf
│   └── province_boundaries.prj
├── cities/
│   └── city_boundaries.shp
│   └── city_boundaries.shx
│   └── city_boundaries.dbf
│   └── city_boundaries.prj
```