import os
import json
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import transaction
from django.apps import apps

class Command(BaseCommand):
    help = "Initialize models with data from fixtures or detail folder"

    def add_arguments(self, parser):
        parser.add_argument(
            '--source',
            choices=['fixtures', 'detail'],
            default='fixtures',
            help='Source of initialization data (fixtures or detail folder)'
        )
        parser.add_argument(
            '--fixtures-dir',
            default='fixtures',
            help='Directory containing fixture files'
        )
        parser.add_argument(
            '--detail-dir',
            default='app/detail',
            help='Directory containing detail files'
        )
        parser.add_argument(
            '--skip-existing',
            action='store_true',
            help='Skip models that already have data'
        )

    def handle(self, *args, **options):
        source = options['source']
        fixtures_dir = options['fixtures_dir']
        detail_dir = options['detail_dir']
        skip_existing = options['skip_existing']

        # Define the order of models to initialize (respecting dependencies)
        model_order = [
            'MainCategoryModel',
            'ProvinceModel',
            'SubCategoryModel',
            'CityModel',
            'BooleanAttributeModel',
            'TextAttributeModel',
            'ChoiceAttributeModel',
            'SubCategoryLevelTwoModel',
            'ChoiceOptionModel',
            'PropertyModel',
            'HighlightAttributeModel',
        ]

        if source == 'fixtures':
            self._initialize_from_fixtures(fixtures_dir, model_order, skip_existing)
        else:
            self._initialize_from_detail(detail_dir, skip_existing)

    @transaction.atomic
    def _initialize_from_fixtures(self, fixtures_dir, model_order, skip_existing):
        """Initialize models from fixture files"""
        self.stdout.write("Initializing models from fixtures...")

        # Check if fixtures directory exists
        if not os.path.isdir(fixtures_dir):
            self.stderr.write(self.style.ERROR(f"Fixtures directory not found: {fixtures_dir}"))
            return

        # Get all fixture files
        fixture_files = {}
        for file in os.listdir(fixtures_dir):
            if file.endswith('.json') and file != 'manifest.json':
                model_name = file.replace('app_', '').replace('.json', '')
                fixture_files[model_name.lower()] = os.path.join(fixtures_dir, file)

        # Load fixtures in the correct order
        for model_name in model_order:
            model_name_lower = model_name.lower()
            
            # Check if fixture file exists
            if model_name_lower not in fixture_files:
                self.stdout.write(f"No fixture file found for {model_name}, skipping...")
                continue
            
            # Check if model already has data
            model = apps.get_model('app', model_name)
            if skip_existing and model.objects.exists():
                self.stdout.write(f"{model_name} already has data, skipping...")
                continue
            
            # Load fixture
            fixture_file = fixture_files[model_name_lower]
            self.stdout.write(f"Loading {model_name} from {fixture_file}...")
            
            try:
                call_command('loaddata', fixture_file)
                self.stdout.write(self.style.SUCCESS(f"Successfully loaded {model_name}"))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error loading {model_name}: {str(e)}"))

        self.stdout.write(self.style.SUCCESS("Model initialization from fixtures completed"))

    @transaction.atomic
    def _initialize_from_detail(self, detail_dir, skip_existing):
        """Initialize models from detail folder"""
        self.stdout.write("Initializing models from detail folder...")

        # Check if detail directory exists
        if not os.path.isdir(detail_dir):
            self.stderr.write(self.style.ERROR(f"Detail directory not found: {detail_dir}"))
            return

        # First, create fixtures from CSV files if they exist
        try:
            if os.path.exists(os.path.join(detail_dir, 'csv')):
                self.stdout.write("Creating fixtures from CSV files...")
                call_command('create_fixtures_from_csv', dir=os.path.join(detail_dir, 'csv'), output_dir='fixtures')
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error creating fixtures from CSV: {str(e)}"))
            # Continue anyway, as we might have JSON files

        # Check for JSON files in the detail directory
        json_files = {}
        for file in os.listdir(detail_dir):
            if file.endswith('.json'):
                model_name = file.replace('.json', '')
                json_files[model_name.lower()] = os.path.join(detail_dir, file)

        # Load JSON files if they exist
        if json_files:
            for model_name, json_file in json_files.items():
                self.stdout.write(f"Loading {model_name} from {json_file}...")
                try:
                    call_command('loaddata', json_file)
                    self.stdout.write(self.style.SUCCESS(f"Successfully loaded {model_name}"))
                except Exception as e:
                    self.stderr.write(self.style.ERROR(f"Error loading {model_name}: {str(e)}"))

        # Finally, create relationships for PropertyModel
        self._create_property_relationships(skip_existing)

    def _create_property_relationships(self, skip_existing):
        """Create relationships for PropertyModel"""
        from app.models import (
            MainCategoryModel, SubCategoryModel, SubCategoryLevelTwoModel,
            BooleanAttributeModel, TextAttributeModel, ChoiceAttributeModel,
            PropertyModel
        )

        self.stdout.write("Creating PropertyModel relationships...")

        # Check if PropertyModel already has data with relationships
        if skip_existing and PropertyModel.objects.filter(boolean_attributes__isnull=False).exists():
            self.stdout.write("PropertyModel already has relationships, skipping...")
            return

        try:
            # Create relationships for residential properties
            residential = MainCategoryModel.objects.get(key="residential-property")
            apartment = SubCategoryModel.objects.get(key="apartment")
            apartment_sale = SubCategoryLevelTwoModel.objects.get(key="apartment-sale")

            # Create property model
            property_model, created = PropertyModel.objects.get_or_create(
                main_category=residential,
                sub_category=apartment,
                sub_category_level_two=apartment_sale,
                defaults={'is_active': True}
            )

            # Add boolean attributes
            boolean_attrs = BooleanAttributeModel.objects.filter(
                key__in=["parking", "elevator", "storage"]
            )
            property_model.boolean_attributes.add(*boolean_attrs)

            # Add text attributes
            text_attrs = TextAttributeModel.objects.filter(
                key__in=["area", "year-built"]
            )
            property_model.text_attributes.add(*text_attrs)

            # Add choice attributes
            choice_attrs = ChoiceAttributeModel.objects.filter(
                key__in=["rooms", "floor"]
            )
            property_model.choice_attributes.add(*choice_attrs)

            self.stdout.write(self.style.SUCCESS(
                f"Successfully created relationships for {apartment.name}"
            ))

            # Repeat for other property types as needed
            # Commercial properties
            commercial = MainCategoryModel.objects.get(key="commercial-property")
            office = SubCategoryModel.objects.get(key="office")
            office_sale = SubCategoryLevelTwoModel.objects.get(key="office-sale")

            commercial_property, created = PropertyModel.objects.get_or_create(
                main_category=commercial,
                sub_category=office,
                sub_category_level_two=office_sale,
                defaults={'is_active': True}
            )

            commercial_property.boolean_attributes.add(*boolean_attrs)
            commercial_property.text_attributes.add(*text_attrs)
            commercial_property.choice_attributes.add(*choice_attrs)

            self.stdout.write(self.style.SUCCESS(
                f"Successfully created relationships for {office.name}"
            ))

        except Exception as e:
            self.stderr.write(self.style.ERROR(
                f"Error creating PropertyModel relationships: {str(e)}"
            ))

        self.stdout.write(self.style.SUCCESS("PropertyModel relationships created"))
