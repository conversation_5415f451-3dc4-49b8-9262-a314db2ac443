"""
Management command for extracting translatable messages.
"""

import os
import subprocess
from typing import Any, List, Optional

from django.conf import settings
from django.core.management.base import BaseCommand, CommandError

class Command(BaseCommand):
    help = "Extract translatable messages from the codebase"
    
    def add_arguments(self, parser):
        parser.add_argument(
            "--locale",
            "-l",
            dest="locale",
            help="Locale to process (e.g., en, fa, ar, tr)",
        )
        parser.add_argument(
            "--domain",
            "-d",
            dest="domain",
            default="messages",
            help="Translation domain (default: messages)",
        )
        parser.add_argument(
            "--all",
            "-a",
            dest="all",
            action="store_true",
            help="Process all locales",
        )
        parser.add_argument(
            "--update",
            "-u",
            dest="update",
            action="store_true",
            help="Update existing translations",
        )
    
    def handle(self, *args: Any, **options: Any) -> Optional[str]:
        locale = options.get("locale")
        domain = options.get("domain", "messages")
        process_all = options.get("all", False)
        update = options.get("update", False)
        
        # Available locales
        available_locales = ["en", "fa", "ar", "tr"]
        
        # Determine locales to process
        locales_to_process = []
        if process_all:
            locales_to_process = available_locales
        elif locale:
            if locale not in available_locales:
                raise CommandError(f"Invalid locale: {locale}")
            locales_to_process = [locale]
        else:
            raise CommandError("No locale specified. Use --locale or --all")
        
        # Create locale directories
        locale_dir = os.path.join(settings.BASE_DIR, "locale")
        os.makedirs(locale_dir, exist_ok=True)
        
        for locale in locales_to_process:
            locale_path = os.path.join(locale_dir, locale, "LC_MESSAGES")
            os.makedirs(locale_path, exist_ok=True)
            
            # Extract messages
            self.stdout.write(f"Extracting messages for locale: {locale}")
            
            # Find Python files
            python_files = []
            for root, _, files in os.walk(settings.BASE_DIR):
                for file in files:
                    if file.endswith(".py"):
                        python_files.append(os.path.join(root, file))
            
            # Create POT file
            pot_file = os.path.join(locale_dir, f"{domain}.pot")
            xgettext_cmd = [
                "xgettext",
                "--language=Python",
                "--keyword=_",
                "--keyword=_lazy",
                "--keyword=gettext",
                "--keyword=ngettext:1,2",
                "--keyword=pgettext:1c,2",
                "--keyword=npgettext:1c,2,3",
                "--output=" + pot_file,
                "--from-code=UTF-8",
                "--add-comments=Translators:",
                "--package-name=soodam",
                "--package-version=1.0.0",
                "--msgid-bugs-address=<EMAIL>",
                "--copyright-holder=Soodam",
            ]
            xgettext_cmd.extend(python_files)
            
            try:
                subprocess.run(xgettext_cmd, check=True)
                self.stdout.write(f"Created POT file: {pot_file}")
            except subprocess.CalledProcessError as e:
                self.stderr.write(f"Error creating POT file: {e}")
                continue
            
            # Create or update PO file
            po_file = os.path.join(locale_path, f"{domain}.po")
            
            if os.path.exists(po_file) and update:
                # Update existing PO file
                msgmerge_cmd = [
                    "msgmerge",
                    "--update",
                    "--backup=none",
                    po_file,
                    pot_file,
                ]
                
                try:
                    subprocess.run(msgmerge_cmd, check=True)
                    self.stdout.write(f"Updated PO file: {po_file}")
                except subprocess.CalledProcessError as e:
                    self.stderr.write(f"Error updating PO file: {e}")
                    continue
            else:
                # Create new PO file
                msginit_cmd = [
                    "msginit",
                    "--input=" + pot_file,
                    "--output=" + po_file,
                    "--locale=" + locale,
                    "--no-translator",
                ]
                
                try:
                    subprocess.run(msginit_cmd, check=True)
                    self.stdout.write(f"Created PO file: {po_file}")
                except subprocess.CalledProcessError as e:
                    self.stderr.write(f"Error creating PO file: {e}")
                    continue
            
            # Compile PO file to MO file
            mo_file = os.path.join(locale_path, f"{domain}.mo")
            msgfmt_cmd = [
                "msgfmt",
                "--output-file=" + mo_file,
                po_file,
            ]
            
            try:
                subprocess.run(msgfmt_cmd, check=True)
                self.stdout.write(f"Compiled MO file: {mo_file}")
            except subprocess.CalledProcessError as e:
                self.stderr.write(f"Error compiling MO file: {e}")
                continue
        
        self.stdout.write(self.style.SUCCESS("Message extraction completed"))
