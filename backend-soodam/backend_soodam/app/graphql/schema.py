"""
GraphQL schema for the Soodam backend.
"""

import graphene
from graphene_django import DjangoObjectType
from graphene_django.filter import DjangoFilterConnectionField
from graphql import GraphQLError
from graphene.types.scalars import Scalar
from django.contrib.gis.geos import Point

from app.models.advertisement import (
    AdvertisementModel,
    MainCategoryModel as AdvertisementCategoryModel,
    AdvertisementImagesModel as AdvertisementImageModel,
    PropertyTextValueModel as AdvertisementAttributeModel,
    AdvertisementViewModel as AdvertisementStatisticsModel,
    AdvertisementTagModel
    # There's no direct equivalent for these in advertising.py, so we'll need to create them
    # or modify the GraphQL schema to not use them
)
from app.enums.status import AdvertisementStatus
from app.models.advertisement import  AdvertisementLocationModel

# Define custom scalar for PointField
class PointScalar(Scalar):
    """
    Custom scalar for Django's PointField.
    """
    @staticmethod
    def serialize(point):
        if point is None:
            return None
        return {
            'longitude': point.x,
            'latitude': point.y
        }

# Import or define AdvertisementTagModel
from django.db import models

# class AdvertisementTagModel(models.Model):
#     """
#     Temporary model for advertisement tags.
#
#     This model is used by the GraphQL API to represent advertisement tags.
#     """
#     name = models.CharField(max_length=100, verbose_name="Name")
#
#     class Meta:
#         managed = False  # This model won't be managed by Django migrations
#
#     def __str__(self):
#         return self.name
from app.models.user import CustomUserModel


class UserType(DjangoObjectType):
    """GraphQL type for CustomUserModel."""

    class Meta:
        model = CustomUserModel
        fields = (
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "is_active",
            "date_joined",
            "last_login",
        )


class AdvertisementCategoryType(DjangoObjectType):
    """GraphQL type for AdvertisementCategoryModel."""

    class Meta:
        model = AdvertisementCategoryModel
        fields = [
            "id", "name", "star", "key", "description",
            "review", "total_adv", "icon", "created_at", "updated_at"
        ]
        filter_fields = {
            "id": ["exact"],
            "name": ["exact", "icontains"],
            "star": ["exact"],
            "key": ["exact", "icontains"],
        }
        interfaces = (graphene.relay.Node,)


class AdvertisementLocationType(DjangoObjectType):
    """GraphQL type for AdvertisementLocationModel."""

    geolocation = graphene.Field(PointScalar)

    class Meta:
        model = AdvertisementLocationModel
        fields = [
            "id", "province", "city", "street", "address",
            "zip_code", "srid", "longitude", "latitude"
        ]
        filter_fields = {
            "id": ["exact"],
            "city": ["exact"],
            "province": ["exact"],
        }
        interfaces = (graphene.relay.Node,)

    def resolve_geolocation(self, info):
        if self.geolocation:
            return {
                'longitude': self.geolocation.x,
                'latitude': self.geolocation.y
            }
        return None


class AdvertisementImageType(DjangoObjectType):
    """GraphQL type for AdvertisementImageModel."""

    class Meta:
        model = AdvertisementImageModel
        fields = [
            "id", "advertisement", "image", "url",
        ]
        filter_fields = {
            "id": ["exact"],
            "advertisement_id": ["exact"],
        }
        interfaces = (graphene.relay.Node,)


class AdvertisementAttributeType(DjangoObjectType):
    """GraphQL type for AdvertisementAttributeModel."""

    class Meta:
        model = AdvertisementAttributeModel
        fields = [
            "id", "property", "attribute", "value"
        ]
        filter_fields = {
            "id": ["exact"],
            "property_id": ["exact"],
            "attribute_id": ["exact"],
        }
        interfaces = (graphene.relay.Node,)


class AdvertisementTagType(DjangoObjectType):
    """GraphQL type for AdvertisementTagModel."""

    class Meta:
        model = AdvertisementTagModel
        fields = ["id", "name"]
        filter_fields = {
            "id": ["exact"],
            "name": ["exact", "icontains"],
        }
        interfaces = (graphene.relay.Node,)


class AdvertisementStatisticsType(DjangoObjectType):
    """GraphQL type for AdvertisementStatisticsModel."""

    class Meta:
        model = AdvertisementStatisticsModel
        fields = [
            "id", "advertisement", "user", "created_at", "modified_at"
        ]
        filter_fields = {
            "id": ["exact"],
            "advertisement_id": ["exact"],
            "user_id": ["exact"],
        }
        interfaces = (graphene.relay.Node,)


class AdvertisementType(DjangoObjectType):
    """GraphQL type for AdvertisementModel."""

    class Meta:
        model = AdvertisementModel
        fields = [
            "id", "status", "security_code_owner_building", "phone_number_owner_building",
            "title", "description", "main_category", "sub_category", "sub_category_level_two",
            "user", "address", "review", "is_active", "choice_attributes", "boolean_attributes",
            "text_attributes", "created_at", "updated_at"
        ]
        filter_fields = {
            "id": ["exact"],
            "title": ["exact", "icontains"],
            "status": ["exact"],
            "main_category": ["exact"],
            "sub_category": ["exact"],
            "user": ["exact"],
            "is_active": ["exact"],
            "created_at": ["exact", "gt", "lt", "gte", "lte"],
        }
        interfaces = (graphene.relay.Node,)


class Query(graphene.ObjectType):
    """GraphQL queries."""

    # Relay queries
    category = graphene.relay.Node.Field(AdvertisementCategoryType)
    all_categories = DjangoFilterConnectionField(AdvertisementCategoryType)

    location = graphene.relay.Node.Field(AdvertisementLocationType)
    all_locations = DjangoFilterConnectionField(AdvertisementLocationType)

    advertisement = graphene.relay.Node.Field(AdvertisementType)
    all_advertisements = DjangoFilterConnectionField(AdvertisementType)

    # Custom queries
    advertisement_by_id = graphene.Field(
        AdvertisementType,
        id=graphene.Int(required=True)
    )


    advertisements_by_category = graphene.List(
        AdvertisementType,
        category_id=graphene.Int(required=True),
        limit=graphene.Int(default_value=10)
    )

    advertisements_by_location = graphene.List(
        AdvertisementType,
        location_id=graphene.Int(required=True),
        limit=graphene.Int(default_value=10)
    )

    search_advertisements = graphene.List(
        AdvertisementType,
        query=graphene.String(required=True),
        limit=graphene.Int(default_value=10)
    )

    featured_advertisements = graphene.List(
        AdvertisementType,
        limit=graphene.Int(default_value=10)
    )

    def resolve_advertisement_by_id(self, info, id):
        """Resolve advertisement by ID."""
        try:
            return AdvertisementModel.objects.get(id=id)
        except AdvertisementModel.DoesNotExist:
            raise GraphQLError(f"Advertisement with ID {id} does not exist")

    def resolve_advertisements_by_category(self, info, category_id, limit):
        """Resolve advertisements by category."""
        return AdvertisementModel.objects.filter(
            main_category_id=category_id,
            status=AdvertisementStatus.APPROVED
        ).order_by("-created_at")[:limit]

    def resolve_advertisements_by_location(self, info, location_id, limit):
        """Resolve advertisements by location."""
        return AdvertisementModel.objects.filter(
            address_id=location_id,
            status=AdvertisementStatus.APPROVED
        ).order_by("-created_at")[:limit]

    def resolve_search_advertisements(self, info, query, limit):
        """Resolve search advertisements."""
        return AdvertisementModel.objects.filter(
            title__icontains=query,
            status=AdvertisementStatus.APPROVED
        ).order_by("-created_at")[:limit]

    def resolve_featured_advertisements(self, info, limit):
        """Resolve featured advertisements."""
        # Since there's no is_featured field, we'll use is_active instead
        return AdvertisementModel.objects.filter(
            is_active=True,
            status=AdvertisementStatus.APPROVED
        ).order_by("-created_at")[:limit]


class CreateAdvertisementMutation(graphene.Mutation):
    """Mutation to create an advertisement."""

    class Arguments:
        title = graphene.String(required=True)
        description = graphene.String(required=True)
        category_id = graphene.Int(required=True)
        location_id = graphene.Int(required=True)

    advertisement = graphene.Field(AdvertisementType)

    @staticmethod
    def mutate(root, info, title, description, category_id, location_id):
        """Mutate to create an advertisement."""
        user = info.context.user

        if not user.is_authenticated:
            raise GraphQLError("You must be logged in to create an advertisement")

        advertisement = AdvertisementModel(
            title=title,
            description=description,
            main_category_id=category_id,
            address_id=location_id,
            user=user,
            status=AdvertisementStatus.PENDING
        )
        advertisement.save()

        return CreateAdvertisementMutation(advertisement=advertisement)


class UpdateAdvertisementMutation(graphene.Mutation):
    """Mutation to update an advertisement."""

    class Arguments:
        id = graphene.Int(required=True)
        title = graphene.String()
        description = graphene.String()
        category_id = graphene.Int()
        location_id = graphene.Int()

    advertisement = graphene.Field(AdvertisementType)

    @staticmethod
    def mutate(root, info, id, **kwargs):
        """Mutate to update an advertisement."""
        user = info.context.user

        if not user.is_authenticated:
            raise GraphQLError("You must be logged in to update an advertisement")

        try:
            advertisement = AdvertisementModel.objects.get(id=id)
        except AdvertisementModel.DoesNotExist:
            raise GraphQLError(f"Advertisement with ID {id} does not exist")

        if advertisement.user != user and not user.is_staff:
            raise GraphQLError("You don't have permission to update this advertisement")

        for key, value in kwargs.items():
            setattr(advertisement, key, value)

        advertisement.save()

        return UpdateAdvertisementMutation(advertisement=advertisement)


class DeleteAdvertisementMutation(graphene.Mutation):
    """Mutation to delete an advertisement."""

    class Arguments:
        id = graphene.Int(required=True)

    success = graphene.Boolean()

    @staticmethod
    def mutate(root, info, id):
        """Mutate to delete an advertisement."""
        user = info.context.user

        if not user.is_authenticated:
            raise GraphQLError("You must be logged in to delete an advertisement")

        try:
            advertisement = AdvertisementModel.objects.get(id=id)
        except AdvertisementModel.DoesNotExist:
            raise GraphQLError(f"Advertisement with ID {id} does not exist")

        if advertisement.user != user and not user.is_staff:
            raise GraphQLError("You don't have permission to delete this advertisement")

        advertisement.delete()

        return DeleteAdvertisementMutation(success=True)


class Mutation(graphene.ObjectType):
    """GraphQL mutations."""

    create_advertisement = CreateAdvertisementMutation.Field()
    update_advertisement = UpdateAdvertisementMutation.Field()
    delete_advertisement = DeleteAdvertisementMutation.Field()


schema = graphene.Schema(query=Query, mutation=Mutation)
