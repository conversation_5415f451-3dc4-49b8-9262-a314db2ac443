from datetime import date
from uuid import UUID
from jdatetime import date as jdate
# from pydantic import BaseModel, Field, validator, EmailStr
import re
from typing import Optional
from app.schemas.gis_geolocation import AddressReadSchema, CustomUserLocationEditSchema

from datetime import date
from datetime import datetime
from typing import Optional, List
from pydantic import  Field

from pydantic import BaseModel,validator
from pydantic import Extra


class Admin(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    password: str
    is_admin: bool
    user_type: int
    is_superuser: bool
    avatar: str
    phone_number: str
    is_staff: int
    created_at: date = None
    updated_at: date = None

    class Config:
        from_attributes = True


class UserReferenceObjectSchema(BaseModel):
    pass


class ReadUserInfoSchema(BaseModel):
    id: int
    first_name: str

    class Config:
        from_attributes = True
        extra = Extra.allow


class ReadUserSchema(BaseModel):
    id: int
    uuid: UUID
    username: str
    first_name: str
    last_name: str
    email: str
    is_active: bool
    is_admin: bool

    # groups:list = []

    class Config:
        from_attributes = True
        extra = Extra.allow


class ReadUsersSchema(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone_number: str
    is_active: bool
    is_admin: bool

    # groups:list = []

    class Config:
        from_attributes = True


class ReadUserListSchema(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone_number: str
    is_active: bool

    # groups:list = []

    class Config:
        from_attributes = True


class EditUserSchema(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    phone_number: str
    is_active: bool
    # groups:list = []


class CreateUserSchema(BaseModel):
    first_name: str
    last_name: str
    email: str
    password: str
    # avatar: str = ''
    phone_number: str
    is_active: bool

    # groups:list = []

    class Config:
        extra = Extra.allow


class UploadFilesSchema(BaseModel):
    first_name: str = ''


class DeleteUserSchema(BaseModel):
    id: int


class CustomUserSchema(BaseModel):
    pass


class CustomUserReadSellerInfoSchema(BaseModel):
    id: int
    phone_number: Optional[str] = ''
    first_name: Optional[str] = ''
    last_name: Optional[str] = ''
    class Config:
        from_attributes = True

    @validator('phone_number', 'first_name', 'last_name', pre=True, always=True)
    def none_to_empty(cls, v):
            return v or ''

class EditUserWalletSchema(BaseModel):
    pass


class EditUserSubScriptionSchema(BaseModel):
    pass

class EditUserInfoSchema(BaseModel):
    phone_number: str = Field(..., description="Phone number is required")
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    father_name: Optional[str] = None
    security_number: Optional[str] = None
    email: Optional[str] = None
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    full_address: Optional[CustomUserLocationEditSchema] = None
    birthday: Optional[str] = None
    gender: Optional[str] = None
    avatar: Optional[str] = None

    @validator('security_number')
    def validate_security_number(cls, v):
        if v is not None and not re.match(r'^\d{10}$', v):
            raise ValueError('Security number must be 10 digits')
        return v

    @validator('birthday')
    def validate_birthday(cls, v):
        if v is not None and not re.match(r'^\d{4}-\d{2}-\d{2}$', v):
            raise ValueError('Birthday must be in YYYY-MM-DD format')
        return v

    @validator('email')
    def validate_email(cls, v):
        if v is not None and not re.match(r"[^@]+@[^@]+\.[^@]+", v):
            raise ValueError('Invalid email format')
        return v

    @validator('phone_number')
    def validate_phone_number(cls, v):
        if v is not None and not re.match(r"^09[0-9]{9}$", v):
            raise ValueError('Phone number must start with 09 and be 11 digits')
        return v

    class Config:
        from_attributes = True
        extra = Extra.allow


class EditUserWalletSchema(BaseModel):
    pass


class EditSubScriptionSchema(BaseModel):
    pass


class Token(BaseModel):
    token: str
    # username: str
    # is_admin: bool
    # is_active: bool
    # user_group: int
    # is_superuser: bool
    token_type: str


class UserPaymentSchema(BaseModel):
    id: int
    payment_id: str
    reference_number: str
    sale_reference_id: str
    status_payment: bool
    payment_finished: bool
    amount: int
    bank_name: str
    # user:read
    buy_datetime: date = None


class SubscriptionPlanSchema(BaseModel):
    """Schema for subscription plans."""

    id: int = Field(..., description="Plan ID")
    name: str = Field(..., description="Plan name")
    description: str = Field(..., description="Plan description")
    price: float = Field(..., description="Plan price")
    duration_days: int = Field(..., description="Duration in days")
    features: List[str] = Field(..., description="Plan features")
    max_advertisements: int = Field(..., description="Maximum number of advertisements")
    is_featured: bool = Field(..., description="Whether advertisements are featured")
    priority_placement: bool = Field(..., description="Whether advertisements get priority placement")

    class Config:
        from_attributes = True


class UserSubscriptionSchema(BaseModel):
    """Schema for user subscriptions."""

    id: int = Field(..., description="Subscription ID")
    plan: SubscriptionPlanSchema = Field(..., description="Subscription plan")
    start_date: datetime = Field(..., description="Start date")
    end_date: datetime = Field(..., description="End date")
    is_active: bool = Field(..., description="Whether the subscription is active")
    remaining_advertisements: int = Field(..., description="Remaining advertisements")

    class Config:
        from_attributes = True
