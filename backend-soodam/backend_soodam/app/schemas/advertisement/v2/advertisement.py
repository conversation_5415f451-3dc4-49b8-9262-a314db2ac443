"""
Consolidated Advertisement Schemas for the Soodam backend.

This module provides schema definitions for advertisement operations, combining
functionality from the original advertising.py and advertisement_v2.py files.
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Dict, List, Optional, Union
from uuid import UUID

import jdatetime
from pydantic import BaseModel, Extra, Field, validator

from app.enums.status import AdvertisementStatus
from app.schemas.advertisement.v1.advertisement import AdvertisementPriceSchema, AdvertisementContactSchema, \
    AdvertisementCategorySchema, AdvertisementLocationSchema, AdvertisementAttributeSchema, AdvertisementImageSchema, \
    AdvertisementUserSchema, AdvertisementStatisticsSchema, AdvertisementVideoSchema

class AdvertisementLocationSchemaV2(BaseModel):

    # country_id: int = Field(..., description="Country ID", min_length=3, max_length=100)
    province_id: int = Field(..., description="Province ID")
    city_id: int = Field(..., description="City ID")
    address: str = Field(..., description="Address ")
    street: Optional[str] = Field(..., description="street ")
    zip_code: str = Field(..., description="Zip Code", min_length=10,max_length=10)
    longitude: float = Field(..., description="Longitude ")
    latitude: float = Field(..., description="Latitude ")

class AdvertisementCreateSchemaV2(BaseModel):
    """Schema for creating an advertisement in API v2."""
    title: str = Field(..., description="Advertisement title", min_length=3, max_length=100)
    description: str = Field(..., description="Advertisement description", min_length=10)
    security_code_owner_building: str = Field(..., description="Security code owner building")
    phone_number_owner_building: str = Field(..., description="Phone number owner building")
    category_id: int = Field(..., description="Category ID")
    category_id_lvl_2: int = Field(..., description="Sub Category Level Two ID")
    price: AdvertisementPriceSchema = Field(..., description="Price information")
    full_address: Optional[AdvertisementLocationSchemaV2] = Field(None, description="Full address")
    # attributes: Optional[List[Dict[str, str]]] = Field(None, description="Advertisement attributes")
    attributes: Optional[List[Dict]] = Field(None, description="Advertisement attributes")
    images: Optional[List[AdvertisementImageSchema]] = Field(None, description="Advertisement images")
    videos: Optional[List[AdvertisementVideoSchema]] = Field(None, description="Advertisement videos")
    # contact: Optional[AdvertisementContactSchema] = Field(None, description="Contact information")
    # is_featured: Optional[bool] = Field(False, description="Whether the advertisement is featured")
    tags: Optional[List[str]] = Field(None, description="Advertisement tags")
    expiry_date: Optional[datetime] = Field(None, description="Expiry date")

class AdvertisementUpdateSchemaV2(BaseModel):
    """Schema for updating an advertisement in API v2."""
    # title: Optional[str] = Field(None, description="Advertisement title", min_length=3, max_length=100)
    # description: Optional[str] = Field(None, description="Advertisement description", min_length=10)
    # category_id: Optional[int] = Field(None, description="Category ID")
    # price: Optional[AdvertisementPriceSchema] = Field(None, description="Price information")
    # location_id: Optional[int] = Field(None, description="Location ID")
    # attributes: Optional[List[Dict[str, str]]] = Field(None, description="Advertisement attributes")
    # images: Optional[List[Dict[str, Union[str, bool, int]]]] = Field(None, description="Advertisement images")
    # videos: Optional[List[Dict[str, Union[str, bool, int]]]] = Field(None, description="Advertisement videos")
    # contact: Optional[AdvertisementContactSchema] = Field(None, description="Contact information")
    # is_featured: Optional[bool] = Field(None, description="Whether the advertisement is featured")
    # tags: Optional[List[str]] = Field(None, description="Advertisement tags")
    # expiry_date: Optional[datetime] = Field(None, description="Expiry date")
    # status: Optional[AdvertisementStatus] = Field(None, description="Advertisement status")
    title: Optional[str] = None
    description: Optional[str] = None
    security_code_owner_building: Optional[str] = None
    phone_number_owner_building: Optional[str] = None
    category_id: Optional[int] = None
    price: Optional[AdvertisementPriceSchema] = None
    full_address: Optional[AdvertisementLocationSchemaV2] = None
    attributes: Optional[List[AdvertisementAttributeSchema]] = None
    images: Optional[List[AdvertisementImageSchema]] = None
    videos: Optional[List[AdvertisementVideoSchema]] = None
    contact: Optional[AdvertisementContactSchema] = None
    is_featured: Optional[bool] = None
    tags: Optional[List[str]] = None
    expiry_date: Optional[datetime] = None
    status: Optional[AdvertisementStatus] = None

    class Config:
        from_attributes = True


class AdvertisementEditCreateSchemaV2(BaseModel):
    """Schema for creating an advertisement edit request."""
    title: Optional[str] = None
    description: Optional[str] = None
    security_code_owner_building: Optional[str] = None
    phone_number_owner_building: Optional[str] = None
    category_id: Optional[int] = None
    price: Optional[AdvertisementPriceSchema] = None
    full_address: Optional[AdvertisementLocationSchemaV2] = None
    attributes: Optional[List[AdvertisementAttributeSchema]] = None
    images: Optional[List[AdvertisementImageSchema]] = None
    videos: Optional[List[AdvertisementVideoSchema]] = None

    class Config:
        from_attributes = True


class AdvertisementEditDetailSchemaV2(BaseModel):
    """Schema for advertisement edit details."""
    id: int = Field(..., description="Edit ID")
    original_advertisement_id: int = Field(..., description="Original advertisement ID")
    edit_status: AdvertisementStatus = Field(..., description="Edit status")
    title: Optional[str] = Field(None, description="Advertisement title")
    description: Optional[str] = Field(None, description="Advertisement description")
    security_code_owner_building: Optional[str] = Field(None, description="Security code owner building")
    phone_number_owner_building: Optional[str] = Field(None, description="Phone number owner building")
    category: Optional[AdvertisementCategorySchema] = Field(None, description="Category")
    price: Optional[AdvertisementPriceSchema] = Field(None, description="Price information")
    full_address: Optional[AdvertisementLocationSchema] = Field(None, description="Location")
    attributes: Optional[List[AdvertisementAttributeSchema]] = Field(None, description="Advertisement attributes")
    images: Optional[List[AdvertisementImageSchema]] = Field(None, description="Advertisement images")
    videos: Optional[List[AdvertisementVideoSchema]] = Field(None, description="Advertisement videos")
    admin_notes: Optional[str] = Field(None, description="Admin notes")
    reviewed_by: Optional[AdvertisementUserSchema] = Field(None, description="Reviewed by admin")
    reviewed_at: Optional[str] = Field(None, description="Review date")
    created_at: Optional[str] = Field(None, description="Creation date")
    updated_at: Optional[str] = Field(None, description="Last update date")

    class Config:
        from_attributes = True


class AdvertisementEditListSchemaV2(BaseModel):
    """Schema for listing advertisement edits (for admin)."""
    items: List[AdvertisementEditDetailSchemaV2] = Field(..., description="Edit items")
    total: int = Field(..., description="Total number of edits")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")

    class Config:
        from_attributes = True


class AdvertisementEditApprovalSchemaV2(BaseModel):
    """Schema for approving/rejecting advertisement edits."""
    action: str = Field(..., description="Action: 'approve' or 'reject'")
    admin_notes: Optional[str] = Field(None, description="Admin notes")

    class Config:
        from_attributes = True


class AdvertisementDetailSchemaV2(BaseModel):
    """Schema for advertisement details in API v2."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    description: str = Field(..., description="Advertisement description")
    security_code_owner_building: str = Field(..., description="Security code owner building")
    phone_number_owner_building: str = Field(..., description="Phone number owner building")
    category: AdvertisementCategorySchema = Field(..., description="Category")
    price: AdvertisementPriceSchema = Field(..., description="Price information")
    full_address: AdvertisementLocationSchema = Field(..., description="Location")
    attributes: List[AdvertisementAttributeSchema] = Field(..., description="Advertisement attributes")
    highlight_attributes: List[AdvertisementAttributeSchema] = Field(..., description="Advertisement attributes")
    images: List[AdvertisementImageSchema] = Field(..., description="Advertisement images")
    # contact: AdvertisementContactSchema = Field(..., description="Contact information")
    user: AdvertisementUserSchema = Field(..., description="User")
    status: AdvertisementStatus = Field(..., description="Advertisement status")
    # is_featured: bool = Field(..., description="Whether the advertisement is featured")
    tags: List[str] = Field(..., description="Advertisement tags")
    statistics: AdvertisementStatisticsSchema = Field(..., description="Advertisement statistics")
    created_at: Optional[str] = Field(None, description="Creation date")
    updated_at: Optional[str] = Field(None, description="Last update date")
    expiry_date: Optional[str] = Field(None, description="Expiry date")
    has_pending_edit: bool = Field(False, description="Whether this advertisement has a pending edit")
    # slug: str = Field(..., description="Advertisement slug")
    # similar_ads: List[int] = Field(..., description="Similar advertisement IDs")
    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        if not g_date:
            return None
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)

        # Convert created_at to Jalali format
        if hasattr(obj, 'created_at') and obj.created_at:
            data.created_at = cls.jalali_date(obj.created_at)

        # Convert updated_at to Jalali format
        if hasattr(obj, 'updated_at') and obj.updated_at:
            data.updated_at = cls.jalali_date(obj.updated_at)

        # Convert expiry_date to Jalali format
        if hasattr(obj, 'expiry_date') and obj.expiry_date:
            data.expiry_date = cls.jalali_date(obj.expiry_date)

        return data

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

class AdvertisementListItemSchemaV2(BaseModel):
    """Schema for advertisement list item in API v2."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    price: AdvertisementPriceSchema = Field(None, description="Price information")
    full_address: AdvertisementLocationSchema = Field(..., description="Full address")
    category: AdvertisementCategorySchema = Field(..., description="Category")
    highlight_attributes: List[AdvertisementAttributeSchema] = Field(..., description="Advertisement attributes")
    primary_image: Optional[str] = Field(None, description="Primary image URL")
    created_at: Optional[str] = Field(None, description="Creation date")
    updated_at: Optional[str] = Field(None, description="Last update date")
    status: AdvertisementStatus = Field(..., description="Advertisement status")
    user: AdvertisementUserSchema = Field(..., description="User")

    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        if not g_date:
            return None
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)

        # Convert created_at to Jalali format
        if hasattr(obj, 'created_at') and obj.created_at:
            data.created_at = cls.jalali_date(obj.created_at)

        # Convert updated_at to Jalali format
        if hasattr(obj, 'updated_at') and obj.updated_at:
            data.updated_at = cls.jalali_date(obj.updated_at)

        return data

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

class AdvertisementListSchemaV2(BaseModel):
    """Schema for advertisement list in API v2."""

    items: List[AdvertisementListItemSchemaV2] = Field(..., description="Advertisement items")
    total: int = Field(..., description="Total number of advertisements")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")
    filters: Dict[str, any] = Field(..., description="Applied filters")
    sort: Dict[str, str] = Field(..., description="Applied sorting")
    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

class FlagAdvertisementSchema(BaseModel):
    """Schema for flagging an advertisement."""
    reason: str = Field(..., description="Reason for flagging the advertisement")
    class Config:
        json_schema_extra = {
            "example": {
                "reason": "This advertisement contains inappropriate content"
            }
        }
