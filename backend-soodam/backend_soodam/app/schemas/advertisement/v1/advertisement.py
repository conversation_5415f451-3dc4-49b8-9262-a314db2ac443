
from datetime import date, datetime
from decimal import Decimal
from typing import Dict, List, Optional, Union
from uuid import UUID

import jdatetime
from pydantic import BaseModel, Extra, Field, validator

from app.enums.status import AdvertisementStatus
from app.schemas.gis_geolocation import AddressReadSchema, ProvinceReadSchema, CityReadSchema, \
    AdvertisementLocationCreateSchema, AdvertisementLocationEditSchema
from app.schemas.users.v1 import CustomUserReadSellerInfoSchema

#
# Category Schemas
#

class MainCategorySchema(BaseModel):
    """Schema for main categories."""

    id: int = Field(..., description="Main category ID")
    name: str = Field(..., description="Main category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")


class ReadMainCategoriesSchema(BaseModel):
    """Schema for reading main categories with minimal information."""

    id: int = Field(..., description="Main category ID")
    name: str = Field(..., description="Main category name")

    class Config:
        from_attributes = True


class SubCategorySchema(BaseModel):
    """Schema for sub-categories."""

    id: int = Field(..., description="Sub-category ID")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category")
    name: str = Field(..., description="Sub-category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")


class ReadSubCategoriesSchema(BaseModel):
    """Schema for reading sub-categories with minimal information."""

    id: int = Field(..., description="Sub-category ID")
    name: str = Field(..., description="Sub-category name")

    class Config:
        from_attributes = True


class SubCategoryLevelTwoSchema(BaseModel):
    """Schema for level two sub-categories."""

    id: int = Field(..., description="Level two sub-category ID")
    sub_category: Optional[ReadSubCategoriesSchema] = Field(None, description="Sub-category")
    name: str = Field(..., description="Level two sub-category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")


class ReadAllSubCategoryLevelTwoSchema(BaseModel):
    """Schema for reading all level two sub-categories."""

    id: int = Field(..., description="Level two sub-category ID")
    sub_category: Optional[ReadSubCategoriesSchema] = Field(None, description="Sub-category")
    name: str = Field(..., description="Level two sub-category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")

    class Config:
        from_attributes = True


class ReadAllSubCategoriesSchema(BaseModel):
    """Schema for reading all sub-categories."""

    id: int = Field(..., description="Sub-category ID")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category")
    name: str = Field(..., description="Sub-category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")

    class Config:
        from_attributes = True


class ReadAllMainCategoriesSchema(BaseModel):
    """Schema for reading all main categories."""

    id: int = Field(..., description="Main category ID")
    name: str = Field(..., description="Main category name")
    star: bool = Field(..., description="Star status")
    key: str = Field(..., description="Category key")
    description: str = Field(..., description="Category description")
    review: int = Field(..., description="Review count")
    total_adv: int = Field(..., description="Total advertisements")
    icon: str = Field('', description="Category icon")
    sub_categories: list = Field([ReadSubCategoriesSchema], description="Sub-categories")
    created_at: Optional[date] = Field(None, description="Creation date")
    updated_at: Optional[date] = Field(None, description="Last update date")

    class Config:
        from_attributes = True
        extra = Extra.allow


class ReadFeatureByCategorySchema(BaseModel):
    """Schema for reading features by category."""

    sub_category_id: int = Field(..., description="Sub-category ID")
    sub_category_level_two_id: int = Field(..., description="Level two sub-category ID")

class ReadFeaturesSchema(BaseModel):
    """Schema for reading features."""
    pass


class AdvertisementMedias(BaseModel):
    """Schema for advertisement media."""

    id: int = Field(..., description="Media ID")
    media_url: str = Field(..., description="Media URL")
    media_type: str = Field(..., description="Media type")

    class Config:
        from_attributes = True

class AdvertisementLocationSchema(BaseModel):
    """Schema for advertisement location."""
    id: int = Field(-1, description="Location ID")
    province: Optional[ProvinceReadSchema] = Field(None, description="State or province name")
    city: Optional[CityReadSchema] = Field(None, description="City name")
    # country: str = Field(..., description="Country name")
    latitude: Optional[float] = Field(None, description="Latitude")
    longitude: Optional[float] = Field(None, description="Longitude")
    address: Optional[str] = Field(None, description="Full address")
    zip_code: Optional[str] = Field(None, description="Postal code")
    geolocation: Optional[str] = Field(..., description="Geolocation")

class AdvertisementCategorySchema(BaseModel):
    """Schema for advertisement category."""

    id: int = Field(..., description="Category ID")
    name: str = Field(..., description="Category name")
    key: str = Field(..., description="Category name")
    # slug: str = Field(..., description="Category slug")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category ID")
    icon: Optional[str] = Field(None, description="Category icon")

class AdvertisementPriceSchema(BaseModel):
    """Schema for advertisement price."""

    deposit: float = Field(..., description="Deposit amount")
    rent: float = Field(..., description="Rent amount")
    amount: float = Field(..., description="Price amount")
    currency: str = Field(..., description="Price currency")
    is_negotiable: bool = Field(..., description="Whether the price is negotiable")
    discount_amount: Optional[float] = Field(None, description="Discount amount")
    original_amount: Optional[float] = Field(None, description="Original price amount")
    price_per_unit: Optional[float] = Field(None, description="Price per unit")
    unit: Optional[str] = Field(None, description="Unit for price per unit")

class AdvertisementImageSchema(BaseModel):
    """Schema for advertisement images."""

    # id: int = Field(..., description="Image ID")
    url: str = Field(..., description="Image URL")
    is_primary: bool = Field(..., description="Whether this is the primary image")
    order: int = Field(..., description="Display order")
    width: Optional[int] = Field(None, description="Image width")
    height: Optional[int] = Field(None, description="Image height")
    alt_text: Optional[str] = Field(None, description="Alternative text for the image")

class AdvertisementVideoSchema(BaseModel):
    """Schema for advertisement video."""
    url: str = Field(..., description="Video URL")
    thumbnail_url: Optional[str] = Field(None, description="Thumbnail URL")
    is_primary: bool = Field(False, description="Whether this is the primary Video")
    order: int = Field(0, description="Video order")
    duration: Optional[int] = Field(0, description="Duration Video")
    title: Optional[str] = Field(..., description="Video Title")
    description: Optional[str] = Field(..., description="Video Description")

class AdvertisementAttributeValueSchema(BaseModel):
    id: str
    value: str

class AdvertisementAttributeSchema(BaseModel):
    """Schema for advertisement attributes."""
    id: str = Field(..., description="Attribute ID")
    name: str = Field(..., description="Attribute name")
    key: str = Field(..., description="Attribute key")
    type: str = Field(..., description="Attribute type (choice, bool, text)")
    # value: Union[Dict, bool, str] = Field(..., description="Attribute value")
    value: Union[AdvertisementAttributeValueSchema, str, bool]
    # unit: Optional[str] = Field(None, description="Attribute unit")
    icon: Optional[str] = Field(None, description="Attribute icon")

    class Config:
        from_attributes = True

class AdvertisementContactSchema(BaseModel):
    """Schema for advertisement contact information."""

    phone: Optional[str] = Field(None, description="Contact phone number")
    email: Optional[str] = Field(None, description="Contact email")
    website: Optional[str] = Field(None, description="Contact website")
    whatsapp: Optional[str] = Field(None, description="WhatsApp number")
    telegram: Optional[str] = Field(None, description="Telegram username")
    show_phone: bool = Field(True, description="Whether to show the phone number")
    show_email: bool = Field(True, description="Whether to show the email")

class AdvertisementStatisticsSchema(BaseModel):
    """Schema for advertisement statistics."""

    views: int = Field(..., description="Number of views")
    favorites: int = Field(..., description="Number of times added to favorites")
    inquiries: int = Field(..., description="Number of inquiries")
    shares: int = Field(..., description="Number of shares")
    last_viewed_at: Optional[datetime] = Field(None, description="Last viewed at")

class AdvertisementUserSchema(BaseModel):
    """Schema for advertisement user."""

    id: int = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    full_name: str = Field(..., description="Full name")
    avatar: Optional[str] = Field(None, description="Avatar URL")
    rating: Optional[float] = Field(None, description="User rating")
    is_verified: bool = Field(..., description="Whether the user is verified")
    created_at: Optional[str] = Field(..., description="Member since")

    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        if not g_date:
            return None
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)

        # Convert created_at to Jalali format
        if hasattr(obj, 'created_at') and obj.created_at:
            data.created_at = cls.jalali_date(obj.created_at)
        return data


class AdvertisementSchema(BaseModel):
    """Schema for advertisements."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    description: str = Field(..., description="Advertisement description")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category")
    sub_category: Optional[ReadSubCategoriesSchema] = Field(None, description="Sub-category")
    user: Optional[CustomUserReadSellerInfoSchema] = Field(None, description="User")
    address: Optional[AddressReadSchema] = Field(None, description="Address")
    review: int = Field(..., description="Review count")
    status: int = Field(..., description="Status")
    highlight_features: list = Field([], description="Highlight features")
    created_at: Optional[str] = Field(None, description="Creation date")
    medias: list[AdvertisementMedias] = Field([], description="Media")

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)
        data.created_at = cls.jalali_date(obj.created_at)
        return data

class AdvertisementByIDSchema(BaseModel):
    """Schema for advertisements by ID."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    description: str = Field(..., description="Advertisement description")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category")
    sub_category: Optional[ReadSubCategoriesSchema] = Field(None, description="Sub-category")
    user: Optional[CustomUserReadSellerInfoSchema] = Field(None, description="User")
    address: Optional[AddressReadSchema] = Field(None, description="Address")
    review: int = Field(..., description="Review count")
    highlight_features: list = Field([], description="Highlight features")
    features: list = Field([], description="Features")
    created_at: Optional[str] = Field(None, description="Creation date")
    medias: list[AdvertisementMedias] = Field([], description="Media")

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)
        data.created_at = cls.jalali_date(obj.created_at)
        return data

class CreateAdvertisementSchema(BaseModel):
    """Schema for creating advertisements."""

    title: str = Field(..., description="Advertisement title")
    security_code_owner_building: str = Field(..., description="Security code of the building owner")
    phone_number_owner_building: str = Field(..., description="Phone number of the building owner")
    description: str = Field(..., description="Advertisement description")
    sub_category_id: int = Field(..., description="Sub-category ID")
    sub_sub_category_id: int = Field(..., description="Level two sub-category ID")
    full_address: Optional[AdvertisementLocationCreateSchema] = Field(None, description="Full address")
    features: list = Field([], description="Features")
    medias: list = Field([], description="Media")

    class Config:
        extra = Extra.allow

class EditAdvertisementSchema(BaseModel):
    """Schema for editing advertisements."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    security_code_owner_building: str = Field(..., description="Security code of the building owner")
    phone_number_owner_building: str = Field(..., description="Phone number of the building owner")
    description: str = Field(..., description="Advertisement description")
    sub_category_id: int = Field(..., description="Sub-category ID")
    sub_sub_category_id: int = Field(..., description="Level two sub-category ID")
    full_address: Optional[AdvertisementLocationEditSchema] = Field(None, description="Full address")
    features: list = Field([], description="Features")
    medias: list = Field([], description="Media")

    class Config:
        extra = Extra.allow

class FlaggedContentSchema(BaseModel):
    """Schema for flagged content."""

    id: int = Field(..., description="Flagged content ID")
    user_id: int = Field(..., description="User ID")
    advertisement_id: int = Field(..., description="Advertisement ID")
    reason: str = Field(..., description="Reason")
    advertisement_status: int = Field(..., description="Advertisement status")
    created_at: Optional[str] = Field(None, description="Creation date")

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    @staticmethod
    def jalali_date(g_date: datetime) -> str:
        """Convert Gregorian date to Jalali date."""
        j_date = jdatetime.datetime.fromgregorian(datetime=g_date)
        return j_date.strftime('%Y-%m-%d')

    @classmethod
    def from_orm(cls, obj):
        """Convert ORM object to schema."""
        data = super().from_orm(obj)
        data.created_at = cls.jalali_date(obj.created_at)
        return data

class AdvertisementListSchema(BaseModel):
    """Schema for listing advertisements with minimal information."""

    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    main_category: Optional[ReadMainCategoriesSchema] = Field(None, description="Main category")
    sub_category: Optional[ReadSubCategoriesSchema] = Field(None, description="Sub-category")
    status: int = Field(..., description="Status")
    created_at: Optional[str] = Field(None, description="Creation date")
    medias: list[AdvertisementMedias] = Field([], description="Media")

    class Config:
        from_attributes = True

class AdvertisementFilterSchema(BaseModel):
    """Schema for filtering advertisements."""

    query: Optional[str] = Field(None, description="Search query")
    category_id: Optional[int] = Field(None, description="Category ID")
    sub_category_id: Optional[int] = Field(None, description="Sub-category ID")
    min_price: Optional[Decimal] = Field(None, description="Minimum price")
    max_price: Optional[Decimal] = Field(None, description="Maximum price")
    location_lat: Optional[float] = Field(None, description="Location latitude")
    location_lng: Optional[float] = Field(None, description="Location longitude")
    distance_km: Optional[int] = Field(10, description="Distance in kilometers")
    limit: int = Field(20, description="Limit")
    offset: int = Field(0, description="Offset")

