"""
Notification schemas for the Soodam backend.
"""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class NotificationSchema(BaseModel):
    """Schema for notification."""
    
    id: int = Field(..., description="Notification ID")
    type: str = Field(..., description="Notification type")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    data: Dict = Field(..., description="Notification data")
    is_read: bool = Field(..., description="Whether the notification is read")
    created_at: datetime = Field(..., description="Creation date")


class NotificationUpdateSchema(BaseModel):
    """Schema for updating a notification."""
    
    is_read: bool = Field(..., description="Whether the notification is read")


class NotificationListSchema(BaseModel):
    """Schema for notification list."""
    
    items: List[NotificationSchema] = Field(..., description="Notification items")
    total: int = Field(..., description="Total number of notifications")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    unread_count: int = Field(..., description="Number of unread notifications")


class NotificationPreferenceSchema(BaseModel):
    """Schema for notification preferences."""
    
    email_enabled: Optional[bool] = Field(None, description="Whether email notifications are enabled")
    push_enabled: Optional[bool] = Field(None, description="Whether push notifications are enabled")
    sms_enabled: Optional[bool] = Field(None, description="Whether SMS notifications are enabled")
    advertisement_created: Optional[bool] = Field(None, description="Whether to notify when an advertisement is created")
    advertisement_updated: Optional[bool] = Field(None, description="Whether to notify when an advertisement is updated")
    advertisement_deleted: Optional[bool] = Field(None, description="Whether to notify when an advertisement is deleted")
    advertisement_approved: Optional[bool] = Field(None, description="Whether to notify when an advertisement is approved")
    advertisement_rejected: Optional[bool] = Field(None, description="Whether to notify when an advertisement is rejected")
    advertisement_expired: Optional[bool] = Field(None, description="Whether to notify when an advertisement expires")
    advertisement_viewed: Optional[bool] = Field(None, description="Whether to notify when an advertisement is viewed")
    advertisement_favorited: Optional[bool] = Field(None, description="Whether to notify when an advertisement is favorited")
    advertisement_inquiry: Optional[bool] = Field(None, description="Whether to notify when an advertisement receives an inquiry")
    advertisement_comment: Optional[bool] = Field(None, description="Whether to notify when an advertisement receives a comment")
    advertisement_reply: Optional[bool] = Field(None, description="Whether to notify when an advertisement comment receives a reply")
    advertisement_match: Optional[bool] = Field(None, description="Whether to notify when an advertisement matches criteria")
    user_followed: Optional[bool] = Field(None, description="Whether to notify when a user is followed")
    user_mentioned: Optional[bool] = Field(None, description="Whether to notify when a user is mentioned")
    user_message: Optional[bool] = Field(None, description="Whether to notify when a user receives a message")
    system_announcement: Optional[bool] = Field(None, description="Whether to notify for system announcements")


class NotificationDeviceSchema(BaseModel):
    """Schema for notification device."""
    
    id: int = Field(..., description="Device ID")
    device_token: str = Field(..., description="Device token")
    device_type: str = Field(..., description="Device type")
    is_active: bool = Field(..., description="Whether the device is active")
    created_at: datetime = Field(..., description="Creation date")
    last_used_at: Optional[datetime] = Field(None, description="Last used date")


class NotificationDeviceCreateSchema(BaseModel):
    """Schema for creating a notification device."""
    
    device_token: str = Field(..., description="Device token")
    device_type: str = Field(..., description="Device type")
