"""
Chat schemas for the Soodam backend.
"""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class ChatUserSchema(BaseModel):
    """Schema for chat user."""
    
    id: int = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    full_name: str = Field(..., description="Full name")
    avatar_url: Optional[str] = Field(None, description="Avatar URL")


class ChatAdvertisementSchema(BaseModel):
    """Schema for chat advertisement."""
    
    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    image_url: Optional[str] = Field(None, description="Advertisement image URL")


class ChatAttachmentSchema(BaseModel):
    """Schema for chat attachment."""
    
    id: int = Field(..., description="Attachment ID")
    file_url: str = Field(..., description="File URL")
    file_type: str = Field(..., description="File type")
    file_name: str = Field(..., description="File name")
    file_size: int = Field(..., description="File size")
    created_at: datetime = Field(..., description="Creation date")


class ChatMessageSchema(BaseModel):
    """Schema for chat message."""
    
    id: int = Field(..., description="Message ID")
    room_id: int = Field(..., description="Room ID")
    sender_id: int = Field(..., description="Sender ID")
    content: str = Field(..., description="Message content")
    is_read: bool = Field(..., description="Whether the message is read")
    created_at: datetime = Field(..., description="Creation date")
    attachments: List[ChatAttachmentSchema] = Field([], description="Message attachments")


class ChatMessageCreateSchema(BaseModel):
    """Schema for creating a chat message."""
    
    content: str = Field(..., description="Message content")
    attachments: Optional[List[Dict]] = Field(None, description="Message attachments")


class ChatRoomSchema(BaseModel):
    """Schema for chat room."""
    
    id: int = Field(..., description="Room ID")
    advertisement: ChatAdvertisementSchema = Field(..., description="Advertisement")
    sender: ChatUserSchema = Field(..., description="Sender")
    receiver: ChatUserSchema = Field(..., description="Receiver")
    is_active: bool = Field(..., description="Whether the room is active")
    created_at: datetime = Field(..., description="Creation date")
    updated_at: datetime = Field(..., description="Last update date")
    last_message: Optional[ChatMessageSchema] = Field(None, description="Last message")
    unread_count: int = Field(0, description="Number of unread messages")


class ChatRoomCreateSchema(BaseModel):
    """Schema for creating a chat room."""
    
    advertisement_id: int = Field(..., description="Advertisement ID")
    receiver_id: int = Field(..., description="Receiver ID")
    message: str = Field(..., description="Initial message")


class ChatRoomListSchema(BaseModel):
    """Schema for chat room list."""
    
    items: List[ChatRoomSchema] = Field(..., description="Chat room items")
    total: int = Field(..., description="Total number of chat rooms")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


class ChatMessageListSchema(BaseModel):
    """Schema for chat message list."""
    
    items: List[ChatMessageSchema] = Field(..., description="Chat message items")
    total: int = Field(..., description="Total number of chat messages")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
