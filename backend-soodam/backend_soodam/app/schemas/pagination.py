from typing import Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')

class PaginationMetadata(BaseModel):
    """Metadata for paginated responses."""
    page: int = Field(..., description="Current page number")
    limit: int = Field(..., description="Number of items per page")
    total_count: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")

class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response schema."""
    items: List[T] = Field(..., description="List of items for the current page")
    metadata: PaginationMetadata = Field(..., description="Pagination metadata")

    class Config:
        # from_attributes = True
        arbitrary_types_allowed = True
