"""
Security schemas for the Soodam backend.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class APIKeyCreate(BaseModel):
    """Schema for creating an API key."""
    
    name: str = Field(..., description="Name of the API key")
    expires_at: Optional[datetime] = Field(None, description="Expiration date of the API key")


class APIKeyResponse(BaseModel):
    """Schema for API key response."""
    
    id: int = Field(..., description="ID of the API key")
    name: str = Field(..., description="Name of the API key")
    key: str = Field(..., description="The API key (only shown once)")
    is_active: bool = Field(..., description="Whether the API key is active")
    created_at: datetime = Field(..., description="Creation date of the API key")
    expires_at: Optional[datetime] = Field(None, description="Expiration date of the API key")


class APIKeyInfo(BaseModel):
    """Schema for API key information."""
    
    id: int = Field(..., description="ID of the API key")
    name: str = Field(..., description="Name of the API key")
    is_active: bool = Field(..., description="Whether the API key is active")
    created_at: datetime = Field(..., description="Creation date of the API key")
    last_used_at: Optional[datetime] = Field(None, description="Last usage date of the API key")
    expires_at: Optional[datetime] = Field(None, description="Expiration date of the API key")


class APIKeyList(BaseModel):
    """Schema for API key list."""
    
    items: List[APIKeyInfo] = Field(..., description="List of API keys")
    total: int = Field(..., description="Total number of API keys")


class TwoFactorAuthEnable(BaseModel):
    """Schema for enabling two-factor authentication."""
    
    token: str = Field(..., description="TOTP token")


class TwoFactorAuthVerify(BaseModel):
    """Schema for verifying two-factor authentication."""
    
    token: str = Field(..., description="TOTP token")


class TwoFactorAuthSetup(BaseModel):
    """Schema for two-factor authentication setup."""
    
    secret: str = Field(..., description="TOTP secret")
    uri: str = Field(..., description="TOTP URI for QR code generation")


class TwoFactorAuthStatus(BaseModel):
    """Schema for two-factor authentication status."""
    
    is_enabled: bool = Field(..., description="Whether two-factor authentication is enabled")
    created_at: Optional[datetime] = Field(None, description="Creation date of two-factor authentication")
    last_verified_at: Optional[datetime] = Field(None, description="Last verification date of two-factor authentication")


class LoginWithTwoFactor(BaseModel):
    """Schema for login with two-factor authentication."""
    
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")
    token: Optional[str] = Field(None, description="TOTP token")


class LoginResponse(BaseModel):
    """Schema for login response."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    requires_two_factor: bool = Field(False, description="Whether two-factor authentication is required")


class RefreshToken(BaseModel):
    """Schema for refreshing a token."""
    
    refresh_token: str = Field(..., description="JWT refresh token")


class LoginAttemptInfo(BaseModel):
    """Schema for login attempt information."""
    
    id: int = Field(..., description="ID of the login attempt")
    ip_address: str = Field(..., description="IP address of the login attempt")
    user_agent: str = Field(..., description="User agent of the login attempt")
    success: bool = Field(..., description="Whether the login attempt was successful")
    created_at: datetime = Field(..., description="Creation date of the login attempt")


class LoginAttemptList(BaseModel):
    """Schema for login attempt list."""
    
    items: List[LoginAttemptInfo] = Field(..., description="List of login attempts")
    total: int = Field(..., description="Total number of login attempts")
