import re
from typing import Optional, Literal
from pydantic import BaseModel, Field, validator, EmailStr


# =============================================================================
# LEGACY SCHEMAS (for backward compatibility)
# =============================================================================

class PhoneNumberRegisteredSchema(BaseModel):
    phone_number: str


class CallSendVerifyCode(BaseModel):
    phone_number: str

class LogAccessSchema(BaseModel):
    user:object
    try_to_verify: int
    phone_number: str
    user_token: str
    verify_code: str
    ip_address: str


class LogAccessEditSchema(BaseModel):
    try_to_verify: int
    phone_number: str
    user_token: str
    verify_code: str
    ip_address: str


class LogAccessCreateSchema(BaseModel):
    phone_number: str
    user: object
    user_token: str
    verify_code: str
    ip_address: str


class VerifyCodeSchema(BaseModel):
    phone_number: str
    verify_code: str


# =============================================================================
# ENHANCED DUAL USERNAME SCHEMAS
# =============================================================================

class DualUsernameLoginSchema(BaseModel):
    """
    Schema for login with dual username support.

    Supports both email and phone number as username fields.
    """

    username: str = Field(
        ...,
        description="Email address or phone number",
        example="<EMAIL> or 09123456789"
    )
    password: str = Field(
        ...,
        min_length=6,
        description="User password",
        example="secure_password123"
    )
    remember_me: bool = Field(
        default=False,
        description="Remember user login"
    )

    @validator('username')
    def validate_username(cls, v):
        """Validate username format (email or Iranian phone number)."""
        v = v.strip()

        # Check if it's an email
        if '@' in v:
            # Basic email validation
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', v):
                raise ValueError('Invalid email format')
            return v.lower()

        # Check if it's an Iranian phone number
        # Remove spaces, dashes, and other characters
        cleaned = ''.join(filter(str.isdigit, v))

        # Handle different formats
        if cleaned.startswith('0098') and len(cleaned) == 14:
            cleaned = '0' + cleaned[4:]
        elif cleaned.startswith('98') and len(cleaned) == 12:
            cleaned = '0' + cleaned[2:]
        elif not cleaned.startswith('0') and len(cleaned) == 10:
            cleaned = '0' + cleaned

        # Validate Iranian phone format
        if not re.match(r'^09[0-9]{9}$', cleaned):
            raise ValueError('Username must be a valid email address or Iranian phone number (09xxxxxxxxx)')

        return cleaned

    class Config:
        json_schema_extra = {
            "example": {
                "username": "<EMAIL>",
                "password": "secure_password123",
                "remember_me": False
            }
        }


class EmailLoginSchema(BaseModel):
    """Schema for email-only login."""

    email: EmailStr = Field(
        ...,
        description="Email address",
        example="<EMAIL>"
    )
    password: str = Field(
        ...,
        min_length=6,
        description="User password"
    )
    remember_me: bool = Field(default=False)


class PhoneLoginSchema(BaseModel):
    """Schema for phone number-only login."""

    phone_number: str = Field(
        ...,
        description="Iranian phone number",
        example="09123456789"
    )
    password: str = Field(
        ...,
        min_length=6,
        description="User password"
    )
    remember_me: bool = Field(default=False)

    @validator('phone_number')
    def validate_phone_number(cls, v):
        """Validate Iranian phone number format."""
        from app.models.user import normalize_iranian_phone_number
        try:
            return normalize_iranian_phone_number(v)
        except ValueError as e:
            raise ValueError(str(e))


class LoginResponseSchema(BaseModel):
    """Schema for login response."""

    access_token: str = Field(
        ...,
        description="JWT access token"
    )
    token_type: str = Field(
        default="bearer",
        description="Token type"
    )
    expires_in: Optional[int] = Field(
        None,
        description="Token expiration time in seconds"
    )
    user_info: dict = Field(
        ...,
        description="Basic user information"
    )


class UsernameTypeSchema(BaseModel):
    """Schema for username type identification."""

    username: str = Field(
        ...,
        description="Username to identify"
    )
    username_type: Literal["email", "phone", "unknown"] = Field(
        ...,
        description="Identified username type"
    )
    normalized_username: str = Field(
        ...,
        description="Normalized username"
    )
    is_valid: bool = Field(
        ...,
        description="Whether the username format is valid"
    )
