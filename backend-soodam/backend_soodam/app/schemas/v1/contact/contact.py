from pydantic import BaseModel, EmailStr, Field
from datetime import datetime

class ContactCreate(BaseModel):
    name: str = Field(..., max_length=100)
    email: EmailStr
    phone_number : str = Field(..., max_length=11)
    address: str = Field(..., max_length=250)
    message: str

class ContactResponse(BaseModel):
    id: int
    name: str
    email: EmailStr
    phone_number: str
    address: str
    message: str
    created_at: datetime

    class Config:
        from_attributes = True