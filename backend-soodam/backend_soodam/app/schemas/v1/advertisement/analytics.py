from pydantic import BaseModel, HttpUrl, IPvAnyAddress
from typing import Optional
from datetime import datetime

class AnalyticsSchema:
    class AdvertisementViewCreateSchema(BaseModel):
        """Schema for creating a new advertisement view."""
        advertisement_id: int
        user_id: Optional[int] = None
        ip_address: Optional[IPvAnyAddress] = None
        user_agent: Optional[str] = None
        referrer: Optional[HttpUrl] = None

        class Config:
            from_attributes = True


    class AdvertisementViewResponseSchema(BaseModel):
        """Schema for advertisement view response."""
        id: int
        advertisement_id: int
        user_id: Optional[int] = None
        ip_address: Optional[str] = None
        user_agent: Optional[str] = None
        referrer: Optional[str] = None
        created_at: datetime
        modified_at: datetime

        class Config:
            from_attributes = True