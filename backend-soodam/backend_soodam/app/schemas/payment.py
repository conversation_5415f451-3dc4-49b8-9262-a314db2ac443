from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PaymentInitRequest(BaseModel):
    """Schema for initializing a payment"""
    amount: float = Field(..., gt=0, description="Payment amount in Tomans")
    bank_name: str = Field(..., description="Bank name (zarinpal, mellat, saman, parsian)")
    description: str = Field(..., description="Payment description")
    mobile_number: Optional[str] = Field(None, description="Mobile number for payment notification")
    callback_url: Optional[str] = Field(None, description="Custom callback URL (optional)")


class PaymentInitResponse(BaseModel):
    """Schema for payment initialization response"""
    redirect_url: str = Field(..., description="URL to redirect user to bank gateway")
    reference_number: str = Field(..., description="Payment reference number")
    tracking_code: str = Field(..., description="Payment tracking code")


class PaymentVerifyRequest(BaseModel):
    """Schema for payment verification request"""
    tracking_code: str = Field(..., description="Payment tracking code")
    status: int = Field(..., description="Payment status code")
    reference_id: Optional[str] = Field(None, description="Bank reference ID")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="Additional data from bank")


class PaymentVerifyResponse(BaseModel):
    """Schema for payment verification response"""
    is_success: bool = Field(..., description="Payment success status")
    reference_number: str = Field(..., description="Payment reference number")
    tracking_code: str = Field(..., description="Payment tracking code")
    bank_reference: Optional[str] = Field(None, description="Bank reference ID")
    message: Optional[str] = Field(None, description="Payment result message")


class PaymentHistoryItem(BaseModel):
    """Schema for a payment history item"""
    id: str = Field(..., description="Payment ID")
    amount: float = Field(..., description="Payment amount")
    bank_name: str = Field(..., description="Bank name")
    status: str = Field(..., description="Payment status")
    date: datetime = Field(..., description="Payment date")
    reference_number: str = Field(..., description="Payment reference number")
    sale_reference_id: Optional[str] = Field(None, description="Sale reference ID")


class PaymentHistoryResponse(BaseModel):
    """Schema for payment history response"""
    payments: List[PaymentHistoryItem] = Field(..., description="List of payments")
    total_count: int = Field(..., description="Total number of payments")
    page: int = Field(1, description="Current page")
    page_size: int = Field(10, description="Page size")