from pydantic import BaseModel
from datetime import date


class BlogsSchema(BaseModel):
    id: int
    post_author: int
    post_title: str
    post_description: str
    post_content: str
    post_status: str
    post_edited_by: int
    post_start_publication: date = None
    created_at: date = None
    updated_at: date = None


class BlogsReadSchema(BaseModel):
    id: int
    post_author: str
    post_title: str
    post_description: str
    post_content: str
    created_at: date = None
    post_image: str = ''
    blog_file: str = ''

    class Config:
        from_attributes = True


class BlogByIdSchema(BaseModel):
    id: int
    post_author: str
    post_title: str
    post_description: str
    post_image: list[str] = []
    blog_file: str
    post_content: str
    post_status: str
    created_at: date = None

    class Config:
        from_attributes = True


class CreateBlogSchema(BaseModel):
    post_title: str
    post_author: str
    post_description: str
    post_image: str
    blog_file: str
    post_content: str
    post_status: str


class EditBlogSchema(BaseModel):
    id: int
    post_title: str
    post_author: str
    post_description: str
    post_image: str
    blog_file: str
    post_content: str
    post_status: str

    class Config:
        from_attributes = True


class DeleteBlogSchema(BaseModel):
    id: int

    class Config:
        from_attributes = True
