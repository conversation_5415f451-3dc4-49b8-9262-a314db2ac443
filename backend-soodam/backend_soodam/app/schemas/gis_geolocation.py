from datetime import date
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Extra, Field, validator
import re

from pydantic import BaseModel
from pydantic import Extra


class ProvinceSchema(BaseModel):
    name: str
    slug: str
    tel_prefix: str


class CitySchema(BaseModel):
    province_id: int
    name: str
    slug: str


class ProvinceReadSchema(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True


class CityReadSchema(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True


class AddressSchema(BaseModel):
    province: ProvinceReadSchema = None
    city: CityReadSchema = None
    street: str
    address: str
    zip_code: str
    srid: int
    longitude: float
    latitude: float
    # geolocation:Point=None


class AddressReadSchema(BaseModel):
    province: ProvinceReadSchema = None
    city: CityReadSchema = None
    # street:str
    address: str
    zip_code: str
    longitude: float
    latitude: float

    class Config:
        from_attributes = True

class AddressGeoLocationSchema(BaseModel):
    province:int
    city:int
    address:str
    zip_code:str
    longitude:float
    latitude:float
    class Config:
        from_attributes = True
class CustomUserLocationCreateSchema(BaseModel):
    province_id: int
    city_id: int
    # street: str
    address: str
    zip_code: str
    longitude: float
    latitude: float


class CustomUserLocationEditSchema(BaseModel):
    id: Optional[int] = None
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None

    @validator('zip_code')
    def validate_zip_code(cls, v):
        if v is not None and not re.match(r'^\d{10}$', v):
            raise ValueError('Zip code must be 10 digits')
        return v

    @validator('longitude')
    def validate_longitude(cls, v):
        if v is not None and (v < -180 or v > 180):
            raise ValueError('Longitude must be between -180 and 180')
        return v

    @validator('latitude')
    def validate_latitude(cls, v):
        if v is not None and (v < -90 or v > 90):
            raise ValueError('Latitude must be between -90 and 90')
        return v

    class Config:
        from_attributes = True
        extra = Extra.allow


class AdvertisementLocationCreateSchema(BaseModel):
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None

    @validator('zip_code')
    def validate_zip_code(cls, v):
        if v is not None and not re.match(r'^\d{10}$', v):
            raise ValueError('Zip code must be 10 digits')
        return v

    @validator('longitude')
    def validate_longitude(cls, v):
        if v is not None and (v < -180 or v > 180):
            raise ValueError('Longitude must be between -180 and 180')
        return v

    @validator('latitude')
    def validate_latitude(cls, v):
        if v is not None and (v < -90 or v > 90):
            raise ValueError('Latitude must be between -90 and 90')
        return v

    class Config:
        from_attributes = True
        # extra = Extra.allow

class AdvertisementLocationEditSchema(BaseModel):
    province_id: Optional[int] = None
    city_id: Optional[int] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None
    @validator('zip_code')
    def validate_zip_code(cls, v):
        if v is not None and not re.match(r'^\d{10}$', v):
            raise ValueError('Zip code must be 10 digits')
        return v
    @validator('longitude')
    def validate_longitude(cls, v):
        if v is not None and (v < -180 or v > 180):
            raise ValueError('Longitude must be between -180 and 180')
        return v
    @validator('latitude')
    def validate_latitude(cls, v):
        if v is not None and (v < -90 or v > 90):
            raise ValueError('Latitude must be between -90 and 90')
        return v
    class Config:
        from_attributes = True
        extra = Extra.allow