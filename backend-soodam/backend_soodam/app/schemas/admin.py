from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.enums import AdvertisementStatus
from app.schemas.advertisement.v2.advertisement import  AdvertisementUserSchema, AdvertisementLocationSchema, \
    AdvertisementPriceSchema, AdvertisementImageSchema, AdvertisementAttributeSchema, AdvertisementStatisticsSchema, \
    AdvertisementListItemSchemaV2
from app.schemas.pagination import PaginationMetadata, PaginatedResponse
from app.schemas.users.v1 import CustomUserSchema


class AdminDashboardStatsResponse(BaseModel):
    total_users: int
    active_users: int
    total_advertisements: int
    pending_advertisements: int
    approved_advertisements: int
    rejected_advertisements: int
    advertisements_by_category: List[Dict[str, Any]] = []

class StatusUpdateResponse(BaseModel):
    id: int
    status: Any
    message: str

class UserResponse(BaseModel):
    id: int
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    is_admin: bool
    date_joined: datetime
    
    class Config:
        from_attributes = True

class CategoryBase(BaseModel):
    id: int
    name: str

class ProvinceBase(BaseModel):
    id: int
    name: str

class CityBase(BaseModel):
    id: int
    name: str

class AddressResponse(BaseModel):
    id: Optional[int] = None
    province: Optional[ProvinceBase] = None
    city: Optional[CityBase] = None

class PriceResponse(BaseModel):
    id: int
    deposit: Optional[float] = None
    rent: Optional[float] = None
    amount: Optional[float] = None
    currency: Optional[str] = None
    negotiable: Optional[bool] = None
    original_amount: Optional[float] = None
    price_per_unit: Optional[float] = None
    unit: Optional[str] = None

class MediaResponse(BaseModel):
    id: int
    url: str

class TagResponse(BaseModel):
    id: int
    name: str

class StatisticsResponse(BaseModel):
    id: int
    views: int
    likes_count: int
    dislikes_count: int
    comments_count: int

class UserRatingResponse(BaseModel):
    id: int
    rating: float

class AdvertisementResponse(BaseModel):
    id: int = Field(..., description="Advertisement ID")
    title: str = Field(..., description="Advertisement title")
    description: str = Field(..., description="Advertisement description")
    sub_category: Optional[CategoryBase] = None
    # sub_category: AdvertisementCategorySchema = Field(..., description="Category")
    main_category: Optional[CategoryBase] = None
    price: AdvertisementPriceSchema = Field(..., description="Price information")
    full_address: Optional[AdvertisementLocationSchema] = Field(..., description="Location")
    attributes: List[AdvertisementAttributeSchema] = Field(..., description="Advertisement attributes")
    images: List[AdvertisementImageSchema] = Field(..., description="Advertisement images")
    user: Optional[AdvertisementUserSchema] = Field(..., description="User")
    status: AdvertisementStatus = Field(..., description="Advertisement status")
    tags: List[str] = Field(..., description="Advertisement tags")
    statistics: AdvertisementStatisticsSchema = Field(..., description="Advertisement statistics")
    # user_rating: Optional[UserRatingResponse] = None
    created_at: Optional[str] = Field(None, description="Creation date")
    updated_at: Optional[str] = Field(None, description="Last update date")
    expiry_date: Optional[str] = Field(None, description="Expiry date")

class BlogResponse(BaseModel):
    id: int
    title: str
    content: Optional[str] = None
    post_status: str
    created_at: datetime
    author_id: int
    
    class Config:
        from_attributes = True

# Add pagination response model

class PaginatedUserResponse(BaseModel):
    items: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int

class PaginatedAdvertisementResponse(BaseModel):
    items: List[AdvertisementListItemSchemaV2]
    metadata: PaginationMetadata

    class Config:
        from_attributes = True

class PaginatedBlogResponse(BaseModel):
    items: List[BlogResponse]
    total: int
    page: int
    size: int
    pages: int

# Add schemas for special admin management
class AdminTypeSchema(BaseModel):
    id: Optional[int] = None
    name: str = Field(..., min_length=2, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    
    class Config:
        from_attributes = True

class AdminTransactionSchema(BaseModel):
    id: Optional[int] = None
    amount: float
    transaction_type: int
    description: Optional[str] = None
    is_positive: bool = True
    transaction_date: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class SpecialAdminCreateSchema(BaseModel):
    user_id: int
    admin_type_id: int
    can_manage_users: bool = False
    can_manage_advertisements: bool = False
    can_manage_blogs: bool = False
    can_manage_agents: bool = False
    city_ids: Optional[List[int]] = []
    province_ids: Optional[List[int]] = []
    
    @validator('city_ids', 'province_ids')
    def validate_empty_list(cls, v):
        if v is None:
            return []
        return v

class SpecialAdminUpdateSchema(BaseModel):
    admin_type_id: Optional[int] = None
    can_manage_users: Optional[bool] = None
    can_manage_advertisements: Optional[bool] = None
    can_manage_blogs: Optional[bool] = None
    can_manage_agents: Optional[bool] = None
    city_ids: Optional[List[int]] = None
    province_ids: Optional[List[int]] = None
    status: Optional[int] = None  # 1=Active, 2=Inactive, 3=Suspended
    
    @validator('city_ids', 'province_ids')
    def validate_empty_list(cls, v):
        if v is None:
            return []
        return v

class UserBasicInfo(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: Optional[str]
    phone_number: str
    
    class Config:
        from_attributes = True

class SpecialAdminResponseSchema(BaseModel):
    id: int
    user: UserBasicInfo
    admin_type: AdminTypeSchema
    can_manage_users: bool
    can_manage_advertisements: bool
    can_manage_blogs: bool
    can_manage_agents: bool
    cities: List[Dict[str, Any]]
    provinces: List[Dict[str, Any]]
    status: int
    total_earnings: float
    created_at: datetime
    created_by: Optional[UserBasicInfo] = None
    
    class Config:
        from_attributes = True

class AdminTransactionCreateSchema(BaseModel):
    admin_id: int
    amount: float = Field(..., gt=0)
    transaction_type: int = Field(..., ge=1, le=5)  # 1=Commission, 2=Bonus, 3=Penalty, 4=Withdrawal, 5=Other
    description: Optional[str] = Field(None, max_length=200)
    is_positive: bool = True  # True for credit, False for debit

class AdminTransactionResponseSchema(BaseModel):
    id: int
    admin_id: int
    amount: float
    transaction_type: int
    description: Optional[str]
    is_positive: bool
    transaction_date: datetime
    
    class Config:
        from_attributes = True

class AdminActivityResponseSchema(BaseModel):
    id: int
    admin_id: int
    admin_name: str
    action_type: int  # 1=Create, 2=Update, 3=Delete, 4=View, 5=Other
    entity_type: str
    entity_id: int
    description: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class PaginatedSpecialAdminResponse(BaseModel):
    items: List[SpecialAdminResponseSchema]
    total: int
    page: int
    size: int
    pages: int

class PaginatedAdminActivityResponse(BaseModel):
    items: List[AdminActivityResponseSchema]
    total: int
    page: int
    size: int
    pages: int

class PaginatedTransactionResponse(BaseModel):
    items: List[AdminTransactionResponseSchema]
    total: int
    page: int
    size: int
    pages: int

