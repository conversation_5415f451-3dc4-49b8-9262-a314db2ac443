from django import forms

from ..models import CategoryAdvModel, SubCategoryAdvModel,AdModel,FeatureValueModel


class SubCategoryAdvForm(forms.ModelForm):

    def __init__(self, *args, **kwargs):
        super(SubCategoryAdvForm, self).__init__(*args, **kwargs)
        self.fields['parent_category'].queryset = CategoryAdvModel.objects.all()
        self.fields['parent_category'].label_from_instance = lambda obj: "%s" % obj.name
        # self.fields['name_author'].queryset = CustomUser.objects.all()
        # self.fields['name_author'].label_from_instance = lambda obj: "%s" % obj.first_name

    class Meta:
        model = SubCategoryAdvModel
        fields = '__all__'


class AdForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super(AdForm, self).__init__(*args, **kwargs)
        self.fields['main_category'].queryset = CategoryAdvModel.objects.all()
        self.fields['main_category'].label_from_instance = lambda obj: "%s" % obj.name
        self.fields['sub_category'].queryset = SubCategoryAdvModel.objects.all()
        self.fields['sub_category'].label_from_instance = lambda obj: "%s" % obj.name

    class Meta:
        model = AdModel
        fields = '__all__'


class FeatureForm(forms.ModelForm):

    def __init__(self, *args, **kwargs):
        super(FeatureForm, self).__init__(*args, **kwargs)
        self.fields['sub_category'].queryset = SubCategoryAdvModel.objects.all()
        self.fields['sub_category'].label_from_instance = lambda obj: "%s" % obj.name
        # self.fields['name_author'].queryset = CustomUser.objects.all()
        # self.fields['name_author'].label_from_instance = lambda obj: "%s" % obj.first_name

    class Meta:
        model = FeatureValueModel
        fields = '__all__'
