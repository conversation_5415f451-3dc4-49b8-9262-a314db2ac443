from django import forms
from app.models import ProvinceModel, CityModel, AdvertisementLocationModel
from django import forms
from django.contrib.gis.geos import Point


class AdvertisementLocationForm(forms.ModelForm):
    # latitude = forms.FloatField(
    #     min_value=-90,
    #     max_value=90,
    #     # required=True,
    # )
    # longitude = forms.FloatField(
    #     min_value=-180,
    #     max_value=180,
    #     # required=True,
    # )

    class Meta:
        model = AdvertisementLocationModel
        fields = '__all__'
        exclude = []
        # widgets = {'point': forms.HiddenInput()}

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     coordinates = self.initial.get('point', None)
    #     if isinstance(coordinates, Point):
    #         self.initial['longitude'], self.initial['latitude'] = coordinates.tuple
    #
    #
    def clean(self):
        data = super().clean()
        geolocation = data.get('geolocation')
        data['srid'] = geolocation.srid
        data['longitude'] = geolocation.x
        data['latitude'] = geolocation.y
        return data
