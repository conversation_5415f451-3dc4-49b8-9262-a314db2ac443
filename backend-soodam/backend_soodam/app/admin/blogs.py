from django.contrib import admin

# Register your models here.
from ..models import BlogPictureModel, BlogModel, BlogFileModel


#                     LikeBlogModel, DisLikeBlogModel, CommentModel, LikeCommentModel, \
# DisLikeCommentModel)


# from app.forms import BlogForm


class ImageInline(admin.TabularInline):
    model = BlogPictureModel
    # insert_after = 'title'


class FileInline(admin.TabularInline):
    model = BlogFileModel


class BlogAdmin(admin.ModelAdmin):
    list_display = ['post_title', 'post_status', 'post_description', 'created_at',
                    'updated_at', 'post_author', 'post_edited_by']
    list_filter = ['post_status', ]
    search_fields = ['post_title', 'post_content', 'post_description', 'post_author']
    # date_hierarchy = ''
    fields = ['post_title', 'post_description', 'post_content', 'post_status',
              'post_author', 'post_edited_by']
    inlines = [
        ImageInline,
        FileInline
    ]
    # form = BlogForm
    change_form_template = 'admin/change_form.html'

    class Media:
        css = {
            'all': (
                'css/admin.css',
            )
        }


admin.site.register(BlogModel, BlogAdmin)
# admin.site.register(LikeBlogModel, BlogAdmin)
# admin.site.register(DisLikeBlogModel, BlogAdmin)
# admin.site.register(CommentModel, BlogAdmin)
# admin.site.register(LikeCommentModel, BlogAdmin)
# admin.site.register(DisLikeCommentModel, BlogAdmin)
