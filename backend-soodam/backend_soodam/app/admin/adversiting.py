from django.contrib import admin
from django.contrib.contenttypes import admin as ct_admin

from django.contrib import admin
from django.contrib.contenttypes import admin as ct_admin

from ..forms.advertisement import AdvertisementLocationForm
from ..models import MainCategoryModel, SubCategoryModel, GenericImage, BaseAttributeModel, ChoiceAttributeModel, \
    ChoiceOptionModel, \
    BooleanAttributeModel, TextAttributeModel, PropertyBooleanValueModel, PropertyTextValueModel, PropertyModel, \
    AdvertisementModel, AdvertisementBooleanValueModel, AdvertisementTextValueModel, AdvertisementImagesModel, \
    HighlightAttributeModel, SubCategoryLevelTwoModel, FavoriteAdvertisementModel, \
    AdvertisementPriceModel, AdvertisementLocationModel
from django.contrib.gis.admin import  GISModelAdmin

from django.contrib import admin




@admin.register(MainCategoryModel)
class MainCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)
    # inlines = [GenericImageInline]


@admin.register(SubCategoryModel)
class SubCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'main_category')
    # inlines = [GenericImageInline]


@admin.register(SubCategoryLevelTwoModel)
class SubCategoryLevelTwoAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'sub_category')
    # inlines = [GenericImageInline]


@admin.register(ChoiceAttributeModel)
class ChoiceAttributeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name','key', 'star',)


@admin.register(ChoiceOptionModel)
class ChoiceOptionAdmin(admin.ModelAdmin):
    list_display = ('id', 'attribute', 'value',)


@admin.register(BooleanAttributeModel)
class BooleanAttributeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'star')


@admin.register(TextAttributeModel)
class TextAttributeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'star')


@admin.register(PropertyBooleanValueModel)
class PropertyBooleanValue(admin.ModelAdmin):
    list_display = ('id', 'attribute', 'value',)


@admin.register(PropertyTextValueModel)
class PropertyTextValue(admin.ModelAdmin):
    list_display = ('id', 'attribute', 'value',)


class PropertyBooleanValueInline(admin.TabularInline):
    model = PropertyBooleanValueModel
    extra = 1


class PropertyTextValueInline(admin.TabularInline):
    model = PropertyTextValueModel
    extra = 1


@admin.register(PropertyModel)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ('id', 'main_category__id',"main_category","sub_category__id","sub_category", 'is_active')
    list_filter = ('main_category', 'sub_category', 'is_active')
    search_fields = ()
    filter_horizontal = ('choice_attributes',)
    inlines = [PropertyBooleanValueInline, PropertyTextValueInline]


@admin.register(HighlightAttributeModel)
class HighlightAttributeAdmin(admin.ModelAdmin):
    list_display = ('id', 'sub_category')


@admin.register(AdvertisementBooleanValueModel)
class AdvertisementBooleanValueAdmin(admin.ModelAdmin):
    list_display = ('id', 'advertisement', 'value')


@admin.register(AdvertisementTextValueModel)
class AdvertisementTextValueAdmin(admin.ModelAdmin):
    list_display = ('id', 'advertisement', 'value')


class AdvertisementBooleanValueInline(admin.TabularInline):
    model = AdvertisementBooleanValueModel
    extra = 1


class AdvertisementTextValueInline(admin.TabularInline):
    model = AdvertisementTextValueModel
    extra = 1


class AdvertisingImagesInlineAdmin(admin.TabularInline):
    model = AdvertisementImagesModel
    extra = 1


class AdvertisementPriceInline(admin.StackedInline):
    model = AdvertisementPriceModel
    extra = 1
    max_num = 1
    can_delete = False
    verbose_name = "Price Information"
    verbose_name_plural = "Price Information"
    fields = ('amount', 'deposit', 'rent', 'currency', 'is_negotiable', 
              'discount_amount', 'original_amount', 'price_per_unit', 'unit')

@admin.register(AdvertisementLocationModel)
class AdvertisementAddressAdmin(GISModelAdmin):
    raw_id_fields = ('province', 'city')
    autocomplete_fields = ('province', 'city')
    # inlines = [GenericGeoLocationInline]
    # form = AdvertisementLocationForm

class AdvertisementAddressInline(admin.StackedInline):
    model = AdvertisementLocationModel
    extra = 1
    max_num = 1
    can_delete = False
    verbose_name = "Address Information"
    verbose_name_plural = "Address Information"
    fields = ('province', 'city', 'address', 'zip_code', 'latitude', 'longitude')


@admin.register(AdvertisementModel)
class AdvertisementAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'main_category', 'sub_category', 'get_price', 'get_address', 'status', 'is_active', 'created_at')
    list_filter = ('main_category', 'sub_category', 'status', 'is_active')
    search_fields = ('title', 'description', 'user__phone_number')
    readonly_fields = ('created_at', 'updated_at', 'get_price', 'get_address')
    list_per_page = 20
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'user', 'status', 'is_active')
        }),
        ('Categories', {
            'fields': ('main_category', 'sub_category', 'sub_category_level_two')
        }),
        ('Price Information', {
            'fields': ('get_price',),
            'classes': ('collapse',)
        }),
        ('Location Information', {
            'fields': ('get_address',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'review')
        }),
    )

    inlines = [AdvertisementPriceInline, AdvertisementAddressInline, 
               AdvertisingImagesInlineAdmin, AdvertisementBooleanValueInline, 
               AdvertisementTextValueInline]
    
    @admin.display(description="Price")
    def get_price(self, obj):
        """Display price information in the admin."""
        from django.utils.html import format_html
        from asgiref.sync import sync_to_async
        
        try:
            # Get the price object synchronously in admin context
            price = obj.price.first()
            
            if not price:
                return "No price information"
                
            price_info = []
            
            if price.amount > 0:
                price_info.append(f"<strong>Amount:</strong> {price.amount:,} {price.currency}")
                
            if price.deposit > 0:
                price_info.append(f"<strong>Deposit:</strong> {price.deposit:,} {price.currency}")
                
            if price.rent > 0:
                price_info.append(f"<strong>Rent:</strong> {price.rent:,} {price.currency}")
                
            if price.price_per_unit > 0:
                price_info.append(f"<strong>Price per {price.unit}:</strong> {price.price_per_unit:,} {price.currency}")
                
            if price.is_negotiable:
                price_info.append("<strong>Negotiable:</strong> Yes")
                
            if price.discount_amount > 0:
                price_info.append(f"<strong>Discount:</strong> {price.discount_amount:,} {price.currency}")
                
            if not price_info:
                return "No price details available"
                
            return format_html("<br>".join(price_info))
            
        except Exception as e:
            return f"Error retrieving price: {str(e)}"
    
    @admin.display(description="Address")
    def get_address(self, obj):
        """Display address information in the admin."""
        from django.utils.html import format_html
        
        try:
            # Get the address object synchronously in admin context
            address = obj.address.first()
            
            if not address:
                return "No address information"
                
            address_info = []
            
            if hasattr(address, 'province') and address.province:
                address_info.append(f"<strong>Province:</strong> {address.province.name}")
                
            if hasattr(address, 'city') and address.city:
                address_info.append(f"<strong>City:</strong> {address.city.name}")
                
            if address.address:
                address_info.append(f"<strong>Address:</strong> {address.address}")
                
            if address.zip_code:
                address_info.append(f"<strong>Zip Code:</strong> {address.zip_code}")
                
            if address.latitude and address.longitude:
                map_link = f"https://www.google.com/maps?q={address.latitude},{address.longitude}"
                address_info.append(f"<strong>Coordinates:</strong> <a href='{map_link}' target='_blank'>{address.latitude}, {address.longitude}</a>")
                
            if not address_info:
                return "No address details available"
                
            return format_html("<br>".join(address_info))
            
        except Exception as e:
            return f"Error retrieving address: {str(e)}"
    
    def delete_model(self, request, obj):
        """
        Custom delete method to handle related data deletion.
        """
        # Use async_to_sync to call our async delete method
        from asgiref.sync import async_to_sync
        success, message = async_to_sync(AdvertisementModel.delete_with_relations)(obj.id)
        if not success:
            self.message_user(request, message, level='error')
        else:
            self.message_user(request, "Advertisement and all related data deleted successfully")
    
    def delete_queryset(self, request, queryset):
        """
        Custom delete method for queryset to handle related data deletion.
        """
        from asgiref.sync import async_to_sync
        for obj in queryset:
            success, message = async_to_sync(AdvertisementModel.delete_with_relations)(obj.id)
            if not success:
                self.message_user(request, f"Error deleting advertisement {obj.id}: {message}", level='error')
        
        self.message_user(request, f"Deleted {queryset.count()} advertisements and their related data")


@admin.register(FavoriteAdvertisementModel)
class FavoriteAdvertisementAdmin(admin.ModelAdmin):
    list_display = ('user', 'advertisement', 'created_at')
    search_fields = ('user__username', 'advertisement__title')
