# from django.contrib.gis import admin
from django.contrib.gis.geos import Point

from ..models import ProvinceModel, CityModel
from django.contrib.contenttypes import admin as ct_admin
from django.contrib import admin
from ..forms import AddressForm

from django.contrib.gis.admin import  GISModelAdmin


# class GeoLocationInline(admin.TabularInline):
#     model = GeoLocationModel
#     extra = 1

@admin.register(ProvinceModel)
class ProvinceAdmin(admin.ModelAdmin):
    list_display = ('id', 'province_id', 'name', 'slug', 'tel_prefix')
    search_fields = ('name',)


@admin.register(CityModel)
class CityAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'slug')
    search_fields = ('name',)


# @admin.register(GeoLocationModel)
# class GeoLocationAdmin(admin.ModelAdmin):
#     pass

