from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ..models.admin import AdminType, SpecialAdmin, AdminTransaction, AdminActivity

# Register admin models
@admin.register(AdminType)
class AdminTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name',)

class AdminTransactionAdmin(admin.TabularInline):
    model = AdminTransaction
    extra = 1
    # readonly_fields = ('transaction_date',)
@admin.register(SpecialAdmin)
class SpecialAdminAdmin(admin.ModelAdmin):
    list_display = ('user', 'admin_type', 'status', 'total_earnings', 'created_at')
    list_filter = ('admin_type', 'status', 'can_manage_users', 'can_manage_advertisements', 
                  'can_manage_blogs', 'can_manage_agents')
    search_fields = ('user__first_name', 'user__last_name', 'user__email', 'user__phone_number')
    readonly_fields = ('created_at', 'updated_at', 'total_earnings')
    filter_horizontal = ('cities', 'provinces')
    raw_id_fields = ('user', 'created_by')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('user', 'admin_type', 'status')
        }),
        (_('Permissions'), {
            'fields': ('can_manage_users', 'can_manage_advertisements', 
                      'can_manage_blogs', 'can_manage_agents')
        }),
        (_('Geographic Scope'), {
            'fields': ('cities', 'provinces')
        }),
        (_('Financial Information'), {
            'fields': ('total_earnings',)
        }),
        (_('Audit Information'), {
            'fields': ('created_at', 'created_by')
        }),
    )
    
    inlines = [
        AdminTransactionAdmin
        # admin.TabularInline(
        #     model=AdminTransaction,
        #     extra=1,
        #     readonly_fields=('transaction_date',)
        # )
    ]

@admin.register(AdminTransaction)
class AdminTransactionAdmin(admin.ModelAdmin):
    list_display = ('admin', 'amount', 'transaction_type', 'is_positive', 'transaction_date')
    list_filter = ('transaction_type', 'is_positive')
    search_fields = ('admin__user__first_name', 'admin__user__last_name', 'description')
    readonly_fields = ('transaction_date',)
    raw_id_fields = ('admin',)

@admin.register(AdminActivity)
class AdminActivityAdmin(admin.ModelAdmin):
    list_display = ('admin', 'action_type', 'entity_type', 'entity_id', 'created_at', 'ip_address')
    list_filter = ('action_type', 'entity_type', 'created_at')
    search_fields = ('admin__user__first_name', 'admin__user__last_name', 'description', 'entity_type')
    readonly_fields = ('created_at', 'ip_address')
    raw_id_fields = ('admin',)
