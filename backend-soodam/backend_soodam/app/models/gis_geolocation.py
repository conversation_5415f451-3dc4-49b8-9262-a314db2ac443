from email.policy import default

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.geos import Point
from django.utils.translation import gettext_lazy as _
from django.contrib.gis.db import models

class CountryModel(models.Model):
    Country_id = models.IntegerField(default=0)
    name = models.CharField(max_length=255, blank=False, null=False)
    slug = models.CharField(max_length=255, blank=False, null=False)
    tel_prefix = models.CharField(max_length=255, blank=False, null=False)
    def __str__(self):
        return self.name

    class Meta:
        db_table = 'country'
        verbose_name = "کشورها"
        verbose_name_plural = "کشور ها"


class ProvinceModel(models.Model):
    province_id = models.IntegerField(default=0)
    name = models.CharField(max_length=255, blank=False, null=False)
    slug = models.CharField(max_length=255, blank=False, null=False)
    tel_prefix = models.CharField(max_length=255, blank=False, null=False)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'province'
        verbose_name = "استان ها"
        verbose_name_plural = "استان ها"


class CityModel(models.Model):
    province = models.ForeignKey(ProvinceModel, on_delete=models.DO_NOTHING, null=True, )
    name = models.CharField(max_length=255, blank=False, null=False, default='')
    slug = models.CharField(max_length=255, blank=False, null=False, default='')

    def __str__(self):
        return self.name

    @property
    def get_state_name(self):
        try:
            if self.province:
                return ProvinceModel.objects.get(id=int(self.province.pk)).name
            else:
                return map(lambda c: c.name, ProvinceModel.objects.all())
        except Exception as e:
            return "Error:%s" % str(e)

    class Meta:
        db_table = 'city'
        verbose_name = "شهرها"
        verbose_name_plural = "شهرها"

