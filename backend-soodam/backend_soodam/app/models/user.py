"""
Enhanced User Models for Soodam Backend

This module contains comprehensive user models with improved validation,
security features, and Persian language support.
"""

import uuid
import logging
import re
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, List

import jdatetime
from dateutil.relativedelta import relativedelta
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.contrib.contenttypes.fields import GenericRelation
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator, RegexValidator
from django.conf import settings
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_jalali.db import models as jmodels
from jdatetime import datetime as jdatetime
from model_utils import Choices
from .base import BaseModelMixin
from .gis_geolocation import CityModel, ProvinceModel

logger = logging.getLogger(__name__)

# =============================================================================
# CONSTANTS AND CHOICES
# =============================================================================

# Iranian phone number validator
IRANIAN_PHONE_REGEX = r'^09[0-9]{9}$'
iranian_phone_validator = RegexValidator(
    regex=IRANIAN_PHONE_REGEX,
    message=_('شماره تلفن باید با 09 شروع شده و 11 رقم باشد'),
    code='invalid_phone'
)


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def normalize_iranian_phone_number(phone_number: str) -> str:
    """
    Utility function to normalize Iranian phone number format.

    This function can be used anywhere in the codebase without needing
    access to model instances or managers.

    Args:
        phone_number: Raw phone number in various formats

    Returns:
        Normalized phone number in 09xxxxxxxxx format

    Raises:
        ValueError: If phone number format is invalid

    Examples:
        >>> normalize_iranian_phone_number("989123456789")
        "09123456789"
        >>> normalize_iranian_phone_number("+98 ************")
        "09123456789"
        >>> normalize_iranian_phone_number("091-234-56789")
        "09123456789"
    """
    if not phone_number:
        raise ValueError(_('شماره تلفن نمی‌تواند خالی باشد'))

    # Remove spaces, dashes, and other non-digit characters
    cleaned = ''.join(filter(str.isdigit, phone_number))

    if not cleaned:
        raise ValueError(_('شماره تلفن باید حداقل یک رقم داشته باشد'))

    # Handle different formats with proper length validation
    if cleaned.startswith('0098') and len(cleaned) == 14:
        # Convert 00989123456789 to 09123456789
        cleaned = '0' + cleaned[4:]
    elif cleaned.startswith('98') and len(cleaned) == 12:
        # Convert 989123456789 to 09123456789
        cleaned = '0' + cleaned[2:]
    elif not cleaned.startswith('0') and len(cleaned) == 10:
        # Convert 9123456789 to 09123456789
        cleaned = '0' + cleaned

    # Validate final format
    if not re.match(IRANIAN_PHONE_REGEX, cleaned):
        raise ValueError(_('فرمت شماره تلفن نامعتبر است. شماره باید با 09 شروع شده و 11 رقم باشد'))

    return cleaned

# Iranian national code validator
IRANIAN_NATIONAL_CODE_REGEX = r'^[0-9]{10}$'
iranian_national_code_validator = RegexValidator(
    regex=IRANIAN_NATIONAL_CODE_REGEX,
    message=_('کد ملی باید 10 رقم باشد'),
    code='invalid_national_code'
)

# Global status choices
GlobalStatus = (
    (1, _('فعال')),
    (2, _('غیرفعال'))
)
# Gender choices with Persian labels
GENDER_TYPE = Choices(
    ('unknown', _('نامشخص')),
    ('male', _('مرد')),
    ('female', _('زن'))
)
APP_SERVICES=(
    (1, _("Soodam")),
    (1, _("Agent")),
    (1, _("Admin")),

)
# Admin group choices
ADMIN_GROUP = (
    (1, _('سوپر ادمین')),
    (2, _('ادمین')),
    (3, _('کاربر اشتراکی')),
    (4, _('کاربر عادی'))
)
# User type choices
USER_TYPE_CHOICES = (
    (1, _("کاربر معمولی")),
    (2, _("کاربر عضو")),
    (3, _("بازاریاب")),
    (4, _("بنگاه املاک")),
)


# =============================================================================
# ENHANCED USER MANAGER
# =============================================================================

class EnhancedUserManager(BaseUserManager):
    """Enhanced user manager with comprehensive validation and logging."""

    use_in_migrations = True

    def _create_user(
        self,
        phone_number: str = None,
        email: str = None,
        password: str = None,
        **extra_fields
    ) -> "CustomUserModel":
        """
        Create and save a user with comprehensive validation.

        Args:
            phone_number: Iranian phone number (primary identifier)
            email: Email address (optional)
            password: User password
            **extra_fields: Additional user fields

        Returns:
            CustomUserModel instance

        Raises:
            ValueError: If required fields are missing or invalid
        """
        # Validate phone number (primary identifier)
        if not phone_number:
            raise ValueError(_('شماره تلفن الزامی است'))

        # Normalize and validate phone number
        phone_number = self._normalize_phone_number(phone_number)

        # Normalize email if provided
        if email:
            email = self.normalize_email(email)

        # Create user instance
        user = self.model(
            phone_number=phone_number,
            email=email,
            **extra_fields
        )

        # Set password if provided
        if password:
            user.set_password(password)

        # Save user
        user.save(using=self._db)

        # Log user creation
        logger.info(f"User created successfully: {phone_number}")

        return user

    def _normalize_phone_number(self, phone_number: str) -> str:
        """
        Manager method for phone number normalization.

        Args:
            phone_number: Raw phone number

        Returns:
            Normalized phone number

        Raises:
            ValueError: If phone number format is invalid
        """
        return normalize_iranian_phone_number(phone_number)

    def create_user(self, phone_number: str, email: str = None, password: str = None, **extra_fields) -> "CustomUserModel":
        """Create a regular user."""
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        extra_fields.setdefault("user_type", 1)  # Regular user
        extra_fields.setdefault("user_group", 4)  # Regular user group

        return self._create_user(phone_number, email, password, **extra_fields)

    def create_superuser(self, phone_number: str, email: str = None, password: str = None, **extra_fields) -> "CustomUserModel":
        """Create a superuser."""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_admin", True)
        extra_fields.setdefault("user_type", 1)
        extra_fields.setdefault("user_group", 1)  # SuperAdmin

        if extra_fields.get("is_staff") is not True:
            raise ValueError(_("سوپر یوزر باید is_staff=True باشد"))
        if extra_fields.get("is_superuser") is not True:
            raise ValueError(_("سوپر یوزر باید is_superuser=True باشد"))

        return self._create_user(phone_number, email, password, **extra_fields)

    def get_by_phone(self, phone_number: str) -> Optional["CustomUserModel"]:
        """Get user by phone number with normalization."""
        try:
            normalized_phone = self._normalize_phone_number(phone_number)
            return self.get(phone_number=normalized_phone)
        except (self.model.DoesNotExist, ValueError):
            return None

    def get_by_email(self, email: str) -> Optional["CustomUserModel"]:
        """Get user by email address."""
        try:
            return self.get(email=email)
        except self.model.DoesNotExist:
            return None

    def get_by_username_field(self, username: str) -> Optional["CustomUserModel"]:
        """
        Get user by either email or phone number.

        This method supports dual username functionality by trying
        to find the user using either email or phone number.

        Args:
            username: Email address or phone number

        Returns:
            CustomUserModel instance or None if not found
        """
        # First, try to determine if it's an email or phone number
        if '@' in username:
            # Looks like an email
            return self.get_by_email(username)
        else:
            # Assume it's a phone number
            return self.get_by_phone(username)

    def get_by_any_username(self, username: str) -> Optional["CustomUserModel"]:
        """
        Get user by email, phone number, or username field.

        This method provides comprehensive username lookup supporting
        all possible username fields.

        Args:
            username: Email, phone number, or username

        Returns:
            CustomUserModel instance or None if not found
        """
        # Try email first
        user = self.get_by_email(username)
        if user:
            return user

        # Try phone number
        user = self.get_by_phone(username)
        if user:
            return user

        # Try username field
        try:
            return self.get(username=username)
        except self.model.DoesNotExist:
            return None

    def authenticate_user(self, username: str, password: str) -> Optional["CustomUserModel"]:
        """
        Authenticate user with dual username support.

        Args:
            username: Email address or phone number
            password: User password

        Returns:
            CustomUserModel instance if authentication successful, None otherwise
        """
        from django.contrib.auth.hashers import check_password

        # Get user by any username field
        user = self.get_by_any_username(username)

        if not user:
            return None

        # Check password
        if not check_password(password, user.password):
            return None

        # Check if user is active
        if not user.is_active:
            return None

        return user

    def active_users(self):
        """Get only active users."""
        return self.filter(is_active=True)

    def verified_users(self):
        """Get only verified users."""
        return self.filter(is_verified=True)


# Legacy alias for backward compatibility
UserManager = EnhancedUserManager

class UserManager(BaseUserManager):
    use_in_migrations = True

    def _create_user(
            self, username: str, email: str, password: str, **extra_fields: dict
    ) -> "CustomUserModel":
        """Create and save a user with the given username, email, and
        password."""
        if not username:
            raise ValueError("The given username must be set.")

        email = self.normalize_email(email)
        username = self.model.normalize_username(username)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(  # type: ignore
            self, username: str, email: str, password: str, **extra_fields
    ) -> "User":
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(username, email, password, **extra_fields)

    def create_superuser(  # type: ignore
            self, username: str, email: str, password: str, **extra_fields
    ) -> "User":
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")
        return self._create_user(username, email, password, **extra_fields)



# =============================================================================
# ENHANCED CUSTOM USER MODEL
# =============================================================================


class CustomUserModel(AbstractBaseUser, PermissionsMixin, BaseModelMixin):
    """
    Enhanced Custom User Model with comprehensive validation and features.

    This model serves as the primary user model for the Soodam platform,
    supporting Iranian phone numbers, Persian names, and comprehensive
    user management features.
    """

    # objects = UserManager()
    objects = EnhancedUserManager()

    # =============================================================================
    # CORE IDENTIFICATION FIELDS
    # =============================================================================

    # Phone number as primary identifier (Iranian format)
    phone_number = models.CharField(
        _('شماره تلفن'),
        max_length=11,
        unique=True,
        validators=[iranian_phone_validator],
        help_text=_('شماره تلفن همراه با فرمت 09xxxxxxxxx'),
        db_index=True
    )

    # Email (optional but unique if provided)
    email = models.EmailField(
        _('آدرس ایمیل'),
        max_length=254,
        unique=True,
        blank=True,
        null=True,
        db_index=True
    )

    # Username (optional, for display purposes)
    username = models.CharField(
        _('نام کاربری'),
        max_length=50,
        blank=True,
        null=True,
        help_text=_('نام کاربری اختیاری برای نمایش')
    )

    # =============================================================================
    # PERSONAL INFORMATION FIELDS
    # =============================================================================

    # Persian names with proper length
    first_name = models.CharField(
        _('نام'),
        max_length=50,
        blank=True,
        null=True,
        help_text=_('نام کوچک')
    )

    last_name = models.CharField(
        _('نام خانوادگی'),
        max_length=50,
        blank=True,
        null=True,
        help_text=_('نام خانوادگی')
    )

    father_name = models.CharField(
        _('نام پدر'),
        max_length=50,
        blank=True,
        null=True,
        help_text=_('نام پدر')
    )

    # Iranian national code with validation
    security_number = models.CharField(
        _('کد ملی'),
        max_length=10,
        blank=True,
        null=True,
        validators=[iranian_national_code_validator],
        help_text=_('کد ملی 10 رقمی'),
        db_index=True
    )

    # Gender with Persian choices
    gender = models.CharField(
        _('جنسیت'),
        choices=GENDER_TYPE,
        default=GENDER_TYPE.unknown,
        max_length=20,
        blank=True
    )

    # Birthday using Jalali calendar
    birthday = jmodels.jDateField(
        _('تاریخ تولد'),
        blank=True,
        null=True,
        help_text=_('تاریخ تولد به تقویم شمسی')
    )

    # =============================================================================
    # LOCATION FIELDS
    # =============================================================================

    # Province and city for location
    province = models.ForeignKey(
        ProvinceModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('استان'),
        help_text=_('استان محل سکونت')
    )

    city = models.ForeignKey(
        CityModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('شهر'),
        help_text=_('شهر محل سکونت')
    )

    # Multiple addresses support
    address = models.ManyToManyField(
        "CustomUserLocationModel",
        blank=True,
        related_name='users',
        verbose_name=_('آدرس‌ها'),
        help_text=_('آدرس‌های کاربر')
    )

    # Country code for international support
    country_code = models.CharField(
        _('کد کشور'),
        max_length=5,
        blank=True,
        default='IR',
        help_text=_('کد کشور (پیش‌فرض: ایران)')
    )

    # =============================================================================
    # USER STATUS AND PERMISSIONS
    # =============================================================================

    # User type with Persian labels
    user_type = models.IntegerField(
        _('نوع کاربر'),
        default=1,
        choices=USER_TYPE_CHOICES,
        help_text=_('نوع کاربر در سیستم')
    )

    # User group for admin hierarchy
    user_group = models.IntegerField(
        _('گروه کاربری'),
        choices=ADMIN_GROUP,
        default=4,
        help_text=_('سطح دسترسی کاربر')
    )


    # Status flags
    is_active = models.BooleanField(
        _('فعال'),
        default=True,
        help_text=_('آیا این کاربر فعال است؟')
    )

    is_staff = models.BooleanField(
        _('کارمند'),
        default=False,
        help_text=_('آیا این کاربر می‌تواند به پنل ادمین دسترسی داشته باشد؟')
    )

    is_verified = models.BooleanField(
        _('تایید شده'),
        default=False,
        help_text=_('آیا شماره تلفن کاربر تایید شده است؟')
    )

    is_admin = models.BooleanField(
        _('ادمین'),
        default=False,
        help_text=_('آیا این کاربر ادمین است؟')
    )

    # =============================================================================
    # SECURITY AND VERIFICATION FIELDS
    # =============================================================================

    # Unique verification UUID for security
    verification_uuid = models.UUIDField(
        _('شناسه تایید'),
        default=uuid.uuid4,
        unique=True,
        help_text=_('شناسه منحصر به فرد برای تایید کاربر')
    )

    # Device IMEI for mobile app security
    IMEI = models.CharField(
        _('شناسه دستگاه'),
        max_length=15,
        unique=True,
        blank=True,
        null=True,
        help_text=_('شناسه IMEI دستگاه موبایل')
    )

    # =============================================================================
    # MEDIA AND AVATAR FIELDS
    # =============================================================================

    # User avatar with enhanced validation
    avatar = models.FileField(
        _('تصویر پروفایل'),
        upload_to='documents/user/avatar',
        # default='documents/user/avatar/user.png',
        null=True,
        blank=True,
        validators=[
            FileExtensionValidator(
                allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'webp']
            )
        ],
        help_text=_('تصویر پروفایل کاربر (حداکثر 5MB)')
    )

    # =============================================================================
    # TIMESTAMP FIELDS
    # =============================================================================

    # Join date (when user first registered)
    date_joined = models.DateTimeField(
        _('تاریخ عضویت'),
        default=timezone.now,
        help_text=_('تاریخ عضویت در سیستم')
    )

    # Jalali timestamps for Persian calendar support
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)"),
        help_text=_('تاریخ ایجاد به تقویم شمسی')
    )

    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ آپدیت (شمسی)"),
        help_text=_('تاریخ آخرین به‌روزرسانی به تقویم شمسی')
    )

    # =============================================================================
    # DJANGO AUTH CONFIGURATION
    # =============================================================================

    # Support both email and phone_number as username fields
    USERNAME_FIELD = "phone_number"  # Primary username field for Django admin
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = ["email"]  # Required when creating superuser

    # Custom fields for dual username support
    DUAL_USERNAME_FIELDS = ["email", "phone_number"]  # Both can be used for login

    class Meta:
        verbose_name = _('کاربر')
        verbose_name_plural = _('کاربران')
        ordering = ["-created_at"]
        db_table = 'custom_user'
        indexes = [
            models.Index(fields=['phone_number']),
            models.Index(fields=['email']),
            models.Index(fields=['is_active', 'is_verified']),
            models.Index(fields=['user_type', 'user_group']),
            models.Index(fields=['province', 'city']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(phone_number__regex=IRANIAN_PHONE_REGEX),
                name='valid_phone_number'
            ),
        ]

    # =============================================================================
    # ENHANCED MODEL METHODS
    # =============================================================================

    def __str__(self) -> str:
        """String representation of the user."""
        if self.get_full_name():
            return self.get_full_name()
        elif self.phone_number:
            return self.phone_number
        else:
            return f"User {self.id}"

    def __repr__(self) -> str:
        """Developer representation of the user."""
        return f"<CustomUserModel: {self.phone_number} ({self.get_full_name()})>"

    def get_full_name(self) -> str:
        """
        Get user's full name in Persian format.

        Returns:
            Full name string or empty string if no name provided
        """
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}".strip()
        elif self.first_name:
            return self.first_name.strip()
        elif self.last_name:
            return self.last_name.strip()
        else:
            return ""

    def get_short_name(self) -> str:
        """
        Get user's short name (first name or phone number).

        Returns:
            Short name string
        """
        return self.first_name or self.phone_number or "کاربر"

    def get_display_name(self) -> str:
        """
        Get user's display name for UI purposes.

        Returns:
            Display name string
        """
        full_name = self.get_full_name()
        if full_name:
            return full_name
        elif self.username:
            return self.username
        else:
            return f"کاربر {self.phone_number}"

    def get_avatar_url(self) -> str:
        """
        Get user's avatar URL.

        Returns:
            Avatar URL string
        """
        if self.avatar and hasattr(self.avatar, 'url'):
            return self.avatar.url
        else:
            return None
            # return f"{settings.MEDIA_URL}documents/user/avatar/user.png"

    def get_username_display(self) -> str:
        """
        Get the primary username for display purposes.

        Returns the email if available, otherwise phone number.

        Returns:
            Primary username string
        """
        if self.email:
            return self.email
        elif self.phone_number:
            return self.phone_number
        else:
            return f"User {self.id}"

    def get_all_usernames(self) -> List[str]:
        """
        Get all possible username values for this user.

        Returns:
            List of all username values (email, phone_number, username)
        """
        usernames = []

        if self.email:
            usernames.append(self.email)
        if self.phone_number:
            usernames.append(self.phone_number)
        if self.username:
            usernames.append(self.username)

        return usernames

    def can_login_with(self, username: str) -> bool:
        """
        Check if user can login with the given username.

        Args:
            username: Username to check

        Returns:
            True if user can login with this username, False otherwise
        """
        # Normalize phone number if it looks like one
        if not '@' in username:
            try:
                username = normalize_iranian_phone_number(username)
            except ValueError:
                pass  # Not a valid phone number, continue with original

        return username in self.get_all_usernames()

    def is_profile_complete(self) -> bool:
        """
        Check if user profile is complete.

        Returns:
            True if profile is complete, False otherwise
        """
        required_fields = [
            self.first_name,
            self.last_name,
            self.phone_number,
            self.is_verified
        ]
        return all(required_fields)

    def get_age(self) -> Optional[int]:
        """
        Calculate user's age based on birthday.

        Returns:
            Age in years or None if birthday not set
        """
        if not self.birthday:
            return None

        try:
            today = jdatetime.date.today()
            age = today.year - self.birthday.year

            # Adjust if birthday hasn't occurred this year
            if today.month < self.birthday.month or \
               (today.month == self.birthday.month and today.day < self.birthday.day):
                age -= 1

            return age
        except (AttributeError, ValueError):
            return None

    def clean(self):
        """
        Validate model data.

        Raises:
            ValidationError: If validation fails
        """
        super().clean()

        # Validate phone number format
        if self.phone_number:
            iranian_phone_validator(self.phone_number)

        # Validate national code format
        if self.security_number:
            iranian_national_code_validator(self.security_number)

        # Validate city belongs to province
        if self.city and self.province:
            if self.city.province_id != self.province.id:
                raise ValidationError({
                    'city': _('شهر انتخابی متعلق به استان انتخابی نیست')
                })

    # def save(self, *args, **kwargs):
    #     """
    #     Enhanced save method with validation and logging.
    #     """
    #     # Run validation
    #     self.full_clean()
    #
    #     # Normalize phone number using class method
    #     if self.phone_number:
    #         self.phone_number = self._normalize_phone_number_instance(self.phone_number)
    #
    #     # Log user creation/update
    #     is_new = self.pk is None
    #
    #     # Call parent save
    #     super().save(*args, **kwargs)
    #
    #     # Log the action
    #     if is_new:
    #         logger.info(f"New user created: {self.phone_number}")
    #     else:
    #         logger.info(f"User updated: {self.phone_number}")

    def _normalize_phone_number_instance(self, phone_number: str) -> str:
        """
        Instance method for phone number normalization.

        Args:
            phone_number: Raw phone number

        Returns:
            Normalized phone number

        Raises:
            ValueError: If phone number format is invalid
        """
        return normalize_iranian_phone_number(phone_number)

    def delete(self, using=None, keep_parents=False):
        """
        Enhanced delete method with logging.
        """
        phone_number = self.phone_number
        result = super().delete(using, keep_parents)
        logger.info(f"User deleted: {phone_number}")
        return result

    # Add a method to handle address management
    async def add_address(self, address_data: dict) -> 'CustomUserLocationModel':
        """
        Add a new address for the user.
        
        Args:
            address_data: Dictionary containing address information
            
        Returns:
            CustomUserLocationModel instance
        """
        from django.contrib.gis.geos import Point
        
        # Create the address
        address = await CustomUserLocationModel.objects.acreate(**address_data)
        # add to user's addresses   
        await self.address.aadd(address)
        
        return address

    async def remove_address(self, address_id: int) -> bool:
        """
        Remove an address from the user's addresses.
        
        Args:
            address_id: ID of the address to remove
            
        Returns:
            bool: True if address was removed, False otherwise
        """
        try:
            address = await CustomUserLocationModel.objects.aget(id=address_id)
            await self.address.aremove(address)
            return True
        except CustomUserLocationModel.DoesNotExist:
            return False

    async def get_addresses(self) -> List['CustomUserLocationModel']:
        """
        Get all addresses for the user.
        
        Returns:
            List of CustomUserLocationModel instances
        """
        return [addr async for addr in self.address.select_related('province', 'city').all()]

# =============================================================================
# ENHANCED USER Location MODEL
# =============================================================================
class CustomUserLocationModel(models.Model):
    """
    Enhanced user location model with comprehensive address management.
    Supports multiple addresses per user with geolocation capabilities.
    """
    # Remove the direct ForeignKey to user since we're using ManyToMany
    # user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE)
    
    # Location fields
    province = models.ForeignKey(
        ProvinceModel,
        on_delete=models.CASCADE,
        verbose_name=_('استان'),
        help_text=_('استان محل')
    )
    
    city = models.ForeignKey(
        CityModel,
        on_delete=models.CASCADE,
        verbose_name=_('شهر'),
        help_text=_('شهر محل')
    )
    
    street = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('نام خیابان اصلی'),
        help_text=_('نام خیابان اصلی')
    )
    
    address = models.CharField(
        max_length=255,
        null=False,
        blank=False,
        verbose_name=_('آدرس'),
        help_text=_('آدرس کامل')
    )
    
    zip_code = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_('کد پستی'),
        help_text=_('کد پستی 10 رقمی')
    )
    
    # Geolocation fields
    srid = models.IntegerField(
        blank=True,
        null=True,
        verbose_name=_('Srid'),
        help_text=_('Srid (4326)')
    )
    
    longitude = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_('طول جغرافیایی'),
        help_text=_('Longitude or x')
    )
    
    latitude = models.FloatField(
        blank=True,
        null=True,
        verbose_name=_('عرض جغرافیایی'),
        help_text=_('Latitude or y')
    )
    
    # GIS field for geolocation
    from django.contrib.gis.db import models
    geolocation = models.PointField(
        geography=True,
        null=True,
        blank=True,
        verbose_name=_('موقعیت جغرافیایی'),
        help_text=_('موقعیت جغرافیایی نقطه')
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاریخ ایجاد')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاریخ به‌روزرسانی')
    )
    
    # Jalali timestamps
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)")
    )
    
    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ به‌روزرسانی (شمسی)")
    )

    class Meta:
        db_table = "user_locations"
        verbose_name = _('آدرس کاربر')
        verbose_name_plural = _('آدرس‌های کاربران')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['province', 'city']),
            models.Index(fields=['zip_code']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.address} - {self.city.name if self.city else ''}"

    def save(self, *args, **kwargs):
        # Update geolocation if latitude and longitude are provided
        if self.latitude is not None and self.longitude is not None:
            from django.contrib.gis.geos import Point
            self.geolocation = Point(self.longitude, self.latitude, srid=4326)
        super().save(*args, **kwargs)

    def get_full_address(self):
        """Get formatted full address."""
        parts = []
        if self.province:
            parts.append(self.province.name)
        if self.city:
            parts.append(self.city.name)
        if self.street:
            parts.append(self.street)
        if self.address:
            parts.append(self.address)
        return "، ".join(parts)

# =============================================================================
# ENHANCED USER RATING MODEL
# =============================================================================

class UserRating(models.Model):
    """Enhanced user rating model with comprehensive rating system."""

    user = models.OneToOneField(
        "CustomUserModel",
        on_delete=models.CASCADE,
        related_name='rating',
        verbose_name=_('کاربر')
    )

    positive_rating = models.FloatField(
        _('امتیاز مثبت'),
        default=0.0,
        help_text=_('مجموع امتیازات مثبت')
    )

    negative_rating = models.FloatField(
        _('امتیاز منفی'),
        default=0.0,
        help_text=_('مجموع امتیازات منفی')
    )

    total_reviews = models.PositiveIntegerField(
        _('تعداد نظرات'),
        default=0,
        help_text=_('تعداد کل نظرات ثبت شده')
    )

    average_rating = models.FloatField(
        _('میانگین امتیاز'),
        default=0.0,
        help_text=_('میانگین امتیاز کاربر')
    )
    # Jalali timestamps for Persian calendar support
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)"),
        help_text=_('تاریخ ایجاد به تقویم شمسی')
    )

    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ آپدیت (شمسی)"),
        help_text=_('تاریخ آخرین به‌روزرسانی به تقویم شمسی')
    )


    class Meta:
        db_table = 'custom_user_rating'
        verbose_name = _('امتیاز کاربر')
        verbose_name_plural = _('امتیازات کاربران')
        indexes = [
            models.Index(fields=['average_rating']),
            models.Index(fields=['total_reviews']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.get_rating():.1f}"

    def get_rating(self) -> float:
        """Calculate net rating (positive - negative)."""
        return self.positive_rating - self.negative_rating

    def get_percentage_rating(self) -> float:
        """Calculate percentage rating (0-100)."""
        if self.total_reviews == 0:
            return 0.0
        return (self.positive_rating / (self.positive_rating + self.negative_rating)) * 100

    def update_rating(self, new_rating: float, is_positive: bool = True):
        """Update rating with new review."""
        if is_positive:
            self.positive_rating += new_rating
        else:
            self.negative_rating += abs(new_rating)

        self.total_reviews += 1
        self.average_rating = self.get_rating() / self.total_reviews if self.total_reviews > 0 else 0
        self.save()
# =============================================================================
# ENHANCED USER WALLET MODEL
# =============================================================================

class UserWallet(models.Model):
    """Enhanced user wallet model with transaction tracking."""

    user = models.OneToOneField(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name='wallet',
        verbose_name=_('کاربر')
    )

    amount = models.DecimalField(
        _('موجودی'),
        max_digits=15,
        decimal_places=0,  # Iranian Rial doesn't use decimals
        default=0,
        help_text=_('موجودی کیف پول به ریال')
    )

    is_active = models.BooleanField(
        _('فعال'),
        default=True,
        help_text=_('آیا کیف پول فعال است؟')
    )

    last_transaction_at = models.DateTimeField(
        _('آخرین تراکنش'),
        null=True,
        blank=True,
        help_text=_('زمان آخرین تراکنش')
    )

    # Jalali timestamps for Persian calendar support
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)"),
        help_text=_('تاریخ ایجاد به تقویم شمسی')
    )

    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ آپدیت (شمسی)"),
        help_text=_('تاریخ آخرین به‌روزرسانی به تقویم شمسی')
    )


    class Meta:
        db_table = 'custom_user_wallet'
        verbose_name = _('کیف پول کاربر')
        verbose_name_plural = _('کیف پول‌های کاربران')
        indexes = [
            models.Index(fields=['amount']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.amount:,} ریال"

    def get_formatted_amount(self) -> str:
        """Get formatted amount in Persian."""
        return f"{self.amount:,} ریال"

    def can_withdraw(self, amount: int) -> bool:
        """Check if user can withdraw specified amount."""
        return self.is_active and self.amount >= amount

    def add_funds(self, amount: int, description: str = ""):
        """Add funds to wallet."""
        if amount <= 0:
            raise ValueError("مبلغ باید مثبت باشد")

        self.amount += amount
        self.last_transaction_at = timezone.now()
        self.save()

        # Log transaction
        logger.info(f"Funds added to wallet {self.user.phone_number}: {amount} - {description}")

    def withdraw_funds(self, amount: int, description: str = ""):
        """Withdraw funds from wallet."""
        if not self.can_withdraw(amount):
            raise ValueError("موجودی کافی نیست")

        self.amount -= amount
        self.last_transaction_at = timezone.now()
        self.save()

        # Log transaction
        logger.info(f"Funds withdrawn from wallet {self.user.phone_number}: {amount} - {description}")

class SubScriptionModel(models.Model):
    SUBSCRIPTION_TYPE = (
        (0, "Free"),
        (1, "by adv"),
        (2, "by month"),
    )
    SUBSCRIPTION_BY_ADV_COUNT = (
        (1, "20 Adv"),
        (2, "50 Adv"),
        (3, "100 Adv"),
        (4, "Unlimited")
    )
    SUBSCRIPTION_BY_MONTH = (
        (1, "1 Month"),
        (2, "3 Month"),
        (3,"6 Month"),
        (4,"1 Year"),
        (5,"2 Year"),
    )
    user = models.ForeignKey(CustomUserModel, on_delete=models.DO_NOTHING, null=False)
    subscription_type = models.IntegerField(choices=SUBSCRIPTION_TYPE, default=0)
    subscription_plan_count = models.IntegerField(choices=SUBSCRIPTION_BY_ADV_COUNT, default=1)
    subscription_plan_month = models.IntegerField(choices=SUBSCRIPTION_BY_MONTH, default=1)
    total_views=models.IntegerField(default=0)
    adv_views=models.IntegerField(default=0)
    expire_at_time=jmodels.jDateTimeField(auto_now_add=True)
    active_plan=models.BooleanField(default=False)
    status=models.IntegerField(choices=GlobalStatus,default=1)
    # Jalali timestamps for Persian calendar support
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)"),
        help_text=_('تاریخ ایجاد به تقویم شمسی')
    )

    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ آپدیت (شمسی)"),
        help_text=_('تاریخ آخرین به‌روزرسانی به تقویم شمسی')
    )

    class Meta:
        db_table = "subscription"
        verbose_name = "اشتراک"
        verbose_name_plural = "اشتراک"

    def save(self, *args, **kwargs):
        if not self.pk and self.subscription_type == 2:
            # تعیین تعداد ماه اشتراک
            plan_month_mapping = {
                1: 1,  # 1 Month
                2: 3,  # 3 Month
                3: 6,  # 6 Month
                4: 12,  # 1 Year
                5: 24  # 2 Year
            }
            months = plan_month_mapping.get(self.subscription_plan_month, 1)
            # زمان فعلی جلالی
            now_jalali = jdatetime.datetime.now()
            # افزودن ماه‌ها (هر ماه ~30 روز) و تبدیل به میلادی
            expire_jalali = now_jalali + jdatetime.timedelta(days=30 * months)
            self.expire_at_time = expire_jalali.togregorian()

        super().save(*args, **kwargs)

    def check_subscription(self):
        if not (self.status == 1 and self.active_plan):
            return False

        if self.subscription_type == 0:
            return False

        if self.subscription_type == 1:
            return self.adv_views <= self.total_views

        if self.subscription_type == 2:
            return timezone.now() <= self.expire_at_time

        return False  # fallback برای حالتی که نوع اشتراک ناشناس باشد


class UserPayment(models.Model):
    payment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference_number = models.CharField(max_length=11, unique=True)
    sale_reference_id = models.CharField(max_length=11, unique=True)
    status_payment = models.BooleanField(default=False)
    payment_finished = models.BooleanField(default=False)
    amount = models.DecimalField(max_digits=50, decimal_places=2, default=0, null=True)
    bank_name = models.CharField(max_length=100)

    user = models.ForeignKey(CustomUserModel, on_delete=models.DO_NOTHING, null=False)
    # buy_datetime = jmodels.jDateTimeField(auto_now_add=True)
    # Jalali timestamps for Persian calendar support
    created_at_jalali = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name=_("تاریخ ایجاد (شمسی)"),
        help_text=_('تاریخ ایجاد به تقویم شمسی')
    )

    updated_at_jalali = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name=_("تاریخ آپدیت (شمسی)"),
        help_text=_('تاریخ آخرین به‌روزرسانی به تقویم شمسی')
    )

    class Meta:
        db_table = "user_payment"


class LogAccess(models.Model):
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, null=False, default=0)
    phone_number = models.CharField(max_length=17, unique=True, blank=True)
    group_key = models.IntegerField(default=0)
    user_token = models.CharField(max_length=254, blank=True, null=True)
    verify_code = models.CharField(max_length=254, blank=True, null=True)
    ip_address = models.CharField(max_length=254, blank=True, null=True)
    try_to_verify = models.IntegerField(default=0)
    try_to_call = models.IntegerField(default=0)
    time_to_create = models.DateTimeField(auto_now_add=True)

    # def __str__(self):
    #     return "".join(self.user.first_name)

    class Meta:
        db_table = 'log_access'


class RoleModel(models.Model):
    name = models.CharField(max_length=50, unique=True, verbose_name="Role Name")
    description = models.TextField(blank=True, null=True, verbose_name="Role Description")

    class Meta:
        db_table = 'roles'
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def __str__(self):
        return self.name


class PermissionModel(models.Model):
    name = models.CharField(max_length=50, unique=True, verbose_name="Permission Name")
    description = models.TextField(blank=True, null=True, verbose_name="Permission Description")

    class Meta:
        db_table = 'permissions'
        verbose_name = "Permission"
        verbose_name_plural = "Permissions"

    def __str__(self):
        return self.name


class RolePermissionModel(models.Model):
    role = models.ForeignKey(RoleModel, on_delete=models.CASCADE, related_name="permissions")
    permission = models.ForeignKey(PermissionModel, on_delete=models.CASCADE, related_name="roles")

    class Meta:
        unique_together = ('role', 'permission')
        db_table = 'role_permissions'
        verbose_name = "Role Permission"
        verbose_name_plural = "Role Permissions"


class UserRoleModel(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="roles")
    role = models.ForeignKey(RoleModel, on_delete=models.CASCADE, related_name="users")

    class Meta:
        unique_together = ('user', 'role')
        db_table = 'user_roles'
        verbose_name = "User Role"
        verbose_name_plural = "User Roles"