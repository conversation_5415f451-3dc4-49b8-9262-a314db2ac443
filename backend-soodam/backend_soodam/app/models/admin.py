from django.db import models
from django.conf import settings
from django.utils import timezone
from django_jalali.db import models as jmodels

class AdminType(models.Model):
    """Admin type model for categorizing special admins"""
    name = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name

class SpecialAdmin(models.Model):
    """Special admin model with specific permissions"""
    STATUS_CHOICES = (
        (1, 'Active'),
        (2, 'Inactive'),
        (3, 'Suspended'),
    )
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='special_admin')
    admin_type = models.ForeignKey(AdminType, on_delete=models.PROTECT, related_name='admins')
    can_manage_users = models.BooleanField(default=False)
    can_manage_advertisements = models.BooleanField(default=False)
    can_manage_blogs = models.BooleanField(default=False)
    can_manage_agents = models.BooleanField(default=False)
    cities = models.ManyToManyField('CityModel', blank=True, related_name='admins')
    provinces = models.ManyToManyField('ProvinceModel', blank=True, related_name='admins')
    status = models.IntegerField(choices=STATUS_CHOICES, default=1)
    total_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    created_at = jmodels.jDateTimeField(auto_now_add=True)
    updated_at = jmodels.jDateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_admins'
    )

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.admin_type.name}"

class AdminTransaction(models.Model):
    """Financial transactions for admins"""
    TRANSACTION_TYPES = (
        (1, 'Commission'),
        (2, 'Bonus'),
        (3, 'Penalty'),
        (4, 'Withdrawal'),
        (5, 'Other'),
    )
    
    admin = models.ForeignKey(SpecialAdmin, on_delete=models.CASCADE, related_name='transactions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_type = models.IntegerField(choices=TRANSACTION_TYPES)
    description = models.CharField(max_length=200, null=True, blank=True)
    is_positive = models.BooleanField(default=True)  # True for credit, False for debit
    transaction_date = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.admin.user.get_full_name()} - {self.get_transaction_type_display()} - {self.amount}"

class AdminActivity(models.Model):
    """Log of admin activities"""
    ACTION_TYPES = (
        (1, 'Create'),
        (2, 'Update'),
        (3, 'Delete'),
        (4, 'View'),
        (5, 'Other'),
    )
    
    admin = models.ForeignKey(SpecialAdmin, on_delete=models.CASCADE, related_name='activities')
    action_type = models.IntegerField(choices=ACTION_TYPES)
    entity_type = models.CharField(max_length=50)  # e.g., "User", "Advertisement", "Blog"
    entity_id = models.IntegerField()
    description = models.CharField(max_length=255)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.admin.user.get_full_name()} - {self.get_action_type_display()} - {self.entity_type}"
    
    class Meta:
        verbose_name_plural = "Admin Activities"
