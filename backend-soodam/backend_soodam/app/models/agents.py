
from django.db import models
from django.conf import settings as django_settings, settings
from django.core.validators import FileExtensionValidator

from app.models import CustomUserModel,StateModel,CityModel

class AgentsModel(models.Model):
    COUNT_EMPLOYEES = (
        (1, "کمتر از ۳ نفر"),
        (2, "۳ تا ۵ نفر"),
        (3, "۵ تا ۱۰ نفر"),
        (4, "بیشتر از ۱۰ نفر"),
    )
    agents_admin = models.ForeignKey(CustomUserModel, null=False, on_delete=models.CASCADE, verbose_name='صاحب آژانس')
    name_agents = models.CharField(max_length=250, null=True, blank=True, verbose_name='نام آژانس')
    city_agents = models.ForeignKey(CityModel, on_delete=models.CASCADE, verbose_name='شهر حوزه فعالیت')
    phone_number_agents = models.CharField(max_length=17, unique=True, blank=True, null=True,
                                           verbose_name='شماره تماس آژانس')
    address = models.CharField(max_length=255, null=True, blank=True, verbose_name='آدرس')
    description = models.TextField(blank=True, null=True, verbose_name='توضیحات')
    count_employees = models.IntegerField(default=1, choices=COUNT_EMPLOYEES, verbose_name='تعداد کارمندان آژانس')
    agents_picture_profile = models.FileField(upload_to='profile_pictures/agents',
                                              default=django_settings.MEDIA_URL + 'profile_pictures/user.jpg',
                                              null=True, blank=True,
                                              validators=[
                                                  FileExtensionValidator(
                                                      allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])],
                                              verbose_name="آواتار آژانس")

    class Meta:
        db_table = "agents_model"

