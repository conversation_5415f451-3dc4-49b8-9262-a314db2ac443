"""
Security models for the Soodam backend.
"""

from django.db import models
from django_jalali.db import models as jmodels

from .user import CustomUserModel


class APIKeyModel(models.Model):
    """
    API key model.
    
    This model stores API keys for third-party integrations.
    """
    
    name = models.CharField(max_length=255, verbose_name="نام")
    key_hash = models.CharField(max_length=255, verbose_name="کلید هش شده")
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="api_keys",
        verbose_name="کاربر"
    )
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان ایجاد")
    last_used_at = jmodels.jDateTimeField(null=True, blank=True, verbose_name="آخرین استفاده")
    expires_at = jmodels.jDateTimeField(null=True, blank=True, verbose_name="زمان انقضا")
    
    class Meta:
        db_table = "api_keys"
        verbose_name = "کلید API"
        verbose_name_plural = "کلیدهای API"
    
    def __str__(self):
        return f"{self.name} ({self.user.email})"


class TwoFactorAuthModel(models.Model):
    """
    Two-factor authentication model.
    
    This model stores TOTP secrets for two-factor authentication.
    """
    
    user = models.OneToOneField(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="two_factor_auth",
        verbose_name="کاربر"
    )
    secret = models.CharField(max_length=255, verbose_name="رمز")
    is_enabled = models.BooleanField(default=False, verbose_name="فعال")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان ایجاد")
    last_verified_at = jmodels.jDateTimeField(null=True, blank=True, verbose_name="آخرین تایید")
    
    class Meta:
        db_table = "two_factor_auth"
        verbose_name = "احراز هویت دو مرحله‌ای"
        verbose_name_plural = "احراز هویت دو مرحله‌ای"
    
    def __str__(self):
        return f"2FA for {self.user.email}"


class LoginAttemptModel(models.Model):
    """
    Login attempt model.
    
    This model stores login attempts for security monitoring.
    """
    
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="login_attempts",
        null=True,
        blank=True,
        verbose_name="کاربر"
    )
    ip_address = models.GenericIPAddressField(verbose_name="آدرس IP")
    user_agent = models.TextField(verbose_name="مرورگر کاربر")
    success = models.BooleanField(verbose_name="موفق")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان ایجاد")
    
    class Meta:
        db_table = "login_attempts"
        verbose_name = "تلاش ورود"
        verbose_name_plural = "تلاش‌های ورود"
    
    def __str__(self):
        return f"Login attempt for {self.user.email if self.user else 'unknown'} from {self.ip_address}"
