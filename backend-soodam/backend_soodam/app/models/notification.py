"""
Notification models for the <PERSON>dam backend.
"""

from django.db import models
from django_jalali.db import models as jmodels

from .user import CustomUserModel


class NotificationType(models.TextChoices):
    """Notification type choices."""
    
    ADVERTISEMENT_CREATED = "ADVERTISEMENT_CREATED", "Advertisement Created"
    ADVERTISEMENT_UPDATED = "ADVERTISEMENT_UPDATED", "Advertisement Updated"
    ADVERTISEMENT_DELETED = "ADVERTISEMENT_DELETED", "Advertisement Deleted"
    ADVERTISEMENT_APPROVED = "ADVERTISEMENT_APPROVED", "Advertisement Approved"
    ADVERTISEMENT_REJECTED = "ADVERTISEMENT_REJECTED", "Advertisement Rejected"
    ADVERTISEMENT_EXPIRED = "ADVERTISEMENT_EXPIRED", "Advertisement Expired"
    ADVERTISEMENT_VIEWED = "ADVERTISEMENT_VIEWED", "Advertisement Viewed"
    ADVERTISEMENT_FAVORITED = "ADVERTISEMENT_FAVORITED", "Advertisement Favorited"
    ADVERTISEMENT_INQUIRY = "ADVERTISEMENT_INQUIRY", "Advertisement Inquiry"
    ADVERTISEMENT_COMMENT = "ADVERTISEMENT_COMMENT", "Advertisement Comment"
    ADVERTISEMENT_REPLY = "ADVERTISEMENT_REPLY", "Advertisement Reply"
    ADVERTISEMENT_MATCH = "ADVERTISEMENT_MATCH", "Advertisement Match"
    USER_FOLLOWED = "USER_FOLLOWED", "User Followed"
    USER_MENTIONED = "USER_MENTIONED", "User Mentioned"
    USER_MESSAGE = "USER_MESSAGE", "User Message"
    SYSTEM_ANNOUNCEMENT = "SYSTEM_ANNOUNCEMENT", "System Announcement"


class NotificationModel(models.Model):
    """
    Notification model.
    
    This model stores notifications for users.
    """
    
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="notifications",
        verbose_name="کاربر"
    )
    type = models.CharField(
        max_length=50,
        choices=NotificationType.choices,
        verbose_name="نوع اعلان"
    )
    title = models.CharField(
        max_length=255,
        verbose_name="عنوان"
    )
    message = models.TextField(
        verbose_name="پیام"
    )
    data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="داده‌ها"
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name="خوانده شده"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "notifications"
        verbose_name = "اعلان"
        verbose_name_plural = "اعلان‌ها"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.title} ({self.user.email})"


class NotificationPreferenceModel(models.Model):
    """
    Notification preference model.
    
    This model stores notification preferences for users.
    """
    
    user = models.OneToOneField(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="notification_preferences",
        verbose_name="کاربر"
    )
    email_enabled = models.BooleanField(
        default=True,
        verbose_name="ایمیل فعال"
    )
    push_enabled = models.BooleanField(
        default=True,
        verbose_name="اعلان پوش فعال"
    )
    sms_enabled = models.BooleanField(
        default=False,
        verbose_name="پیامک فعال"
    )
    advertisement_created = models.BooleanField(
        default=True,
        verbose_name="ایجاد آگهی"
    )
    advertisement_updated = models.BooleanField(
        default=True,
        verbose_name="به‌روزرسانی آگهی"
    )
    advertisement_deleted = models.BooleanField(
        default=True,
        verbose_name="حذف آگهی"
    )
    advertisement_approved = models.BooleanField(
        default=True,
        verbose_name="تأیید آگهی"
    )
    advertisement_rejected = models.BooleanField(
        default=True,
        verbose_name="رد آگهی"
    )
    advertisement_expired = models.BooleanField(
        default=True,
        verbose_name="انقضای آگهی"
    )
    advertisement_viewed = models.BooleanField(
        default=True,
        verbose_name="مشاهده آگهی"
    )
    advertisement_favorited = models.BooleanField(
        default=True,
        verbose_name="علاقه‌مندی به آگهی"
    )
    advertisement_inquiry = models.BooleanField(
        default=True,
        verbose_name="استعلام آگهی"
    )
    advertisement_comment = models.BooleanField(
        default=True,
        verbose_name="نظر آگهی"
    )
    advertisement_reply = models.BooleanField(
        default=True,
        verbose_name="پاسخ آگهی"
    )
    advertisement_match = models.BooleanField(
        default=True,
        verbose_name="تطابق آگهی"
    )
    user_followed = models.BooleanField(
        default=True,
        verbose_name="دنبال کردن کاربر"
    )
    user_mentioned = models.BooleanField(
        default=True,
        verbose_name="اشاره به کاربر"
    )
    user_message = models.BooleanField(
        default=True,
        verbose_name="پیام کاربر"
    )
    system_announcement = models.BooleanField(
        default=True,
        verbose_name="اعلان سیستم"
    )
    
    class Meta:
        db_table = "notification_preferences"
        verbose_name = "ترجیحات اعلان"
        verbose_name_plural = "ترجیحات اعلان‌ها"
    
    def __str__(self):
        return f"Notification preferences for {self.user.email}"


class NotificationDeviceModel(models.Model):
    """
    Notification device model.
    
    This model stores notification devices for users.
    """
    
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="notification_devices",
        verbose_name="کاربر"
    )
    device_token = models.CharField(
        max_length=255,
        verbose_name="توکن دستگاه"
    )
    device_type = models.CharField(
        max_length=50,
        verbose_name="نوع دستگاه"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="فعال"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    last_used_at = jmodels.jDateTimeField(
        null=True,
        blank=True,
        verbose_name="آخرین استفاده"
    )
    
    class Meta:
        db_table = "notification_devices"
        verbose_name = "دستگاه اعلان"
        verbose_name_plural = "دستگاه‌های اعلان"
        unique_together = ["user", "device_token"]
    
    def __str__(self):
        return f"{self.device_type} device for {self.user.email}"
