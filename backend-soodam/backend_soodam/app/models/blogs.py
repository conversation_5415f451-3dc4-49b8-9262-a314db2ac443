import os

from django.core.validators import FileExtensionValidator
from django.db import models
from model_utils import Choices
from django.utils.translation import gettext_lazy as _

from ..models import CustomUserModel

ENTRY_STATUS = Choices('Published', 'Hidden', 'Draft')


# Create your models here.
class BlogModel(models.Model):
    post_title = models.CharField(max_length=200, default="")
    # post_start_publication = models.DateTimeField()
    post_description = models.TextField(default='')
    post_content = models.TextField(max_length=4000, null=True, blank=True)
    post_status = models.CharField(_('level'), choices=ENTRY_STATUS, default=ENTRY_STATUS.Published, max_length=20)
    post_author = models.ForeignKey(CustomUserModel, on_delete=models.DO_NOTHING)
    # post_creation_date = models.DateTimeField(auto_now_add=True)
    # post_last_update = models.DateTimeField(auto_now=True)
    post_edited_by = models.Foreign<PERSON>ey(CustomUserModel, on_delete=models.DO_NOTHING, null=True, blank=True, related_name="+")
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        verbose_name = "blog"
        verbose_name_plural = "blog"
        db_table = "blog"

    def __unicode__(self):
        return self.post_title


class BlogPictureModel(models.Model):
    blog = models.ForeignKey(
        BlogModel, on_delete=models.CASCADE, null=True, blank=True
    )
    image = models.FileField(
        upload_to='documents/blogs/', null=True, blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg'])])

    # blog_file = models.FileField(
    #     upload_to='documents/blogs/doc', null=True, blank=True,
    #     validators=[FileExtensionValidator(allowed_extensions=['pdf', 'docx','doc'])])
    def delete(self, *args, **kwargs):
        os.remove(self.image.path)
        self.image.delete(False)
        super(BlogPictureModel, self).delete(*args, **kwargs)

    class Meta:
        db_table = 'blog_picture'


class BlogFileModel(models.Model):
    blog = models.ForeignKey(
        BlogModel, on_delete=models.CASCADE, null=True, blank=True
    )
    blog_file = models.FileField(
        upload_to='documents/blogs/doc', null=True, blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'docx', 'doc'])])

    def delete(self, *args, **kwargs):
        os.remove(self.image.path)
        self.blog_file.delete(False)
        super(BlogFileModel, self).delete(*args, **kwargs)

    class Meta:
        db_table = 'blog_file'
# class LikeBlogModel(models.Model):
#     user = models.ForeignKey(Admin, on_delete=models.DO_NOTHING)
#     blog = models.ForeignKey(BlogModel, on_delete=models.CASCADE)
#     time_to_create = models.DateTimeField(auto_now_add=True)
#
#     @property
#     def get_user_name(self):
#         try:
#             if self.user:
#                 return Admin.objects.get(id=int(self.user_id)).phone_number
#             else:
#                 return map(lambda c: c.name, Admin.objects.all())
#         except Exception as e:
#             return "Error:%s" % str(e)
#
#     class Meta:
#         db_table = 'like_blog'
#
#
# class DisLikeBlogModel(models.Model):
#     user = models.ForeignKey(Admin, on_delete=models.DO_NOTHING)
#     blog = models.ForeignKey(BlogModel, on_delete=models.CASCADE)
#     time_to_create = models.DateTimeField(auto_now_add=True)
#
#     @property
#     def get_user_name(self):
#         try:
#             if self.user:
#                 return Admin.objects.get(id=int(self.user_id)).phone_number
#             else:
#                 return map(lambda c: c.name, Admin.objects.all())
#         except Exception as e:
#             return "Error:%s" % str(e)
#
#     class Meta:
#         db_table = 'dislike_blog'
#
#
# class CommentModel(models.Model):
#     name_author = models.ForeignKey(Admin, null=False, on_delete=models.CASCADE)
#     nick_name = models.CharField(max_length=255, null=True, blank=True)
#     time_to_create = models.DateTimeField(auto_now_add=True)
#     message_title = models.CharField(max_length=100, blank=True, null=True)
#     message_text = models.CharField(max_length=255, blank=True)
#     # like_comment = models.BigIntegerField(default=0)
#     # dis_like_comment = models.BigIntegerField(default=0)
#     is_active = models.BooleanField(default=False)
#     blog = models.ForeignKey(BlogModel, on_delete=models.CASCADE, null=False)
#     created_at = models.DateTimeField(
#         verbose_name=_("created_at"),
#         # db_index=True,
#         auto_now_add=True,
#     )
#
#     class Meta:
#         db_table = 'comment'
#
#
# class LikeCommentModel(models.Model):
#     user = models.ForeignKey(Admin, on_delete=models.DO_NOTHING)
#     blog = models.ForeignKey(BlogModel, on_delete=models.CASCADE)
#     time_to_create = models.DateTimeField(auto_now_add=True)
#
#     @property
#     def get_user_name(self):
#         try:
#             if self.user:
#                 return Admin.objects.get(id=int(self.user_id)).phone_number
#             else:
#                 return map(lambda c: c.name, Admin.objects.all())
#         except Exception as e:
#             return "Error:%s" % str(e)
#
#     class Meta:
#         db_table = 'like_comment'
#
#
# class DisLikeCommentModel(models.Model):
#     user = models.ForeignKey(Admin, on_delete=models.DO_NOTHING)
#     blog = models.ForeignKey(BlogModel, on_delete=models.CASCADE)
#     time_to_create = models.DateTimeField(auto_now_add=True)
#
#     @property
#     def get_user_name(self):
#         try:
#             if self.user:
#                 return Admin.objects.get(id=int(self.user_id)).phone_number
#             else:
#                 return map(lambda c: c.name, Admin.objects.all())
#         except Exception as e:
#             return "Error:%s" % str(e)
#
#     class Meta:
#         db_table = 'dislike_comment'
