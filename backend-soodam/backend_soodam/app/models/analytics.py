"""
Analytics models for the Soodam backend.
"""

from django.db import models
from django_jalali.db import models as jmodels

from .advertisement import AdvertisementModel
from .user import CustomUserModel


class APIRequestLogModel(models.Model):
    """
    API request log model.
    
    This model represents a log of API requests.
    """
    
    endpoint = models.CharField(
        max_length=255,
        verbose_name="آدرس"
    )
    method = models.CharField(
        max_length=10,
        verbose_name="متد"
    )
    status_code = models.IntegerField(
        verbose_name="کد وضعیت"
    )
    response_time = models.FloatField(
        verbose_name="زمان پاسخ (میلی‌ثانیه)"
    )
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="api_requests",
        verbose_name="کاربر"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="آدرس IP"
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name="مرورگر کاربر"
    )
    request_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name="داده‌های درخواست"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "api_request_logs"
        verbose_name = "لاگ درخواست API"
        verbose_name_plural = "لاگ‌های درخواست API"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.method} {self.endpoint} - {self.status_code}"


class UserActivityLogModel(models.Model):
    """
    User activity log model.
    
    This model represents a log of user activities.
    """
    
    ACTION_TYPES = (
        ('LOGIN', 'ورود'),
        ('LOGOUT', 'خروج'),
        ('REGISTER', 'ثبت‌نام'),
        ('PASSWORD_CHANGE', 'تغییر رمز عبور'),
        ('PROFILE_UPDATE', 'به‌روزرسانی پروفایل'),
        ('ADVERTISEMENT_CREATE', 'ایجاد آگهی'),
        ('ADVERTISEMENT_UPDATE', 'به‌روزرسانی آگهی'),
        ('ADVERTISEMENT_DELETE', 'حذف آگهی'),
        ('ADVERTISEMENT_VIEW', 'مشاهده آگهی'),
        ('ADVERTISEMENT_FAVORITE', 'علاقه‌مندی به آگهی'),
        ('ADVERTISEMENT_UNFAVORITE', 'حذف علاقه‌مندی به آگهی'),
        ('CHAT_MESSAGE', 'پیام چت'),
        ('SEARCH', 'جستجو'),
        ('OTHER', 'سایر'),
    )
    
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="activity_logs",
        verbose_name="کاربر"
    )
    action_type = models.CharField(
        max_length=50,
        choices=ACTION_TYPES,
        verbose_name="نوع فعالیت"
    )
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="activity_logs",
        verbose_name="آگهی"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="آدرس IP"
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name="مرورگر کاربر"
    )
    details = models.JSONField(
        null=True,
        blank=True,
        verbose_name="جزئیات"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "user_activity_logs"
        verbose_name = "لاگ فعالیت کاربر"
        verbose_name_plural = "لاگ‌های فعالیت کاربر"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_action_type_display()}"


class SearchQueryLogModel(models.Model):
    """
    Search query log model.
    
    This model represents a log of search queries.
    """
    
    query = models.CharField(
        max_length=255,
        verbose_name="عبارت جستجو"
    )
    user = models.ForeignKey(
        CustomUserModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="search_queries",
        verbose_name="کاربر"
    )
    category_id = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="دسته‌بندی"
    )
    location_id = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="موقعیت"
    )
    filters = models.JSONField(
        null=True,
        blank=True,
        verbose_name="فیلترها"
    )
    results_count = models.IntegerField(
        default=0,
        verbose_name="تعداد نتایج"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="آدرس IP"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "search_query_logs"
        verbose_name = "لاگ جستجو"
        verbose_name_plural = "لاگ‌های جستجو"
        ordering = ["-created_at"]
    
    def __str__(self):
        return self.query


class DailyStatisticsModel(models.Model):
    """
    Daily statistics model.
    
    This model represents daily statistics for the application.
    """
    
    date = jmodels.jDateField(
        unique=True,
        verbose_name="تاریخ"
    )
    new_users = models.IntegerField(
        default=0,
        verbose_name="کاربران جدید"
    )
    active_users = models.IntegerField(
        default=0,
        verbose_name="کاربران فعال"
    )
    new_advertisements = models.IntegerField(
        default=0,
        verbose_name="آگهی‌های جدید"
    )
    total_views = models.IntegerField(
        default=0,
        verbose_name="بازدیدها"
    )
    total_searches = models.IntegerField(
        default=0,
        verbose_name="جستجوها"
    )
    total_favorites = models.IntegerField(
        default=0,
        verbose_name="علاقه‌مندی‌ها"
    )
    total_messages = models.IntegerField(
        default=0,
        verbose_name="پیام‌ها"
    )
    total_api_requests = models.IntegerField(
        default=0,
        verbose_name="درخواست‌های API"
    )
    average_response_time = models.FloatField(
        default=0,
        verbose_name="میانگین زمان پاسخ (میلی‌ثانیه)"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان به‌روزرسانی"
    )
    
    class Meta:
        db_table = "daily_statistics"
        verbose_name = "آمار روزانه"
        verbose_name_plural = "آمارهای روزانه"
        ordering = ["-date"]
    
    def __str__(self):
        return str(self.date)


class CategoryStatisticsModel(models.Model):
    """
    Category statistics model.
    
    This model represents statistics for advertisement categories.
    """
    
    category_id = models.IntegerField(
        verbose_name="شناسه دسته‌بندی"
    )
    category_name = models.CharField(
        max_length=100,
        verbose_name="نام دسته‌بندی"
    )
    date = jmodels.jDateField(
        verbose_name="تاریخ"
    )
    advertisements_count = models.IntegerField(
        default=0,
        verbose_name="تعداد آگهی‌ها"
    )
    views_count = models.IntegerField(
        default=0,
        verbose_name="تعداد بازدیدها"
    )
    searches_count = models.IntegerField(
        default=0,
        verbose_name="تعداد جستجوها"
    )
    favorites_count = models.IntegerField(
        default=0,
        verbose_name="تعداد علاقه‌مندی‌ها"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان به‌روزرسانی"
    )
    
    class Meta:
        db_table = "category_statistics"
        verbose_name = "آمار دسته‌بندی"
        verbose_name_plural = "آمارهای دسته‌بندی"
        unique_together = ["category_id", "date"]
        ordering = ["-date", "category_name"]
    
    def __str__(self):
        return f"{self.category_name} - {self.date}"


class LocationStatisticsModel(models.Model):
    """
    Location statistics model.
    
    This model represents statistics for advertisement locations.
    """
    
    location_id = models.IntegerField(
        verbose_name="شناسه موقعیت"
    )
    city = models.CharField(
        max_length=100,
        verbose_name="شهر"
    )
    state = models.CharField(
        max_length=100,
        verbose_name="استان"
    )
    date = jmodels.jDateField(
        verbose_name="تاریخ"
    )
    advertisements_count = models.IntegerField(
        default=0,
        verbose_name="تعداد آگهی‌ها"
    )
    views_count = models.IntegerField(
        default=0,
        verbose_name="تعداد بازدیدها"
    )
    searches_count = models.IntegerField(
        default=0,
        verbose_name="تعداد جستجوها"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان به‌روزرسانی"
    )
    
    class Meta:
        db_table = "location_statistics"
        verbose_name = "آمار موقعیت"
        verbose_name_plural = "آمارهای موقعیت"
        unique_together = ["location_id", "date"]
        ordering = ["-date", "city"]
    
    def __str__(self):
        return f"{self.city}, {self.state} - {self.date}"
