"""
Chat models for the Soodam backend.
"""

from django.db import models
from django_jalali.db import models as jmodels

from .advertisement import AdvertisementModel
from .user import CustomUserModel


class ChatRoomModel(models.Model):
    """
    Chat room model.
    
    This model represents a chat room between two users about an advertisement.
    """
    
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.CASCADE,
        related_name="chat_rooms",
        verbose_name="آگهی"
    )
    sender = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="sent_chat_rooms",
        verbose_name="فرستنده"
    )
    receiver = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="received_chat_rooms",
        verbose_name="گیرنده"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="فعال"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان به‌روزرسانی"
    )
    
    class Meta:
        db_table = "chat_rooms"
        verbose_name = "اتاق گفتگو"
        verbose_name_plural = "اتاق‌های گفتگو"
        unique_together = ["advertisement", "sender", "receiver"]
    
    def __str__(self):
        return f"Chat room for {self.advertisement.title} between {self.sender.email} and {self.receiver.email}"


class ChatMessageModel(models.Model):
    """
    Chat message model.
    
    This model represents a message in a chat room.
    """
    
    room = models.ForeignKey(
        ChatRoomModel,
        on_delete=models.CASCADE,
        related_name="messages",
        verbose_name="اتاق"
    )
    sender = models.ForeignKey(
        CustomUserModel,
        on_delete=models.CASCADE,
        related_name="sent_messages",
        verbose_name="فرستنده"
    )
    content = models.TextField(
        verbose_name="محتوا"
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name="خوانده شده"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "chat_messages"
        verbose_name = "پیام گفتگو"
        verbose_name_plural = "پیام‌های گفتگو"
        ordering = ["created_at"]
    
    def __str__(self):
        return f"Message from {self.sender.email} in {self.room}"


class ChatAttachmentModel(models.Model):
    """
    Chat attachment model.
    
    This model represents an attachment in a chat message.
    """
    
    message = models.ForeignKey(
        ChatMessageModel,
        on_delete=models.CASCADE,
        related_name="attachments",
        verbose_name="پیام"
    )
    file_url = models.URLField(
        verbose_name="آدرس فایل"
    )
    file_type = models.CharField(
        max_length=50,
        verbose_name="نوع فایل"
    )
    file_name = models.CharField(
        max_length=255,
        verbose_name="نام فایل"
    )
    file_size = models.IntegerField(
        verbose_name="اندازه فایل"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    
    class Meta:
        db_table = "chat_attachments"
        verbose_name = "پیوست گفتگو"
        verbose_name_plural = "پیوست‌های گفتگو"
    
    def __str__(self):
        return f"Attachment {self.file_name} in {self.message}"
