from email.policy import default
from tabnanny import verbose
import logging
import os

from asgiref.sync import sync_to_async
from django.db import models
from django.db import models
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django_jalali.db import models as jmodels
from jdatetime import datetime as jdatetime
from django.utils.translation import gettext_lazy as _
from django.conf import settings as django_settings, settings
from django.core.validators import FileExtensionValidator
from django.contrib.gis.geos import Point

from .user import CustomUserModel
from .gis_geolocation import ProvinceModel, CityModel, CountryModel
from ..models.base import GenericImage

import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MainCategoryModel(models.Model):
    IMAGE_ALLOWED_EXTENSIONS = ("PNG", "JPG", "JPEG")
    # This value should be in bytes
    IMAGE_LIMIT_SIZE = 1024 * 1024 * 5
    name = models.CharField(max_length=255, unique=True, blank=True, null=False, verbose_name='نام دسته اصلی')
    star = models.BooleanField(default=False, verbose_name='برگزیده یا پیشنهادی')
    key = models.CharField(max_length=255, null=False, verbose_name='کلید واژه')
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name='توضیحات')
    review = models.IntegerField(default=0, verbose_name='تعداد مشاهده شده')
    total_adv = models.BigIntegerField(default=0, verbose_name='تعداد آگهی')
    icon = models.FileField(upload_to='documents/features',
                            default='documents/features/user.jpg',
                            null=True, blank=True,
                            validators=[
                                FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])
    # images = GenericRelation(GenericImage)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'main_category'
        verbose_name = "دسته بندی اصلی"
        verbose_name_plural = "دسته بندی اصلی"


class SubCategoryModel(models.Model):
    main_category = models.ForeignKey(MainCategoryModel, on_delete=models.CASCADE, verbose_name='دسته اصلی')
    name = models.CharField(max_length=255, unique=True, blank=True, null=False, verbose_name='دسته فرعی')
    star = models.BooleanField(default=False, verbose_name='برگزیده')
    key = models.CharField(max_length=255, null=False, verbose_name='کلید')
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name='توضیحات')
    review = models.IntegerField(default=0, verbose_name='تعداد مشاهده شده')
    total_adv = models.BigIntegerField(default=0, verbose_name=' تعداد آگهی در این دسته ')
    icon = models.FileField(upload_to='documents/features',
                            default='documents/features/user.jpg',
                            null=True, blank=True,
                            validators=[
                                FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])

    # images = GenericRelation(GenericImage)
    def get_parent_category(self):
        return self.main_category.name

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'sub_category'
        verbose_name = "دسته بندی فرعی"
        verbose_name_plural = "دسته بندی فرعی"


class SubCategoryLevelTwoModel(models.Model):
    sub_category = models.ForeignKey(SubCategoryModel, on_delete=models.CASCADE, verbose_name='دسته فرعی')
    name = models.CharField(max_length=255, unique=True, blank=True, null=False, verbose_name='دسته فرعی مرحله دوم')
    star = models.BooleanField(default=False, verbose_name='برگزیده')
    key = models.CharField(max_length=255, null=False, verbose_name='کلید')
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name='توضیحات')
    review = models.IntegerField(default=0, verbose_name='تعداد مشاهده شده')
    total_adv = models.BigIntegerField(default=0, verbose_name=' تعداد آگهی در این دسته ')
    icon = models.FileField(upload_to='documents/features',
                            default='documents/features/user.jpg',
                            null=True, blank=True,
                            validators=[
                                FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])

    def get_parent_category(self):
        return self.sub_category.name

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'sub_sub_category'
        verbose_name = "دسته بندی فرعی مرحله دوم"
        verbose_name_plural = "دسته بندی فرعی مرحله دوم"


class BaseAttributeModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, verbose_name="نام ویژگی")
    # sub_category = models.ForeignKey(SubCategoryModel, on_delete=models.CASCADE, related_name='%(class)s',
    #                                  verbose_name="دسته‌بندی فرعی")
    # sub_category = models.ManyToManyField(SubCategoryModel,
    #                                       # through='PropertyTextValueModel',
    #                                       verbose_name="ویژگی‌های توضیحی")
    star = models.BooleanField(default=False, verbose_name='برگزیده')
    key = models.CharField(max_length=100, null=True, blank=True, verbose_name="کلید دسترسی")
    placeholder = models.CharField(max_length=100, null=True, blank=True, verbose_name="متن پیش فرض")
    icon = models.FileField(upload_to='documents/features',
                            default='documents/features/user.jpg',
                            null=True, blank=True,
                            validators=[
                                FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'svg'])])

    class Meta:
        abstract = True

    def __str__(self):
        return self.name


class ChoiceAttributeModel(BaseAttributeModel):
    pass

    class Meta:
        db_table = 'choice_attribute'
        verbose_name = "امکانات انتخابی"
        verbose_name_plural = "امکانات انتخابی"


class ChoiceOptionModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    attribute = models.ForeignKey(ChoiceAttributeModel, on_delete=models.CASCADE, related_name='options')
    value = models.CharField(max_length=100, verbose_name="گزینه")

    def __str__(self):
        return self.value

    class Meta:
        db_table = 'choice_option'
        verbose_name = "مقادیر امکانات انتخابی "
        verbose_name_plural = "مقادیر امکانات انتخابی"


class BooleanAttributeModel(BaseAttributeModel):
    pass

    class Meta:
        db_table = 'boolean_attribute'
        verbose_name = "امکانات بولی"
        verbose_name_plural = "امکانات بولی"


class TextAttributeModel(BaseAttributeModel):
    pass

    class Meta:
        db_table = 'text_attribute'
        verbose_name = "امکانات توضیحی "
        verbose_name_plural = "امکانات توضیحی"


class PropertyBooleanValueModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey('PropertyModel', on_delete=models.CASCADE)
    attribute = models.ForeignKey(BooleanAttributeModel, on_delete=models.CASCADE)
    value = models.BooleanField(default=False, verbose_name="مقدار")

    class Meta:
        unique_together = ('property', 'attribute')
        db_table = 'property_boolean_value'
        verbose_name = "مقادیر امکانات بولی"
        verbose_name_plural = "مقادیر امکانات بولی"

    def __str__(self):
        return f"{self.attribute.name}: {self.value}"


class PropertyTextValueModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey('PropertyModel', on_delete=models.CASCADE)
    attribute = models.ForeignKey(TextAttributeModel, on_delete=models.CASCADE)
    value = models.TextField(verbose_name="مقدار")

    class Meta:
        unique_together = ('property', 'attribute')
        db_table = 'property_text_value'
        verbose_name = "مقادیر امکانات توضیحی"
        verbose_name_plural = "مقادیر امکانات توضیحی"

    def __str__(self):
        return f"{self.attribute.name}: {self.value}"


class PropertyModel(models.Model):
    main_category = models.ForeignKey(MainCategoryModel, on_delete=models.CASCADE, verbose_name="دسته‌بندی اصلی")
    sub_category = models.ForeignKey(SubCategoryModel, on_delete=models.CASCADE, verbose_name="دسته‌بندی فرعی")
    sub_category_level_two = models.ForeignKey(SubCategoryLevelTwoModel, on_delete=models.CASCADE,
                                               verbose_name="دسته‌بندی فرعی مرحله دوم", null=True, blank=True)
    is_active = models.BooleanField(default=True, verbose_name="فعال/غیرفعال")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاریخ ایجاد")
    choice_attributes = models.ManyToManyField(ChoiceAttributeModel, blank=True, verbose_name="ویژگی‌های انتخابی")
    boolean_attributes = models.ManyToManyField(BooleanAttributeModel,
                                                blank=True,
                                                # through='PropertyBooleanValueModel',
                                                verbose_name="ویژگی‌های بولی")
    text_attributes = models.ManyToManyField(TextAttributeModel,
                                             blank=True,
                                             # through='PropertyTextValueModel',
                                             verbose_name="ویژگی‌های توضیحی")

    class Meta:
        db_table = 'property'
        verbose_name = "امکانات"
        verbose_name_plural = "امکانات"

    # def __str__(self):
    #     return self.title


class HighlightAttributeModel(models.Model):
    sub_category = models.ForeignKey(SubCategoryModel, on_delete=models.CASCADE)
    sub_category_level_two = models.ForeignKey(SubCategoryLevelTwoModel, on_delete=models.CASCADE,
                                               verbose_name="دسته‌بندی فرعی مرحله دوم", null=True, blank=True)
    choice_attributes = models.ManyToManyField(ChoiceAttributeModel, blank=True,
                                               verbose_name="ویژگی‌های انتخابی برگزیده")
    boolean_attributes = models.ManyToManyField(BooleanAttributeModel,
                                                blank=True,
                                                # through='PropertyBooleanValueModel',
                                                verbose_name="ویژگی‌های بولی برگزیده")
    text_attributes = models.ManyToManyField(TextAttributeModel,
                                             blank=True,
                                             # through='PropertyTextValueModel',
                                             verbose_name="ویژگی‌های توضیحی برگزیده")
    class Meta:
        db_table = 'highlight_attribute'
        verbose_name = "امکانات برگزیده"
        verbose_name_plural = "امکانات برگزیده"


from ..enums.status import AdvertisementStatus


class AdvertisementModel(models.Model):
    STATUS_CHOICES = (
        (AdvertisementStatus.PENDING, "Pending"),
        (AdvertisementStatus.APPROVED, "Approved"),
        (AdvertisementStatus.REJECTED, "Rejected"),
        (AdvertisementStatus.DELETED, "Deleted")
    )

    status = models.IntegerField(choices=STATUS_CHOICES, default=AdvertisementStatus.PENDING, verbose_name="وضعیت")

    # Add index to frequently queried fields
    class Meta:
        db_table = 'advertisement'
        verbose_name = "آگهی"
        verbose_name_plural = "آگهی"
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['main_category']),
            models.Index(fields=['sub_category']),
            models.Index(fields=['created_at']),
        ]

    # GENDER_TYPE = Choices('Unknown', 'Male', 'Woman')
    # id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # id = models.BigIntegerField(primary_key=True, default=0, editable=False)
    security_code_owner_building = models.CharField(max_length=11, null=True, blank=True, default=0)
    phone_number_owner_building = models.CharField(max_length=11, null=False, blank=False, default=0)
    title = models.CharField(max_length=200, verbose_name="عنوان آگهی")
    description = models.TextField(verbose_name="توضیحات آگهی")
    main_category = models.ForeignKey(MainCategoryModel, on_delete=models.DO_NOTHING, verbose_name="دسته‌بندی اصلی")
    sub_category = models.ForeignKey(SubCategoryModel, on_delete=models.DO_NOTHING, verbose_name="دسته‌بندی فرعی")
    sub_category_level_two = models.ForeignKey(SubCategoryLevelTwoModel, on_delete=models.DO_NOTHING,
                                               verbose_name="دسته‌بندی فرعی مرحله دوم", null=True, blank=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.DO_NOTHING, null=True, verbose_name="فروشنده")
    # address = models.ForeignKey("AdvertisementLocationModel",
    #                             on_delete=models.CASCADE,
    #                             # related_name="advertisement_address",
    #                             blank=True, null=True,
    #                             verbose_name='ثبت آدرس')
    review = models.IntegerField(default=0, verbose_name='تعداد مشاهده شده')
    # status=models.IntegerField(choices=STATUS_TYPE,default=1)
    is_active = models.BooleanField(default=True, verbose_name="فعال/غیرفعال")
    choice_attributes = models.ManyToManyField(ChoiceOptionModel,
                                               # through="AdvertisementChoiceValueModel",
                                               #    related_name='advertisement_choices',
                                               blank=True, verbose_name="ویژگی‌های انتخابی")
    boolean_attributes = models.ManyToManyField(BooleanAttributeModel,
                                                through='AdvertisementBooleanValueModel',
                                                # related_name='advertisement_boolean_choices',
                                                verbose_name="ویژگی‌های بولی")
    text_attributes = models.ManyToManyField(TextAttributeModel,
                                             through='AdvertisementTextValueModel',
                                             # related_name='advertisement_text_choices',
                                             verbose_name="ویژگی‌های توضیحی")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان ساختن ")
    updated_at = jmodels.jDateTimeField(default=jdatetime.now, editable=False, verbose_name="زمان آپدیت ")
    expiry_date = jmodels.jDateTimeField(default=jdatetime.now, editable=False, verbose_name="زمان انقضاء ")

    def __str__(self):
        return self.title

    @classmethod
    async def delete_with_relations(cls, advertisement_id):
        """
        Delete an advertisement and all its related data.
        This method ensures all relationships are properly deleted.
        """
        try:
            # Get the advertisement
            advertisement = await cls.objects.filter(id=advertisement_id).afirst()
            if not advertisement:
                return False, "Advertisement not found"

            # Delete related data in the correct order to avoid foreign key constraints

            # 1. Delete images
            await AdvertisementImagesModel.objects.filter(advertisement=advertisement).adelete()

            # 2. Delete videos
            await AdvertisementVideoModel.objects.filter(advertisement=advertisement).adelete()

            # 3. Delete location
            await AdvertisementLocationModel.objects.filter(advertisement=advertisement).adelete()

            # 4. Delete price
            await AdvertisementPriceModel.objects.filter(advertisement=advertisement).adelete()

            # 5. Delete statistics
            await AdvertisementStatisticsModel.objects.filter(advertisement=advertisement).adelete()

            # 6. Delete attribute values (through models for ManyToMany relationships)
            await AdvertisementBooleanValueModel.objects.filter(advertisement=advertisement).adelete()
            await AdvertisementTextValueModel.objects.filter(advertisement=advertisement).adelete()

            # 7. Clear direct ManyToMany relationships
            # For choice_attributes, we need to clear the relationship before deleting
            # This is handled automatically by Django when the advertisement is deleted,
            # but we do it explicitly for clarity and to ensure proper cleanup
            await sync_to_async(advertisement.choice_attributes.clear)()

            # 8. Delete favorites and saves
            await AdvertisementFavoriteModel.objects.filter(advertisement=advertisement).adelete()
            await AdvertisementSaveModel.objects.filter(advertisement=advertisement).adelete()

            # 9. Delete tags
            await AdvertisementTagModel.objects.filter(advertisement=advertisement).adelete()

            # 10. Delete views
            await AdvertisementViewModel.objects.filter(advertisement=advertisement).adelete()

            # 11. Delete flagged content
            await FlaggedContentModel.objects.filter(advertisement=advertisement).adelete()

            # 12. Finally delete the advertisement itself
            await advertisement.adelete()

            return True, "Advertisement and all related data deleted successfully"

        except Exception as e:
            logger.error(f"Error deleting advertisement with relations: {str(e)}")
            return False, f"Error deleting advertisement: {str(e)}"


class AdvertisementChoiceValueModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.CASCADE)
    attribute = models.ForeignKey(ChoiceAttributeModel, on_delete=models.CASCADE)
    attribute_value = models.ForeignKey(ChoiceOptionModel, on_delete=models.CASCADE)

    # value = models.BooleanField(default=False, verbose_name="مقدار")

    class Meta:
        unique_together = ('advertisement', 'attribute')
        db_table = 'advertisement_choice_value'
        verbose_name = "مقادیر انتخابی آگهی"
        verbose_name_plural = "مقادیر انتخابی آگهی"


class AdvertisementBooleanValueModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel,
                                      on_delete=models.DO_NOTHING,
                                      related_name='advertisement_boolean_values'
                                      )
    attribute = models.ForeignKey(BooleanAttributeModel, on_delete=models.DO_NOTHING)
    value = models.BooleanField(default=False, verbose_name="مقدار")

    class Meta:
        unique_together = ('advertisement', 'attribute')
        db_table = 'advertisement_boolean_value'
        verbose_name = "مقادیر بولی آگهی"
        verbose_name_plural = "مقادیر بولی آگهی"

    def __str__(self):
        return f"{self.attribute.name}: {self.value}"


class AdvertisementTextValueModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel,
                                      on_delete=models.DO_NOTHING,
                                      related_name='advertisement_text_values'
                                      )
    attribute = models.ForeignKey(TextAttributeModel, on_delete=models.DO_NOTHING)
    value = models.TextField(verbose_name="مقدار")

    class Meta:
        unique_together = ('advertisement', 'attribute')
        db_table = 'advertisement_text_value'
        verbose_name = "مقادیر توضیحی آگهی"
        verbose_name_plural = "مقادیر توضیحی آگهی"

    def __str__(self):
        return f"{self.attribute.name}: {self.value}"


class AdvertisementPriceModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel,
                                      on_delete=models.DO_NOTHING,
                                      related_name='price',
                                      )
    deposit = models.FloatField(default=0, verbose_name="Deposit amount")
    rent = models.FloatField(default=0, verbose_name="Rent amount")
    amount = models.FloatField(default=0, verbose_name="Sell Price amount")
    currency = models.CharField(max_length=25, verbose_name="Price currency")
    is_negotiable = models.BooleanField(default=0, verbose_name="Whether the price is negotiable")
    discount_amount = models.FloatField(default=0, verbose_name="Discount amount")
    original_amount = models.FloatField(default=0, verbose_name="Original price amount")
    price_per_unit = models.FloatField(default=0, verbose_name="Price per uint")
    unit = models.CharField(max_length=25, verbose_name="Unit for price per unit")

    class Meta:
        db_table = 'advertisement_price'
        verbose_name = 'قیمت آگهی ها'
        verbose_name_plural = 'قیمت آگهی ها'


class AdvertisementLocationModel(models.Model):
    """
    Model for advertisement locations.

    This model is used by the Elasticsearch sync service and GraphQL API.
    It provides a simplified interface to the AddressesModel.
    """
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.DO_NOTHING,
        related_name='address'
    )
    province = models.ForeignKey(ProvinceModel, on_delete=models.CASCADE, default=1)
    # country = models.ForeignKey(CountryModel, on_delete=models.CASCADE, default=1)
    city = models.ForeignKey(CityModel, on_delete=models.CASCADE, default=1)
    street = models.CharField(max_length=255, blank=True, null=True, verbose_name="نام خیابان اصلی")
    address = models.CharField(max_length=255, null=False, blank=False, verbose_name="آدرس")
    zip_code = models.CharField(max_length=10, default="4777114691", null=False, blank=False, verbose_name="کد پستی")
    srid = models.IntegerField(blank=True, null=True, verbose_name='Srid (4326):')
    longitude = models.FloatField(blank=True, null=True, verbose_name='Longitude or x : ')
    latitude = models.FloatField(blank=True, null=True, verbose_name='Latitude or y: ')
    from django.contrib.gis.db import models
    geolocation = models.PointField(geography=True,
                                    # default=Point(52.94410227992549, 36.7205385945049), srid=4326,
                                    verbose_name='مکان یابی gps آگهی')
    created_at = jmodels.jDateTimeField(auto_now_add=True)
    modified_at = jmodels.jDateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'advertisement_location'
        verbose_name = "موقعیت آگهی"
        verbose_name_plural = "موقعیت‌های آگهی"
        indexes = [
            models.Index(fields=['advertisement']),
            # Add a spatial index for geolocation
            models.Index(fields=['geolocation']),
        ]

    def __str__(self):
        return f"{self.city}, {self.province}"  # , {self.address.country}"

    # @classmethod
    # def from_address(cls, address):
    #     """
    #     Create a location model from an AddressesModel instance.
    #     """
    #     if not address:
    #         return None
    #
    #     location, created = cls.objects.get_or_create(
    #         address=address,
    #         defaults={
    #             'city': address.city.name if address.city else "",
    #             'state': address.province.name if address.province else "",
    #             'country': "Iran",
    #             'latitude': address.latitude,
    #             'longitude': address.longitude
    #         }
    #     )
    #
    #     return location


class AdvertisementImagesModel(models.Model):
    """
    Model for advertisement images.

    This model stores image files for an advertisement.
    Supports various image formats including iOS HEIC/HEIF which are automatically converted to JPEG.
    Note: HEIC/HEIF files are automatically converted to JPEG during upload.
    """
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.DO_NOTHING,
        related_name='images',
        verbose_name="آگهی"
    )
    image = models.FileField(
        upload_to='documents/advertisement/images',
        null=True, blank=True,
        validators=[
            FileExtensionValidator(
                allowed_extensions=['png', 'jpg', 'jpeg', 'webp'],
                message="Only image files (PNG, JPG, JPEG, WEBP) are allowed. HEIC/HEIF files will be automatically converted to JPEG during upload."
            )
        ]
    )
    url = models.URLField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name="آدرس تصویر"
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name="تصویر اصلی"
    )
    order = models.IntegerField(
        default=0,
        verbose_name="ترتیب نمایش"
    )
    width = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="عرض تصویر"
    )
    height = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="ارتفاع تصویر"
    )
    alt_text = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name="متن جایگزین"
    )
    original_format = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="فرمت اصلی تصویر",
        help_text="Original format of the image before conversion (if applicable)"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان بروزرسانی"
    )

    class Meta:
        db_table = 'advertisement_images'
        verbose_name = "تصویر آگهی"
        verbose_name_plural = "تصاویر آگهی"
        ordering = ['order', '-is_primary']

    def __str__(self):
        return f"{self.advertisement.title} - Image {self.order}"

    def get_absolute_url(self):
        """Return the image URL, preferring the URL field if available."""
        if self.url:
            return self.url
        elif self.image:
            return self.image.url
        return None

    def save(self, *args, **kwargs):
        """Override save method to handle image format conversion."""
        if self.image:
            # Store original format if it's HEIC/HEIF
            ext = os.path.splitext(self.image.name)[1].lower()
            if ext in ['.heic', '.heif']:
                self.original_format = ext[1:]  # Remove the dot
        super().save(*args, **kwargs)


class AdvertisementVideoModel(models.Model):
    """
    Model for advertisement videos.

    This model stores video files for an advertisement.
    """
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.DO_NOTHING,
        related_name='videos',
        verbose_name="آگهی"
    )
    video = models.FileField(
        upload_to='documents/adv/advertisement/videos',
        null=True, blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mkv', 'avi'])]
    )
    url = models.URLField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name="آدرس ویدیو"
    )
    thumbnail = models.FileField(
        upload_to='documents/adv/advertisement/thumbnails',
        null=True, blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['png', 'jpg', 'jpeg', 'webp'])],
        verbose_name="تصویر بندانگشتی"
    )
    thumbnail_url = models.URLField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name="آدرس تصویر بندانگشتی"
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name="ویدیو اصلی"
    )
    order = models.IntegerField(
        default=0,
        verbose_name="ترتیب نمایش"
    )
    duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="مدت زمان (ثانیه)"
    )
    title = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name="عنوان"
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="توضیحات"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان بروزرسانی"
    )

    class Meta:
        db_table = 'advertisement_videos'
        verbose_name = "ویدیو آگهی"
        verbose_name_plural = "ویدیوهای آگهی"
        ordering = ['order', '-is_primary']

    def __str__(self):
        return f"{self.advertisement.title} - Video {self.order}"

    def get_absolute_url(self):
        """Return the video URL, preferring the URL field if available."""
        if self.url:
            return self.url
        elif self.video:
            return self.video.url
        return None

    def get_thumbnail_url(self):
        """Return the thumbnail URL, preferring the URL field if available."""
        if self.thumbnail_url:
            return self.thumbnail_url
        elif self.thumbnail:
            return self.thumbnail.url
        return None


class AdvertisementStatisticsModel(models.Model):
    """
    Model for advertisement statistics.

    This model tracks various statistics for an advertisement including views,
    favorites, inquiries, and shares. It's used by the analytics service and
    recommendation engine to provide insights and personalized content.
    """
    advertisement = models.OneToOneField(
        AdvertisementModel,
        on_delete=models.DO_NOTHING,
        related_name='statistics',
        verbose_name="آگهی"
    )

    views = models.PositiveIntegerField(
        default=0,
        verbose_name="تعداد بازدید"
    )
    likes_count = models.IntegerField(
        default=0, 
        verbose_name="تعداد لایک ها"
    )
    dislikes_count = models.IntegerField(
        default=0, 
        verbose_name="تعداد دیسلایک ها"
    )
    comments_count = models.IntegerField(
        default=0, 
        verbose_name="تعداد نظرات"
    )
    favorites = models.PositiveIntegerField(
        default=0,
        verbose_name="تعداد علاقه‌مندی‌ها"
    )
    inquiries = models.PositiveIntegerField(
        default=0,
        verbose_name="تعداد درخواست‌ها"
    )
    shares = models.PositiveIntegerField(
        default=0,
        verbose_name="تعداد اشتراک‌گذاری‌ها"
    )
    last_viewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="آخرین بازدید"
    )
    created_at = jmodels.jDateTimeField(
        auto_now_add=True,
        verbose_name="زمان ایجاد"
    )
    updated_at = jmodels.jDateTimeField(
        auto_now=True,
        verbose_name="زمان بروزرسانی"
    )


    class Meta:
        db_table = 'advertisement_statistics'
        verbose_name = "آمار آگهی"
        verbose_name_plural = "آمار آگهی‌ها"
        indexes = [
            models.Index(fields=['views']),
            models.Index(fields=['favorites']),
            models.Index(fields=['last_viewed_at']),
        ]

    def __str__(self):
        return f"Statistics for {self.advertisement.title}"

    def update_counts(self):
        """Update all counts from related models."""
        self.likes_count = self.advertisement.likes.count()
        self.dislikes_count = self.advertisement.dislikes.count()
        self.comments_count = self.advertisement.comments.count()
        self.save()


    def increment_views(self):
        """Increment the view count and update last_viewed_at."""
        self.views += 1
        self.last_viewed_at = datetime.now()
        self.save()

    def increment_favorites(self):
        """Increment the favorites count."""
        self.favorites += 1
        self.save()

    def increment_inquiries(self):
        """Increment the inquiries count."""
        self.inquiries += 1
        self.save()

    def increment_shares(self):
        """Increment the shares count."""
        self.shares += 1
        self.save()


class AdvertisementSaveModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.DO_NOTHING, null=False)
    user = models.ForeignKey(CustomUserModel, on_delete=models.DO_NOTHING, null=True)

    class Meta:
        db_table = "adv_save"
        verbose_name = "آگهی های ذخبره شده"
        verbose_name_plural = "آگهی ذخیره شده"


class AdvertisementViewModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.CASCADE)
    user = models.ForeignKey(CustomUserModel, on_delete=models.DO_NOTHING, null=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="IP Address")
    user_agent = models.TextField(null=True, blank=True, verbose_name="User Agent")
    referrer = models.URLField(null=True, blank=True, verbose_name="Referrer")
    created_at = jmodels.jDateTimeField(auto_now_add=True)
    modified_at = jmodels.jDateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'adv_view'
        verbose_name = "Advertisement View"
        verbose_name_plural = "Advertisement Views"
        indexes = [
            models.Index(fields=['advertisement']),
            models.Index(fields=['user']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"View of {self.advertisement.title} by {self.user.username if self.user else 'Anonymous'}"


class FavoriteAdvertisementModel(models.Model):
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, verbose_name="کاربر")
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.CASCADE, verbose_name="آگهی")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان افزودن به علاقه‌مندی‌ها")
    modified_at = jmodels.jDateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'advertisement')
        db_table = 'favorite_advertisement'
        verbose_name = "آگهی مورد علاقه"
        verbose_name_plural = "آگهی‌های مورد علاقه"

    def __str__(self):
        return f"{self.user.username} - {self.advertisement.title}"


class FlaggedContentModel(models.Model):
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.CASCADE, null=True, blank=True)
    flagged_by = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE)
    reason = models.TextField()
    advertisement_status = models.IntegerField(
        choices=AdvertisementModel.STATUS_CHOICES,
        default=AdvertisementStatus.APPROVED
    )
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان افزودن به علاقه‌مندی‌ها")
    modified_at = jmodels.jDateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'flagged_content'


class AdvertisementFavoriteModel(models.Model):
    """
    Model for tracking user favorites of advertisements.

    This model is used by the recommendation engine and analytics service.
    """
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, verbose_name="کاربر")
    advertisement = models.ForeignKey(AdvertisementModel, on_delete=models.CASCADE, verbose_name="آگهی")
    created_at = jmodels.jDateTimeField(auto_now_add=True, verbose_name="زمان افزودن به علاقه‌مندی‌ها")

    class Meta:
        unique_together = ('user', 'advertisement')
        db_table = 'advertisement_favorite'
        verbose_name = "علاقه‌مندی آگهی"
        verbose_name_plural = "علاقه‌مندی‌های آگهی"

    def __str__(self):
        return f"{self.user.username} - {self.advertisement.title}"


class AdvertisementTagModel(models.Model):
    """
    Model for advertisement tags.

    This model is used by the Elasticsearch sync service and recommendation engine.
    It provides a way to tag advertisements for better search and recommendations.
    """
    name = models.CharField(max_length=100, verbose_name="نام برچسب")
    slug = models.SlugField(max_length=120, unique=True, verbose_name="اسلاگ")
    advertisement = models.ForeignKey(
        AdvertisementModel,
        on_delete=models.DO_NOTHING,
        related_name='tags',
        verbose_name="آگهی"
    )
    tag_id = models.IntegerField(verbose_name="شناسه برچسب")

    class Meta:
        db_table = 'advertisement_tag'
        verbose_name = "برچسب آگهی"
        verbose_name_plural = "برچسب‌های آگهی"
        unique_together = ('advertisement', 'tag_id')

    def __str__(self):
        return self.name


from django.db import models
from django.utils.translation import gettext_lazy as _

class AdvertisementLikeModel(models.Model):
    """Model for storing advertisement likes."""
    advertisement = models.ForeignKey('AdvertisementModel', on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, related_name='advertisement_likes')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('advertisement', 'user')
        verbose_name = _('Advertisement Like')
        verbose_name_plural = _('Advertisement Likes')

    def __str__(self):
        return f"{self.user.email} liked {self.advertisement.title}"

class AdvertisementDislikeModel(models.Model):
    """Model for storing advertisement dislikes."""
    advertisement = models.ForeignKey('AdvertisementModel', on_delete=models.CASCADE, related_name='dislikes')
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, related_name='advertisement_dislikes')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('advertisement', 'user')
        verbose_name = _('Advertisement Dislike')
        verbose_name_plural = _('Advertisement Dislikes')

    def __str__(self):
        return f"{self.user.email} disliked {self.advertisement.title}"

class AdvertisementCommentModel(models.Model):
    """Model for storing advertisement comments."""
    advertisement = models.ForeignKey('AdvertisementModel', on_delete=models.CASCADE, related_name='comments')
    user = models.ForeignKey(CustomUserModel, on_delete=models.CASCADE, related_name='advertisement_comments')
    content = models.TextField(_('Comment Content'))
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Advertisement Comment')
        verbose_name_plural = _('Advertisement Comments')

    def __str__(self):
        return f"Comment by {self.user.email} on {self.advertisement.title}"
