from django.db import models
from django.utils.translation import gettext_lazy as _

class Contact(models.Model):
    name = models.Char<PERSON>ield(_('نام'), max_length=100)
    email = models.EmailField(_('ایمیل'), max_length=100,null=False, blank=False)
    phone_number = models.CharField(max_length=11, null=True, blank=True, default=0)
    address=models.CharField(max_length=250, null=True, blank=True, default=0)
    message = models.TextField(_('پیام'))
    created_at = models.DateTimeField(_('تاریخ ایجاد'), auto_now_add=True)

    class Meta:
        db_table = 'contact_us'
        verbose_name = _('تماس با ما')
        verbose_name_plural = _('پیام‌های تماس با ما')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.name} <{self.email}>"