"""
Custom Authentication Backend for Dual Username Support

This module provides authentication backends that support both email and phone number
as username fields for the Soodam platform.
"""

import logging
from typing import Optional

from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.hashers import check_password
from django.http import HttpRequest

from app.models.user import CustomUserModel, normalize_iranian_phone_number

logger = logging.getLogger(__name__)


class DualUsernameAuthenticationBackend(BaseBackend):
    """
    Custom authentication backend that supports both email and phone number as username.
    
    This backend allows users to login with either their email address or phone number,
    providing flexibility and better user experience.
    """

    def authenticate(
        self, 
        request: HttpRequest, 
        username: str = None, 
        password: str = None, 
        **kwargs
    ) -> Optional[CustomUserModel]:
        """
        Authenticate user with email or phone number.
        
        Args:
            request: HTTP request object
            username: Email address or phone number
            password: User password
            **kwargs: Additional keyword arguments
            
        Returns:
            CustomUserModel instance if authentication successful, None otherwise
        """
        if username is None or password is None:
            return None

        try:
            # Use the manager's authenticate method
            user = CustomUserModel.objects.authenticate_user(username, password)
            
            if user:
                logger.info(f"Successful authentication for user: {user.get_username_display()}")
                return user
            else:
                logger.warning(f"Failed authentication attempt for username: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Authentication error for username {username}: {e}")
            return None

    def get_user(self, user_id: int) -> Optional[CustomUserModel]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            CustomUserModel instance or None if not found
        """
        try:
            return CustomUserModel.objects.get(pk=user_id)
        except CustomUserModel.DoesNotExist:
            return None


class EmailAuthenticationBackend(BaseBackend):
    """
    Authentication backend specifically for email-based login.
    """

    def authenticate(
        self, 
        request: HttpRequest, 
        username: str = None, 
        password: str = None, 
        **kwargs
    ) -> Optional[CustomUserModel]:
        """
        Authenticate user with email address only.
        
        Args:
            request: HTTP request object
            username: Email address
            password: User password
            **kwargs: Additional keyword arguments
            
        Returns:
            CustomUserModel instance if authentication successful, None otherwise
        """
        if username is None or password is None:
            return None

        # Only proceed if username looks like an email
        if '@' not in username:
            return None

        try:
            user = CustomUserModel.objects.get_by_email(username)
            
            if user and check_password(password, user.password) and user.is_active:
                logger.info(f"Successful email authentication for user: {user.email}")
                return user
            else:
                logger.warning(f"Failed email authentication attempt for: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Email authentication error for {username}: {e}")
            return None

    def get_user(self, user_id: int) -> Optional[CustomUserModel]:
        """Get user by ID."""
        try:
            return CustomUserModel.objects.get(pk=user_id)
        except CustomUserModel.DoesNotExist:
            return None


class PhoneAuthenticationBackend(BaseBackend):
    """
    Authentication backend specifically for phone number-based login.
    """

    def authenticate(
        self, 
        request: HttpRequest, 
        username: str = None, 
        password: str = None, 
        **kwargs
    ) -> Optional[CustomUserModel]:
        """
        Authenticate user with phone number only.
        
        Args:
            request: HTTP request object
            username: Phone number
            password: User password
            **kwargs: Additional keyword arguments
            
        Returns:
            CustomUserModel instance if authentication successful, None otherwise
        """
        if username is None or password is None:
            return None

        # Only proceed if username looks like a phone number
        if '@' in username:
            return None

        try:
            user = CustomUserModel.objects.get_by_phone(username)
            
            if user and check_password(password, user.password) and user.is_active:
                logger.info(f"Successful phone authentication for user: {user.phone_number}")
                return user
            else:
                logger.warning(f"Failed phone authentication attempt for: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Phone authentication error for {username}: {e}")
            return None

    def get_user(self, user_id: int) -> Optional[CustomUserModel]:
        """Get user by ID."""
        try:
            return CustomUserModel.objects.get(pk=user_id)
        except CustomUserModel.DoesNotExist:
            return None


# =============================================================================
# UTILITY FUNCTIONS FOR AUTHENTICATION
# =============================================================================

def identify_username_type(username: str) -> str:
    """
    Identify the type of username (email or phone).
    
    Args:
        username: Username string
        
    Returns:
        'email', 'phone', or 'unknown'
    """
    if '@' in username:
        return 'email'
    
    try:
        # Try to normalize as phone number
        normalize_iranian_phone_number(username)
        return 'phone'
    except ValueError:
        return 'unknown'


def get_user_by_username(username: str) -> Optional[CustomUserModel]:
    """
    Get user by any username type (email or phone).
    
    Args:
        username: Email address or phone number
        
    Returns:
        CustomUserModel instance or None if not found
    """
    return CustomUserModel.objects.get_by_any_username(username)


def authenticate_user_credentials(username: str, password: str) -> Optional[CustomUserModel]:
    """
    Authenticate user with username and password.
    
    This is a utility function that can be used in API views
    for authentication without going through Django's auth system.
    
    Args:
        username: Email address or phone number
        password: User password
        
    Returns:
        CustomUserModel instance if authentication successful, None otherwise
    """
    return CustomUserModel.objects.authenticate_user(username, password)


# =============================================================================
# AUTHENTICATION HELPERS FOR API VIEWS
# =============================================================================

class AuthenticationHelper:
    """
    Helper class for authentication in API views.
    """
    
    @staticmethod
    def authenticate(username: str, password: str) -> Optional[CustomUserModel]:
        """
        Authenticate user with dual username support.
        
        Args:
            username: Email address or phone number
            password: User password
            
        Returns:
            CustomUserModel instance if successful, None otherwise
        """
        return authenticate_user_credentials(username, password)
    
    @staticmethod
    def get_username_type(username: str) -> str:
        """Get the type of username."""
        return identify_username_type(username)
    
    @staticmethod
    def normalize_username(username: str) -> str:
        """
        Normalize username based on its type.
        
        Args:
            username: Raw username
            
        Returns:
            Normalized username
        """
        username_type = identify_username_type(username)
        
        if username_type == 'phone':
            try:
                return normalize_iranian_phone_number(username)
            except ValueError:
                return username
        elif username_type == 'email':
            return username.lower().strip()
        else:
            return username.strip()
    
    @staticmethod
    def validate_credentials(username: str, password: str) -> dict:
        """
        Validate credentials and return detailed information.
        
        Args:
            username: Email address or phone number
            password: User password
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': False,
            'user': None,
            'username_type': identify_username_type(username),
            'normalized_username': None,
            'error': None
        }
        
        try:
            # Normalize username
            result['normalized_username'] = AuthenticationHelper.normalize_username(username)
            
            # Authenticate
            user = AuthenticationHelper.authenticate(username, password)
            
            if user:
                result['valid'] = True
                result['user'] = user
            else:
                result['error'] = 'Invalid credentials'
                
        except Exception as e:
            result['error'] = str(e)
        
        return result
