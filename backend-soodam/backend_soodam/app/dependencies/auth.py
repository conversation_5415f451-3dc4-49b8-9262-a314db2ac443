from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from app.models import CustomUserModel, SpecialAdmin
from config.jwt import decode_access_token
import logging
from jose import JW<PERSON>rror
from config.exceptions import InvalidTokenException
logger = logging.getLogger(__name__)

async def get_token_from_query(token: str):
    """
    Validate a token provided as a query parameter and return the user.

    Args:
        token: The JWT token to validate

    Returns:
        The user associated with the token

    Raises:
        HTTPException: If the token is invalid or the user doesn't exist
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    payload = decode_access_token(token)
    if payload is None:
        raise credentials_exception

    user_id = payload.get("sub")
    if user_id is None:
        raise credentials_exception

    user = await CustomUserModel.objects.filter(uuid=user_id).afirst()
    if user is None:
        raise credentials_exception

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )

    return user

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = decode_access_token(token)
        if payload is None:
            raise credentials_exception
    except JWTError:
        raise InvalidTokenException()

    user_id = payload.get("sub")
    if user_id is None:
        raise credentials_exception

    user = await CustomUserModel.objects.filter(uuid=user_id).afirst()
    if not user or user is None:
        raise InvalidTokenException()

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    return user

async def get_current_admin_user(
    current_user: CustomUserModel = Depends(get_current_user),
):
    """
    Get the current user if they are an admin or superuser.
    Raises 403 if the user is not an admin.
    """
    if not (current_user.is_superuser or current_user.is_admin or current_user.is_staff):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have admin privileges"
        )
    return current_user

async def require_super_admin(
    current_user: CustomUserModel = Depends(get_current_user),
):
    """
    Check if the current user is a superuser.
    Raises 403 if the user is not a superuser.
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="This action requires super admin privileges"
        )
    return current_user

def check_admin_permission(permission_name: str):
    """
    Dependency factory to check if an admin has a specific permission.
    Usage: Depends(check_admin_permission("can_manage_users"))
    """
    async def _check_permission(
        permissions = Depends(get_special_admin_permissions)
    ):
        if permissions is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have admin privileges"
            )

        if not permissions.get(permission_name, False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"You don't have the required permission: {permission_name}"
            )

        return permissions

    return _check_permission

# Add dependency for checking special admin permissions
async def get_special_admin_permissions(
    current_user: CustomUserModel = Depends(get_current_user),
):
    """
    Get the special admin permissions for the current user.
    Returns None if the user is not a special admin.
    """
    if current_user.is_superuser:
        # Super admins have all permissions
        return {
            "can_manage_users": True,
            "can_manage_advertisements": True,
            "can_manage_blogs": True,
            "can_manage_agents": True,
            "cities": [],  # Empty list means all cities
            "provinces": []  # Empty list means all provinces
        }

    try:
        special_admin = await SpecialAdmin.objects.filter(
            user_id=current_user.id,
            status=1  # Active status
        ).prefetch_related('cities', 'provinces').afirst()

        if not special_admin:
            return None

        # Get city and province IDs
        city_ids = [city.id for city in await special_admin.cities.all()]
        province_ids = [province.id for province in await special_admin.provinces.all()]

        return {
            "can_manage_users": special_admin.can_manage_users,
            "can_manage_advertisements": special_admin.can_manage_advertisements,
            "can_manage_blogs": special_admin.can_manage_blogs,
            "can_manage_agents": special_admin.can_manage_agents,
            "cities": city_ids,
            "provinces": province_ids,
            "admin_type": special_admin.admin_type_id
        }
    except Exception as e:
        logger.error(f"Error getting special admin permissions: {str(e)}")
        return None
