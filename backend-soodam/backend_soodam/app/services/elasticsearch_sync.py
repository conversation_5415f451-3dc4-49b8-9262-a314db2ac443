"""
Elasticsearch sync service for the Soodam backend.

This module provides utilities for syncing data with Elasticsearch.
"""

import logging
from typing import Dict, List, Optional, Union

from django.db.models import Q

from ..core.elasticsearch import (
    ADVERTISEMENT_INDEX,
    BLOG_INDEX,
    USER_INDEX,
    delete_advertisement,
    es_client,
    index_advertisement,
)
from ..models import CustomUserModel
from ..models.advertisement import (
    AdvertisementModel,
    MainCategoryModel as AdvertisementCategoryModel,
    AdvertisementImagesModel as AdvertisementImageModel,
    PropertyTextValueModel as AdvertisementAttributeModel,
    AdvertisementViewModel as AdvertisementStatisticsModel,
    AdvertisementLocationModel,
    AdvertisementTagModel,

)
from ..models.blogs import BlogModel #, BlogCategoryModel

logger = logging.getLogger(__name__)


class ElasticsearchSyncService:
    """
    Elasticsearch sync service.
    
    This class provides utilities for syncing data with Elasticsearch.
    """
    
    @staticmethod
    async def sync_advertisement(advertisement_id: int) -> Dict:
        """
        Sync an advertisement with Elasticsearch.
        
        Args:
            advertisement_id: The advertisement ID
            
        Returns:
            Dict: The sync result
            
        Raises:
            Exception: If an error occurs
        """
        try:
            # Get the advertisement
            advertisement = await AdvertisementModel.objects.select_related(
                'category', 'location', 'user', 'statistics'
            ).prefetch_related(
                'images', 'attributes', 'tags'
            ).filter(id=advertisement_id).afirst()
            
            if not advertisement:
                # Delete from Elasticsearch if it exists
                await delete_advertisement(advertisement_id)
                
                return {
                    'success': True,
                    'action': 'delete',
                    'id': advertisement_id
                }
            
            # Get the primary image
            primary_image = await advertisement.images.filter(is_primary=True).afirst()
            
            # Get the attributes
            attributes = []
            for attribute in await advertisement.attributes.all():
                attributes.append({
                    'name': attribute.name,
                    'value': attribute.value,
                    'unit': attribute.unit
                })
            
            # Get the tags
            tags = [tag.name for tag in await advertisement.tags.all()]
            
            # Get the statistics
            views = 0
            favorites = 0
            inquiries = 0
            if hasattr(advertisement, 'statistics'):
                views = advertisement.statistics.views
                favorites = advertisement.statistics.favorites
                inquiries = advertisement.statistics.inquiries
            
            # Build the document
            document = {
                'id': advertisement.id,
                'title': advertisement.title,
                'description': advertisement.description,
                'category_id': advertisement.category.id,
                'category_name': advertisement.category.name,
                'location_id': advertisement.location.id,
                'location_city': advertisement.location.city,
                'location_state': advertisement.location.state,
                'location_country': advertisement.location.country,
                'location_point': {
                    'lat': advertisement.location.latitude,
                    'lon': advertisement.location.longitude
                } if advertisement.location.latitude and advertisement.location.longitude else None,
                'price': advertisement.price.amount if hasattr(advertisement, 'price') else None,
                'currency': advertisement.price.currency if hasattr(advertisement, 'price') else None,
                'is_negotiable': advertisement.price.is_negotiable if hasattr(advertisement, 'price') else False,
                'user_id': advertisement.user.id,
                'user_name': f"{advertisement.user.first_name} {advertisement.user.last_name}",
                'status': advertisement.status,
                'is_featured': advertisement.is_featured,
                'tags': tags,
                'created_at': advertisement.created_at.isoformat(),
                'updated_at': advertisement.updated_at.isoformat(),
                'expiry_date': advertisement.expiry_date.isoformat() if advertisement.expiry_date else None,
                'views': views,
                'favorites': favorites,
                'inquiries': inquiries,
                'image_url': primary_image.url if primary_image else None,
                'attributes': attributes
            }
            
            # Index the document
            result = await index_advertisement(document)
            
            return {
                'success': True,
                'action': 'index',
                'id': advertisement.id,
                'result': result
            }
        except Exception as e:
            logger.error(f"Error syncing advertisement {advertisement_id}: {str(e)}")
            raise
    
    @staticmethod
    async def sync_all_advertisements() -> Dict:
        """
        Sync all advertisements with Elasticsearch.
        
        Returns:
            Dict: The sync result
            
        Raises:
            Exception: If an error occurs
        """
        try:
            # Get all advertisement IDs
            advertisement_ids = await AdvertisementModel.objects.values_list('id', flat=True)
            
            # Sync each advertisement
            results = []
            for advertisement_id in advertisement_ids:
                try:
                    result = await ElasticsearchSyncService.sync_advertisement(advertisement_id)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error syncing advertisement {advertisement_id}: {str(e)}")
                    results.append({
                        'success': False,
                        'id': advertisement_id,
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'total': len(advertisement_ids),
                'results': results
            }
        except Exception as e:
            logger.error(f"Error syncing all advertisements: {str(e)}")
            raise
    
    @staticmethod
    async def sync_user(user_id: int) -> Dict:
        """
        Sync a user with Elasticsearch.
        
        Args:
            user_id: The user ID
            
        Returns:
            Dict: The sync result
            
        Raises:
            Exception: If an error occurs
        """
        try:
            # Get the user
            user = await CustomUserModel.objects.filter(id=user_id).afirst()
            
            if not user:
                # Delete from Elasticsearch if it exists
                try:
                    await es_client.delete(
                        index=USER_INDEX,
                        id=user_id,
                        refresh=True
                    )
                except Exception:
                    pass
                
                return {
                    'success': True,
                    'action': 'delete',
                    'id': user_id
                }
            
            # Build the document
            document = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}",
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'avatar_url': user.avatar_url,
                'phone_number': user.phone_number,
                'location_id': user.location.id if hasattr(user, 'location') else None,
                'location_city': user.location.city if hasattr(user, 'location') else None,
                'location_state': user.location.state if hasattr(user, 'location') else None,
                'location_country': user.location.country if hasattr(user, 'location') else None
            }
            
            # Index the document
            result = await es_client.index(
                index=USER_INDEX,
                id=user.id,
                document=document,
                refresh=True
            )
            
            return {
                'success': True,
                'action': 'index',
                'id': user.id,
                'result': result
            }
        except Exception as e:
            logger.error(f"Error syncing user {user_id}: {str(e)}")
            raise
    
    @staticmethod
    async def sync_blog(blog_id: int) -> Dict:
        """
        Sync a blog with Elasticsearch.
        
        Args:
            blog_id: The blog ID
            
        Returns:
            Dict: The sync result
            
        Raises:
            Exception: If an error occurs
        """
        try:
            # Get the blog
            blog = await BlogModel.objects.select_related(
                'category', 'author'
            ).prefetch_related(
                'tags'
            ).filter(id=blog_id).afirst()
            
            if not blog:
                # Delete from Elasticsearch if it exists
                try:
                    await es_client.delete(
                        index=BLOG_INDEX,
                        id=blog_id,
                        refresh=True
                    )
                except Exception:
                    pass
                
                return {
                    'success': True,
                    'action': 'delete',
                    'id': blog_id
                }
            
            # Get the tags
            tags = [tag.name for tag in await blog.tags.all()]
            
            # Build the document
            document = {
                'id': blog.id,
                'title': blog.title,
                'content': blog.content,
                'excerpt': blog.excerpt,
                'category_id': blog.category.id,
                'category_name': blog.category.name,
                'author_id': blog.author.id,
                'author_name': f"{blog.author.first_name} {blog.author.last_name}",
                'status': blog.status,
                'tags': tags,
                'created_at': blog.created_at.isoformat(),
                'updated_at': blog.updated_at.isoformat(),
                'published_at': blog.published_at.isoformat() if blog.published_at else None,
                'views': blog.views,
                'likes': blog.likes,
                'comments': blog.comments,
                'image_url': blog.image_url
            }
            
            # Index the document
            result = await es_client.index(
                index=BLOG_INDEX,
                id=blog.id,
                document=document,
                refresh=True
            )
            
            return {
                'success': True,
                'action': 'index',
                'id': blog.id,
                'result': result
            }
        except Exception as e:
            logger.error(f"Error syncing blog {blog_id}: {str(e)}")
            raise
