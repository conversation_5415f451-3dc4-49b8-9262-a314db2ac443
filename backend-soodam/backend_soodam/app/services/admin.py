from app.models import AdvertisementModel
from app.models.user import CustomUserModel
from app.models.roles import RoleModel, PermissionModel, RolePermissionModel, UserRoleModel
from fastapi import HTTPException

class AdminService:

    async def get_all_advertisements():
        return await AdvertisementModel.objects.all()

    async def approve_advertisement(adv_id: int, approve: bool):
        advertisement = await AdvertisementModel.objects.filter(id=adv_id).afirst()
        if not advertisement:
            raise HTTPException(status_code=404, detail="Advertisement not found")

        advertisement.status = 1 if approve else 2
        await advertisement.asave()
        return {"message": "Advertisement updated successfully", "status": "approved" if approve else "rejected"}

    async def get_all_users():
        return await CustomUserModel.objects.all()

    async def ban_user(user_id: int, ban: bool):
        user = await CustomUserModel.objects.filter(id=user_id).afirst()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        user.is_active = not ban
        await user.asave()
        return {"message": "User updated successfully", "status": "banned" if ban else "unbanned"}

    async def create_role(name: str, description: str):
        role, created = await RoleModel.objects.get_or_create(name=name, defaults={"description": description})
        if not created:
            raise HTTPException(status_code=400, detail="Role already exists")
        return role

    async def create_permission(name: str, description: str):
        permission, created = await PermissionModel.objects.get_or_create(name=name, defaults={"description": description})
        if not created:
            raise HTTPException(status_code=400, detail="Permission already exists")
        return permission

    async def assign_permission_to_role(role_id: int, permission_id: int):
        role = await RoleModel.objects.filter(id=role_id).afirst()
        permission = await PermissionModel.objects.filter(id=permission_id).afirst()
        if not role or not permission:
            raise HTTPException(status_code=404, detail="Role or Permission not found")

        await RolePermissionModel.objects.get_or_create(role=role, permission=permission)
        return {"message": "Permission assigned to role successfully"}

    async def assign_role_to_user(user_id: int, role_id: int):
        user = await CustomUserModel.objects.filter(id=user_id).afirst()
        role = await RoleModel.objects.filter(id=role_id).afirst()
        if not user or not role:
            raise HTTPException(status_code=404, detail="User or Role not found")

        await UserRoleModel.objects.get_or_create(user=user, role=role)
        return {"message": "Role assigned to user successfully"}