# Iranian Payment Gateway Integration

This module provides a custom implementation for Iranian bank payment gateways without relying on external libraries like az-iranian-bank-gateways.

## Supported Banks

- <PERSON>arinpal
- <PERSON>lat
- <PERSON><PERSON> (planned)
- Parsian (planned)

## Configuration

Add the following environment variables to your `.env` file:

```
# Payment gateway settings
PAYMENT_SANDBOX_MODE=True
PAYMENT_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Zarinpal settings
ZARINPAL_MERCHANT_ID=your-zarinpal-merchant-id
ZARINPAL_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Mellat settings
MELLAT_MERCHANT_ID=your-mellat-merchant-id
MELLAT_TERMINAL_ID=your-mellat-terminal-id
MELLAT_USERNAME=your-mellat-username
MELLAT_PASSWORD=your-mellat-password
MELLAT_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Saman settings
SAMAN_MERCHANT_ID=your-saman-merchant-id
SAMAN_CALLBACK_URL=https://api.soodam.com/api/payments/callback

# Parsian settings
PARSIAN_PIN=your-parsian-pin
PARSIAN_CALLBACK_URL=https://api.soodam.com/api/payments/callback
```

## Usage

### Initializing a Payment

```python
from app.services.iranian_payment import PaymentGatewayFactory

# Create a gateway instance
gateway = PaymentGatewayFactory.create_gateway(
    bank_name="zarinpal",  # or "mellat", "saman", "parsian"
    callback_url="https://your-domain.com/callback"  # Optional, will use default if not provided
)

# Request a payment
payment_result = await gateway.request_payment(
    amount=10000,  # Amount in Tomans
    description="Payment for order #123",
    mobile="***********"  # Optional
)

if payment_result["success"]:
    # Redirect the user to the payment gateway
    redirect_url = payment_result["redirect_url"]
    tracking_code = payment_result["tracking_code"]
    reference_number = payment_result["reference_number"]
    
    # Store the tracking_code and reference_number in your database
    # ...
    
    # Redirect the user to the payment gateway
    return {"redirect_url": redirect_url}
else:
    # Handle payment initialization error
    error_code = payment_result.get("error_code")
    error_message = payment_result.get("error_message")
    
    # Log the error
    logger.error(f"Payment initialization failed: {error_message} (code: {error_code})")
    
    # Return error to the user
    return {"error": error_message}
```

### Verifying a Payment

```python
from app.services.iranian_payment import PaymentGatewayFactory

# Create a gateway instance
gateway = PaymentGatewayFactory.create_gateway(
    bank_name="zarinpal"  # Use the same bank as the initial payment
)

# Verify the payment
verify_result = await gateway.verify_payment(
    tracking_code="tracking-code-from-callback",
    reference_id="reference-id-from-callback"  # Optional for some banks
)

if verify_result["success"]:
    # Payment was successful
    reference_id = verify_result["reference_id"]
    message = verify_result["message"]
    
    # Update your database
    # ...
    
    # Return success to the user
    return {"success": True, "message": message}
else:
    # Handle payment verification error
    error_code = verify_result.get("error_code")
    error_message = verify_result.get("error_message")
    
    # Log the error
    logger.error(f"Payment verification failed: {error_message} (code: {error_code})")
    
    # Return error to the user
    return {"success": False, "error": error_message}
```

## API Endpoints

The payment module provides the following API endpoints:

### Initialize Payment

```
POST /api/payments/initialize
```

Request body:
```json
{
  "amount": 10000,
  "bank_name": "zarinpal",
  "description": "Payment for order #123",
  "mobile_number": "***********",
  "callback_url": "https://your-domain.com/callback"
}
```

Response:
```json
{
  "redirect_url": "https://www.zarinpal.com/pg/StartPay/abc123",
  "reference_number": "def456",
  "tracking_code": "ghi789"
}
```

### Verify Payment

```
POST /api/payments/callback
```

Request body:
```json
{
  "tracking_code": "ghi789",
  "status": 1,
  "reference_id": "jkl012",
  "additional_data": {}
}
```

Response:
```json
{
  "is_success": true,
  "reference_number": "def456",
  "tracking_code": "ghi789",
  "bank_reference": "jkl012",
  "message": "پرداخت با موفقیت انجام شد"
}
```

### Get Payment History

```
GET /api/payments/history?page=1&page_size=10
```

Response:
```json
{
  "payments": [
    {
      "id": "123",
      "amount": 10000,
      "bank_name": "zarinpal",
      "status": "موفق",
      "date": "2023-05-01T12:00:00",
      "reference_number": "def456",
      "sale_reference_id": "jkl012"
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 10
}
```

## Error Handling

The payment module provides detailed error messages for each bank. The error codes are specific to each bank and are documented in the code.

## Testing

To run the tests for the payment module:

```bash
pytest tests/test_payment_service.py -v
```

## Adding a New Bank

To add a new bank, create a new class that inherits from `IranianBankGateway` and implement the required methods:

```python
class NewBankGateway(IranianBankGateway):
    """Implementation for New Bank payment gateway"""
    
    def __init__(self, merchant_id: str, callback_url: str):
        super().__init__(merchant_id, callback_url)
        # Initialize bank-specific properties
        
    async def request_payment(self, amount: float, description: str, mobile: Optional[str] = None) -> Dict[str, Any]:
        """Request a payment from New Bank"""
        # Implement bank-specific payment request
        
    async def verify_payment(self, tracking_code: str, reference_id: Optional[str] = None) -> Dict[str, Any]:
        """Verify a payment with New Bank"""
        # Implement bank-specific payment verification
```

Then, update the `PaymentGatewayFactory` to support the new bank:

```python
@staticmethod
def create_gateway(bank_name: str, callback_url: Optional[str] = None) -> IranianBankGateway:
    """Create a payment gateway instance based on bank name"""
    bank_name = bank_name.lower()
    
    # Use default callback URL if not provided
    if not callback_url:
        callback_url = settings.PAYMENT_CALLBACK_URL
        
    if bank_name == "zarinpal":
        return ZarinpalGateway(
            merchant_id=settings.ZARINPAL_MERCHANT_ID,
            callback_url=callback_url
        )
    elif bank_name == "mellat":
        return MellatGateway(
            merchant_id=settings.MELLAT_MERCHANT_ID,
            callback_url=callback_url,
            terminal_id=settings.MELLAT_TERMINAL_ID,
            username=settings.MELLAT_USERNAME,
            password=settings.MELLAT_PASSWORD
        )
    elif bank_name == "newbank":
        return NewBankGateway(
            merchant_id=settings.NEWBANK_MERCHANT_ID,
            callback_url=callback_url
        )
    else:
        raise ValueError(f"Unsupported bank: {bank_name}")
```
