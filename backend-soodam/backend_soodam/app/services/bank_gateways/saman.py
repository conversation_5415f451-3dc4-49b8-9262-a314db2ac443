import logging
import requests
import uuid
from typing import Dict, Any, Optional, <PERSON><PERSON>

from app.core.config import SAMAN_MERCHANT_ID, SAMAN_CALLBACK_URL

logger = logging.getLogger(__name__)
import logging

from zeep import Client, Transport

from app.services.bank_gateways.banks import BaseBank
from app.exceptions.banks import SettingDoesNotExist,BankGatewayRejectPayment
from app.models import BankType, CurrencyEnum, PaymentStatus


class SamanGateway:
    """Saman Bank payment gateway implementation"""
    
    TOKEN_URL = "https://sep.shaparak.ir/MobilePG/MobilePayment"
    VERIFY_URL = "https://sep.shaparak.ir/payments/referencepayment.asmx?WSDL"
    
    @staticmethod
    async def request_payment(
        amount: int,
        cell_number: Optional[str] = None,
        order_id: Optional[str] = None
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Request a payment from Saman Bank
        
        Args:
            amount: Payment amount in Rials
            cell_number: Customer mobile number (optional)
            order_id: Unique order ID (optional, will be generated if not provided)
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Generate order ID if not provided
            if not order_id:
                order_id = str(uuid.uuid4())
            
            # Prepare request data
            data = {
                "Action": "Token",
                "TerminalId": SAMAN_MERCHANT_ID,
                "RedirectUrl": SAMAN_CALLBACK_URL,
                "ResNum": order_id,
                "Amount": amount
            }
            
            # Add cell number if provided
            if cell_number:
                data["CellNumber"] = cell_number
            
            # Send request to Saman
            response = requests.post(SamanGateway.TOKEN_URL, json=data)
            result = response.json()
            
            # Check if request was successful
            if "status" in result and result["status"] == 1:
                token = result.get("token", "")
                payment_url = f"https://sep.shaparak.ir/OnlinePG/OnlinePG?Token={token}"
                
                return True, "Payment request successful", {
                    "token": token,
                    "payment_url": payment_url,
                    "order_id": order_id
                }
            else:
                error_code = result.get("errorCode", "unknown")
                error_message = result.get("errorDesc", "Unknown error")
                logger.error(f"Saman payment request failed: {error_code} - {error_message}")
                
                return False, f"Payment request failed: {error_message}", {
                    "error_code": error_code
                }
                
        except Exception as e:
            logger.error(f"Error in Saman payment request: {str(e)}")
            return False, f"Payment request failed: {str(e)}", {}
    
    @staticmethod
    async def verify_payment(
        reference_number: str,
        order_id: str
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Verify a payment with Saman Bank
        
        Args:
            reference_number: Reference number from callback
            order_id: Order ID from request_payment
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Create SOAP client
            import zeep
            client = zeep.Client(wsdl=SamanGateway.VERIFY_URL)
            
            # Call the verification service
            result = client.service.verifyTransaction(reference_number, SAMAN_MERCHANT_ID)
            
            # Check verification result
            if result > 0:
                # Verification successful, result is the amount
                return True, "Payment verified successfully", {
                    "reference_number": reference_number,
                    "amount": result
                }
            else:
                # Verification failed
                error_message = SamanGateway._get_error_message(str(result))
                logger.error(f"Saman payment verification failed: {result} - {error_message}")
                
                return False, f"Payment verification failed: {error_message}", {
                    "error_code": result
                }
                
        except Exception as e:
            logger.error(f"Error in Saman payment verification: {str(e)}")
            return False, f"Payment verification failed: {str(e)}", {}
    
    @staticmethod
    def _get_error_message(error_code: str) -> str:
        """Get error message for Saman error code"""
        error_messages = {
            "-1": "خطای در پردازش اطلاعات ارسالی",
            "-2": "IP و يا مرچنت كد پذيرنده صحيح نيست",
            "-3": "با توجه به محدوديت های شاپرک امکان پرداخت با رقم درخواست شده ميسر نمی باشد",
            "-4": "سطح تاييد پذيرنده پايين تر از سطح نقره ای است",
            "-5": "خطای دسترسی: درخواست از سرور غیر مجاز ارسال شده است",
            "-6": "خطای در پردازش اطلاعات ارسالی",
            "-7": "رقم تراکنش برای پذيرنده معتبر نيست",
            "-8": "طول رشته درخواست غیر مجاز است",
            "-9": "وضعيت پذيرنده غير فعال است",
            "-10": "آدرس بازگشت پذيرنده نامعتبر است",
            "-11": "مرچنت کد فعال نیست لطفا با بخش امور پذيرندگان تماس حاصل فرماييد",
            "-12": "تلاش بيش از حد در يک بازه زمانی کوتاه",
            "-13": "شماره رزرو شده است",
            "-14": "چنین تراکنشی قبلا انجام نشده است",
            "-15": "امکان ارسال درخواست براي اين پذيرنده وجود ندارد",
            "-16": "با توجه به محدوديت های شاپرک امکان پرداخت با رقم درخواست شده ميسر نمی باشد",
            "-17": "آدرس IP پذيرنده نا معتبر است",
            "-18": "اشکال در تاريخ و زمان سيستم"
        }
        
        return error_messages.get(error_code, f"خطای ناشناخته: {error_code}")