import logging
import requests
from typing import Dict, Any, Optional, Tuple

from app.core.config import ZARINPAL_MERCHANT_ID, ZARINPAL_CALLBACK_URL, ZARINPAL_SANDBOX

logger = logging.getLogger(__name__)
class Zarinpal(BaseBank):
    _merchant_code = None
    _sandbox = None

    def __init__(self, **kwargs):
        kwargs.setdefault("SANDBOX", 0)
        super(Zarinpal, self).__init__(**kwargs)
        self.set_gateway_currency(CurrencyEnum.IRT)
        self._payment_url = "https://www.zarinpal.com/pg/StartPay/{}/ZarinGate"
        self._sandbox_url = "https://sandbox.zarinpal.com/pg/StartPay/{}/ZarinGate"

    def get_bank_type(self):
        return BankType.ZARINPAL

    def set_default_settings(self):
        for item in ["MERCHANT_CODE", "SANDBOX"]:
            if item not in self.default_setting_kwargs:
                raise SettingDoesNotExist(f"{item} does not exist in default_setting_kwargs")
            setattr(self, f"_{item.lower()}", self.default_setting_kwargs[item])

    """
    gateway
    """

    @classmethod
    def get_minimum_amount(cls):
        return 1000

    def _get_gateway_payment_url_parameter(self):
        if self._sandbox:
            return self._sandbox_url.format(self.get_reference_number())
        return self._payment_url.format(self.get_reference_number())

    def _get_gateway_payment_parameter(self):
        return {}

    def _get_gateway_payment_method_parameter(self):
        return "GET"

    """
    pay
    """

    def get_pay_data(self):
        description = "خرید با شماره پیگیری - {}".format(self.get_tracking_code())

        return {
            "Description": description,
            "MerchantID": self._merchant_code,
            "Amount": self.get_gateway_amount(),
            "Email": None,
            "Mobile": self.get_mobile_number(),
            "CallbackURL": self._get_gateway_callback_url(),
        }

    def prepare_pay(self):
        super(Zarinpal, self).prepare_pay()

    def pay(self):
        super(Zarinpal, self).pay()
        data = self.get_pay_data()
        client = self._get_client()
        result = client.service.PaymentRequest(**data)
        if result.Status == 100:
            token = result.Authority
            self._set_reference_number(token)
        else:
            logging.critical("Zarinpal gateway reject payment")
            raise BankGatewayRejectPayment(self.get_transaction_status_text())

    """
    verify from gateway
    """

    def prepare_verify_from_gateway(self):
        super(Zarinpal, self).prepare_verify_from_gateway()
        token = self.get_request().GET.get("Authority", None)
        self._set_reference_number(token)
        self._set_bank_record()

    def verify_from_gateway(self, request):
        super(Zarinpal, self).verify_from_gateway(request)

    """
    verify
    """

    def get_verify_data(self):
        super(Zarinpal, self).get_verify_data()
        return {
            "MerchantID": self._merchant_code,
            "Authority": self.get_reference_number(),
            "Amount": self.get_gateway_amount(),
        }

    def prepare_verify(self, tracking_code):
        super(Zarinpal, self).prepare_verify(tracking_code)

    def verify(self, transaction_code):
        super(Zarinpal, self).verify(transaction_code)
        data = self.get_verify_data()
        client = self._get_client(timeout=10)
        result = client.service.PaymentVerification(**data)
        if result.Status in [100, 101]:
            self._set_payment_status(PaymentStatus.COMPLETE)
        else:
            self._set_payment_status(PaymentStatus.CANCEL_BY_USER)
            logging.debug("Zarinpal gateway unapprove payment")

    def _get_client(self, timeout=5):
        transport = Transport(timeout=timeout, operation_timeout=timeout)
        if self._sandbox:
            return Client(
                "https://sandbox.zarinpal.com/pg/services/WebGate/wsdl",
                transport=transport,
            )

        return Client(
            "https://www.zarinpal.com/pg/services/WebGate/wsdl",
            transport=transport,
        )

class ZarinpalGateway:
    """Zarinpal payment gateway implementation"""
    
    @staticmethod
    async def request_payment(
        amount: int,
        description: str,
        mobile: Optional[str] = None,
        email: Optional[str] = None
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Request a payment from Zarinpal
        
        Args:
            amount: Payment amount in Tomans (will be multiplied by 10 for Rials)
            description: Payment description
            mobile: Customer mobile number (optional)
            email: Customer email (optional)
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Convert Tomans to Rials
            amount_rials = amount * 10
            
            # Prepare request data
            data = {
                "merchant_id": ZARINPAL_MERCHANT_ID,
                "amount": amount_rials,
                "description": description,
                "callback_url": ZARINPAL_CALLBACK_URL
            }
            
            if mobile:
                data["metadata"] = {"mobile": mobile}
                
            if email:
                if "metadata" not in data:
                    data["metadata"] = {}
                data["metadata"]["email"] = email
            
            # Determine API endpoint based on sandbox mode
            if ZARINPAL_SANDBOX:
                url = "https://sandbox.zarinpal.com/pg/rest/WebGate/PaymentRequest.json"
            else:
                url = "https://api.zarinpal.com/pg/v4/payment/request.json"
            
            # Send request to Zarinpal
            response = requests.post(url, json=data)
            result = response.json()
            
            # Check if request was successful
            if result.get("status") == 100:
                authority = result.get("authority", "")
                
                # Build payment URL
                if ZARINPAL_SANDBOX:
                    payment_url = f"https://sandbox.zarinpal.com/pg/StartPay/{authority}"
                else:
                    payment_url = f"https://www.zarinpal.com/pg/StartPay/{authority}"
                
                return True, "Payment request successful", {
                    "authority": authority,
                    "payment_url": payment_url
                }
            else:
                error_code = result.get("status", "unknown")
                error_message = result.get("error", {}).get("message", "Unknown error")
                logger.error(f"Zarinpal payment request failed: {error_code} - {error_message}")
                return False, f"Payment request failed: {error_message}", {}
                
        except Exception as e:
            logger.error(f"Error in Zarinpal payment request: {str(e)}")
            return False, f"Payment request failed: {str(e)}", {}
    
    @staticmethod
    async def verify_payment(authority: str, amount: int) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Verify a payment with Zarinpal
        
        Args:
            authority: Payment authority from request_payment
            amount: Payment amount in Tomans (will be multiplied by 10 for Rials)
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Convert Tomans to Rials
            amount_rials = amount * 10
            
            # Prepare verification data
            data = {
                "merchant_id": ZARINPAL_MERCHANT_ID,
                "authority": authority,
                "amount": amount_rials
            }
            
            # Determine API endpoint based on sandbox mode
            if ZARINPAL_SANDBOX:
                url = "https://sandbox.zarinpal.com/pg/rest/WebGate/PaymentVerification.json"
            else:
                url = "https://api.zarinpal.com/pg/v4/payment/verify.json"
            
            # Send verification request
            response = requests.post(url, json=data)
            result = response.json()
            
            # Check verification result
            if result.get("status") == 100:
                ref_id = result.get("ref_id", "")
                return True, "Payment verified successfully", {
                    "ref_id": ref_id,
                    "card_pan": result.get("card_pan", ""),
                    "card_hash": result.get("card_hash", "")
                }
            else:
                error_code = result.get("status", "unknown")
                error_message = result.get("error", {}).get("message", "Unknown error")
                logger.error(f"Zarinpal payment verification failed: {error_code} - {error_message}")
                return False, f"Payment verification failed: {error_message}", {}
                
        except Exception as e:
            logger.error(f"Error in Zarinpal payment verification: {str(e)}")
            return False, f"Payment verification failed: {str(e)}", {}