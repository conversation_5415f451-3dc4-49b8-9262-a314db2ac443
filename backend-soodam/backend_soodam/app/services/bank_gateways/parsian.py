import logging
import requests
import zeep
from typing import Dict, Any, Optional, <PERSON><PERSON>

from app.core.config import PARSIAN_PIN, PARSIAN_CALLBACK_URL

logger = logging.getLogger(__name__)

class ParsianGateway:
    """Parsian Bank payment gateway implementation"""
    
    SALE_URL = "https://pec.shaparak.ir/NewIPGServices/Sale/SaleService.asmx?WSDL"
    CONFIRM_URL = "https://pec.shaparak.ir/NewIPGServices/Confirm/ConfirmService.asmx?WSDL"
    
    @staticmethod
    async def request_payment(
        amount: int,
        order_id: str,
        additional_data: Optional[str] = None
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Request a payment from Parsian Bank
        
        Args:
            amount: Payment amount in Rials
            order_id: Unique order ID
            additional_data: Additional data (optional)
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Create SOAP client
            client = zeep.Client(wsdl=ParsianGateway.SALE_URL)
            
            # Prepare request parameters
            params = {
                'LoginAccount': PARSIAN_PIN,
                'Amount': amount,
                'OrderId': order_id,
                'CallBackUrl': PARSIAN_CALLBACK_URL,
                'AdditionalData': additional_data or ''
            }
            
            # Call the SOAP service
            result = client.service.SalePaymentRequest(params)
            
            # Check if request was successful
            if result.Status == 0:  # 0 means success
                token = result.Token
                payment_url = f"https://pec.shaparak.ir/NewIPG/?Token={token}"
                
                return True, "Payment request successful", {
                    "token": token,
                    "payment_url": payment_url
                }
            else:
                error_message = ParsianGateway._get_error_message(result.Status)
                logger.error(f"Parsian payment request failed: {result.Status} - {error_message}")
                
                return False, f"Payment request failed: {error_message}", {
                    "error_code": result.Status
                }
                
        except Exception as e:
            logger.error(f"Error in Parsian payment request: {str(e)}")
            return False, f"Payment request failed: {str(e)}", {}
    
    @staticmethod
    async def verify_payment(
        token: str,
        order_id: str
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Verify a payment with Parsian Bank
        
        Args:
            token: Token from callback
            order_id: Order ID from request_payment
            
        Returns:
            Tuple of (success, message, data)
        """
        try:
            # Create SOAP client
            client = zeep.Client(wsdl=ParsianGateway.CONFIRM_URL)
            
            # Prepare verification parameters
            params = {
                'LoginAccount': PARSIAN_PIN,
                'Token': token
            }
            
            # Call the SOAP service
            result = client.service.ConfirmPayment(params)
            
            # Check if verification was successful
            if result.Status == 0:  # 0 means success
                return True, "Payment verified successfully", {
                    "reference_number": result.RRN,
                    "card_number": result.CardNumberMasked,
                    "trace_number": result.TraceNumber
                }
            else:
                error_message = ParsianGateway._get_error_message(result.Status)
                logger.error(f"Parsian payment verification failed: {result.Status} - {error_message}")
                
                return False, f"Payment verification failed: {error_message}", {
                    "error_code": result.Status
                }
                
        except Exception as e:
            logger.error(f"Error in Parsian payment verification: {str(e)}")
            return False, f"Payment verification failed: {str(e)}", {}
    
    @staticmethod
    def _get_error_message(status_code: int) -> str:
        """Get error message for Parsian status code"""
        error_messages = {
            -32768: "خطای ناشناخته رخ داده است",
            -1552: "برگشت تراکنش مجاز نمی باشد",
            -1551: "برگشت تراکنش قبلاً انجام شده است",
            -1550: "برگشت تراکنش در وضعیت جاری امکان پذیر نمی باشد",
            -1549: "زمان مجاز برای درخواست برگشت تراکنش به اتمام رسیده است",
            -1548: "فراخوانی سرویس درخواست پرداخت قبض ناموفق بود",
            -1540: "تایید تراکنش ناموفق می باشد",
            -1536: "فراخوانی سرویس درخواست شارژ تاپ آپ ناموفق بود",
            -1533: "تراکنش قبلاً تایید شده است",
            -1532: "تراکنش از سوی پذیرنده تایید شد",
            -1531: "تراکنش به دلیل انصراف شما کاربر گرامی ناموفق بود",
            -1530: "پذیرنده مجاز به دیدن سوابق این تراکنش نمی باشد",
            -1528: "شماره سفارش تراکنش موجود نمی باشد",
            -1527: "اطلاعات پرداخت یافت نشد",
            -1507: "تراکنش برگشت به سوئیچ ارسال شد",
            -1505: "تایید تراکنش توسط پذیرنده انجام شد",
            -138: "عملیات پرداخت توسط کاربر لغو شد",
            -132: "مبلغ تراکنش کمتر از حداقل مجاز می باشد",
            -131: "Token نامعتبر می باشد",
            -130: "Token زمان منقضی شده است",
            -128: "قالب آدرس IP معتبر نمی باشد",
            -127: "آدرس اینترنتی معتبر نمی باشد",
            -126: "کد شناسایی پذیرنده معتبر نمی باشد",
            -124: "مبلغ تراکنش بیش از حد مجاز می باشد",
            -123: "مبلغ تراکنش معتبر نمی باشد",
            -122: "مبلغ تراکنش معتبر نمی باشد",
            -121: "شماره سفارش تکراری است",
            -120: "شماره سفارش معتبر نمی باشد",
            -119: "زمان Token منقضی شده است",
            -118: "اطلاعات پذیرنده صحیح نمی باشد",
            -117: "نوع پرداخت معتبر نمی باشد",
            -116: "پذیرنده غیرفعال می باشد",
            -115: "زمان جلسه کاری به پایان رسیده است",
            -114: "شناسه قبض نادرست است",
            -113: "شناسه پرداخت نادرست است",
            -112: "صادرکننده قبض نامعتبر است",
            -111: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -108: "پذیرنده مجاز به انجام تراکنش با این کارت نمی باشد",
            -107: "پذیرنده مجاز به انجام تراکنش با این کارت نمی باشد",
            -106: "سامانه موقتا غیرفعال می باشد",
            -105: "کارت استفاده شده نامعتبر می باشد",
            -104: "سیستم بانکی پاسخگو نمی باشد",
            -103: "کارت استفاده شده نامعتبر می باشد",
            -102: "سیستم بانکی پاسخگو نمی باشد",
            -101: "تراکنش با موفقیت انجام شد",
            -100: "پرداخت تایید نشد",
            -99: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -98: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -97: "تراکنش تکراری است یا قبلاً انجام شده است",
            -96: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -95: "تراکنش ناموفق می باشد",
            -94: "تراکنش ناموفق می باشد",
            -93: "تراکنش ناموفق می باشد",
            -92: "یافت نشد",
            -91: "تراکنش ناموفق می باشد",
            -90: "تراکنش ناموفق می باشد",
            -89: "تراکنش ناموفق می باشد",
            -88: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -87: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -86: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -85: "تراکنش ناموفق می باشد",
            -84: "تراکنش ناموفق می باشد",
            -83: "تراکنش ناموفق می باشد",
            -82: "تراکنش ناموفق می باشد",
            -81: "خطای سامانه رخ داده است، لطفا با پشتیبانی تماس بگیرید",
            -80: "تراکنش ناموفق می باشد",
            -79: "مبلغ سند برگشتی، از مبلغ تراکنش اصلی بیشتر است",
            -78: "تراکنش ناموفق می باشد",
            -77: "تراکنش ناموفق می باشد",
            -76: "تراکنش ناموفق می باشد",
            -75: "تراکنش ناموفق می باشد",
            -74: "تراکنش ناموفق می باشد",
            -73: "تراکنش ناموفق می باشد",
            -72: "تراکنش ناموفق می باشد",
            -71: "تراکنش ناموفق می باشد",
            -70: "تراکنش ناموفق می باشد",
            -69: "تراکنش ناموفق می باشد",
            -68: "تراکنش ناموفق می باشد",
            -67: "تراکنش ناموفق می باشد",
            -66: "تراکنش ناموفق می باشد",
            -65: "تراکنش ناموفق می باشد",
            -64: "تراکنش ناموفق می باشد",
            -63: "تراکنش ناموفق می باشد",
            -62: "تراکنش ناموفق می باشد",
            -61: "تراکنش ناموفق می باشد",
            -60: "تراکنش ناموفق می باشد",
            -59: "تراکنش ناموفق می باشد",
            -58: "تراکنش ناموفق می باشد",
            -57: "تراکنش ناموفق می باشد",
            -56: "کارت نامعتبر است",
            -55: "رمز کارت نامعتبر است",
            -54: "تاریخ انقضای کارت گذشته است",
            -53: "تراکنش ناموفق می باشد",
            -52: "کارت شما حساب کافی ندارد",
            -51: "موجودی حساب کافی نمی باشد",
            -50: "تراکنش ناموفق می باشد",
            -49: "تراکنش ناموفق می باشد",
            -48: "تراکنش ناموفق می باشد",
            -47: "تراکنش ناموفق می باشد",
            -46: "تراکنش ناموفق می باشد",
            -45: "تراکنش ناموفق می باشد",
            -44: "تراکنش ناموفق می باشد",
            -43: "تراکنش ناموفق می باشد",
            -42: "کارت حساب شده است",
            -41: "کارت مفقودی می باشد",
            -40: "عملیات ناموفق می باشد",
            -39: "حساب نامعتبر است",
            -38: "تراکنش ناموفق می باشد",
            -37: "تراکنش ناموفق می باشد",
            -36: "تراکنش ناموفق می باشد",
            -35: "تراکنش ناموفق می باشد",
            -34: "تراکنش ناموفق می باشد",
            -33: "تاریخ انقضای کارت سپری شده است",
            -32: "تراکنش ناموفق می باشد",
            -31: "تراکنش ناموفق می باشد",
            -30: "فرمت اطلاعات وارد شده صحیح نمی باشد",
            -29: "تراکنش ناموفق می باشد",
            -28: "تراکنش ناموفق می باشد",
            -27: "تراکنش ناموفق می باشد",
            -26: "تراکنش ناموفق می باشد",
            -25: "تراکنش ناموفق می باشد",
            -24: "تراکنش ناموفق می باشد",
            -23: "تراکنش ناموفق می باشد",
            -22: "تراکنش ناموفق می باشد",
            -21: "پذیرنده نامعتبر است",
            -20: "تراکنش ناموفق می باشد",
            -19: "مبلغ برداشت وجه بیش از حد مجاز است",
            -18: "تراکنش ناموفق می باشد",
            -17: "تراکنش ناموفق می باشد",
            -16: "دفعات برداشت وجه بیش از حد مجاز است",
            -15: "تراکنش ناموفق می باشد",
            -14: "شماره کارت ارسالی نامعتبر است",
            -13: "تراکنش ناموفق می باشد",
            -12: "تراکنش نامعتبر است",
            -11: "تراکنش ناموفق می باشد",
            -10: "تراکنش ناموفق می باشد",
            -9: "تراکنش ناموفق می باشد",
            -8: "تراکنش ناموفق می باشد",
            -7: "رمز کارت 3 مرتبه اشتباه وارد شده است در صورت داشتن رمز دوم مجدد تلاش کنید",
            -6: "تراکنش ناموفق می باشد",
            -5: "تراکنش ناموفق می باشد",
            -4: "تراکنش ناموفق می باشد",
            -3: "تراکنش ناموفق می باشد",
            -2: "تراکنش ناموفق می باشد",
            -1: "تراکنش ناموفق می باشد",
            0: "تراکنش با موفقیت انجام شد",
            1: "صادرکننده کارت از انجام تراکنش صرف نظر کرد",
            2: "عملیات تاییدیه این تراکنش قبلا با موفقیت صورت پذیرفته است",
            3: "پذیرنده فروشگاهی نامعتبر می باشد",
            5: "از انجام تراکنش صرف نظر شد",
            6: "بروز خطا",
            8: "بروز خطا",
            9: "بروز خطا",
            12: "تراکنش نامعتبر است",
            13: "مبلغ تراکنش نادرست است",
            14: "شماره کارت ارسالی نامعتبر است",
            15
