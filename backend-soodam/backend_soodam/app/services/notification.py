"""
Notification service for the <PERSON>dam backend.

This module provides utilities for sending notifications to users.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Set, Union

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from ..core.websocket import WebSocketMessage, manager
from ..models import CustomUserModel
from ..models.notification import (
    NotificationDeviceModel,
    NotificationModel,
    NotificationPreferenceModel,
    NotificationType,
)

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Notification service.
    
    This class provides utilities for sending notifications to users.
    """
    
    @staticmethod
    async def create_notification(
        user: CustomUserModel,
        notification_type: str,
        title: str,
        message: str,
        data: Optional[Dict] = None,
    ) -> NotificationModel:
        """
        Create a notification for a user.
        
        Args:
            user: The user to notify
            notification_type: The notification type
            title: The notification title
            message: The notification message
            data: Additional data for the notification
            
        Returns:
            NotificationModel: The created notification
        """
        # Create the notification
        notification = await NotificationModel.objects.acreate(
            user=user,
            type=notification_type,
            title=title,
            message=message,
            data=data or {}
        )
        
        # Send the notification
        await NotificationService.send_notification(notification)
        
        return notification
    
    @staticmethod
    async def send_notification(notification: NotificationModel) -> None:
        """
        Send a notification to a user.
        
        Args:
            notification: The notification to send
        """
        # Get the user's notification preferences
        preferences = await NotificationPreferenceModel.objects.filter(
            user=notification.user
        ).afirst()
        
        # If no preferences, create default preferences
        if not preferences:
            preferences = await NotificationPreferenceModel.objects.acreate(
                user=notification.user
            )
        
        # Check if the notification type is enabled
        notification_type_field = notification.type.lower()
        if hasattr(preferences, notification_type_field) and not getattr(preferences, notification_type_field):
            logger.info(f"Notification type {notification.type} is disabled for user {notification.user.email}")
            return
        
        # Send WebSocket notification
        await NotificationService.send_websocket_notification(notification)
        
        # Send email notification
        if preferences.email_enabled:
            await NotificationService.send_email_notification(notification)
        
        # Send push notification
        if preferences.push_enabled:
            await NotificationService.send_push_notification(notification)
        
        # Send SMS notification
        if preferences.sms_enabled:
            await NotificationService.send_sms_notification(notification)
    
    @staticmethod
    async def send_websocket_notification(notification: NotificationModel) -> None:
        """
        Send a WebSocket notification to a user.
        
        Args:
            notification: The notification to send
        """
        try:
            # Create the WebSocket message
            message = WebSocketMessage(
                type="notification",
                data={
                    "id": notification.id,
                    "type": notification.type,
                    "title": notification.title,
                    "message": notification.message,
                    "data": notification.data,
                    "created_at": notification.created_at.isoformat(),
                },
                channel=f"user:{notification.user.id}:notifications"
            )
            
            # Send the message to the user
            await manager.broadcast_to_user(message, notification.user.id)
            
            # Send the message to the notifications channel
            await manager.broadcast_to_channel(message, f"user:{notification.user.id}:notifications")
            
            logger.info(f"WebSocket notification sent to user {notification.user.email}")
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")
    
    @staticmethod
    async def send_email_notification(notification: NotificationModel) -> None:
        """
        Send an email notification to a user.
        
        Args:
            notification: The notification to send
        """
        try:
            # Render the email template
            html_message = render_to_string(
                "emails/notification.html",
                {
                    "notification": notification,
                    "user": notification.user,
                }
            )
            
            # Send the email
            send_mail(
                subject=notification.title,
                message=notification.message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notification.user.email],
                html_message=html_message,
                fail_silently=False,
            )
            
            logger.info(f"Email notification sent to user {notification.user.email}")
        except Exception as e:
            logger.error(f"Error sending email notification: {str(e)}")
    
    @staticmethod
    async def send_push_notification(notification: NotificationModel) -> None:
        """
        Send a push notification to a user.
        
        Args:
            notification: The notification to send
        """
        try:
            # Get the user's devices
            devices = await NotificationDeviceModel.objects.filter(
                user=notification.user,
                is_active=True
            ).values_list("device_token", "device_type")
            
            if not devices:
                logger.info(f"No active devices found for user {notification.user.email}")
                return
            
            # Send push notification to each device
            for device_token, device_type in devices:
                # TODO: Implement push notification sending
                # This would typically involve using a service like Firebase Cloud Messaging (FCM)
                # or Apple Push Notification Service (APNS)
                logger.info(f"Push notification sent to device {device_token} of type {device_type}")
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
    
    @staticmethod
    async def send_sms_notification(notification: NotificationModel) -> None:
        """
        Send an SMS notification to a user.
        
        Args:
            notification: The notification to send
        """
        try:
            # Get the user's phone number
            phone_number = notification.user.phone_number
            
            if not phone_number:
                logger.info(f"No phone number found for user {notification.user.email}")
                return
            
            # TODO: Implement SMS notification sending
            # This would typically involve using a service like Twilio or a local SMS gateway
            logger.info(f"SMS notification sent to phone number {phone_number}")
        except Exception as e:
            logger.error(f"Error sending SMS notification: {str(e)}")
    
    @staticmethod
    async def mark_notification_as_read(notification_id: int, user_id: int) -> bool:
        """
        Mark a notification as read.
        
        Args:
            notification_id: The notification ID
            user_id: The user ID
            
        Returns:
            bool: True if the notification was marked as read, False otherwise
        """
        try:
            # Get the notification
            notification = await NotificationModel.objects.filter(
                id=notification_id,
                user_id=user_id
            ).afirst()
            
            if not notification:
                logger.warning(f"Notification {notification_id} not found for user {user_id}")
                return False
            
            # Mark as read
            notification.is_read = True
            await notification.asave()
            
            logger.info(f"Notification {notification_id} marked as read for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            return False
    
    @staticmethod
    async def mark_all_notifications_as_read(user_id: int) -> int:
        """
        Mark all notifications as read for a user.
        
        Args:
            user_id: The user ID
            
        Returns:
            int: The number of notifications marked as read
        """
        try:
            # Get unread notifications
            unread_notifications = await NotificationModel.objects.filter(
                user_id=user_id,
                is_read=False
            )
            
            # Mark all as read
            count = await unread_notifications.aupdate(is_read=True)
            
            logger.info(f"{count} notifications marked as read for user {user_id}")
            return count
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {str(e)}")
            return 0
    
    @staticmethod
    async def delete_notification(notification_id: int, user_id: int) -> bool:
        """
        Delete a notification.
        
        Args:
            notification_id: The notification ID
            user_id: The user ID
            
        Returns:
            bool: True if the notification was deleted, False otherwise
        """
        try:
            # Get the notification
            notification = await NotificationModel.objects.filter(
                id=notification_id,
                user_id=user_id
            ).afirst()
            
            if not notification:
                logger.warning(f"Notification {notification_id} not found for user {user_id}")
                return False
            
            # Delete the notification
            await notification.adelete()
            
            logger.info(f"Notification {notification_id} deleted for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting notification: {str(e)}")
            return False
    
    @staticmethod
    async def delete_all_notifications(user_id: int) -> int:
        """
        Delete all notifications for a user.
        
        Args:
            user_id: The user ID
            
        Returns:
            int: The number of notifications deleted
        """
        try:
            # Get notifications
            notifications = await NotificationModel.objects.filter(
                user_id=user_id
            )
            
            # Delete all
            count, _ = await notifications.adelete()
            
            logger.info(f"{count} notifications deleted for user {user_id}")
            return count
        except Exception as e:
            logger.error(f"Error deleting all notifications: {str(e)}")
            return 0
