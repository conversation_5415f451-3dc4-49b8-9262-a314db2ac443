"""
Analytics service for the Soodam backend.

This module provides utilities for logging and analytics.
"""

import json
import logging
import time
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Union

from django.db.models import Avg, Count, F, Q, Sum
from fastapi import Request, Response

from ..models import CustomUserModel
from ..models.advertisement import (
    AdvertisementModel,
    MainCategoryModel as AdvertisementCategoryModel,
    AdvertisementImagesModel as AdvertisementImageModel,
    PropertyTextValueModel as AdvertisementAttributeModel,
    AdvertisementViewModel as AdvertisementStatisticsModel,
    AdvertisementTagModel,
    AdvertisementFavoriteModel,
    AdvertisementLocationModel,
)
from ..models.analytics import (
    APIRequestLogModel,
    CategoryStatisticsModel,
    DailyStatisticsModel,
    LocationStatisticsModel,
    SearchQueryLogModel,
    UserActivityLogModel,
)
from ..models.chat import ChatMessageModel

logger = logging.getLogger(__name__)


class AnalyticsService:
    """
    Analytics service.
    
    This class provides utilities for logging and analytics.
    """
    
    @staticmethod
    async def log_api_request(
        request: Request,
        response: Response,
        response_time: float,
        user: Optional[CustomUserModel] = None,
    ) -> APIRequestLogModel:
        """
        Log an API request.
        
        Args:
            request: The request
            response: The response
            response_time: The response time in milliseconds
            user: The user (optional)
            
        Returns:
            APIRequestLogModel: The created log
        """
        try:
            # Get request data
            request_data = None
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = await request.body()
                    if body:
                        request_data = json.loads(body)
                except Exception:
                    pass
            
            # Create the log
            log = await APIRequestLogModel.objects.acreate(
                endpoint=str(request.url.path),
                method=request.method,
                status_code=response.status_code,
                response_time=response_time,
                user=user,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get('user-agent'),
                request_data=request_data
            )
            
            return log
        except Exception as e:
            logger.error(f"Error logging API request: {str(e)}")
            return None
    
    @staticmethod
    async def log_user_activity(
        user: CustomUserModel,
        action_type: str,
        advertisement_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict] = None,
    ) -> UserActivityLogModel:
        """
        Log a user activity.
        
        Args:
            user: The user
            action_type: The action type
            advertisement_id: The advertisement ID (optional)
            ip_address: The IP address (optional)
            user_agent: The user agent (optional)
            details: Additional details (optional)
            
        Returns:
            UserActivityLogModel: The created log
        """
        try:
            # Get the advertisement if provided
            advertisement = None
            if advertisement_id:
                advertisement = await AdvertisementModel.objects.filter(id=advertisement_id).afirst()
            
            # Create the log
            log = await UserActivityLogModel.objects.acreate(
                user=user,
                action_type=action_type,
                advertisement=advertisement,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details
            )
            
            return log
        except Exception as e:
            logger.error(f"Error logging user activity: {str(e)}")
            return None
    
    @staticmethod
    async def log_search_query(
        query: str,
        user: Optional[CustomUserModel] = None,
        category_id: Optional[int] = None,
        location_id: Optional[int] = None,
        filters: Optional[Dict] = None,
        results_count: int = 0,
        ip_address: Optional[str] = None,
    ) -> SearchQueryLogModel:
        """
        Log a search query.
        
        Args:
            query: The search query
            user: The user (optional)
            category_id: The category ID (optional)
            location_id: The location ID (optional)
            filters: Additional filters (optional)
            results_count: The number of results
            ip_address: The IP address (optional)
            
        Returns:
            SearchQueryLogModel: The created log
        """
        try:
            # Create the log
            log = await SearchQueryLogModel.objects.acreate(
                query=query,
                user=user,
                category_id=category_id,
                location_id=location_id,
                filters=filters,
                results_count=results_count,
                ip_address=ip_address
            )
            
            return log
        except Exception as e:
            logger.error(f"Error logging search query: {str(e)}")
            return None
    
    @staticmethod
    async def generate_daily_statistics(
        target_date: Optional[date] = None,
    ) -> DailyStatisticsModel:
        """
        Generate daily statistics.
        
        Args:
            target_date: The target date (defaults to yesterday)
            
        Returns:
            DailyStatisticsModel: The created statistics
        """
        try:
            # Set the target date to yesterday if not provided
            if target_date is None:
                target_date = date.today() - timedelta(days=1)
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            # Count new users
            new_users = await CustomUserModel.objects.filter(
                date_joined__gte=start_datetime,
                date_joined__lte=end_datetime
            ).acount()
            
            # Count active users
            active_users = await CustomUserModel.objects.filter(
                last_login__gte=start_datetime,
                last_login__lte=end_datetime
            ).acount()
            
            # Count new advertisements
            new_advertisements = await AdvertisementModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).acount()
            
            # Count total views
            total_views = await AdvertisementViewModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).acount()
            
            # Count total searches
            total_searches = await SearchQueryLogModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).acount()
            
            # Count total favorites
            total_favorites = await AdvertisementFavoriteModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).acount()
            
            # Count total messages
            total_messages = await ChatMessageModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).acount()
            
            # Count total API requests
            api_requests = await APIRequestLogModel.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            )
            
            total_api_requests = await api_requests.acount()
            
            # Calculate average response time
            average_response_time = 0
            if total_api_requests > 0:
                avg_response_time = await api_requests.aggregate(avg=Avg('response_time'))
                average_response_time = avg_response_time['avg'] if avg_response_time['avg'] else 0
            
            # Create or update the statistics
            stats, created = await DailyStatisticsModel.objects.aupdate_or_create(
                date=target_date,
                defaults={
                    'new_users': new_users,
                    'active_users': active_users,
                    'new_advertisements': new_advertisements,
                    'total_views': total_views,
                    'total_searches': total_searches,
                    'total_favorites': total_favorites,
                    'total_messages': total_messages,
                    'total_api_requests': total_api_requests,
                    'average_response_time': average_response_time
                }
            )
            
            return stats
        except Exception as e:
            logger.error(f"Error generating daily statistics: {str(e)}")
            return None
    
    @staticmethod
    async def generate_category_statistics(
        target_date: Optional[date] = None,
    ) -> List[CategoryStatisticsModel]:
        """
        Generate category statistics.
        
        Args:
            target_date: The target date (defaults to yesterday)
            
        Returns:
            List[CategoryStatisticsModel]: The created statistics
        """
        try:
            # Set the target date to yesterday if not provided
            if target_date is None:
                target_date = date.today() - timedelta(days=1)
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            # Get all categories
            categories = await AdvertisementCategoryModel.objects.all()
            
            results = []
            for category in categories:
                # Count advertisements in this category
                advertisements_count = await AdvertisementModel.objects.filter(
                    category=category,
                    created_at__lte=end_datetime
                ).acount()
                
                # Count views for advertisements in this category
                views_count = await AdvertisementViewModel.objects.filter(
                    advertisement__category=category,
                    created_at__gte=start_datetime,
                    created_at__lte=end_datetime
                ).acount()
                
                # Count searches for this category
                searches_count = await SearchQueryLogModel.objects.filter(
                    category_id=category.id,
                    created_at__gte=start_datetime,
                    created_at__lte=end_datetime
                ).acount()
                
                # Count favorites for advertisements in this category
                favorites_count = await AdvertisementFavoriteModel.objects.filter(
                    advertisement__category=category,
                    created_at__gte=start_datetime,
                    created_at__lte=end_datetime
                ).acount()
                
                # Create or update the statistics
                stats, created = await CategoryStatisticsModel.objects.aupdate_or_create(
                    category_id=category.id,
                    date=target_date,
                    defaults={
                        'category_name': category.name,
                        'advertisements_count': advertisements_count,
                        'views_count': views_count,
                        'searches_count': searches_count,
                        'favorites_count': favorites_count
                    }
                )
                
                results.append(stats)
            
            return results
        except Exception as e:
            logger.error(f"Error generating category statistics: {str(e)}")
            return []
    
    @staticmethod
    async def generate_location_statistics(
        target_date: Optional[date] = None,
    ) -> List[LocationStatisticsModel]:
        """
        Generate location statistics.
        
        Args:
            target_date: The target date (defaults to yesterday)
            
        Returns:
            List[LocationStatisticsModel]: The created statistics
        """
        try:
            # Set the target date to yesterday if not provided
            if target_date is None:
                target_date = date.today() - timedelta(days=1)
            
            # Convert to datetime for filtering
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            # Get all locations
            locations = await AdvertisementLocationModel.objects.all()
            
            results = []
            for location in locations:
                # Count advertisements in this location
                advertisements_count = await AdvertisementModel.objects.filter(
                    location=location,
                    created_at__lte=end_datetime
                ).acount()
                
                # Count views for advertisements in this location
                views_count = await AdvertisementViewModel.objects.filter(
                    advertisement__location=location,
                    created_at__gte=start_datetime,
                    created_at__lte=end_datetime
                ).acount()
                
                # Count searches for this location
                searches_count = await SearchQueryLogModel.objects.filter(
                    location_id=location.id,
                    created_at__gte=start_datetime,
                    created_at__lte=end_datetime
                ).acount()
                
                # Create or update the statistics
                stats, created = await LocationStatisticsModel.objects.aupdate_or_create(
                    location_id=location.id,
                    date=target_date,
                    defaults={
                        'city': location.city,
                        'state': location.state,
                        'advertisements_count': advertisements_count,
                        'views_count': views_count,
                        'searches_count': searches_count
                    }
                )
                
                results.append(stats)
            
            return results
        except Exception as e:
            logger.error(f"Error generating location statistics: {str(e)}")
            return []
