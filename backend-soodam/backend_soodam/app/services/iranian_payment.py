"""
Iranian Bank Payment Gateway Service

This module provides a custom implementation for Iranian bank payment gateways
without relying on external libraries like az-iranian-bank-gateways.

Supported banks:
- Zarinpal
- <PERSON>lat
- Sam<PERSON>
- Parsian
"""

import uuid
import logging
import json
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional, Tuple
import httpx
from datetime import datetime

from app.core.payment_settings import payment_settings as settings

logger = logging.getLogger(__name__)

class IranianBankGateway:
    """Base class for Iranian bank payment gateways"""

    def __init__(self, merchant_id: str, callback_url: str):
        self.merchant_id = merchant_id
        self.callback_url = callback_url

    async def request_payment(self, amount: float, description: str, mobile: Optional[str] = None) -> Dict[str, Any]:
        """Request a payment from the bank gateway"""
        raise NotImplementedError("Subclasses must implement this method")

    async def verify_payment(self, tracking_code: str, reference_id: Optional[str] = None) -> Dict[str, Any]:
        """Verify a payment with the bank gateway"""
        raise NotImplementedError("Subclasses must implement this method")


class ZarinpalGateway(IranianBankGateway):
    """Implementation for Zarinpal payment gateway"""

    SANDBOX_MODE = settings.PAYMENT_SANDBOX_MODE

    def __init__(self, merchant_id: str, callback_url: str):
        super().__init__(merchant_id, callback_url)
        self.base_url = "https://sandbox.zarinpal.com/pg/rest/WebGate" if self.SANDBOX_MODE else "https://api.zarinpal.com/pg/rest/WebGate"
        self.payment_url = "https://sandbox.zarinpal.com/pg/StartPay" if self.SANDBOX_MODE else "https://www.zarinpal.com/pg/StartPay"

    async def request_payment(self, amount: float, description: str, mobile: Optional[str] = None) -> Dict[str, Any]:
        """Request a payment from Zarinpal"""
        try:
            # Convert amount from Tomans to Rials (Zarinpal works with Rials)
            amount_rials = int(amount * 10)

            # Prepare request data
            request_data = {
                "MerchantID": self.merchant_id,
                "Amount": amount_rials,
                "Description": description,
                "CallbackURL": self.callback_url,
            }

            if mobile:
                request_data["Mobile"] = mobile

            # Send request to Zarinpal
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/PaymentRequest.json",
                    json=request_data,
                    timeout=10.0
                )

            result = response.json()

            if result.get("Status") == 100:
                authority = result.get("Authority", "")
                tracking_code = authority

                return {
                    "success": True,
                    "redirect_url": f"{self.payment_url}/{authority}",
                    "tracking_code": tracking_code,
                    "reference_number": str(uuid.uuid4().hex)[:11]
                }
            else:
                logger.error(f"Zarinpal payment request failed: {result}")
                return {
                    "success": False,
                    "error_code": result.get("Status", -1),
                    "error_message": self._get_error_message(result.get("Status", -1))
                }

        except Exception as e:
            logger.error(f"Error in Zarinpal payment request: {str(e)}")
            return {
                "success": False,
                "error_code": -1,
                "error_message": f"خطا در ارتباط با درگاه زرین‌پال: {str(e)}"
            }

    async def verify_payment(self, tracking_code: str, reference_id: Optional[str] = None) -> Dict[str, Any]:
        """Verify a payment with Zarinpal"""
        try:
            # Prepare verification data
            verify_data = {
                "MerchantID": self.merchant_id,
                "Authority": tracking_code,
                "Amount": reference_id  # In Zarinpal, we need to pass the amount for verification
            }

            # Send verification request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/PaymentVerification.json",
                    json=verify_data,
                    timeout=10.0
                )

            result = response.json()

            if result.get("Status") == 100:
                return {
                    "success": True,
                    "reference_id": result.get("RefID", ""),
                    "message": "پرداخت با موفقیت انجام شد"
                }
            else:
                logger.error(f"Zarinpal payment verification failed: {result}")
                return {
                    "success": False,
                    "error_code": result.get("Status", -1),
                    "error_message": self._get_error_message(result.get("Status", -1))
                }

        except Exception as e:
            logger.error(f"Error in Zarinpal payment verification: {str(e)}")
            return {
                "success": False,
                "error_code": -1,
                "error_message": f"خطا در تایید پرداخت زرین‌پال: {str(e)}"
            }

    def _get_error_message(self, status_code: int) -> str:
        """Get error message for Zarinpal status code"""
        error_codes = {
            -1: "اطلاعات ارسال شده ناقص است",
            -2: "IP یا مرچنت کد پذیرنده صحیح نیست",
            -3: "با توجه به محدودیت‌های شاپرک، امکان پرداخت با رقم درخواست شده میسر نیست",
            -4: "سطح تایید پذیرنده پایین‌تر از سطح نقره‌ای است",
            -11: "درخواست مورد نظر یافت نشد",
            -12: "امکان ویرایش درخواست میسر نیست",
            -21: "هیچ نوع عملیات مالی برای این تراکنش یافت نشد",
            -22: "تراکنش ناموفق می‌باشد",
            -33: "رقم تراکنش با رقم پرداخت شده مطابقت ندارد",
            -34: "سقف تقسیم تراکنش از لحاظ تعداد یا رقم عبور نموده است",
            -40: "اجازه دسترسی به متد مربوطه وجود ندارد",
            -41: "اطلاعات ارسال شده مربوط به AdditionalData غیرمعتبر می‌باشد",
            -42: "مدت زمان معتبر طول عمر شناسه پرداخت باید بین ۳۰ دقیقه تا ۴۵ روز باشد",
            -54: "درخواست مورد نظر آرشیو شده است",
            100: "عملیات موفق",
            101: "عملیات پرداخت موفق بوده و قبلا تایید شده است"
        }
        return error_codes.get(status_code, "خطای ناشناخته")


class MellatGateway(IranianBankGateway):
    """Implementation for Mellat Bank payment gateway"""

    SANDBOX_MODE = settings.PAYMENT_SANDBOX_MODE

    def __init__(self, merchant_id: str, callback_url: str, terminal_id: str = None, username: str = None, password: str = None):
        super().__init__(merchant_id, callback_url)
        self.terminal_id = terminal_id or settings.MELLAT_TERMINAL_ID
        self.username = username or settings.MELLAT_USERNAME
        self.password = password or settings.MELLAT_PASSWORD
        self.base_url = "https://sandbox.banktest.ir/mellat" if self.SANDBOX_MODE else "https://bpm.shaparak.ir/pgwchannel/services/pgw"
        self.payment_url = "https://sandbox.banktest.ir/mellat/payment" if self.SANDBOX_MODE else "https://bpm.shaparak.ir/pgwchannel/startpay.mellat"

    async def request_payment(self, amount: float, description: str, mobile: Optional[str] = None) -> Dict[str, Any]:
        """Request a payment from Mellat Bank"""
        try:
            # Convert amount to Rials
            amount_rials = int(amount * 10)

            # Generate a unique order ID
            order_id = int(datetime.now().timestamp())

            # Prepare request data
            request_data = {
                "terminalId": self.terminal_id,
                "userName": self.username,
                "userPassword": self.password,
                "orderId": order_id,
                "amount": amount_rials,
                "localDate": datetime.now().strftime("%Y%m%d"),
                "localTime": datetime.now().strftime("%H%M%S"),
                "additionalData": description,
                "callBackUrl": self.callback_url,
                "payerId": 0
            }

            # Send request to Mellat
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}",
                    json=request_data,
                    timeout=10.0
                )

            result = response.json()

            if result.get("resCode") == "0":
                ref_id = result.get("refId", "")

                return {
                    "success": True,
                    "redirect_url": f"{self.payment_url}?RefId={ref_id}",
                    "tracking_code": str(order_id),
                    "reference_number": str(uuid.uuid4().hex)[:11]
                }
            else:
                logger.error(f"Mellat payment request failed: {result}")
                return {
                    "success": False,
                    "error_code": result.get("resCode", "-1"),
                    "error_message": self._get_error_message(result.get("resCode", "-1"))
                }

        except Exception as e:
            logger.error(f"Error in Mellat payment request: {str(e)}")
            return {
                "success": False,
                "error_code": -1,
                "error_message": f"خطا در ارتباط با درگاه بانک ملت: {str(e)}"
            }

    async def verify_payment(self, tracking_code: str, reference_id: Optional[str] = None) -> Dict[str, Any]:
        """Verify a payment with Mellat Bank"""
        try:
            # Prepare verification data
            verify_data = {
                "terminalId": self.terminal_id,
                "userName": self.username,
                "userPassword": self.password,
                "orderId": tracking_code,
                "saleOrderId": tracking_code,
                "saleReferenceId": reference_id
            }

            # Send verification request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/verifyRequest",
                    json=verify_data,
                    timeout=10.0
                )

            result = response.json()

            if result.get("resCode") == "0":
                return {
                    "success": True,
                    "reference_id": reference_id,
                    "message": "پرداخت با موفقیت انجام شد"
                }
            else:
                logger.error(f"Mellat payment verification failed: {result}")
                return {
                    "success": False,
                    "error_code": result.get("resCode", "-1"),
                    "error_message": self._get_error_message(result.get("resCode", "-1"))
                }

        except Exception as e:
            logger.error(f"Error in Mellat payment verification: {str(e)}")
            return {
                "success": False,
                "error_code": -1,
                "error_message": f"خطا در تایید پرداخت بانک ملت: {str(e)}"
            }

    def _get_error_message(self, status_code: str) -> str:
        """Get error message for Mellat status code"""
        error_codes = {
            "0": "تراکنش با موفقیت انجام شد",
            "11": "شماره کارت نامعتبر است",
            "12": "موجودی کافی نیست",
            "13": "رمز نادرست است",
            "14": "تعداد دفعات وارد کردن رمز بیش از حد مجاز است",
            "15": "کارت نامعتبر است",
            "16": "دفعات برداشت وجه بیش از حد مجاز است",
            "17": "کاربر از انجام تراکنش منصرف شده است",
            "18": "تاریخ انقضای کارت گذشته است",
            "19": "مبلغ برداشت وجه بیش از حد مجاز است",
            "111": "صادر کننده کارت نامعتبر است",
            "112": "خطای سوییچ صادر کننده کارت",
            "113": "پاسخی از صادر کننده کارت دریافت نشد",
            "114": "دارنده کارت مجاز به انجام این تراکنش نیست",
            "21": "پذیرنده نامعتبر است",
            "23": "خطای امنیتی رخ داده است",
            "24": "اطلاعات کاربری پذیرنده نامعتبر است",
            "25": "مبلغ نامعتبر است",
            "31": "پاسخ نامعتبر است",
            "32": "فرمت اطلاعات وارد شده صحیح نمی‌باشد",
            "33": "حساب نامعتبر است",
            "34": "خطای سیستمی",
            "35": "تاریخ نامعتبر است",
            "41": "شماره درخواست تکراری است",
            "42": "تراکنش Sale یافت نشد",
            "43": "قبلا درخواست Verify داده شده است",
            "44": "درخواست Verify یافت نشد",
            "45": "تراکنش Settle شده است",
            "46": "تراکنش Settle نشده است",
            "47": "تراکنش Settle یافت نشد",
            "48": "تراکنش Reverse شده است",
            "49": "تراکنش Refund یافت نشد",
            "412": "شناسه قبض نادرست است",
            "413": "شناسه پرداخت نادرست است",
            "414": "سازمان صادر کننده قبض نامعتبر است",
            "415": "زمان جلسه کاری به پایان رسیده است",
            "416": "خطا در ثبت اطلاعات",
            "417": "شناسه پرداخت کننده نامعتبر است",
            "418": "اشکال در تعریف اطلاعات مشتری",
            "419": "تعداد دفعات ورود اطلاعات از حد مجاز گذشته است",
            "421": "IP نامعتبر است",
            "51": "تراکنش تکراری است",
            "54": "تراکنش مرجع موجود نیست",
            "55": "تراکنش نامعتبر است",
            "61": "خطا در واریز"
        }
        return error_codes.get(status_code, "خطای ناشناخته")


class PaymentGatewayFactory:
    """Factory for creating payment gateway instances"""

    @staticmethod
    def create_gateway(bank_name: str, callback_url: Optional[str] = None) -> IranianBankGateway:
        """Create a payment gateway instance based on bank name"""
        bank_name = bank_name.lower()

        # Use default callback URL if not provided
        if not callback_url:
            callback_url = settings.PAYMENT_CALLBACK_URL

        if bank_name == "zarinpal":
            return ZarinpalGateway(
                merchant_id=settings.ZARINPAL_MERCHANT_ID,
                callback_url=callback_url
            )
        elif bank_name == "mellat":
            return MellatGateway(
                merchant_id=settings.MELLAT_MERCHANT_ID,
                callback_url=callback_url,
                terminal_id=settings.MELLAT_TERMINAL_ID,
                username=settings.MELLAT_USERNAME,
                password=settings.MELLAT_PASSWORD
            )
        elif bank_name == "saman":
            # Implement Saman gateway when needed
            raise NotImplementedError("Saman gateway not implemented yet")
        elif bank_name == "parsian":
            # Implement Parsian gateway when needed
            raise NotImplementedError("Parsian gateway not implemented yet")
        else:
            raise ValueError(f"Unsupported bank: {bank_name}")
