"""
Image service for the <PERSON><PERSON> backend.

This module provides utilities for handling image uploads and processing.
"""

import base64
import logging
import os
import tempfile
import time
import uuid
from io import BytesIO
from typing import Dict, List, Optional, Tuple, Union

import boto3
from django.conf import settings
from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile, status
from PIL import Image, ImageOps, ExifTags

logger = logging.getLogger(__name__)

# AWS S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
    aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
    region_name=os.environ.get('AWS_S3_REGION_NAME')
)

# S3 bucket name
S3_BUCKET = os.environ.get('AWS_STORAGE_BUCKET_NAME')

# Image sizes
IMAGE_SIZES = {
    'thumbnail': (150, 150),
    'small': (300, 300),
    'medium': (600, 600),
    'large': (1200, 1200)
}

# Allowed image formats
ALLOWED_FORMATS = ['JPEG', 'PNG', 'GIF', 'WEBP']

# Maximum image size (10 MB)
MAX_IMAGE_SIZE = 10 * 1024 * 1024


class ImageService:
    """
    Image service.
    
    This class provides utilities for handling image uploads and processing.
    """
    
    @staticmethod
    async def upload_image(
        file: UploadFile,
        folder: str = 'uploads/images',
        generate_sizes: bool = True,
    ) -> Dict:
        """
        Upload an image to S3.
        
        Args:
            file: The uploaded file
            folder: The folder to upload to
            generate_sizes: Whether to generate different sizes
            
        Returns:
            Dict: The upload result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Check file size
            file.file.seek(0, 2)
            file_size = file.file.tell()
            file.file.seek(0)
            
            if file_size > MAX_IMAGE_SIZE:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File size exceeds the maximum allowed size of {MAX_IMAGE_SIZE / 1024 / 1024} MB"
                )
            
            # Read the file
            contents = await file.read()
            
            # Check file format
            try:
                with Image.open(BytesIO(contents)) as img:
                    if img.format not in ALLOWED_FORMATS:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"File format {img.format} is not allowed. Allowed formats: {', '.join(ALLOWED_FORMATS)}"
                        )
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid image file: {str(e)}"
                )
            
            # Generate a unique filename
            filename = f"{uuid.uuid4()}.jpg"
            key = f"{folder}/{filename}"
            
            # Upload the file to S3
            s3_client.upload_fileobj(
                BytesIO(contents),
                S3_BUCKET,
                key,
                ExtraArgs={
                    'ContentType': file.content_type,
                    'ACL': 'public-read'
                }
            )
            
            # Generate the URL
            url = f"https://{S3_BUCKET}.s3.amazonaws.com/{key}"
            
            # Generate different sizes if requested
            sizes = {}
            if generate_sizes:
                sizes = await ImageService.generate_sizes(contents, folder, filename)
            
            # Return the result
            return {
                'key': key,
                'url': url,
                'filename': filename,
                'content_type': file.content_type,
                'size': file_size,
                'sizes': sizes
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading image: {str(e)}"
            )
    
    @staticmethod
    async def generate_sizes(
        contents: bytes,
        folder: str,
        filename: str,
    ) -> Dict:
        """
        Generate different sizes of an image.
        
        Args:
            contents: The image contents
            folder: The folder to upload to
            filename: The filename
            
        Returns:
            Dict: The generated sizes
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            sizes = {}
            
            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix='.jpg') as temp_file:
                temp_file.write(contents)
                temp_file.flush()
                
                # Open the image
                with Image.open(temp_file.name) as img:
                    # Fix orientation
                    img = ImageService.fix_orientation(img)
                    
                    # Get the original image format
                    format = img.format or 'JPEG'
                    
                    # Get the base filename (without extension)
                    base_filename = os.path.splitext(filename)[0]
                    
                    # Resize the image to multiple sizes
                    for size_name, dimensions in IMAGE_SIZES.items():
                        # Resize the image
                        resized_img = ImageOps.fit(img, dimensions, Image.LANCZOS)
                        
                        # Save the resized image to a BytesIO object
                        buffer = BytesIO()
                        resized_img.save(buffer, format=format, quality=85, optimize=True)
                        buffer.seek(0)
                        
                        # Generate the new filename and key
                        new_filename = f"{base_filename}_{size_name}.jpg"
                        new_key = f"{folder}/{new_filename}"
                        
                        # Upload the resized image to S3
                        s3_client.upload_fileobj(
                            buffer,
                            S3_BUCKET,
                            new_key,
                            ExtraArgs={
                                'ContentType': f'image/{format.lower()}',
                                'ACL': 'public-read'
                            }
                        )
                        
                        # Add the size to the result
                        sizes[size_name] = {
                            'key': new_key,
                            'url': f"https://{S3_BUCKET}.s3.amazonaws.com/{new_key}",
                            'width': dimensions[0],
                            'height': dimensions[1]
                        }
            
            return sizes
        except Exception as e:
            logger.error(f"Error generating image sizes: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating image sizes: {str(e)}"
            )
    
    @staticmethod
    def fix_orientation(img: Image.Image) -> Image.Image:
        """
        Fix the orientation of an image based on EXIF data.
        
        Args:
            img: The image
            
        Returns:
            Image.Image: The fixed image
        """
        try:
            # Get the EXIF data
            exif = img._getexif()
            
            if exif is None:
                return img
            
            # Get the orientation
            orientation_key = next((k for k, v in ExifTags.TAGS.items() if v == 'Orientation'), None)
            
            if orientation_key is None or orientation_key not in exif:
                return img
            
            orientation = exif[orientation_key]
            
            # Rotate the image based on the orientation
            if orientation == 2:
                # Horizontal flip
                return img.transpose(Image.FLIP_LEFT_RIGHT)
            elif orientation == 3:
                # Rotate 180 degrees
                return img.transpose(Image.ROTATE_180)
            elif orientation == 4:
                # Vertical flip
                return img.transpose(Image.FLIP_TOP_BOTTOM)
            elif orientation == 5:
                # Horizontal flip + rotate 90 degrees counter-clockwise
                return img.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.ROTATE_90)
            elif orientation == 6:
                # Rotate 270 degrees counter-clockwise
                return img.transpose(Image.ROTATE_270)
            elif orientation == 7:
                # Horizontal flip + rotate 270 degrees counter-clockwise
                return img.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.ROTATE_270)
            elif orientation == 8:
                # Rotate 90 degrees counter-clockwise
                return img.transpose(Image.ROTATE_90)
        except Exception as e:
            logger.error(f"Error fixing image orientation: {str(e)}")
        
        return img
    
    @staticmethod
    async def delete_image(
        key: str,
        delete_sizes: bool = True,
    ) -> Dict:
        """
        Delete an image from S3.
        
        Args:
            key: The S3 key
            delete_sizes: Whether to delete different sizes
            
        Returns:
            Dict: The deletion result
            
        Raises:
            HTTPException: If an error occurs
        """
        try:
            # Delete the image from S3
            s3_client.delete_object(
                Bucket=S3_BUCKET,
                Key=key
            )
            
            # Delete different sizes if requested
            deleted_sizes = []
            if delete_sizes:
                # Get the folder and filename
                folder = os.path.dirname(key)
                filename = os.path.basename(key)
                
                # Get the base filename (without extension)
                base_filename = os.path.splitext(filename)[0]
                
                # Delete each size
                for size_name in IMAGE_SIZES.keys():
                    # Generate the new filename and key
                    new_filename = f"{base_filename}_{size_name}.jpg"
                    new_key = f"{folder}/{new_filename}"
                    
                    try:
                        # Delete the size from S3
                        s3_client.delete_object(
                            Bucket=S3_BUCKET,
                            Key=new_key
                        )
                        
                        # Add the size to the result
                        deleted_sizes.append(new_key)
                    except Exception as e:
                        logger.error(f"Error deleting image size {new_key}: {str(e)}")
            
            # Return the result
            return {
                'key': key,
                'deleted_sizes': deleted_sizes
            }
        except Exception as e:
            logger.error(f"Error deleting image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting image: {str(e)}"
            )
