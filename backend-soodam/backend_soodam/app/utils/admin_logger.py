from ..models.admin import AdminActivity, SpecialAdmin
import logging
from typing import Optional, Any

logger = logging.getLogger(__name__)

async def log_admin_activity(
    admin_id: int,
    action_type: int,
    entity_type: str,
    entity_id: int,
    description: str,
    ip_address: Optional[str] = None
) -> Optional[AdminActivity]:
    """
    Log an admin activity.
    
    Args:
        admin_id: ID of the special admin
        action_type: Type of action (1=Create, 2=Update, 3=Delete, 4=View, 5=Other)
        entity_type: Type of entity being acted upon (e.g., "User", "Advertisement")
        entity_id: ID of the entity
        description: Description of the activity
        ip_address: IP address of the admin (optional)
        
    Returns:
        The created AdminActivity object, or None if creation failed
    """
    try:
        # Check if admin exists
        admin = await SpecialAdmin.objects.filter(id=admin_id).afirst()
        if not admin:
            logger.error(f"Failed to log admin activity: Admin with ID {admin_id} not found")
            return None
        
        # Create activity log
        activity = await AdminActivity.objects.create(
            admin_id=admin_id,
            action_type=action_type,
            entity_type=entity_type,
            entity_id=entity_id,
            description=description,
            ip_address=ip_address
        )
        
        return activity
    except Exception as e:
        logger.error(f"Failed to log admin activity: {str(e)}")
        return None

async def get_admin_id_from_user(user_id: int) -> Optional[int]:
    """
    Get the special admin ID for a user.
    
    Args:
        user_id: ID of the user
        
    Returns:
        The special admin ID, or None if not found
    """
    try:
        admin = await SpecialAdmin.objects.filter(user_id=user_id).afirst()
        return admin.id if admin else None
    except Exception as e:
        logger.error(f"Failed to get admin ID for user {user_id}: {str(e)}")
        return None