"""
Slugify utility for creating URL-friendly slugs from text.
"""

import re
import unicodedata
from django.utils.text import slugify as django_slugify


def slugify(text, max_length=80, allow_unicode=False):
    """
    Convert a string to a slug that can be used in a URL.
    
    Args:
        text (str): The text to convert to a slug
        max_length (int, optional): Maximum length of the slug. Defaults to 80.
        allow_unicode (bool, optional): Whether to allow unicode characters. Defaults to False.
        
    Returns:
        str: A URL-friendly slug
    """
    # Use Django's built-in slugify function as a base
    slug = django_slugify(text, allow_unicode=allow_unicode)
    
    # Ensure the slug doesn't exceed the maximum length
    if len(slug) > max_length:
        slug = slug[:max_length]
    
    # Remove any trailing hyphens
    slug = slug.rstrip('-')
    
    return slug


def unique_slugify(instance, value, slug_field_name='slug', queryset=None,
                  slug_separator='-'):
    """
    Create a unique slug for a model instance.
    
    Args:
        instance: Model instance for which to create a slug
        value (str): The value to slugify
        slug_field_name (str, optional): Name of the slug field. Defaults to 'slug'.
        queryset: Custom queryset to check for slug uniqueness. Defaults to None.
        slug_separator (str, optional): Separator for the slug. Defaults to '-'.
        
    Returns:
        str: A unique slug for the instance
    """
    # Get the slug field
    slug_field = instance._meta.get_field(slug_field_name)

    # Create the initial slug
    slug = slugify(value)
    
    # Ensure the slug is within the maximum length of the field
    max_length = slug_field.max_length
    if max_length and len(slug) > max_length:
        slug = slug[:max_length]
    
    # Remove any trailing hyphens
    slug = slug.rstrip('-')
    
    # If no queryset is provided, use the model's default manager
    if queryset is None:
        queryset = instance.__class__._default_manager.all()
        
    # Exclude the current instance from the queryset
    if instance.pk:
        queryset = queryset.exclude(pk=instance.pk)
    
    # Find a unique slug
    original_slug = slug
    counter = 1
    
    while queryset.filter(**{slug_field_name: slug}).exists():
        # If the slug already exists, append a number to make it unique
        suffix = f"{slug_separator}{counter}"
        
        # Make sure the slug with suffix doesn't exceed the maximum length
        if max_length and len(original_slug) + len(suffix) > max_length:
            slug = original_slug[:max_length - len(suffix)] + suffix
        else:
            slug = original_slug + suffix
            
        counter += 1

    return slug
