from asgiref.sync import sync_to_async
from math import ceil
from typing import Any, Dict, List, TypeVar, Generic, Optional, Callable, Awaitable
from django.db.models import QuerySet
from fastapi import Request
from ..schemas.pagination import PaginatedResponse, PaginationMetadata

T = TypeVar('T')

async def paginate_queryset(
    queryset: QuerySet,
    page: int = 1,
    size: int = 10,
    order_by: str = '-id'
) -> Dict[str, Any]:
    """
    Paginate a Django queryset and return a dictionary with pagination info.

    Args:
        queryset: The Django queryset to paginate
        page: The page number (1-indexed)
        size: The number of items per page
        order_by: The field to order by (default: '-id')

    Returns:
        A dictionary with pagination info:
        {
            "items": List of items for the current page,
            "total": Total number of items,
            "page": Current page number,
            "size": Number of items per page,
            "pages": Total number of pages
        }
    """
    # Get total count
    total = await sync_to_async(queryset.count)()

    # Calculate total pages
    pages = ceil(total / size) if total > 0 else 1

    # Ensure page is within valid range
    page = max(1, min(page, pages))

    # Calculate offset
    offset = (page - 1) * size

    # Get items for current page
    items = (await sync_to_async(queryset.order_by)(order_by))[offset:offset + size]

    return {
        "items": items,
        "total": total,
        "page": page,
        "size": size,
        "pages": pages
    }

async def paginate_request(
    request: Request,
    queryset: QuerySet,
    page: int = 1,
    limit: int = 10,
    order_by: str = '-created_at',
    filter_func: Optional[Callable[[QuerySet], Awaitable[QuerySet]]] = None
) -> PaginatedResponse:
    """
    Paginate a queryset and return a structured response with pagination metadata.

    Args:
        request: The FastAPI request object
        queryset: The Django queryset to paginate
        page: The page number (1-indexed)
        limit: The number of items per page
        order_by: The field to order by (default: '-created_at')
        filter_func: Optional async function to apply additional filters to the queryset

    Returns:
        PaginatedResponse containing the items and pagination metadata
    """
    # Apply additional filters if provided
    if filter_func:
        queryset = await filter_func(queryset)

    # Calculate offset
    offset = (page - 1) * limit

    # Get total count for pagination
    total_count = await queryset.acount()
    total_pages = ceil(total_count / limit) if total_count > 0 else 1

    # Get paginated results using sync_to_async
    items = await sync_to_async(list)(queryset.order_by(order_by)[offset:offset+limit])
    # items = (await sync_to_async(queryset.order_by)(order_by))[offset:offset+limit]

    # Create pagination metadata
    metadata = PaginationMetadata(
        page=page,
        limit=limit,
        total_count=total_count,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )
    # Add pagination metadata to request state for middleware use
    # request.state.pagination = metadata.dict()
    # Return structured response
    return PaginatedResponse(
        items=items,
        metadata=metadata
    )