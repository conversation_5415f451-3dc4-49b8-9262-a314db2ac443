from enum import IntEnum, Enum


class AdvertisementStatus(IntEnum):
    """Status values for advertisements"""
    PENDING = 0
    APPROVED = 1
    REJECTED = 2
    DELETED = 3  # Add DELETED status

class BlogStatus(str, Enum):
    """Status values for blogs"""
    DRAFT = "draft"
    PUBLISHED = "published"
    REJECTED = "rejected"


class AdminStatus(IntEnum):
    """Status values for special admins"""
    ACTIVE = 1
    INACTIVE = 2
    SUSPENDED = 3


class ActionType(IntEnum):
    """Types of admin actions"""
    CREATE = 1
    UPDATE = 2
    DELETE = 3
    VIEW = 4
    OTHER = 5


class TransactionType(IntEnum):
    """Types of admin transactions"""
    SALARY = 1
    BONUS = 2
    COMMISSION = 3
    REFUND = 4
    OTHER = 5
