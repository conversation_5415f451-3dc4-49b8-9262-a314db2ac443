"""
Monitoring middleware for the <PERSON><PERSON> backend.

This module provides middleware for collecting and exposing API performance metrics.
"""

import time
from typing import Callable, Dict, List, Tuple

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

class MetricsStorage:
    """
    Storage for API metrics.
    
    This class stores metrics for API endpoints, including:
    - Request count
    - Response status codes
    - Response times
    - Error count
    """
    
    def __init__(self):
        # Initialize metrics storage
        self.request_count: Dict[str, int] = {}
        self.status_codes: Dict[str, Dict[int, int]] = {}
        self.response_times: Dict[str, List[float]] = {}
        self.error_count: Dict[str, int] = {}
    
    def record_request(self, endpoint: str) -> None:
        """Record a request to an endpoint"""
        if endpoint not in self.request_count:
            self.request_count[endpoint] = 0
            self.status_codes[endpoint] = {}
            self.response_times[endpoint] = []
            self.error_count[endpoint] = 0
        
        self.request_count[endpoint] += 1
    
    def record_response(self, endpoint: str, status_code: int, response_time: float) -> None:
        """Record a response from an endpoint"""
        if status_code not in self.status_codes[endpoint]:
            self.status_codes[endpoint][status_code] = 0
        
        self.status_codes[endpoint][status_code] += 1
        self.response_times[endpoint].append(response_time)
        
        if status_code >= 400:
            self.error_count[endpoint] += 1
    
    def get_metrics(self) -> Dict:
        """Get all metrics"""
        metrics = {
            "endpoints": {}
        }
        
        for endpoint in self.request_count:
            metrics["endpoints"][endpoint] = {
                "request_count": self.request_count[endpoint],
                "status_codes": self.status_codes[endpoint],
                "error_count": self.error_count[endpoint],
                "response_times": {
                    "min_ms": round(min(self.response_times[endpoint]) * 1000, 2) if self.response_times[endpoint] else 0,
                    "max_ms": round(max(self.response_times[endpoint]) * 1000, 2) if self.response_times[endpoint] else 0,
                    "avg_ms": round(sum(self.response_times[endpoint]) * 1000 / len(self.response_times[endpoint]), 2) if self.response_times[endpoint] else 0
                }
            }
        
        return metrics


# Global metrics storage
metrics_storage = MetricsStorage()


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware for monitoring API performance.
    
    This middleware collects metrics for API endpoints, including:
    - Request count
    - Response status codes
    - Response times
    - Error count
    """
    
    def __init__(self, app: ASGIApp, exclude_paths: List[str] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip monitoring for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Get endpoint
        endpoint = f"{request.method} {request.url.path}"
        
        # Record request
        metrics_storage.record_request(endpoint)
        
        # Record start time
        start_time = time.time()
        
        # Process the request
        response = await call_next(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Record response
        metrics_storage.record_response(endpoint, response.status_code, response_time)
        
        return response


def setup_monitoring(app: FastAPI, exclude_paths: List[str] = None) -> None:
    """
    Set up monitoring for the FastAPI application.
    
    Args:
        app: The FastAPI application
        exclude_paths: List of paths to exclude from monitoring
    """
    # Add the monitoring middleware
    app.add_middleware(
        MonitoringMiddleware,
        exclude_paths=exclude_paths or ["/api/health", "/metrics"]
    )
    
    # Add metrics endpoint
    @app.get("/metrics", tags=["Monitoring"])
    async def get_metrics():
        """Get API metrics"""
        return metrics_storage.get_metrics()
