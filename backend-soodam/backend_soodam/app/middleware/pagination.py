from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from typing import Callable
import json

class PaginationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Check if pagination data exists in request state
        if hasattr(request.state, "pagination"):
            # Get the response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            # Parse the response body
            data = json.loads(body.decode())
            
            # Create a new response with pagination metadata
            pagination_data = request.state.pagination
            new_data = {
                "items": data,
                "total": pagination_data["total_count"],
                "page": pagination_data["page"],
                "size": pagination_data["limit"],
                "pages": pagination_data["total_pages"]
            }
            
            # Create a new response with the updated data
            new_body = json.dumps(new_data).encode()
            
            # Create a new response with the same status code and headers
            new_response = Response(
                content=new_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
            
            return new_response
        
        return response