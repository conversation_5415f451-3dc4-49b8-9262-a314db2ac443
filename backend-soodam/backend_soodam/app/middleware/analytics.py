"""
Analytics middleware for the Soodam backend.

This module provides middleware for logging API requests and user activities.
"""

import logging
import time
from typing import Callable, Dict, Optional

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..services.analytics import AnalyticsService

logger = logging.getLogger(__name__)


class AnalyticsMiddleware(BaseHTTPMiddleware):
    """
    Analytics middleware.
    
    This middleware logs API requests and user activities.
    """
    
    def __init__(self, app: FastAPI):
        """
        Initialize the middleware.
        
        Args:
            app: The FastAPI application
        """
        super().__init__(app)
    
    async def dispatch(
        self, request: Request, call_next: Callable
    ) -> Response:
        """
        Process the request and log analytics data.
        
        Args:
            request: The request
            call_next: The next middleware or endpoint
            
        Returns:
            Response: The response
        """
        # Skip logging for certain paths
        if self._should_skip_logging(request.url.path):
            return await call_next(request)
        
        # Start timer
        start_time = time.time()
        
        # Process the request
        response = await call_next(request)
        
        # Calculate response time in milliseconds
        response_time = (time.time() - start_time) * 1000
        
        # Get the user from the request state if available
        user = getattr(request.state, "user", None)
        
        # Log the API request asynchronously
        try:
            await AnalyticsService.log_api_request(
                request=request,
                response=response,
                response_time=response_time,
                user=user
            )
        except Exception as e:
            logger.error(f"Error logging API request: {str(e)}")
        
        return response
    
    def _should_skip_logging(self, path: str) -> bool:
        """
        Check if logging should be skipped for the given path.
        
        Args:
            path: The request path
            
        Returns:
            bool: True if logging should be skipped, False otherwise
        """
        # Skip logging for static files, health checks, metrics, etc.
        skip_paths = [
            "/static/",
            "/media/",
            "/api/health",
            "/api/metrics",
            "/api/docs",
            "/api/redoc",
            "/api/openapi.json",
            "/favicon.ico"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)


def setup_analytics(app: FastAPI) -> None:
    """
    Set up analytics middleware.
    
    Args:
        app: The FastAPI application
    """
    app.add_middleware(AnalyticsMiddleware)
