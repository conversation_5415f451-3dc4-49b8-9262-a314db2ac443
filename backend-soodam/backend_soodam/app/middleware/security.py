import re
import redis
import ipad<PERSON>
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable, List, Set
import os

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Middleware for enforcing security policies.
    """

    def __init__(self, app):
        super().__init__(app)
        # Skip security checks in development mode
        self.is_development = os.getenv("ENV_STATE") == "local"
        
        if not self.is_development:
            # Initialize Redis connection for IP blocking
            redis_host = os.getenv("REDIS_HOST", "redis")
            redis_port = int(os.getenv("REDIS_PORT", "6379"))
            self.redis = redis.Redis(host=redis_host, port=redis_port, db=0)
            
            # Load blocked IPs from persistent storage
            self.blocked_ips: Set[str] = self._load_blocked_ips()
            
            # Suspicious patterns in request parameters
            self.suspicious_patterns = [
                r"(?i)(?:union\s+select|select\s+.*\s+from|insert\s+into|delete\s+from|drop\s+table)",  # SQL injection
                r"(?i)(?:<script>|javascript:|onerror=|onload=)",  # XSS
                r"(?i)(?:\.\.\/|\.\.\\|\/etc\/passwd|\/bin\/bash|cmd\.exe)",  # Path traversal
                r"(?i)(?:eval\(|exec\(|system\(|passthru\()",  # Code injection
            ]
            
            # Endpoints that should not accept file uploads
            self.non_file_endpoints = [
                "/api/user/edit_user_info",
                "/api/auth/register",
                "/api/auth/refresh",
                "/api/auth/verify",
            ]
            
            # Sensitive endpoints that need extra protection
            self.sensitive_endpoints = [
                "/api/user/edit_user_info",
                "/api/auth/",
                "/api/admin/",
            ]
        else:
            self.redis = None
            self.blocked_ips = set()
            self.suspicious_patterns = []
            self.non_file_endpoints = []
            self.sensitive_endpoints = []


    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip all security checks in development mode
        if self.is_development:
            return await call_next(request)
            
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Check if IP is blocked
        if self._is_ip_blocked(client_ip):
            return JSONResponse(
                status_code=403,
                content={"detail": "Access denied."}
            )
        
        # Check for suspicious patterns
        if self._has_suspicious_patterns(request):
            # Increment violation count
            self._increment_violation_count(client_ip)
            return JSONResponse(
                status_code=400,
                content={"detail": "Invalid request parameters."}
            )
        
        # Block file upload attempts to non-file endpoints
        if self._is_unauthorized_file_upload(request):
            # Increment violation count
            self._increment_violation_count(client_ip)
            return JSONResponse(
                status_code=400,
                content={"detail": "File uploads not allowed for this endpoint."}
            )
        
        # Continue with normal processing for valid requests
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get the client IP address, handling proxies"""
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            # Get the first IP in the chain (client IP)
            return forwarded.split(",")[0].strip()
        return request.client.host if request.client else "0.0.0.0"
    
    def _is_ip_blocked(self, ip: str) -> bool:
        """Check if an IP is blocked"""
        # Check in-memory set
        if ip in self.blocked_ips:
            return True
        
        # Check Redis
        return bool(self.redis.get(f"blocked_ip:{ip}"))
    
    def _increment_violation_count(self, ip: str) -> None:
        """Increment violation count for an IP and block if threshold exceeded"""
        key = f"violation_count:{ip}"
        count = self.redis.incr(key)
        # Set expiry if new key
        if count == 1:
            self.redis.expire(key, 3600)  # 1 hour expiry
        
        # Block IP if threshold exceeded
        if count >= 5:  # Threshold for blocking
            self._block_ip(ip)
    
    def _block_ip(self, ip: str) -> None:
        """Block an IP address"""
        # Add to Redis with 24-hour expiry
        self.redis.setex(f"blocked_ip:{ip}", 86400, 1)  # 24 hours
        
        # Add to in-memory set
        self.blocked_ips.add(ip)
        
        # Log the blocked IP
        import logging
        logger = logging.getLogger("security")
        logger.warning(f"Blocked IP address: {ip}")
    
    def _load_blocked_ips(self) -> Set[str]:
        """Load blocked IPs from Redis"""
        blocked_ips = set()
        for key in self.redis.scan_iter("blocked_ip:*"):
            ip = key.decode('utf-8').split(':')[1]
            blocked_ips.add(ip)
        return blocked_ips
    
    def _has_suspicious_patterns(self, request: Request) -> bool:
        """Check for suspicious patterns in request parameters"""
        # Check query parameters
        for param, value in request.query_params.items():
            for pattern in self.suspicious_patterns:
                if re.search(pattern, value):
                    return True
        
        # Check path for suspicious patterns
        for pattern in self.suspicious_patterns:
            if re.search(pattern, request.url.path):
                return True
        
        return False
    
    def _is_unauthorized_file_upload(self, request: Request) -> bool:
        """Check if this is an unauthorized file upload attempt"""
        # Check if this is a file upload attempt to a protected endpoint
        is_protected_endpoint = any(request.url.path.startswith(endpoint) for endpoint in self.non_file_endpoints)
        is_file_upload = (
            "file" in request.query_params or 
            request.headers.get("content-type", "").startswith("multipart/form-data")
        )
        
        return is_protected_endpoint and is_file_upload
