import os
import posixpath
from urllib.parse import unquote

from django.conf import settings
from django.contrib.staticfiles import utils
from django.contrib.staticfiles.views import serve
from django.core.exceptions import ImproperlyConfigured
from django.http import Http404


class StaticFilesMiddleware:
    """
    Middleware that serves static files in all environments.
    
    This is useful when running Django with ASGI servers like Uvicorn
    that don't serve static files by default.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        if not settings.STATIC_URL:
            raise ImproperlyConfigured(
                "You're using the StaticFilesMiddleware without having set the STATIC_URL setting."
            )
        
        # Normalize static URL to have trailing slash
        self.static_url = settings.STATIC_URL.rstrip('/') + '/'
        self.static_root = settings.STATIC_ROOT
        
        # Normalize media URL to have trailing slash
        self.media_url = settings.MEDIA_URL.rstrip('/') + '/'
        self.media_root = settings.MEDIA_ROOT
    
    def __call__(self, request):
        path = request.path_info
        
        # Handle static files
        if path.startswith(self.static_url) and self.static_root:
            file_path = self._get_path(path, self.static_url)
            try:
                return serve(request, file_path, document_root=self.static_root)
            except Http404:
                # If the file is not found, continue with normal request processing
                pass
        
        # Handle media files
        if path.startswith(self.media_url) and self.media_root:
            file_path = self._get_path(path, self.media_url)
            try:
                return serve(request, file_path, document_root=self.media_root)
            except Http404:
                # If the file is not found, continue with normal request processing
                pass
        
        return self.get_response(request)
    
    def _get_path(self, url, base_url):
        """
        Return the relative path from the URL.
        """
        path = posixpath.normpath(unquote(url[len(base_url):]))
        return path