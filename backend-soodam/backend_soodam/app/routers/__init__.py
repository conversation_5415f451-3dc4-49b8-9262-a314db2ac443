from .versioning import *
from .home import *
from .auth import *
# from .health import *
# from .advertisement import *
from .api_manager import *
from .blogs import *
from .gis_geolocation import *
from .users import *
# from .admin import *  # Using consolidated_admin instead

from .auth import auth_router
from .advertisement import advertisement_router
from .blogs import blogs_router
from .gis_geolocation import gis_geolocation_router
from .users import users_router
from .admin import admin_router
from .contact import router as contact_router

__all__ = [
    "auth_router",
    "advertisement_router",
    "blogs_router",
    "gis_geolocation_router",
    "users_router",
    "admin_router",
    "contact_router"
]
