"""
Consolidated Admin Router for the Soodam backend.

This module provides both standard and versioned API endpoints for admin operations,
combining functionality from the original admin.py and admin_versioned.py files.
"""

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from typing import List, Optional

from app.models import CustomUserModel
from app.schemas.admin import (
    AdminTypeSchema, SpecialAdminCreateSchema, SpecialAdminUpdateSchema,
    SpecialAdminResponseSchema, AdminTransactionCreateSchema, AdminTransactionResponseSchema,
    PaginatedSpecialAdminResponse, PaginatedAdminActivityResponse, PaginatedTransactionResponse,
    AdminDashboardStatsResponse, AdvertisementResponse, StatusUpdateResponse, UserResponse, BlogResponse,
    PaginatedAdvertisementResponse
)
from app.api.admin import AdminAPI
from app.dependencies.auth import get_current_admin_user, require_super_admin
from app.core.versioning import ApiVersion, VersionedAPIRouter

# Create a standard router for admin endpoints
admin_router = APIRouter(tags=["admin"])

# Create a versioned router for admin endpoints
admin_versioned_router = VersionedAPIRouter(prefix="/admin", tags=["admin"])

#
# Standard Admin Router Endpoints
#

# Dashboard
@admin_router.get(
    "/dashboard/stats",
    response_model=AdminDashboardStatsResponse,
    summary="Get admin dashboard statistics",
    description="Retrieve statistics for the admin dashboard."
)
async def get_admin_dashboard_stats(
    request: Request,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get admin dashboard statistics"""
    return await AdminAPI.get_admin_dashboard_stats(request, current_admin)

# Advertisement management
@admin_router.get(
    "/advertisements",
    response_model=PaginatedAdvertisementResponse,
    summary="Get all advertisements",
    description="Retrieve all advertisements with their details and pagination metadata."
)
async def get_all_advertisements(
    request: Request,
    status: Optional[int] = Query(None, description="Filter by status (0=pending, 1=approved, 2=rejected)"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search by title or description"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get all advertisements with filtering and pagination"""
    return await AdminAPI.get_all_advertisements(request, current_admin, status, page, limit, search)

@admin_router.post(
    "/advertisement/{adv_id}/approve",
    response_model=StatusUpdateResponse,
    summary="Approve advertisement",
    description="Approve an advertisement based on its ID."
)
async def approve_advertisement(
    request: Request,
    adv_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Approve an advertisement"""
    return await AdminAPI.approve_advertisement(request, adv_id, True, current_admin)

@admin_router.post(
    "/advertisement/{adv_id}/reject",
    response_model=StatusUpdateResponse,
    summary="Reject advertisement",
    description="Reject an advertisement based on its ID."
)
async def reject_advertisement(
    request: Request,
    adv_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Reject an advertisement"""
    return await AdminAPI.approve_advertisement(request, adv_id, False, current_admin)

@admin_router.delete(
    "/advertisement/{adv_id}",
    response_model=StatusUpdateResponse,
    summary="Delete advertisement",
    description="Delete an advertisement."
)
async def delete_advertisement(
    request: Request,
    adv_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Delete an advertisement"""
    return await AdminAPI.delete_advertisement(request, adv_id, current_admin)


# Advertisement Edit Management
@admin_router.get(
    "/advertisement-edits",
    summary="Get pending advertisement edits",
    description="Get list of advertisement edits for admin review with filtering and pagination."
)
async def get_pending_advertisement_edits(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    status_filter: Optional[str] = Query(None, description="Filter by status (pending, approved, rejected)"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get pending advertisement edits for admin review"""
    return await AdminAPI.get_pending_advertisement_edits(request, current_admin, page, limit, status_filter)


@admin_router.get(
    "/advertisement-edits/{edit_id}",
    summary="Get advertisement edit details",
    description="Get detailed information about a specific advertisement edit."
)
async def get_advertisement_edit_detail(
    request: Request,
    edit_id: int = Path(..., description="The edit ID"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get detailed information about a specific advertisement edit"""
    return await AdminAPI.get_advertisement_edit_detail(request, edit_id, current_admin)


@admin_router.post(
    "/advertisement-edits/{edit_id}/review",
    response_model=StatusUpdateResponse,
    summary="Approve or reject advertisement edit",
    description="Approve or reject an advertisement edit with optional admin notes."
)
async def approve_or_reject_advertisement_edit(
    request: Request,
    edit_id: int = Path(..., description="The edit ID"),
    action: str = Query(..., description="Action to take: 'approve' or 'reject'"),
    admin_notes: Optional[str] = Query(None, description="Optional admin notes"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Approve or reject an advertisement edit"""
    return await AdminAPI.approve_or_reject_advertisement_edit(
        request, edit_id, action, admin_notes, current_admin
    )

# User management
@admin_router.get(
    "/users",
    response_model=List[UserResponse],
    summary="Get all users",
    description="Retrieve all users with their details."
)
async def get_all_users(
    request: Request,
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search by email or name"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get all users with filtering and pagination"""
    return await AdminAPI.get_all_users(request, current_admin, is_active, search, page, limit)

@admin_router.post(
    "/user/{user_id}/ban",
    response_model=StatusUpdateResponse,
    summary="Ban user",
    description="Ban a user based on their ID."
)
async def ban_user(
    request: Request,
    user_id: int,
    reason: Optional[str] = None,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Ban a user"""
    return await AdminAPI.ban_user(request, user_id, True, reason, current_admin)

@admin_router.post(
    "/user/{user_id}/unban",
    response_model=StatusUpdateResponse,
    summary="Unban user",
    description="Unban a user based on their ID."
)
async def unban_user(
    request: Request,
    user_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Unban a user"""
    return await AdminAPI.ban_user(request, user_id, False, None, current_admin)

# Blog management
@admin_router.get(
    "/blogs",
    response_model=List[BlogResponse],
    summary="Get all blogs",
    description="Retrieve all blogs for admin management."
)
async def get_all_blogs(
    request: Request,
    status: Optional[str] = Query(None, description="Filter by status (draft, published, rejected)"),
    search: Optional[str] = Query(None, description="Search by title or content"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get all blogs with filtering and pagination"""
    return await AdminAPI.get_all_blogs(request, current_admin, status, search, page, limit)

@admin_router.post(
    "/blog/{blog_id}/approve",
    response_model=StatusUpdateResponse,
    summary="Approve blog",
    description="Approve a blog post."
)
async def approve_blog(
    request: Request,
    blog_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Approve a blog post"""
    return await AdminAPI.approve_blog(request, blog_id, True, current_admin)

@admin_router.post(
    "/blog/{blog_id}/reject",
    response_model=StatusUpdateResponse,
    summary="Reject blog",
    description="Reject a blog post."
)
async def reject_blog(
    request: Request,
    blog_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Reject a blog post"""
    return await AdminAPI.approve_blog(request, blog_id, False, current_admin)

@admin_router.delete(
    "/blog/{blog_id}",
    response_model=StatusUpdateResponse,
    summary="Delete blog",
    description="Delete a blog post."
)
async def delete_blog(
    request: Request,
    blog_id: int,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Delete a blog post"""
    return await AdminAPI.delete_blog(request, blog_id, current_admin)

# Special Admin Management
@admin_router.get(
    "/special-admins",
    response_model=PaginatedSpecialAdminResponse,
    summary="Get all special admins",
    description="Get all special admins with filtering and pagination."
)
async def get_all_special_admins(
    request: Request,
    admin_type_id: Optional[int] = Query(None, description="Filter by admin type ID"),
    status: Optional[int] = Query(None, description="Filter by status (1=Active, 2=Inactive, 3=Suspended)"),
    search: Optional[str] = Query(None, description="Search by name or email"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """
    Get all special admins with filtering and pagination.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.get_all_special_admins(request, current_admin, admin_type_id, status, page, limit, search)

@admin_router.get(
    "/special-admins/{admin_id}",
    response_model=SpecialAdminResponseSchema,
    summary="Get special admin details",
    description="Get detailed information about a special admin."
)
async def get_special_admin_detail(
    request: Request,
    admin_id: int = Path(..., description="Special admin ID"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """
    Get detailed information about a special admin.
    Super admins can view any admin, other admins can only view their own details.
    """
    return await AdminAPI.get_special_admin_detail(request, admin_id, current_admin)

@admin_router.post(
    "/special-admins",
    response_model=SpecialAdminResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create special admin",
    description="Create a new special admin."
)
async def create_special_admin(
    request: Request,
    schema: SpecialAdminCreateSchema,
    current_admin: CustomUserModel = Depends(require_super_admin)
):
    """
    Create a new special admin.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.create_special_admin(request, schema, current_admin)

@admin_router.put(
    "/special-admins/{admin_id}",
    response_model=SpecialAdminResponseSchema,
    summary="Update special admin",
    description="Update a special admin's details."
)
async def update_special_admin(
    request: Request,
    schema: SpecialAdminUpdateSchema,
    admin_id: int = Path(..., description="Special admin ID"),
    current_admin: CustomUserModel = Depends(require_super_admin)
):
    """
    Update a special admin.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.update_special_admin(request, admin_id, schema, current_admin)

@admin_router.delete(
    "/special-admins/{admin_id}",
    response_model=StatusUpdateResponse,
    summary="Delete special admin",
    description="Delete a special admin."
)
async def delete_special_admin(
    request: Request,
    admin_id: int = Path(..., description="Special admin ID"),
    current_admin: CustomUserModel = Depends(require_super_admin)
):
    """
    Delete a special admin.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.delete_special_admin(request, admin_id, current_admin)

# Admin Transactions
@admin_router.get(
    "/transactions",
    response_model=PaginatedTransactionResponse,
    summary="Get admin transactions",
    description="Get admin transactions with filtering and pagination."
)
async def get_admin_transactions(
    request: Request,
    admin_id: Optional[int] = Query(None, description="Filter by admin ID"),
    transaction_type: Optional[int] = Query(None, description="Filter by transaction type"),
    is_positive: Optional[bool] = Query(None, description="Filter by positive/negative transactions"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """
    Get transactions for admins with filtering and pagination.
    Super admins can view all transactions, other admins can only view their own.
    """
    return await AdminAPI.get_admin_transactions(
        request, admin_id, transaction_type, is_positive, page, limit, current_admin
    )

@admin_router.post(
    "/transactions",
    response_model=AdminTransactionResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create admin transaction",
    description="Create a new admin transaction."
)
async def add_admin_transaction(
    request: Request,
    schema: AdminTransactionCreateSchema,
    current_admin: CustomUserModel = Depends(require_super_admin)
):
    """
    Add a financial transaction for an admin.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.add_admin_transaction(request, schema, current_admin)

# Admin Activities
@admin_router.get(
    "/activities",
    response_model=PaginatedAdminActivityResponse,
    summary="Get admin activities",
    description="Get admin activities with filtering and pagination."
)
async def get_admin_activities(
    request: Request,
    admin_id: Optional[int] = Query(None, description="Filter by admin ID"),
    action_type: Optional[int] = Query(None, description="Filter by action type"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """
    Get admin activities with filtering and pagination.
    Super admins can view all activities, other admins can only view their own.
    """
    return await AdminAPI.get_admin_activities(
        request, admin_id, action_type, entity_type, page, limit, current_admin
    )

# Admin Types
@admin_router.get(
    "/types",
    response_model=List[AdminTypeSchema],
    summary="Get admin types",
    description="Get all admin types."
)
async def get_admin_types(
    request: Request,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """
    Get all admin types.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.get_admin_types(request, current_admin)

@admin_router.post(
    "/types",
    response_model=AdminTypeSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create admin type",
    description="Create a new admin type."
)
async def create_admin_type(
    request: Request,
    schema: AdminTypeSchema,
    current_admin: CustomUserModel = Depends(require_super_admin)
):
    """
    Create a new admin type.
    Only super admins can access this endpoint.
    """
    return await AdminAPI.create_admin_type(request, schema, current_admin)

#
# Versioned Admin Router Endpoints
#

# Dashboard
@admin_versioned_router.get(
    "/dashboard/stats",
    version=ApiVersion.V1_0,
    response_model=AdminDashboardStatsResponse,
    summary="Get admin dashboard statistics",
    description="Retrieve statistics for the admin dashboard."
)
async def get_admin_dashboard_stats_v1(
    request: Request,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get admin dashboard statistics"""
    return await AdminAPI.get_admin_dashboard_stats(request, current_admin)

# Example of a V2 endpoint with enhanced functionality
@admin_versioned_router.get(
    "/dashboard/stats",
    version=ApiVersion.V2_0,
    response_model=AdminDashboardStatsResponse,
    summary="Get admin dashboard statistics (V2)",
    description="Retrieve enhanced statistics for the admin dashboard with additional metrics."
)
async def get_admin_dashboard_stats_v2(
    request: Request,
    current_admin: CustomUserModel = Depends(get_current_admin_user)
):
    """Get enhanced admin dashboard statistics (V2)"""
    # In a real implementation, this would call an enhanced version of the API
    # For now, we'll just call the same method as V1
    stats = await AdminAPI.get_admin_dashboard_stats(request, current_admin)

    # Add some V2-specific enhancements (this is just an example)
    stats["api_version"] = "2.0"
    stats["enhanced_metrics"] = True

    return stats
