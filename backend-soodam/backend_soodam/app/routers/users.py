from typing import Any, Dict

from fastapi import APIRouter, Depends, File, Query, UploadFile
from fastapi.responses import JSONResponse

from app.dependencies.auth import get_current_user
from ..api.user import UserProfileAPI, UserAddressAPI, UserSubscriptionAPI
from ..models import CustomUserModel
from app.schemas.gis_geolocation import (
    CustomUserLocationCreateSchema as CreateAddressSchema,
    CustomUserLocationEditSchema as EditAddressSchema,
)
from app.schemas.users.v1 import EditUserInfoSchema

# Create router with comprehensive tags and metadata
users_router = APIRouter(
    tags=["users"],
    responses={
        401: {"description": "Unauthorized - Invalid or missing authentication"},
        403: {"description": "Forbidden - Insufficient permissions"},
        404: {"description": "Not Found - Resource not found"},
        422: {"description": "Validation Error - Invalid input data"},
        500: {"description": "Internal Server Error"}
    }
)


# =============================================================================
# USER PROFILE ENDPOINTS
# =============================================================================

@users_router.get(
    '/profile',
    summary="Get comprehensive user profile",
    description="Retrieves detailed profile information including wallet, subscription, addresses, and ratings",
    response_description="Complete user profile data"
)
async def get_user_profile(current_user: CustomUserModel = Depends(get_current_user)) -> Dict[str, Any]:
    """Get comprehensive user profile information."""
    return await UserProfileAPI.get_user_profile(current_user)


@users_router.put(
    '/profile',
    summary="Update user profile",
    description="Updates user profile information with comprehensive validation",
    response_description="Updated profile confirmation"
)
async def update_user_profile(
    schema: EditUserInfoSchema,
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update user profile information."""
    return await UserProfileAPI.update_user_profile(schema, current_user)


# =============================================================================
# AVATAR MANAGEMENT ENDPOINTS
# =============================================================================

@users_router.post(
    "/avatar",
    summary="Upload user avatar",
    description="Upload and process user profile picture with automatic optimization",
    response_description="Avatar upload confirmation with details"
)
async def upload_avatar(
    file: UploadFile = File(..., description="Image file (JPEG, PNG, GIF, WebP, max 5MB)"),
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Upload user avatar with image processing and validation."""
    return await UserProfileAPI.upload_user_avatar(file, current_user)


@users_router.delete(
    "/avatar",
    summary="Delete user avatar",
    description="Delete current avatar and reset to default",
    response_description="Avatar deletion confirmation"
)
async def delete_avatar(current_user: CustomUserModel = Depends(get_current_user)) -> Dict[str, Any]:
    """Delete user avatar and reset to default."""
    return await UserProfileAPI.delete_user_avatar(current_user)


# =============================================================================
# WALLET & TRANSACTION ENDPOINTS
# =============================================================================

@users_router.get(
    '/wallet',
    summary="Get wallet information",
    description="Retrieves wallet balance and transaction statistics",
    response_description="Wallet information with balance and statistics"
)
async def get_wallet_info(current_user: CustomUserModel = Depends(get_current_user)) -> Dict[str, Any]:
    """Get user wallet information with statistics."""
    return await UserProfileAPI.get_user_wallet(current_user)


@users_router.get(
    '/transactions',
    summary="Get transaction history",
    description="Retrieves paginated transaction history with filtering options",
    response_description="Paginated transaction list"
)
async def get_transactions(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get user transaction history with pagination."""
    return await UserProfileAPI.get_user_transactions(current_user, page, per_page)


# =============================================================================
# ADDRESS MANAGEMENT ENDPOINTS
# =============================================================================

@users_router.get(
    '/addresses',
    summary="Get user addresses",
    description="Retrieves all addresses associated with the user account",
    response_description="List of user addresses"
)
async def get_user_addresses(current_user: CustomUserModel = Depends(get_current_user)) -> Dict[str, Any]:
    """Get all user addresses."""
    return await UserAddressAPI.get_user_addresses(current_user)


@users_router.post(
    '/addresses',
    summary="Create new address",
    description="Creates a new address for the user with province-city validation",
    response_description="Created address information"
)
async def create_address(
    schema: CreateAddressSchema,
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Create a new address for the user."""
    return await UserAddressAPI.create_address(schema, current_user)


@users_router.put(
    '/addresses/{address_id}',
    summary="Update address",
    description="Updates an existing address with validation",
    response_description="Updated address information"
)
async def update_address(
    address_id: int,
    schema: EditAddressSchema,
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update an existing address."""
    return await UserAddressAPI.update_address(address_id, schema, current_user)


@users_router.delete(
    '/addresses/{address_id}',
    summary="Delete address",
    description="Deletes an address belonging to the user",
    response_description="Deletion confirmation"
)
async def delete_address(
    address_id: int,
    current_user: CustomUserModel = Depends(get_current_user)
) -> Dict[str, Any]:
    """Delete a user address."""
    return await UserAddressAPI.delete_address(address_id, current_user)


# =============================================================================
# SUBSCRIPTION MANAGEMENT ENDPOINTS
# =============================================================================

@users_router.get(
    '/subscription',
    summary="Get subscription information",
    description="Retrieves current subscription details including expiration and usage statistics",
    response_description="Subscription information with status"
)
async def get_subscription(current_user: CustomUserModel = Depends(get_current_user)) -> Dict[str, Any]:
    """Get user subscription information."""
    return await UserSubscriptionAPI.get_user_subscription(current_user)


