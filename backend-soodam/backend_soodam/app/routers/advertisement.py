"""
Consolidated Advertisement Router for the Soodam backend.

This module provides endpoints for advertisement operations, combining
functionality from the original advertisement.py, advertisement_v2.py, and
advertisement_advanced.py files.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, Path, Query, Request, status, UploadFile, File, Body

from app.api.v1.advertisement import AdvertisementAPI
from app.dependencies.auth import get_current_user
from app.models import CustomUserModel
from app.schemas.advertisement.v1.advertisement import (
    ReadFeatureByCategorySchema,
)
from app.schemas.advertisement.v2.advertisement import (
    AdvertisementCreateSchemaV2,
    AdvertisementDetailSchemaV2,
    AdvertisementListSchemaV2,
    FlagAdvertisementSchema,
    AdvertisementUpdateSchemaV2,
)

# Create a router for advertisement endpoints
advertisement_router = APIRouter(tags=["advertisements"])


@advertisement_router.get(
    "/meta",
    summary="Get advertisement metadata",
    description="Get metadata for advertisements including categories and features.",
)
async def get_meta_data(request: Request):
    """Get metadata for advertisements."""
    return await AdvertisementAPI.get_meta_data(request)

@advertisement_router.get(
    "/favorites",
    response_model=AdvertisementListSchemaV2,
    summary="Get favorite advertisements",
    description="Get favorite advertisements for the current user.",
)
async def get_favorite_advertisements(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get favorite advertisements for the current user."""
    return await AdvertisementAPI.get_favorite_advertisements(request, current_user, page, limit)

@advertisement_router.post(
    "/features",
    summary="Get features by category",
    description="Get features for a specific category.",
)
async def get_feature_by_category(request: Request, schema: ReadFeatureByCategorySchema):
    """Get features for a specific category."""
    return await AdvertisementAPI.get_feature_by_category(request, schema)


@advertisement_router.get(
    "/nearby",
    response_model=AdvertisementListSchemaV2,
    summary="Get nearby advertisements",
    description="Get advertisements near a specific location.",
)
async def get_advertisements_nearby(
    request: Request,
    latitude: float = Query(..., description="The latitude"),
    longitude: float = Query(..., description="The longitude"),
    radius: float = Query(10.0, ge=0.1, le=100.0, description="The radius in kilometers"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    category_id: Optional[int] = Query(None, description="Filter by category ID"),
):
    """Get advertisements near a specific location."""
    return await AdvertisementAPI.get_advertisements_nearby(
        request, latitude, longitude, radius, page, limit, category_id
    )


@advertisement_router.get(
    "",
    response_model=AdvertisementListSchemaV2,
    summary="Get advertisements",
    description="Get a list of advertisements with enhanced filtering.",
)
async def get_advertisements(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    category_id: Optional[int] = Query(None, description="Filter by category ID"),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc or desc)"),
    location: Optional[str] = Query(None, description="Filter by location"),
    price_min: Optional[float] = Query(None, ge=0, description="Minimum price"),
    price_max: Optional[float] = Query(None, ge=0, description="Maximum price"),
    featured: Optional[bool] = Query(None, description="Filter by featured status"),
):
    """Get a list of advertisements with enhanced filtering."""
    return await AdvertisementAPI.get_advertisements(
        request, page, limit, category_id, search, sort_by, sort_order,
        location, price_min, price_max, featured
    )


@advertisement_router.get(
    "/{advertisement_id}",
    response_model=AdvertisementDetailSchemaV2,
    summary="Get advertisement",
    description="Get a specific advertisement with enhanced details.",
)
async def get_advertisement(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """Get a specific advertisement with enhanced details."""
    return await AdvertisementAPI.get_advertisement(request, advertisement_id)

@advertisement_router.post(
    "/my_adv",
    # response_model=AdvertisementDetailSchemaV2,
    summary="Get my advertisement details",
    description="Get My advertisement with enhanced details.",
)

async def get_my_advertisement(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get advertisements belonging to the current user."""
    return await AdvertisementAPI.get_my_advertisement(request, current_user)

@advertisement_router.post(
    "/{advertisement_id}/view",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Record advertisement view",
    description="Record a view for a specific advertisement.",
)
async def record_advertisement_view(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """Record a view for a specific advertisement."""
    user_id = current_user.id if current_user else None
    return await AdvertisementAPI.record_advertisement_view(request, advertisement_id, user_id)

@advertisement_router.post(
    "",
    response_model=AdvertisementDetailSchemaV2,
    status_code=status.HTTP_201_CREATED,
    summary="Create advertisement",
    description="Create a new advertisement.",
)
async def create_advertisement(
    request: Request,
    schema: AdvertisementCreateSchemaV2,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Create a new advertisement."""
    return await AdvertisementAPI.create_advertisement(request, schema, current_user)


@advertisement_router.post("/upload_media",
                           # response_model=AdvertisementDetailSchemaV2,
                            status_code=status.HTTP_201_CREATED,
                            summary="Upload media",
                            description="Upload a media file."
                           )
async def upload_advertisement_media(request: Request,
                        files: List[UploadFile]=File(...),
                        current_user: CustomUserModel = Depends(get_current_user),
                       ):
    """Upload a media file."""
    return await AdvertisementAPI.upload_advertisement_media(request, files, current_user)


@advertisement_router.post(
    "/{advertisement_id}/favorite",
    summary="Toggle favorite status",
    description="Toggle favorite status for an advertisement.",
)
async def toggle_favorite(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Toggle favorite status for an advertisement."""
    return await AdvertisementAPI.toggle_favorite(request, advertisement_id, current_user)


@advertisement_router.post(
    "/{advertisement_id}/flag",
    summary="Flag advertisement",
    description="Flag an advertisement as inappropriate.",
)
async def flag_advertisement(
    request: Request,
    schema: FlagAdvertisementSchema,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Flag an advertisement as inappropriate."""
    return await AdvertisementAPI.flag_advertisement(request, advertisement_id, schema.reason, current_user)


@advertisement_router.get(
    "/{advertisement_id}/statistics",
    summary="Get advertisement statistics",
    description="Get statistics for a specific advertisement.",
)
async def get_advertisement_statistics(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get statistics for a specific advertisement."""
    return await AdvertisementAPI.get_advertisement_statistics(request, advertisement_id, current_user)


@advertisement_router.delete(
    "/{advertisement_id}/media/{media_id}",
    summary="Delete advertisement media",
    description="Delete a media file (image or video) from an advertisement.",
)
async def delete_advertisement_media(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    media_id: int = Path(..., description="The ID of the media to delete"),
    media_type: str = Query(..., description="Type of media ('image' or 'video')"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Delete a media file from an advertisement."""
    return await AdvertisementAPI.delete_advertisement_media(
        request, advertisement_id, media_id, media_type, current_user
    )

@advertisement_router.delete(
    "/media/temporary",
    summary="Delete temporary media",
    description="Delete a temporary media file that was uploaded but not yet associated with an advertisement.",
)
async def delete_temporary_media(
    request: Request,
    media_url: str = Query(..., description="The URL of the media file to delete"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Delete a temporary media file."""
    return await AdvertisementAPI.delete_temporary_media(request, media_url, current_user)

@advertisement_router.put(
    "/{advertisement_id}",
    response_model=AdvertisementDetailSchemaV2,
    summary="Update advertisement",
    description="Update an existing advertisement.",
)
async def update_advertisement(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    schema: AdvertisementUpdateSchemaV2 = Body(...),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Update an existing advertisement."""
    return await AdvertisementAPI.update_advertisement(request, advertisement_id, schema, current_user)

@advertisement_router.delete(
    "/{advertisement_id}",
    summary="Delete advertisement",
    description="Delete an existing advertisement.",
)
async def delete_advertisement(
    request: Request,
    advertisement_id: int = Path(..., description="The advertisement ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    return await AdvertisementAPI.delete_advertisement(request, advertisement_id, current_user)
