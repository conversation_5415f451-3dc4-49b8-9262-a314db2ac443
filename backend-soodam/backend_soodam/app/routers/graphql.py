"""
GraphQL router for the Soodam backend.
"""

import json
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from graphene_django.views import GraphQLView
from graphql import GraphQLError

from ..dependencies.auth import get_current_user
from ..graphql.schema import schema
from ..models import CustomUserModel

# Create a router for GraphQL endpoints
graphql_router = APIRouter(tags=["graphql"])


class FastAPIGraphQLView(GraphQLView):
    """GraphQL view for FastAPI."""
    
    def __init__(self, schema, **kwargs):
        """Initialize the GraphQL view."""
        super().__init__(schema=schema, **kwargs)
    
    async def parse_body(self, request: Request) -> Dict[str, Any]:
        """Parse the request body."""
        content_type = request.headers.get("Content-Type", "")
        
        if "application/json" in content_type:
            try:
                body = await request.json()
            except Exception:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid JSON body"
                )
        elif "application/graphql" in content_type:
            try:
                body_text = await request.body()
                body = {"query": body_text.decode()}
            except Exception:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid GraphQL body"
                )
        elif "query" in request.query_params:
            body = {
                "query": request.query_params.get("query"),
                "variables": request.query_params.get("variables"),
                "operation_name": request.query_params.get("operationName")
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No GraphQL query found in the request"
            )
        
        return body
    
    async def get_context(self, request: Request) -> Dict[str, Any]:
        """Get the GraphQL context."""
        return {
            "request": request,
            "user": request.state.user if hasattr(request.state, "user") else None
        }
    
    async def execute_graphql_request(
        self,
        request: Request,
        data: Dict[str, Any],
        query: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        operation_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the GraphQL request."""
        query = data.get("query") or query
        variables = data.get("variables") or variables
        operation_name = data.get("operationName") or operation_name
        context = context or await self.get_context(request)
        
        if not query:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No GraphQL query found in the request"
            )
        
        try:
            result = await schema.execute_async(
                query,
                variable_values=variables,
                operation_name=operation_name,
                context=context
            )
            
            response_data = {"data": result.data}
            
            if result.errors:
                response_data["errors"] = [
                    self.format_error(error) for error in result.errors
                ]
            
            return response_data
        except Exception as e:
            return {
                "errors": [
                    self.format_error(GraphQLError(str(e)))
                ]
            }
    
    def format_error(self, error: GraphQLError) -> Dict[str, Any]:
        """Format a GraphQL error."""
        formatted_error = {
            "message": str(error),
            "locations": [
                {"line": location.line, "column": location.column}
                for location in error.locations
            ] if error.locations else None,
            "path": error.path
        }
        
        # Remove None values
        return {k: v for k, v in formatted_error.items() if v is not None}


@graphql_router.post(
    "",
    summary="GraphQL endpoint",
    description="Execute GraphQL queries and mutations.",
)
async def graphql_endpoint(
    request: Request,
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """GraphQL endpoint."""
    # Set the user in the request state
    request.state.user = current_user
    
    # Create the GraphQL view
    view = FastAPIGraphQLView(schema=schema)
    
    # Parse the request body
    data = await view.parse_body(request)
    
    # Execute the GraphQL request
    result = await view.execute_graphql_request(request, data)
    
    # Return the result
    return result


@graphql_router.get(
    "",
    summary="GraphQL endpoint",
    description="Execute GraphQL queries.",
)
async def graphql_endpoint_get(
    request: Request,
    query: Optional[str] = None,
    variables: Optional[str] = None,
    operation_name: Optional[str] = None,
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """GraphQL endpoint for GET requests."""
    # Set the user in the request state
    request.state.user = current_user
    
    # Create the GraphQL view
    view = FastAPIGraphQLView(schema=schema)
    
    # Parse variables
    parsed_variables = None
    if variables:
        try:
            parsed_variables = json.loads(variables)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid variables JSON"
            )
    
    # Execute the GraphQL request
    result = await view.execute_graphql_request(
        request,
        {},
        query=query,
        variables=parsed_variables,
        operation_name=operation_name
    )
    
    # Return the result
    return result
