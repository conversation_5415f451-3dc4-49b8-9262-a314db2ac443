"""
Search router for the <PERSON>dam backend.

This module provides endpoints for search operations.
"""

from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from ..api.search import SearchAPI
from ..dependencies.auth import get_current_user
from ..models import CustomUserModel

# Create a router for search endpoints
search_router = APIRouter(tags=["search"])


@search_router.get(
    "/advertisements",
    summary="Search advertisements",
    description="Search for advertisements using Elasticsearch.",
)
async def search_advertisements(
    request: Request,
    query: str = Query(..., description="Search query"),
    category_id: Optional[int] = Query(None, description="Filter by category ID"),
    location_id: Optional[int] = Query(None, description="Filter by location ID"),
    price_min: Optional[float] = Query(None, ge=0, description="Filter by minimum price"),
    price_max: Optional[float] = Query(None, ge=0, description="Filter by maximum price"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    status: Optional[str] = Query(None, description="Filter by status"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc or desc)"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    facets: bool = Query(True, description="Include facets in the response"),
):
    """Search for advertisements."""
    return await SearchAPI.search_advertisements(
        request, query, category_id, location_id, price_min, price_max,
        is_featured, status, sort_by, sort_order, page, limit, facets
    )


@search_router.post(
    "/sync/advertisement/{advertisement_id}",
    summary="Sync advertisement",
    description="Sync an advertisement with Elasticsearch.",
)
async def sync_advertisement(
    request: Request,
    advertisement_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Sync an advertisement with Elasticsearch."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to sync advertisements"
        )

    return await SearchAPI.sync_advertisement(request, advertisement_id)


@search_router.post(
    "/sync/advertisements",
    summary="Sync all advertisements",
    description="Sync all advertisements with Elasticsearch.",
)
async def sync_all_advertisements(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Sync all advertisements with Elasticsearch."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to sync advertisements"
        )

    return await SearchAPI.sync_all_advertisements(request)


@search_router.post(
    "/sync/user/{user_id}",
    summary="Sync user",
    description="Sync a user with Elasticsearch.",
)
async def sync_user(
    request: Request,
    user_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Sync a user with Elasticsearch."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to sync users"
        )

    return await SearchAPI.sync_user(request, user_id)


@search_router.post(
    "/sync/blog/{blog_id}",
    summary="Sync blog",
    description="Sync a blog with Elasticsearch.",
)
async def sync_blog(
    request: Request,
    blog_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Sync a blog with Elasticsearch."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to sync blogs"
        )

    return await SearchAPI.sync_blog(request, blog_id)
