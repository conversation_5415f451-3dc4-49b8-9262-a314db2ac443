"""
Recommendations router for the Soodam backend.

This module provides endpoints for recommendation operations.
"""

from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, Query, Request

from ..api.recommendations import RecommendationsAPI
from ..dependencies.auth import get_current_user
from ..models import CustomUserModel

# Create a router for recommendation endpoints
recommendations_router = APIRouter(tags=["recommendations"])


@recommendations_router.get(
    "",
    summary="Get recommendations",
    description="Get personalized recommendations for the current user.",
)
async def get_recommendations(
    request: Request,
    limit: int = Query(10, ge=1, le=50, description="The maximum number of recommendations to return"),
    exclude_ids: Optional[List[int]] = Query(None, description="Advertisement IDs to exclude from recommendations"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get personalized recommendations for the current user."""
    return await RecommendationsAPI.get_user_recommendations(
        request, current_user, limit, exclude_ids
    )


@recommendations_router.get(
    "/behavior",
    summary="Get behavior-based recommendations",
    description="Get recommendations based on user behavior.",
)
async def get_behavior_based_recommendations(
    request: Request,
    limit: int = Query(10, ge=1, le=50, description="The maximum number of recommendations to return"),
    exclude_ids: Optional[List[int]] = Query(None, description="Advertisement IDs to exclude from recommendations"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get recommendations based on user behavior."""
    return await RecommendationsAPI.get_behavior_based_recommendations(
        request, current_user, limit, exclude_ids
    )


@recommendations_router.get(
    "/content",
    summary="Get content-based recommendations",
    description="Get recommendations based on content similarity.",
)
async def get_content_based_recommendations(
    request: Request,
    limit: int = Query(10, ge=1, le=50, description="The maximum number of recommendations to return"),
    exclude_ids: Optional[List[int]] = Query(None, description="Advertisement IDs to exclude from recommendations"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get recommendations based on content similarity."""
    return await RecommendationsAPI.get_content_based_recommendations(
        request, current_user, limit, exclude_ids
    )


@recommendations_router.get(
    "/collaborative",
    summary="Get collaborative recommendations",
    description="Get recommendations based on collaborative filtering.",
)
async def get_collaborative_recommendations(
    request: Request,
    limit: int = Query(10, ge=1, le=50, description="The maximum number of recommendations to return"),
    exclude_ids: Optional[List[int]] = Query(None, description="Advertisement IDs to exclude from recommendations"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get recommendations based on collaborative filtering."""
    return await RecommendationsAPI.get_collaborative_recommendations(
        request, current_user, limit, exclude_ids
    )


@recommendations_router.get(
    "/trending",
    summary="Get trending advertisements",
    description="Get trending advertisements based on recent views and favorites.",
)
async def get_trending_advertisements(
    request: Request,
    limit: int = Query(10, ge=1, le=50, description="The maximum number of advertisements to return"),
    days: int = Query(7, ge=1, le=30, description="The number of days to consider for trending"),
    exclude_ids: Optional[List[int]] = Query(None, description="Advertisement IDs to exclude"),
):
    """Get trending advertisements based on recent views and favorites."""
    return await RecommendationsAPI.get_trending_advertisements(
        request, limit, days, exclude_ids
    )
