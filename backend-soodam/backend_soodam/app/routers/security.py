"""
Security router for the Soodam backend.
"""

from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from ..api.security import SecurityAPI
from ..dependencies.auth import get_current_user
from ..models import CustomUserModel
from ..schemas.security import (
    APIKeyCreate,
    APIKeyList,
    APIKeyResponse,
    LoginAttemptList,
    TwoFactorAuthEnable,
    TwoFactorAuthSetup,
    TwoFactorAuthStatus,
    TwoFactorAuthVerify,
)

security_router = APIRouter()


@security_router.post(
    "/api-keys",
    response_model=APIKeyResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create API key",
    description="Create a new API key for the current user.",
)
async def create_api_key(
    request: Request,
    schema: APIKeyCreate,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Create a new API key."""
    return await SecurityAPI.create_api_key(request, schema, current_user)


@security_router.get(
    "/api-keys",
    response_model=APIKeyList,
    summary="Get API keys",
    description="Get API keys for the current user.",
)
async def get_api_keys(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get API keys for the current user."""
    return await SecurityAPI.get_api_keys(request, current_user, page, limit)


@security_router.delete(
    "/api-keys/{api_key_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Revoke API key",
    description="Revoke an API key for the current user.",
)
async def revoke_api_key(
    request: Request,
    api_key_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Revoke an API key."""
    await SecurityAPI.revoke_api_key(request, api_key_id, current_user)
    return None


@security_router.post(
    "/2fa/setup",
    response_model=TwoFactorAuthSetup,
    summary="Set up 2FA",
    description="Set up two-factor authentication for the current user.",
)
async def setup_two_factor_auth(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Set up two-factor authentication."""
    return await SecurityAPI.setup_two_factor_auth(request, current_user)


@security_router.post(
    "/2fa/enable",
    status_code=status.HTTP_200_OK,
    summary="Enable 2FA",
    description="Enable two-factor authentication for the current user.",
)
async def enable_two_factor_auth(
    request: Request,
    schema: TwoFactorAuthEnable,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Enable two-factor authentication."""
    result = await SecurityAPI.enable_two_factor_auth(request, schema.token, current_user)
    return {"success": result}


@security_router.post(
    "/2fa/disable",
    status_code=status.HTTP_200_OK,
    summary="Disable 2FA",
    description="Disable two-factor authentication for the current user.",
)
async def disable_two_factor_auth(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Disable two-factor authentication."""
    result = await SecurityAPI.disable_two_factor_auth(request, current_user)
    return {"success": result}


@security_router.get(
    "/2fa/status",
    response_model=TwoFactorAuthStatus,
    summary="Get 2FA status",
    description="Get two-factor authentication status for the current user.",
)
async def get_two_factor_auth_status(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get two-factor authentication status."""
    return await SecurityAPI.get_two_factor_auth_status(request, current_user)


@security_router.post(
    "/2fa/verify",
    status_code=status.HTTP_200_OK,
    summary="Verify 2FA",
    description="Verify a two-factor authentication token.",
)
async def verify_two_factor_auth(
    request: Request,
    schema: TwoFactorAuthVerify,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Verify two-factor authentication."""
    result = await SecurityAPI.verify_two_factor_auth(request, schema.token, current_user)
    return {"success": result}


@security_router.get(
    "/login-attempts",
    response_model=LoginAttemptList,
    summary="Get login attempts",
    description="Get login attempts for the current user.",
)
async def get_login_attempts(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    success: Optional[bool] = Query(None, description="Filter by success"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get login attempts for the current user."""
    return await SecurityAPI.get_login_attempts(request, current_user, page, limit, success)
