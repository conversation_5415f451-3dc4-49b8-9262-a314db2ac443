from typing import Any

from app.api import AuthAPI
from app.schemas.users.v1 import Token
from app.schemas.auth import (
    DualUsernameLoginSchema, EmailLoginSchema, PhoneLoginSchema,
    LoginResponseSchema, UsernameTypeSchema
)

from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from app.schemas import PhoneNumberRegisteredSchema, VerifyCodeSchema, CallSendVerifyCode

auth_router = APIRouter(
    tags=["auth"],
    responses={
        401: {"description": "Unauthorized - Invalid credentials"},
        422: {"description": "Validation Error - Invalid input data"},
        500: {"description": "Internal Server Error"}
    }
)


# =============================================================================
# ENHANCED DUAL USERNAME LOGIN ENDPOINTS
# =============================================================================

@auth_router.post(
    "/login/dual",
    response_model=LoginResponseSchema,
    summary="Login with email or phone number",
    description="Login using either email address or phone number with password",
    response_description="JWT token and user information"
)
async def login_dual_username(
    request: Request,
    credentials: DualUsernameLoginSchema
) -> Any:
    """
    Enhanced login endpoint supporting both email and phone number.

    Users can login with either:
    - Email address: <EMAIL>
    - Phone number: 09123456789 (Iranian format)
    """
    from app.dependencies.authentication import AuthenticationHelper

    try:
        # Validate credentials
        validation_result = AuthenticationHelper.validate_credentials(
            credentials.username,
            credentials.password
        )

        if not validation_result['valid']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=validation_result.get('error', 'Invalid credentials')
            )

        user = validation_result['user']

        # Create token response
        from config.jwt import create_access_token_response
        token_data = create_access_token_response({"sub": str(user.uuid)})

        # Prepare user info
        user_info = {
            "id": user.id,
            "email": user.email,
            "phone_number": user.phone_number,
            "full_name": user.get_full_name(),
            "display_name": user.get_display_name(),
            "is_verified": user.is_verified,
            "user_type": user.user_type,
            "avatar_url": user.get_avatar_url()
        }

        return LoginResponseSchema(
            access_token=token_data["token"],
            token_type=token_data["token_type"],
            user_info=user_info
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication error: {str(e)}"
        )


@auth_router.post(
    "/login/email",
    response_model=LoginResponseSchema,
    summary="Login with email only",
    description="Login using email address and password",
    response_description="JWT token and user information"
)
async def login_email(
    request: Request,
    credentials: EmailLoginSchema
) -> Any:
    """Login endpoint specifically for email authentication."""
    # Convert to dual username format
    dual_credentials = DualUsernameLoginSchema(
        username=credentials.email,
        password=credentials.password,
        remember_me=credentials.remember_me
    )
    return await login_dual_username(request, dual_credentials)


@auth_router.post(
    "/login/phone",
    response_model=LoginResponseSchema,
    summary="Login with phone number only",
    description="Login using phone number and password",
    response_description="JWT token and user information"
)
async def login_phone(
    request: Request,
    credentials: PhoneLoginSchema
) -> Any:
    """Login endpoint specifically for phone number authentication."""
    # Convert to dual username format
    dual_credentials = DualUsernameLoginSchema(
        username=credentials.phone_number,
        password=credentials.password,
        remember_me=credentials.remember_me
    )
    return await login_dual_username(request, dual_credentials)


@auth_router.post(
    "/username/identify",
    response_model=UsernameTypeSchema,
    summary="Identify username type",
    description="Identify whether a username is an email or phone number",
    response_description="Username type and validation information"
)
async def identify_username_type(username: str) -> UsernameTypeSchema:
    """
    Identify and validate username type.

    This endpoint helps frontend applications determine the type of username
    and whether it's in a valid format.
    """
    from app.dependencies.authentication import AuthenticationHelper

    username_type = AuthenticationHelper.get_username_type(username)
    normalized_username = AuthenticationHelper.normalize_username(username)

    # Check if format is valid
    is_valid = False
    if username_type == 'email':
        import re
        is_valid = bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', username))
    elif username_type == 'phone':
        try:
            from app.models.user import normalize_iranian_phone_number
            normalize_iranian_phone_number(username)
            is_valid = True
        except ValueError:
            is_valid = False

    return UsernameTypeSchema(
        username=username,
        username_type=username_type,
        normalized_username=normalized_username,
        is_valid=is_valid
    )


# =============================================================================
# LEGACY LOGIN ENDPOINT (for backward compatibility)
# =============================================================================

@auth_router.post("/login", response_model=Token)
async def login(
        request: Request, form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """Legacy login endpoint for backward compatibility."""
    return await AuthAPI.login(request, form_data)


@auth_router.post('/see_ver_code')
async def see_ver_code(request: Request, schema: PhoneNumberRegisteredSchema) -> Any:
    return await AuthAPI.see_ver_code(request, schema)


@auth_router.post('/get_ver_code',
                  )
async def get_ver_code(request: Request, schema: PhoneNumberRegisteredSchema) -> Any:
    return await AuthAPI.get_ver_code(request, schema)


@auth_router.post('/verify',
                  )
async def verify(request: Request, schema: VerifyCodeSchema) -> Any:
    return await AuthAPI.verify(request, schema)


@auth_router.post('/call_send_verify_code'
                  )
async def call_send_verify_code(request: Request, schema: CallSendVerifyCode) -> Any:
    return await AuthAPI.call_send_verify_code(request, schema)
