"""
Autocomplete router for the <PERSON><PERSON> backend.

This module provides endpoints for autocomplete operations.
"""

from typing import Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException, Query, Request, status

from ..api.autocomplete import AutocompleteAP<PERSON>
from ..dependencies.auth import get_current_user
from ..models import CustomUserModel

# Create a router for autocomplete endpoints
autocomplete_router = APIRouter(tags=["autocomplete"])


@autocomplete_router.get(
    "/suggestions",
    summary="Get suggestions",
    description="Get autocomplete suggestions for a query.",
)
async def get_suggestions(
    request: Request,
    query: str = Query(..., description="The query to get suggestions for"),
    types: Optional[List[str]] = Query(None, description="The suggestion types to include"),
    limit: int = Query(10, ge=1, le=50, description="The maximum number of suggestions to return"),
):
    """Get autocomplete suggestions for a query."""
    return await AutocompleteAPI.get_suggestions(request, query, types, limit)


@autocomplete_router.post(
    "/suggestions",
    summary="Index suggestion",
    description="Index a suggestion for autocomplete.",
)
async def index_suggestion(
    request: Request,
    text: str = Body(..., description="The suggestion text"),
    type: str = Body(..., description="The suggestion type"),
    weight: int = Body(1, description="The suggestion weight"),
    metadata: Optional[Dict] = Body(None, description="Additional metadata for the suggestion"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Index a suggestion for autocomplete."""
    return await AutocompleteAPI.index_suggestion(request, text, type, weight, metadata, current_user)


@autocomplete_router.post(
    "/popular-searches",
    summary="Index popular searches",
    description="Index popular searches for autocomplete.",
)
async def index_popular_searches(
    request: Request,
    searches: List[Dict] = Body(..., description="List of popular searches with text, count, and metadata"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Index popular searches for autocomplete."""
    return await AutocompleteAPI.index_popular_searches(request, searches, current_user)


@autocomplete_router.post(
    "/extract-keywords",
    summary="Extract keywords",
    description="Extract keywords from text for autocomplete suggestions.",
)
async def extract_keywords(
    request: Request,
    text: str = Body(..., description="The text to extract keywords from"),
):
    """Extract keywords from text for autocomplete suggestions."""
    return await AutocompleteAPI.extract_keywords(request, text)
