from typing import Any

from fastapi import APIRouter, Depends, Request, Query, Path, Body, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.payment import PaymentAPI
from app.deps.auth import get_current_user
from app.deps.db import get_db
from app.models.user import CustomUserModel
from app.schemas.payment import (
    PaymentInitRequest,
    PaymentInitResponse,
    PaymentVerifyRequest,
    PaymentVerifyResponse,
    PaymentHistoryResponse
)

# Create router
payment_router = APIRouter(prefix="/payments", tags=["payments"])


@payment_router.post(
    "/initialize",
    response_model=PaymentInitResponse,
    summary="Initialize payment",
    description="Initialize a payment with an Iranian bank gateway"
)
async def initialize_payment(
    request: Request,
    payment_data: PaymentInitRequest = Body(...),
    current_user: CustomUserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Initialize a payment with an Iranian bank gateway"""
    return await PaymentAPI.initialize_payment(
        request,
        payment_data.amount,
        payment_data.bank_name,
        payment_data.description,
        current_user,
        db,
        payment_data.callback_url
    )


@payment_router.post(
    "/callback",
    response_model=PaymentVerifyResponse,
    summary="Payment callback",
    description="Callback endpoint for bank gateway"
)
async def payment_callback(
    request: Request,
    callback_data: PaymentVerifyRequest = Body(...),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Callback endpoint for bank gateway"""
    return await PaymentAPI.verify_payment(
        request,
        callback_data.tracking_code,
        callback_data.status,
        callback_data.reference_id,
        db
    )


@payment_router.get(
    "/history",
    response_model=PaymentHistoryResponse,
    summary="Get payment history",
    description="Get payment history for the current user"
)
async def get_payment_history(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Page size"),
    current_user: CustomUserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Get payment history for the current user"""
    return await PaymentAPI.get_payment_history(
        request,
        current_user,
        page,
        page_size,
        db
    )


@payment_router.get(
    "/verify/{tracking_code}",
    response_model=PaymentVerifyResponse,
    summary="Verify payment",
    description="Verify a payment manually"
)
async def verify_payment(
    tracking_code: str = Path(..., description="Payment tracking code"),
    request: Request = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Verify a payment manually"""
    return await PaymentAPI.verify_payment(
        request,
        tracking_code,
        1,  # Assume success for manual verification
        None,
        db
    )