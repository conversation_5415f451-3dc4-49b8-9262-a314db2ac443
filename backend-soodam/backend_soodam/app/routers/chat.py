"""
Chat router for the <PERSON><PERSON> backend.

This module provides endpoints for chat operations.
"""

from typing import Optional

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Query, Request, WebSocket, WebSocketDisconnect, status

from ..api.chat import Chat<PERSON><PERSON>
from ..core.websocket import WebSocket<PERSON><PERSON><PERSON>, manager
from ..dependencies.auth import get_current_user, get_token_from_query
from ..models import CustomUserModel
from ..schemas.chat import (
    ChatMessageCreateSchema,
    ChatMessageListSchema,
    ChatMessageSchema,
    ChatRoomCreateSchema,
    ChatRoomListSchema,
    ChatRoomSchema,
)

# Create a router for chat endpoints
chat_router = APIRouter(tags=["chat"])


@chat_router.get(
    "/rooms",
    response_model=ChatRoomListSchema,
    summary="Get chat rooms",
    description="Get chat rooms for the current user.",
)
async def get_chat_rooms(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    advertisement_id: Optional[int] = Query(None, description="Filter by advertisement ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get chat rooms for the current user."""
    return await ChatAPI.get_chat_rooms(
        request, current_user, page, limit, advertisement_id
    )


@chat_router.get(
    "/rooms/{room_id}",
    response_model=ChatRoomSchema,
    summary="Get chat room",
    description="Get a specific chat room.",
)
async def get_chat_room(
    request: Request,
    room_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get a specific chat room."""
    return await ChatAPI.get_chat_room(request, room_id, current_user)


@chat_router.post(
    "/rooms",
    response_model=ChatRoomSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create chat room",
    description="Create a new chat room.",
)
async def create_chat_room(
    request: Request,
    schema: ChatRoomCreateSchema,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Create a new chat room."""
    return await ChatAPI.create_chat_room(request, schema, current_user)


@chat_router.get(
    "/rooms/{room_id}/messages",
    response_model=ChatMessageListSchema,
    summary="Get chat messages",
    description="Get chat messages for a room.",
)
async def get_chat_messages(
    request: Request,
    room_id: int,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get chat messages for a room."""
    return await ChatAPI.get_chat_messages(
        request, room_id, current_user, page, limit
    )


@chat_router.post(
    "/rooms/{room_id}/messages",
    response_model=ChatMessageSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Send chat message",
    description="Send a chat message.",
)
async def send_chat_message(
    request: Request,
    room_id: int,
    schema: ChatMessageCreateSchema,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Send a chat message."""
    return await ChatAPI.send_chat_message(request, room_id, schema, current_user)


@chat_router.websocket("/ws/chat")
async def chat_websocket(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
):
    """
    WebSocket endpoint for chat.
    
    Args:
        websocket: The WebSocket connection
        token: The authentication token (optional)
    """
    # Get user from token
    user_id = None
    if token:
        try:
            user = await get_token_from_query(token)
            if user:
                user_id = user.id
        except HTTPException:
            # Invalid token, but we'll still allow connection as anonymous
            pass
    
    # Connect the client
    await manager.connect(websocket, user_id)
    
    # Subscribe to user-specific chat channel if authenticated
    if user_id:
        await manager.subscribe(websocket, f"user:{user_id}:chat")
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                # Parse the message
                message_data = json.loads(data)
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    # Respond to ping with pong
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="pong",
                            data={"timestamp": message_data.get("data", {}).get("timestamp")},
                        ),
                        websocket
                    )
                
                elif message_type == "subscribe_room":
                    # Subscribe to a room channel
                    room_id = message_data.get("data", {}).get("room_id")
                    if room_id:
                        await manager.subscribe(websocket, f"chat_room:{room_id}")
                
                elif message_type == "unsubscribe_room":
                    # Unsubscribe from a room channel
                    room_id = message_data.get("data", {}).get("room_id")
                    if room_id:
                        await manager.unsubscribe(websocket, f"chat_room:{room_id}")
                
                else:
                    # Unknown message type
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="error",
                            data={"message": f"Unknown message type: {message_type}"},
                        ),
                        websocket
                    )
            
            except json.JSONDecodeError:
                # Invalid JSON
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Invalid JSON"},
                    ),
                    websocket
                )
            
            except Exception as e:
                # Other error
                logger.error(f"Error processing WebSocket message: {str(e)}")
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Internal server error"},
                    ),
                    websocket
                )
    
    except WebSocketDisconnect:
        # Client disconnected
        await manager.disconnect(websocket)
    
    except Exception as e:
        # Other error
        logger.error(f"WebSocket error: {str(e)}")
        await manager.disconnect(websocket)
