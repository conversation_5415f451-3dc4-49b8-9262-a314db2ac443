"""
Image router for the <PERSON><PERSON> backend.

This module provides endpoints for image operations.
"""

from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, Request, UploadFile, status

from ..api.image import ImageAPI
from ..dependencies.auth import get_current_user
from ..models import CustomUserModel

# Create a router for image endpoints
image_router = APIRouter(tags=["images"])


@image_router.post(
    "/upload",
    summary="Upload image",
    description="Upload an image.",
)
async def upload_image(
    request: Request,
    file: UploadFile = File(...),
    folder: str = Form("uploads/images"),
    generate_sizes: bool = Form(True),
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """Upload an image."""
    return await ImageAPI.upload_image(
        request, file, folder, generate_sizes, current_user
    )


@image_router.post(
    "/advertisements/{advertisement_id}",
    summary="Upload advertisement image",
    description="Upload an image for an advertisement.",
)
async def upload_advertisement_image(
    request: Request,
    advertisement_id: int,
    file: UploadFile = File(...),
    is_primary: bool = Form(False),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Upload an image for an advertisement."""
    return await ImageAPI.upload_advertisement_image(
        request, file, advertisement_id, is_primary, current_user
    )


@image_router.post(
    "/profile",
    summary="Upload profile image",
    description="Upload a profile image.",
)
async def upload_profile_image(
    request: Request,
    file: UploadFile = File(...),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Upload a profile image."""
    return await ImageAPI.upload_profile_image(
        request, file, current_user
    )


@image_router.delete(
    "/{key:path}",
    summary="Delete image",
    description="Delete an image.",
)
async def delete_image(
    request: Request,
    key: str,
    delete_sizes: bool = Query(True),
    current_user: Optional[CustomUserModel] = Depends(get_current_user),
):
    """Delete an image."""
    return await ImageAPI.delete_image(
        request, key, delete_sizes, current_user
    )
