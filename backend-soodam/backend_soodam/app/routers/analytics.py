"""
Analytics router for the <PERSON><PERSON> backend.

This module provides endpoints for analytics operations.
"""

from datetime import date
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from ..api.analytics import AnalyticsAPI
from ..dependencies.auth import get_current_user
from ..models.user import CustomUserModel

# Create a router for analytics endpoints
analytics_router = APIRouter(tags=["analytics"])


@analytics_router.get(
    "/daily",
    summary="Get daily statistics",
    description="Get daily statistics for a date range.",
)
async def get_daily_statistics(
    request: Request,
    start_date: date = Query(..., description="Start date"),
    end_date: date = Query(..., description="End date"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get daily statistics for a date range."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access analytics"
        )
    
    return await AnalyticsAPI.get_daily_statistics(request, start_date, end_date)


@analytics_router.get(
    "/categories",
    summary="Get category statistics",
    description="Get category statistics for a date range.",
)
async def get_category_statistics(
    request: Request,
    start_date: date = Query(..., description="Start date"),
    end_date: date = Query(..., description="End date"),
    category_id: Optional[int] = Query(None, description="Category ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get category statistics for a date range."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access analytics"
        )
    
    return await AnalyticsAPI.get_category_statistics(request, start_date, end_date, category_id)


@analytics_router.get(
    "/locations",
    summary="Get location statistics",
    description="Get location statistics for a date range.",
)
async def get_location_statistics(
    request: Request,
    start_date: date = Query(..., description="Start date"),
    end_date: date = Query(..., description="End date"),
    location_id: Optional[int] = Query(None, description="Location ID"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get location statistics for a date range."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access analytics"
        )
    
    return await AnalyticsAPI.get_location_statistics(request, start_date, end_date, location_id)


@analytics_router.get(
    "/popular-searches",
    summary="Get popular searches",
    description="Get popular searches for a date range.",
)
async def get_popular_searches(
    request: Request,
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get popular searches for a date range."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access analytics"
        )
    
    return await AnalyticsAPI.get_popular_searches(request, start_date, end_date, limit)


@analytics_router.get(
    "/user-activity/{user_id}",
    summary="Get user activity",
    description="Get activity for a specific user.",
)
async def get_user_activity(
    request: Request,
    user_id: int,
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    action_type: Optional[str] = Query(None, description="Action type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get activity for a specific user."""
    # Check if the user is authorized
    if not current_user.is_staff and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this user's activity"
        )
    
    return await AnalyticsAPI.get_user_activity(request, user_id, start_date, end_date, action_type, limit)


@analytics_router.get(
    "/api-requests",
    summary="Get API requests",
    description="Get API request logs.",
)
async def get_api_requests(
    request: Request,
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    endpoint: Optional[str] = Query(None, description="Endpoint"),
    method: Optional[str] = Query(None, description="HTTP method"),
    status_code: Optional[int] = Query(None, description="Status code"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get API request logs."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access API request logs"
        )
    
    return await AnalyticsAPI.get_api_requests(
        request, start_date, end_date, endpoint, method, status_code, limit
    )


@analytics_router.post(
    "/generate",
    summary="Generate statistics",
    description="Generate statistics for a specific date.",
)
async def generate_statistics(
    request: Request,
    target_date: Optional[date] = Query(None, description="Target date (defaults to yesterday)"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Generate statistics for a specific date."""
    # Check if the user is authorized
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to generate statistics"
        )
    
    return await AnalyticsAPI.generate_statistics(request, target_date)
