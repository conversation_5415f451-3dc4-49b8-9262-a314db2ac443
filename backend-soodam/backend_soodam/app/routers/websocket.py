"""
WebSocket router for the Soodam backend.

This module provides WebSocket endpoints for real-time updates.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect, status
from pydantic import ValidationError

from ..core.websocket import WebSocketMessage, manager
from ..dependencies.auth import get_token_from_query
from ..models import CustomUserModel

logger = logging.getLogger(__name__)

# Create a router for WebSocket endpoints
websocket_router = APIRouter(tags=["websocket"])


@websocket_router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
):
    """
    WebSocket endpoint for real-time updates.
    
    Args:
        websocket: The WebSocket connection
        token: The authentication token (optional)
    """
    # Get user from token
    user_id = None
    if token:
        try:
            user = await get_token_from_query(token)
            if user:
                user_id = user.id
        except HTTPException:
            # Invalid token, but we'll still allow connection as anonymous
            pass
    
    # Connect the client
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                # Parse the message
                message_data = json.loads(data)
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    # Respond to ping with pong
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="pong",
                            data={"timestamp": message_data.get("data", {}).get("timestamp")},
                        ),
                        websocket
                    )
                
                elif message_type == "subscribe":
                    # Subscribe to a channel
                    channel = message_data.get("channel")
                    if channel:
                        await manager.subscribe(websocket, channel)
                
                elif message_type == "unsubscribe":
                    # Unsubscribe from a channel
                    channel = message_data.get("channel")
                    if channel:
                        await manager.unsubscribe(websocket, channel)
                
                else:
                    # Unknown message type
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="error",
                            data={"message": f"Unknown message type: {message_type}"},
                        ),
                        websocket
                    )
            
            except json.JSONDecodeError:
                # Invalid JSON
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Invalid JSON"},
                    ),
                    websocket
                )
            
            except ValidationError as e:
                # Invalid message format
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": f"Invalid message format: {str(e)}"},
                    ),
                    websocket
                )
            
            except Exception as e:
                # Other error
                logger.error(f"Error processing WebSocket message: {str(e)}")
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Internal server error"},
                    ),
                    websocket
                )
    
    except WebSocketDisconnect:
        # Client disconnected
        await manager.disconnect(websocket)
    
    except Exception as e:
        # Other error
        logger.error(f"WebSocket error: {str(e)}")
        await manager.disconnect(websocket)


@websocket_router.websocket("/ws/advertisements")
async def advertisements_websocket(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
):
    """
    WebSocket endpoint for advertisement updates.
    
    Args:
        websocket: The WebSocket connection
        token: The authentication token (optional)
    """
    # Get user from token
    user_id = None
    if token:
        try:
            user = await get_token_from_query(token)
            if user:
                user_id = user.id
        except HTTPException:
            # Invalid token, but we'll still allow connection as anonymous
            pass
    
    # Connect the client
    await manager.connect(websocket, user_id)
    
    # Subscribe to the advertisements channel
    await manager.subscribe(websocket, "advertisements")
    
    # Subscribe to user-specific channel if authenticated
    if user_id:
        await manager.subscribe(websocket, f"user:{user_id}:advertisements")
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                # Parse the message
                message_data = json.loads(data)
                message_type = message_data.get("type")
                
                if message_type == "ping":
                    # Respond to ping with pong
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="pong",
                            data={"timestamp": message_data.get("data", {}).get("timestamp")},
                        ),
                        websocket
                    )
                
                elif message_type == "subscribe_category":
                    # Subscribe to a category channel
                    category_id = message_data.get("data", {}).get("category_id")
                    if category_id:
                        await manager.subscribe(websocket, f"category:{category_id}:advertisements")
                
                elif message_type == "unsubscribe_category":
                    # Unsubscribe from a category channel
                    category_id = message_data.get("data", {}).get("category_id")
                    if category_id:
                        await manager.unsubscribe(websocket, f"category:{category_id}:advertisements")
                
                elif message_type == "subscribe_location":
                    # Subscribe to a location channel
                    location_id = message_data.get("data", {}).get("location_id")
                    if location_id:
                        await manager.subscribe(websocket, f"location:{location_id}:advertisements")
                
                elif message_type == "unsubscribe_location":
                    # Unsubscribe from a location channel
                    location_id = message_data.get("data", {}).get("location_id")
                    if location_id:
                        await manager.unsubscribe(websocket, f"location:{location_id}:advertisements")
                
                else:
                    # Unknown message type
                    await manager.send_personal_message(
                        WebSocketMessage(
                            type="error",
                            data={"message": f"Unknown message type: {message_type}"},
                        ),
                        websocket
                    )
            
            except json.JSONDecodeError:
                # Invalid JSON
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Invalid JSON"},
                    ),
                    websocket
                )
            
            except ValidationError as e:
                # Invalid message format
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": f"Invalid message format: {str(e)}"},
                    ),
                    websocket
                )
            
            except Exception as e:
                # Other error
                logger.error(f"Error processing WebSocket message: {str(e)}")
                await manager.send_personal_message(
                    WebSocketMessage(
                        type="error",
                        data={"message": "Internal server error"},
                    ),
                    websocket
                )
    
    except WebSocketDisconnect:
        # Client disconnected
        await manager.disconnect(websocket)
    
    except Exception as e:
        # Other error
        logger.error(f"WebSocket error: {str(e)}")
        await manager.disconnect(websocket)
