"""
Notification router for the <PERSON>dam backend.

This module provides endpoints for notification operations.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from app.api.v1.notification import NotificationsAPI
from app.dependencies.auth import get_current_user
from app.models import CustomUserModel
from app.schemas.notification import (
    NotificationDeviceCreateSchema,
    NotificationDeviceSchema,
    NotificationListSchema,
    NotificationPreferenceSchema,
    NotificationSchema,
    NotificationUpdateSchema,
)

# Create a router for notification endpoints
notification_router = APIRouter(tags=["notifications"])


@notification_router.get(
    "",
    response_model=NotificationListSchema,
    summary="Get notifications",
    description="Get notifications for the current user.",
)
async def get_notifications(
    request: Request,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Page size"),
    is_read: Optional[bool] = Query(None, description="Filter by read status"),
    notification_type: Optional[str] = Query(None, description="Filter by notification type"),
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get notifications for the current user."""
    return await NotificationsAPI.get_all_notifications(
        request, current_user, page, limit, is_read, notification_type
    )


@notification_router.get(
    "/{notification_id}",
    response_model=NotificationSchema,
    summary="Get notification",
    description="Get a specific notification.",
)
async def get_notification(
    request: Request,
    notification_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get a specific notification."""
    return await NotificationsAPI.get_notification(request, notification_id, current_user)


@notification_router.put(
    "/{notification_id}",
    response_model=NotificationSchema,
    summary="Update notification",
    description="Update a notification.",
)
async def update_notification(
    request: Request,
    notification_id: int,
    schema: NotificationUpdateSchema,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Update a notification."""
    if schema.is_read:
        return await NotificationsAPI.update_mark_notification(request, notification_id, current_user)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only marking as read is supported"
        )


@notification_router.post(
    "/{notification_id}/read",
    response_model=NotificationSchema,
    summary="Mark notification as read",
    description="Mark a notification as read.",
)
async def mark_notification_as_read(
    request: Request,
    notification_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Mark a notification as read."""
    return await NotificationsAPI.read_mark_notification(request, notification_id, current_user)


@notification_router.post(
    "/read-all",
    summary="Mark all notifications as read",
    description="Mark all notifications as read.",
)
async def mark_all_notifications_as_read(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Mark all notifications as read."""
    return await NotificationsAPI.read_mark_all_notifications(request, current_user)


@notification_router.delete(
    "/{notification_id}",
    summary="Delete notification",
    description="Delete a notification.",
)
async def delete_notification(
    request: Request,
    notification_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Delete a notification."""
    return await NotificationsAPI.delete_notification(request, notification_id, current_user)


@notification_router.delete(
    "",
    summary="Delete all notifications",
    description="Delete all notifications.",
)
async def delete_all_notifications(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Delete all notifications."""
    return await NotificationsAPI.delete_all_notifications(request, current_user)


@notification_router.get(
    "/preferences",
    response_model=NotificationPreferenceSchema,
    summary="Get notification preferences",
    description="Get notification preferences for the current user.",
)
async def get_notification_preferences(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get notification preferences for the current user."""
    return await NotificationsAPI.get_notification_preferences(request, current_user)


@notification_router.put(
    "/preferences",
    response_model=NotificationPreferenceSchema,
    summary="Update notification preferences",
    description="Update notification preferences for the current user.",
)
async def update_notification_preferences(
    request: Request,
    schema: NotificationPreferenceSchema,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Update notification preferences for the current user."""
    return await NotificationsAPI.update_notification_preferences(request, schema, current_user)


@notification_router.get(
    "/devices",
    response_model=List[NotificationDeviceSchema],
    summary="Get notification devices",
    description="Get notification devices for the current user.",
)
async def get_notification_devices(
    request: Request,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Get notification devices for the current user."""
    return await NotificationsAPI.get_notification_devices(request, current_user)


@notification_router.post(
    "/devices",
    response_model=NotificationDeviceSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create notification device",
    description="Create a notification device for the current user.",
)
async def create_notification_device(
    request: Request,
    schema: NotificationDeviceCreateSchema,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Create a notification device for the current user."""
    return await NotificationsAPI.create_notification_device(request, schema, current_user)


@notification_router.delete(
    "/devices/{device_id}",
    summary="Delete notification device",
    description="Delete a notification device.",
)
async def delete_notification_device(
    request: Request,
    device_id: int,
    current_user: CustomUserModel = Depends(get_current_user),
):
    """Delete a notification device."""
    return await NotificationsAPI.delete_notification_device(request, device_id, current_user)
