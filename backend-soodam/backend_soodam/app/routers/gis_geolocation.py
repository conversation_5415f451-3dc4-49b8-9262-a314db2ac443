from typing import Any

from fastapi import APIRouter, Depends, Request

from fastapi import APIRouter, Request

from .api_manager import VersionedRouter
from .versioning import *
from app.dependencies.auth import get_current_user, get_current_admin_user

from app.api import GisGeolocationAPI
from ..models import  CustomUserModel


gis_geolocation_router = APIRouter()


@gis_geolocation_router.get("/get_provinces",
                            # response_model=""
                            )
async def get_meta_data(request: Request) -> Any:
    return await GisGeolocationAPI.get_province(request)


@gis_geolocation_router.get("/get_cites_by_id/{province_id}",
                            # response_model=""
                            )

async def get_meta_data(request: Request, province_id: int) -> Any:
    return await GisGeolocationAPI.get_cites_by_province_id(request,province_id)

