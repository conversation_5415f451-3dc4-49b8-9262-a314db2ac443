from fastapi import APIRouter
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

home_router = APIRouter()
templates = Jinja2Templates(directory="templates")
# home_router.mount("/static", StaticFiles(directory="static"), name="static")


# templates = Jinja2Templates(directory="templates")


@home_router.get("/", response_class=HTMLResponse)
async def read_item(request: Request):
    return templates.TemplateResponse(
        # request=request,
        name="index.html",
        context={"request": request}
    )


# @home_router.get("/")
# def serve_home(request: Request):
#     return templates.TemplateResponse("index.html", context={"request": request})
