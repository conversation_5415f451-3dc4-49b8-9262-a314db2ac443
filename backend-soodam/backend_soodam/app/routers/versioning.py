from typing import Any, Callable, TypeVar

CallableT = TypeVar('CallableT', bound=Callable[..., Any])


def version(major: int, minor: int = 0) -> Callable[[CallableT], CallableT]:
    """
    Decorator for versioning FastAPI router endpoints with version (major, minor)
    Usage::
    >>> @router.get('/cheesecake')
    >>> @version(1, 2)
    >>> def index():
    ...    return {'cake': 'cheesecake'}
    """

    def decorator(func: CallableT) -> CallableT:
        func._api_version = (major, minor)
        return func

    return decorator
