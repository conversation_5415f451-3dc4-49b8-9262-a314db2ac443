from fastapi import APIRouter

# api_manager = APIRouter()

import string
from typing import <PERSON>ple

from fastapi import APIRouter
from fastapi.routing import APIRoute
from typing import Any, Callable, TypeVar
from fastapi import APIRouter, Request


CallableT = TypeVar('CallableT', bound=Callable[..., Any])

default_version: Tuple[int, int] = (1, 0)
default_version_prefix: str = "/api/{major}.{minor}"


class VersionedRouter(APIRouter):
    """
    Instantiate a FastAPI APIRouter to support easy endpoint versioning by
    the `version` decorator.
    Endpoints without this decorator will have `default_version` as version.
    The version_prefix defaults to `default_version_prefix`. If specified,
    it needs to include "major" and "minor" format keys.
    """

    def __init__(self, version_prefix=None, *args, **kwargs):
        version_prefix = version_prefix or default_version_prefix
        super().__init__(
            route_class=VersionedRoute,
            *args,
            **kwargs,
        )
        self.prefix = version_prefix + self.prefix


class VersionedRoute(APIRoute):
    """
    route_class for VersionedRouter.
    The class takes care of inserting the specified or default versions in
    the paths.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        version = getattr(self.endpoint, "_api_version", default_version)
        major, minor = version

        formatter = string.Formatter()
        mapping = FormatDict(major=major, minor=minor)
        self.path = formatter.vformat(self.path, (), mapping)


class FormatDict(dict):
    """
    use with string.Formatter for partial string formatting
    """

    def __missing__(self, key):
        return f"{{{key}}}"

# health_router = APIRouter()
# health_router = VersionedRouter(
#     version_prefix="/api/{major}.{minor}",
#     prefix="/health",
# )

# health_router.
