"""
Health check router for the <PERSON>dam backend.

This module provides endpoints for checking the health of the application and its dependencies.
"""

import time
from typing import Dict, List, Optional

from django.db import connection
from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel

from app.core.db_pool import get_connection_info

# Create a router for health check endpoints
health_router = APIRouter(tags=["Health"])

class HealthStatus(BaseModel):
    """Health status response model"""
    status: str
    version: str
    uptime: float
    database: Dict[str, str]
    connection_pool: Dict[str, any]
    dependencies: Dict[str, str]
    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

# Application start time
start_time = time.time()

# Application version
app_version = "1.0.0"


@health_router.get(
    "/health",
    response_model=HealthStatus,
    summary="Health check",
    description="Check the health of the application and its dependencies."
)
async def health_check(request: Request) -> HealthStatus:
    """
    Check the health of the application and its dependencies.

    Returns:
        HealthStatus: The health status of the application
    """
    # Check database connection
    db_status = "healthy"
    db_message = "Connected"

    try:
        # Execute a simple query to check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
    except Exception as e:
        db_status = "unhealthy"
        db_message = str(e)

    # Check dependencies
    dependencies = {
        "database": db_status
    }

    # Determine overall status
    status = "healthy"
    if "unhealthy" in dependencies.values():
        status = "unhealthy"

    # Calculate uptime
    uptime = time.time() - start_time

    # Get connection pool info
    connection_pool_info = get_connection_info()

    return HealthStatus(
        status=status,
        version=app_version,
        uptime=uptime,
        database={
            "status": db_status,
            "message": db_message
        },
        connection_pool=connection_pool_info,
        dependencies=dependencies
    )


@health_router.get(
    "/health/liveness",
    summary="Liveness probe",
    description="Check if the application is alive."
)
async def liveness_probe(request: Request) -> Dict[str, str]:
    """
    Liveness probe for Kubernetes.

    Returns:
        Dict[str, str]: The liveness status
    """
    return {"status": "alive"}


@health_router.get(
    "/health/readiness",
    summary="Readiness probe",
    description="Check if the application is ready to serve requests."
)
async def readiness_probe(request: Request) -> Dict[str, str]:
    """
    Readiness probe for Kubernetes.

    Returns:
        Dict[str, str]: The readiness status
    """
    # Check database connection
    try:
        # Execute a simple query to check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection failed: {str(e)}"
        )

    return {"status": "ready"}
