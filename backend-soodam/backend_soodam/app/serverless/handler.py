"""
Serverless function handler for the <PERSON><PERSON> backend.

This module provides handlers for serverless functions.
"""

import base64
import json
import logging
import os
import tempfile
import time
import uuid
from io import BytesIO
from typing import Dict, List, Optional, Tuple, Union

import boto3
from PIL import Image, ImageOps, ExifTags

logger = logging.getLogger(__name__)

# AWS S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
    aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
    region_name=os.environ.get('AWS_S3_REGION_NAME')
)

# S3 bucket name
S3_BUCKET = os.environ.get('AWS_STORAGE_BUCKET_NAME')

# Image sizes
IMAGE_SIZES = {
    'thumbnail': (150, 150),
    'small': (300, 300),
    'medium': (600, 600),
    'large': (1200, 1200)
}


def process_image(event: Dict, context: Dict) -> Dict:
    """
    Process an image uploaded to S3.
    
    This function is triggered when an image is uploaded to S3.
    It creates multiple resized versions of the image and uploads them to S3.
    
    Args:
        event: The event data
        context: The context data
        
    Returns:
        Dict: The response
    """
    try:
        logger.info("Processing image")
        
        # Get the S3 bucket and key from the event
        bucket = event['Records'][0]['s3']['bucket']['name']
        key = event['Records'][0]['s3']['object']['key']
        
        # Skip if the image is already processed
        if any(size in key for size in IMAGE_SIZES.keys()):
            logger.info(f"Skipping already processed image: {key}")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Image already processed'
                })
            }
        
        # Download the image from S3
        logger.info(f"Downloading image from S3: {bucket}/{key}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        image_data = response['Body'].read()
        
        # Process the image
        logger.info("Processing image")
        results = resize_image(image_data, key)
        
        # Return the results
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Image processed successfully',
                'results': results
            })
        }
    except Exception as e:
        logger.error(f"Error processing image: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error processing image: {str(e)}'
            })
        }


def resize_image(image_data: bytes, key: str) -> List[Dict]:
    """
    Resize an image to multiple sizes.
    
    Args:
        image_data: The image data
        key: The S3 key
        
    Returns:
        List[Dict]: The results
    """
    results = []
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(suffix='.jpg') as temp_file:
        temp_file.write(image_data)
        temp_file.flush()
        
        # Open the image
        with Image.open(temp_file.name) as img:
            # Fix orientation
            img = fix_orientation(img)
            
            # Get the original image format
            format = img.format or 'JPEG'
            
            # Get the base key (without extension)
            base_key = os.path.splitext(key)[0]
            
            # Resize the image to multiple sizes
            for size_name, dimensions in IMAGE_SIZES.items():
                # Resize the image
                resized_img = ImageOps.fit(img, dimensions, Image.LANCZOS)
                
                # Save the resized image to a BytesIO object
                buffer = BytesIO()
                resized_img.save(buffer, format=format, quality=85, optimize=True)
                buffer.seek(0)
                
                # Generate the new key
                new_key = f"{base_key}_{size_name}.jpg"
                
                # Upload the resized image to S3
                s3_client.upload_fileobj(
                    buffer,
                    S3_BUCKET,
                    new_key,
                    ExtraArgs={
                        'ContentType': f'image/{format.lower()}',
                        'ACL': 'public-read'
                    }
                )
                
                # Add the result
                results.append({
                    'size': size_name,
                    'key': new_key,
                    'url': f"https://{S3_BUCKET}.s3.amazonaws.com/{new_key}"
                })
    
    return results


def fix_orientation(img: Image.Image) -> Image.Image:
    """
    Fix the orientation of an image based on EXIF data.
    
    Args:
        img: The image
        
    Returns:
        Image.Image: The fixed image
    """
    try:
        # Get the EXIF data
        exif = img._getexif()
        
        if exif is None:
            return img
        
        # Get the orientation
        orientation_key = next((k for k, v in ExifTags.TAGS.items() if v == 'Orientation'), None)
        
        if orientation_key is None or orientation_key not in exif:
            return img
        
        orientation = exif[orientation_key]
        
        # Rotate the image based on the orientation
        if orientation == 2:
            # Horizontal flip
            return img.transpose(Image.FLIP_LEFT_RIGHT)
        elif orientation == 3:
            # Rotate 180 degrees
            return img.transpose(Image.ROTATE_180)
        elif orientation == 4:
            # Vertical flip
            return img.transpose(Image.FLIP_TOP_BOTTOM)
        elif orientation == 5:
            # Horizontal flip + rotate 90 degrees counter-clockwise
            return img.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.ROTATE_90)
        elif orientation == 6:
            # Rotate 270 degrees counter-clockwise
            return img.transpose(Image.ROTATE_270)
        elif orientation == 7:
            # Horizontal flip + rotate 270 degrees counter-clockwise
            return img.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.ROTATE_270)
        elif orientation == 8:
            # Rotate 90 degrees counter-clockwise
            return img.transpose(Image.ROTATE_90)
    except Exception as e:
        logger.error(f"Error fixing image orientation: {str(e)}")
    
    return img


def generate_image_metadata(event: Dict, context: Dict) -> Dict:
    """
    Generate metadata for an image.
    
    This function is triggered when an image is uploaded to S3.
    It generates metadata for the image, such as dimensions, format, and size.
    
    Args:
        event: The event data
        context: The context data
        
    Returns:
        Dict: The response
    """
    try:
        logger.info("Generating image metadata")
        
        # Get the S3 bucket and key from the event
        bucket = event['Records'][0]['s3']['bucket']['name']
        key = event['Records'][0]['s3']['object']['key']
        
        # Download the image from S3
        logger.info(f"Downloading image from S3: {bucket}/{key}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        image_data = response['Body'].read()
        
        # Generate metadata
        logger.info("Generating metadata")
        metadata = get_image_metadata(image_data)
        
        # Update the object metadata
        s3_client.copy_object(
            Bucket=bucket,
            CopySource={'Bucket': bucket, 'Key': key},
            Key=key,
            Metadata=metadata,
            MetadataDirective='REPLACE'
        )
        
        # Return the metadata
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Metadata generated successfully',
                'metadata': metadata
            })
        }
    except Exception as e:
        logger.error(f"Error generating metadata: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error generating metadata: {str(e)}'
            })
        }


def get_image_metadata(image_data: bytes) -> Dict:
    """
    Get metadata for an image.
    
    Args:
        image_data: The image data
        
    Returns:
        Dict: The metadata
    """
    metadata = {}
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(suffix='.jpg') as temp_file:
        temp_file.write(image_data)
        temp_file.flush()
        
        # Open the image
        with Image.open(temp_file.name) as img:
            # Get the image format
            metadata['format'] = img.format or 'JPEG'
            
            # Get the image dimensions
            metadata['width'] = img.width
            metadata['height'] = img.height
            
            # Get the image mode
            metadata['mode'] = img.mode
            
            # Get the image size
            metadata['size'] = len(image_data)
            
            # Get the EXIF data
            exif = img._getexif()
            
            if exif:
                # Extract relevant EXIF data
                for tag_id, tag_name in ExifTags.TAGS.items():
                    if tag_id in exif:
                        if tag_name in ['Make', 'Model', 'DateTime', 'GPSInfo']:
                            metadata[tag_name] = str(exif[tag_id])
    
    return metadata


def moderate_image_content(event: Dict, context: Dict) -> Dict:
    """
    Moderate image content using AWS Rekognition.
    
    This function is triggered when an image is uploaded to S3.
    It uses AWS Rekognition to detect inappropriate content in the image.
    
    Args:
        event: The event data
        context: The context data
        
    Returns:
        Dict: The response
    """
    try:
        logger.info("Moderating image content")
        
        # Get the S3 bucket and key from the event
        bucket = event['Records'][0]['s3']['bucket']['name']
        key = event['Records'][0]['s3']['object']['key']
        
        # Create a Rekognition client
        rekognition = boto3.client('rekognition')
        
        # Detect moderation labels
        response = rekognition.detect_moderation_labels(
            Image={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': key
                }
            },
            MinConfidence=50
        )
        
        # Get the moderation labels
        moderation_labels = response.get('ModerationLabels', [])
        
        # Check if the image contains inappropriate content
        is_inappropriate = any(
            label['Name'] in ['Explicit Nudity', 'Violence', 'Visually Disturbing', 'Drugs', 'Hate Symbols']
            for label in moderation_labels
        )
        
        # Update the object metadata
        s3_client.copy_object(
            Bucket=bucket,
            CopySource={'Bucket': bucket, 'Key': key},
            Key=key,
            Metadata={
                'moderation-status': 'inappropriate' if is_inappropriate else 'appropriate',
                'moderation-labels': json.dumps([label['Name'] for label in moderation_labels])
            },
            MetadataDirective='REPLACE'
        )
        
        # Return the moderation results
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Image moderation completed',
                'is_inappropriate': is_inappropriate,
                'moderation_labels': moderation_labels
            })
        }
    except Exception as e:
        logger.error(f"Error moderating image: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error moderating image: {str(e)}'
            })
        }
