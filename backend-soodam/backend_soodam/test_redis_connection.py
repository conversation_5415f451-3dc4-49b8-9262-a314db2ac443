#!/usr/bin/env python3
"""
Redis Connection Test Script
Tests the Redis connection using django-redis
"""

import os
import sys
import django
from django.conf import settings

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

def test_redis_connection():
    """Test Redis connection"""
    try:
        from django.core.cache import cache
        from django_redis import get_redis_connection
        
        print("🔍 Testing Redis connection...")
        
        # Test basic cache operations
        print("1. Testing basic cache operations...")
        cache.set('test_key', 'test_value', timeout=60)
        value = cache.get('test_key')
        print(f"   ✅ Cache set/get: {value}")
        
        # Test Redis client directly
        print("2. Testing Redis client directly...")
        redis_client = get_redis_connection("default")
        redis_client.set('redis_test', 'redis_value')
        redis_value = redis_client.get('redis_test')
        print(f"   ✅ Redis client: {redis_value}")
        
        # Test cache key pattern
        print("3. Testing cache key pattern...")
        cache.set('soodam:test:pattern', 'pattern_value')
        pattern_value = cache.get('soodam:test:pattern')
        print(f"   ✅ Cache pattern: {pattern_value}")
        
        # Clean up test keys
        cache.delete('test_key')
        cache.delete('soodam:test:pattern')
        redis_client.delete('redis_test')
        
        print("🎉 Redis connection test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Redis connection test FAILED: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1) 