#!/usr/bin/env python3
"""
Test script for phone number normalization

This script tests the phone number normalization function to ensure it works correctly
with various Iranian phone number formats.
"""

import re

# Iranian phone number regex
IRANIAN_PHONE_REGEX = r'^09[0-9]{9}$'

def normalize_phone_number(phone_number: str) -> str:
    """
    Normalize Iranian phone number format.

    Args:
        phone_number: Raw phone number

    Returns:
        Normalized phone number

    Raises:
        ValueError: If phone number format is invalid
    """
    # Remove spaces, dashes, and other characters
    cleaned = ''.join(filter(str.isdigit, phone_number))

    # Handle different formats
    if cleaned.startswith('0098') and len(cleaned) == 14:
        # Convert 00989123456789 to 09123456789
        cleaned = '0' + cleaned[4:]
    elif cleaned.startswith('98') and len(cleaned) == 12:
        # Convert 989123456789 to 09123456789
        cleaned = '0' + cleaned[2:]
    elif not cleaned.startswith('0') and len(cleaned) == 10:
        # Convert 9123456789 to 09123456789
        cleaned = '0' + cleaned

    # Validate format using re.match (FIXED: was cleaned.match())
    if not re.match(IRANIAN_PHONE_REGEX, cleaned):
        raise ValueError(f'فرمت شماره تلفن نامعتبر است: {cleaned}')

    return cleaned

def test_phone_normalization():
    """Test various phone number formats."""

    test_cases = [
        # (input, expected_output)
        ("09123456789", "09123456789"),      # Standard format
        ("9123456789", "09123456789"),       # Without leading zero (10 digits)
        ("989123456789", "09123456789"),     # International without + (12 digits)
        ("00989123456789", "09123456789"),   # International with country code (14 digits)
        ("+989123456789", "09123456789"),    # With + sign (12 digits after cleaning)
        ("091-234-56789", "09123456789"),    # With dashes
        ("091 234 56789", "09123456789"),    # With spaces
        ("(091) 234-56789", "09123456789"),  # With parentheses
    ]

    invalid_cases = [
        "08123456789",    # Wrong prefix
        "091234567890",   # Too long
        "0912345678",     # Too short
        "abc123456789",   # Contains letters
        "09123456abc",    # Contains letters at end
        "",               # Empty string
        "123",            # Too short
    ]

    print("=== Testing Valid Phone Numbers ===")
    for input_phone, expected in test_cases:
        try:
            result = normalize_phone_number(input_phone)
            status = "✅ PASS" if result == expected else "❌ FAIL"
            print(f"{status} | Input: {input_phone:15} | Expected: {expected} | Got: {result}")
        except Exception as e:
            print(f"❌ ERROR | Input: {input_phone:15} | Error: {e}")

    print("\n=== Testing Invalid Phone Numbers ===")
    for invalid_phone in invalid_cases:
        try:
            result = normalize_phone_number(invalid_phone)
            print(f"❌ FAIL | Input: {invalid_phone:15} | Should have failed but got: {result}")
        except ValueError as e:
            print(f"✅ PASS | Input: {invalid_phone:15} | Correctly rejected: {str(e)[:50]}...")
        except Exception as e:
            print(f"❌ ERROR | Input: {invalid_phone:15} | Unexpected error: {e}")

def test_regex_directly():
    """Test the regex pattern directly."""
    print("\n=== Testing Regex Pattern Directly ===")

    test_numbers = [
        "09123456789",    # Valid
        "09012345678",    # Valid (different operator)
        "09912345678",    # Valid (different operator)
        "08123456789",    # Invalid (wrong prefix)
        "091234567890",   # Invalid (too long)
        "0912345678",     # Invalid (too short)
    ]

    for number in test_numbers:
        match = re.match(IRANIAN_PHONE_REGEX, number)
        status = "✅ MATCH" if match else "❌ NO MATCH"
        print(f"{status} | {number}")

if __name__ == "__main__":
    print("Phone Number Normalization Test")
    print("=" * 50)

    test_phone_normalization()
    test_regex_directly()

    print("\n" + "=" * 50)
    print("Test completed!")
