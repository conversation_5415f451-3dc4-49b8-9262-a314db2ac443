service: soodam-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  memorySize: 512
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}
    AWS_ACCESS_KEY_ID: ${env:AWS_ACCESS_KEY_ID}
    AWS_SECRET_ACCESS_KEY: ${env:AWS_SECRET_ACCESS_KEY}
    AWS_S3_REGION_NAME: ${self:provider.region}
    AWS_STORAGE_BUCKET_NAME: ${self:custom.bucketName}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
        - s3:CopyObject
        - s3:ListBucket
      Resource:
        - arn:aws:s3:::${self:custom.bucketName}
        - arn:aws:s3:::${self:custom.bucketName}/*
    - Effect: Allow
      Action:
        - rekognition:DetectModerationLabels
      Resource: "*"

custom:
  bucketName: ${self:service}-${self:provider.stage}-uploads
  pythonRequirements:
    dockerizePip: true
    slim: true
    noDeploy:
      - pytest
      - pytest-cov
      - pytest-django
      - pytest-asyncio
      - black
      - flake8
      - isort
      - mypy
      - pylint
      - django
      - django-jalali
      - fastapi
      - uvicorn
      - starlette
      - httpx
      - psycopg2-binary
      - redis
      - celery
      - prometheus-client
      - graphene-django
  prune:
    automatic: true
    number: 3

package:
  individually: true
  patterns:
    - '!node_modules/**'
    - '!venv/**'
    - '!__pycache__/**'
    - '!.pytest_cache/**'
    - '!.coverage'
    - '!.git/**'
    - '!.github/**'
    - '!.vscode/**'
    - '!.idea/**'
    - '!.DS_Store'
    - '!*.pyc'
    - '!*.pyo'
    - '!*.pyd'
    - '!*.so'
    - '!*.dylib'
    - '!*.egg-info/**'
    - '!*.egg'
    - '!*.log'
    - '!*.sql'
    - '!*.sqlite3'
    - '!*.db'
    - '!*.bak'
    - '!*.swp'
    - '!*.swo'
    - '!*.tmp'
    - '!*.temp'
    - '!*.tox'
    - '!*.mypy_cache/**'
    - '!*.ipynb_checkpoints/**'
    - '!*.serverless/**'
    - '!*.webpack/**'
    - '!*.dynamodb/**'
    - '!*fastapi.env.dev'
    - '!*fastapi.env.dev.*'
    - '!*.envrc'
    - '!*.envrc.*'
    - '!*fastapi.env.dev.example'
    - '!*fastapi.env.dev.sample'
    - '!*fastapi.env.dev.template'
    - '!*fastapi.env.dev.test'
    - '!*fastapi.env.dev.development'
    - '!*fastapi.env.dev.production'
    - '!*fastapi.env.dev.staging'
    - '!*fastapi.env.dev.local'
    - '!*fastapi.env.dev.dev'
    - '!*fastapi.env.dev.prod'
    - '!*fastapi.env.dev.stage'
    - '!*fastapi.env.dev.test'
    - '!*fastapi.env.dev.ci'
    - '!*fastapi.env.dev.example'
    - '!*fastapi.env.dev.sample'
    - '!*fastapi.env.dev.template'
    - '!*fastapi.env.dev.test'
    - '!*fastapi.env.dev.development'
    - '!*fastapi.env.dev.production'
    - '!*fastapi.env.dev.staging'
    - '!*fastapi.env.dev.local'
    - '!*fastapi.env.dev.dev'
    - '!*fastapi.env.dev.prod'
    - '!*fastapi.env.dev.stage'
    - '!*fastapi.env.dev.test'
    - '!*fastapi.env.dev.ci'

functions:
  processImage:
    handler: app.serverless.handler.process_image
    events:
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: uploads/images/
            - suffix: .jpg
          existing: true
    package:
      patterns:
        - app/serverless/handler.py
        - requirements.txt
  
  generateImageMetadata:
    handler: app.serverless.handler.generate_image_metadata
    events:
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: uploads/images/
            - suffix: .jpg
          existing: true
    package:
      patterns:
        - app/serverless/handler.py
        - requirements.txt
  
  moderateImageContent:
    handler: app.serverless.handler.moderate_image_content
    events:
      - s3:
          bucket: ${self:custom.bucketName}
          event: s3:ObjectCreated:*
          rules:
            - prefix: uploads/images/
            - suffix: .jpg
          existing: true
    package:
      patterns:
        - app/serverless/handler.py
        - requirements.txt

plugins:
  - serverless-python-requirements
  - serverless-prune-plugin
