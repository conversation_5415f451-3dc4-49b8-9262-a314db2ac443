version: "3.7"
services:
  fastapi_prod:
    container_name: soodam_prod_backend
    build:
      context: "./backend_soodam"
      target: "prod"
    working_dir: /src
      # restart: always
    volumes:
      - ./backend_soodam:/src
      - ./logs:/src/logs
#      - ./data/media:/src/media
#      - ./data/static:/src/static
      - ./backend_soodam/media:/src/media
      - ./backend_soodam/static:/src/static
    ports:
      - "8000:8000"
      - "8001:8001"
    environment:
      DB_NAME: soodam_prod
      DB_USER: soodam_prod
      DB_PASSWORD: soodam_prod_pass_123456
      DB_HOST: postgres_prod
      DB_PORT: 5432
      #      - ELASTICSEARCH_URL=http://elasticsearch:9200
      REDIS_URL: redis://redis:6379/0
      ENVIRONMENT: production
      ENV_STATE: production
      STATIC_ROOT: /src/static
      MEDIA_ROOT: /src/media
    env_file:
      - ./backend_soodam/fastapi.env
    depends_on:
      - postgres_prod
      - redis
        #      - elasticsearch
#    command: ./scripts/runlocalserver.sh
    command: ./scripts/runlocalserver.sh

  postgres_prod:
    container_name: soodam_prod_postgres
    image: 'postgis/postgis:latest'
      # restart: always
    volumes:
#      - postgres_data_prod:/var/lib/postgresql/data
      - ./data/postgres_prod:/var/lib/postgresql/data
#      - ./postgres-config/pg_hba.conf:/etc/postgresql/pg_hba.conf
#      - ./postgres-config/postgresql.conf:/etc/postgresql/postgresql.conf
    environment:
      POSTGRES_USER: soodam_prod
      POSTGRES_PASSWORD: soodam_prod_pass_123456
      POSTGRES_DB: soodam_prod
#      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - 5432:5432
#    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
#  pgadmin:
#    container_name: PgAdmin_container
#    image: 'dpage/pgadmin4:latest'
#    restart: always
#    depends_on:
#      - postgres_prod
#    environment:
#      PGADMIN_DEFAULT_EMAIL: <EMAIL>
#      PGADMIN_DEFAULT_PASSWORD: pgadmin4
#    ports:
#      - "5050:80"
  redis:
    container_name: soodam_prod_redis
    image: redis:alpine
      # restart: always
    volumes:
      - ./data/redis:/data
    ports:
      - "6381:6378"
#  elasticsearch:
#    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
#    container_name: soodam_prod_elasticsearch
#    restart: always
#    environment:
#      - discovery.type=single-node
#      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
#    volumes:
#      - ./data/elasticsearch:/usr/share/elasticsearch/data
#    ports:
#      - "9200:9200"

#volumes:
#  postgres_data_prod:
#  elasticsearch_data:
