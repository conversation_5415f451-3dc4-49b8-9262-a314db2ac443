@import 'tailwindcss/base';
@import '/fonts/estedad.css';
@import '/fonts/iranyekan.css';
@import '/abstracts';

@import 'tailwindcss/components';
@import 'components.css';

@import 'tailwindcss/utilities';

@layer utilities {
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .line-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
      
}
body {
  background-color: #F6F7FB;
}
.hide-scrollbar {
    overflow-x: auto;
    overflow-y: hidden;
    -ms-overflow-style: none; /* برای اینترنت اکسپلورر و اج */
    scrollbar-width: none; /* برای فایرفاکس */
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* برای کروم، سافاری و اج */
  }

/* other style  */
.leaflet-popup-tip {
    display: none;
}
.leaflet-popup-content-wrapper{
    box-shadow: none !important;
}
.leaflet-popup-content {
    
}

.leaflet-control-zoom {
    display : none
}

.leaflet-control-attribution {
    display: none;
}

.shadow-icon {
    box-shadow: -2px 6px 10px 3px rgba(0, 0, 0, 0.2);
}

.shadow-nav {
    box-shadow: 4px -2px 8px 0px rgba(0, 0, 0, 0.25);
}

.shadow-filter-control {
    box-shadow: 0px 12px 32px 0px #A8A8A826;
}

.triangle {
    width: 0;
    height: 0;
    border-top: 8px solid #D52133;
    border-right: 8px solid transparent;
}

.custom-w{
  width : 32px !important
}

.red-checked {
  border: 1px solid #D52133 !important;
  color:#D52133;
}
.custom-checkbox:checked {
  background-color: red !important; /* رنگ پس‌زمینه به قرمز تغییر می‌کند */
  border-color: red !important; /* مرز قرمز (اختیاری) */
}

.icons {
  width: 32px;
  height: 32px;
}

.custom-dashed {
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='4' ry='4' stroke='%23E3E3E7' stroke-width='2' stroke-dasharray='5' stroke-dashoffset='4' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 4px;
}

.custom-dashed-marketer {
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='4' ry='4' stroke='%23E3E3E7' stroke-width='2' stroke-dasharray='5' stroke-dashoffset='4' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 5px;
}

.custom-dashed-admin {
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='100' ry='100' stroke='%23333' stroke-width='3' stroke-dasharray='14%2c 17' stroke-dashoffset='77' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 100px;
  width: 80px;
  height: 80px;
}

.shadow-bottom {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
.shadow-request {
  box-shadow: 0px 1px 12px 0px rgba(26, 30, 37, 0.12);
}
.bg-dashed {
  background-image: url("/static/Circle.png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}
.bg-dashed2 {
  background-image: url("/static/Circle.png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}
.bg-dashed3 {
  background-image: url("/static/Circle.png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}

.bg-dashed4 {
  background-image: url("/static/Circle.png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}

.bg-dashed5 {
  background-image: url("/static/Circle.png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}

.bg-dashed6 {
  background-image: url("/static/Circle (1).png") !important;
  background-size: cover; /* یا contain */
  background-repeat: no-repeat;
  background-position: center;
  width: 80px !important;
  height: 80px !important;
}

