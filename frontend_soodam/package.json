{"name": "soodam-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "mockServiceWorker": "npx msw init public/ --save"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@reduxjs/toolkit": "^2.4.0", "@tailwindcss/forms": "^0.5.9", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/turf": "^7.2.0", "@types/react-leaflet": "^2.8.3", "@types/recharts": "^1.8.29", "axios": "^1.7.9", "dayjs": "^1.11.13", "geolib": "^3.3.4", "iran-city": "^1.2.1", "jalaali-js": "^1.2.7", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "moment-jalaali": "^0.10.4", "msw": "^1.3.2", "next": "15.0.3", "nprogress": "^0.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "react-leaflet": "^4.2.1", "react-redux": "^9.1.2", "react-select": "^5.10.0", "react-toastify": "^10.0.6", "recharts": "^2.15.0", "swiper": "^11.2.3", "yup": "^1.4.0"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/leaflet": "^1.9.15", "@types/leaflet-draw": "^1.0.11", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "msw": {"workerDirectory": "public"}}